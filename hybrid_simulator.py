#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cryptocurrency Trading Simulator Execution Script
3-minute interval cryptocurrency trading system integrating hybrid architecture (InCA, HiAR, SELA)
"""

import os
import sys
import time
import logging
import argparse
import traceback
import json
import re
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# 로그 설정 임포트
from log_config import *

# Windows 환경에서 UTF-8 인코딩 설정
if sys.platform == 'win32':
    # 환경 변수 설정
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 콘솔 코드 페이지를 UTF-8로 변경
    os.system('chcp 65001 > NUL')

# 로깅 시스템 강제 초기화 (시간 표시 문제 해결)
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# 기본 로깅 설정 (콘솔에 시간 표시)
# Windows 콘솔에서 유니코드 문제 해결을 위해 UTF-8 인코딩 사용
import codecs

# 표준 출력을 UTF-8로 설정 (Windows에서만)
if sys.platform == 'win32':
    import io
    if not isinstance(sys.stdout, io.TextIOWrapper) and hasattr(sys.stdout, 'buffer'):
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    if not isinstance(sys.stderr, io.TextIOWrapper) and hasattr(sys.stderr, 'buffer'):
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 로그 디렉토리 생성
log_dir = os.path.join(os.getcwd(), "logs")
os.makedirs(log_dir, exist_ok=True)

# 로그 파일명 생성
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = os.path.join(log_dir, f"hybrid_{timestamp}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_filename, encoding='utf-8')
    ]
)

# Add project root path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# Logging setup - use existing root logger configuration
def setup_logging():
    # Create log directory
    log_dir = os.path.join(os.getcwd(), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Log filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = os.path.join(log_dir, f"hybrid_{timestamp}.log")

    # Get root logger (already configured with console handler)
    root_logger = logging.getLogger()

    # Add file handler to root logger
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                                      datefmt='%Y-%m-%d %H:%M:%S')
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)

    # Set logger levels by module
    for logger_name in ["simulator", "trading", "hybrid_architecture", "models"]:
        module_logger = logging.getLogger(logger_name)
        module_logger.setLevel(logging.INFO)
        # Ensure propagation to root logger
        module_logger.propagate = True

    # Create and return the main logger
    main_logger = logging.getLogger("hybrid_simulator")
    main_logger.setLevel(logging.INFO)

    # Log a test message to verify timestamp is showing
    main_logger.info("로깅 시스템 초기화 완료 - 타임스탬프 테스트")

    return main_logger

# 하이브리드 시뮬레이터 모듈 임포트
from simulator.bridge import HybridSimulatorBridge
from simulator.utils.market_utils import get_current_market_data
from simulator.trading.portfolio import ExtendedPortfolio
from binance.binance_utils import BinanceUtils

# 고급 트레이딩 시스템 임포트
try:
    from trading.strategies.advanced_trading_system import AdvancedTradingSystem
    ADVANCED_TRADING_AVAILABLE = True
    print("고급 트레이딩 시스템 임포트 성공")  # logger가 아직 정의되지 않았으므로 print 사용
except ImportError as e:
    ADVANCED_TRADING_AVAILABLE = False
    print(f"고급 트레이딩 시스템 임포트 실패: {e}")  # logger가 아직 정의되지 않았으므로 print 사용

# ChatTS 모델 로더 임포트
logger = logging.getLogger("hybrid_simulator")
try:
    import torch
    logger.info("PyTorch 임포트 성공")
except ImportError as e:
    logger.warning(f"PyTorch 임포트 실패: {e}")
    # PyTorch 없이도 계속 진행

try:
    from models.chatts_client import ChatTSClient
    logger.info("ChatTS 클라이언트 임포트 성공")
except ImportError as e:
    logger.error(f"ChatTS 모델 로더 임포트 실패: {e}")
    # 모델 로더 없이도 계속 진행

# 모델 타입 상수 정의
MODEL_TYPE_CHATTS = "chatts"

class HybridTrading:
    """
    통합 하이브리드 거래 시스템 클래스
    실행 플로우(InCA→HiAR→SELA→실행)와 학습 루프(SELA→HiAR→InCA→학습) 관리
    """

    def __init__(self, symbols=None, trading_mode='real', testnet=False, initial_balance=10000.0):
        """
        하이브리드 거래 시스템 초기화

        Args:
            symbols: 거래 심볼 목록
            trading_mode: 거래 모드 ('simulation' 또는 'real')
            testnet: 테스트넷 사용 여부
            initial_balance: 초기 잔액 (USDT)
        """
        self.logger = logging.getLogger("hybrid_simulator")
        self.symbols = symbols or ["BTC", "ETH", "BNB", "SOL"]
        self.trading_mode = trading_mode
        self.testnet = testnet
        self.initial_balance = initial_balance

        # 디렉터리 설정
        self.data_dir = os.path.join(os.getcwd(), 'data')
        self.logs_dir = os.path.join(os.getcwd(), 'logs')

        # 데이터 디렉터리
        self.execution_logs_dir = os.path.join(self.data_dir, 'execution_logs')
        self.learning_data_dir = os.path.join(self.data_dir, 'learning_data')
        self.reasoning_dir = os.path.join(self.data_dir, 'reasoning_traces')
        self.vector_db_dir = os.path.join(self.data_dir, 'vector_db')

        # 디렉터리 생성
        for directory in [self.data_dir, self.logs_dir, self.execution_logs_dir,
                         self.learning_data_dir, self.reasoning_dir, self.vector_db_dir]:
            os.makedirs(directory, exist_ok=True)

        # LLM 모델 설정 (기본값은 None, vLLM 사용 시 나중에 설정됨)
        self.chatts_model = None
        self.logger.info("LLM 모델 설정 준비 완료 (vLLM 사용 시 나중에 설정됨)")

        # 시스템 설명 초기화 (에이전트에 전달되는 기본 설명)
        self.system_description = """
# Hybrid Trading System
- InCA analyzes market data and determines importance
- HiAR organizes reasoning and creates analysis
- SELA generates and executes trading strategies
"""

        # 하이브리드 시스템 설정
        self.config = {
            'symbols': self.symbols,
            'mode': self.trading_mode,
            'trading_mode': self.trading_mode,  # 🚀 실제 거래 모드 설정 추가
            'testnet': self.testnet,
            'data_dir': self.data_dir,
            'collection_interval': 180,  # 3분
            'learning_interval': 3600,   # 1시간
            'importance_threshold': 0.3, # 중요도 임계값 (0.7에서 0.3으로 낮춤)
            'diverse_strategies_count': 5, # 다양한 전략 수
            'llm_model': self.chatts_model  # ChatTS 모델 추가
        }

        # 바이낸스 유틸리티 초기화
        self.binance_utils = BinanceUtils(testnet=self.testnet)
        self.logger.info(f"바이낸스 API 초기화 완료 (테스트넷: {self.testnet})")

        # 포트폴리오 관리자 초기화 (ExtendedPortfolio 사용)
        self.portfolio = ExtendedPortfolio(
            base_currency='USDT',
            initial_balance=self.initial_balance,
            save_path=f'{self.data_dir}/portfolio_data.json',
            max_positions=5,
            mode=self.trading_mode,
            testnet=self.testnet,
            data_dir=self.data_dir,
            binance_utils=self.binance_utils
        )

        # 고급 트레이딩 시스템 초기화
        self.advanced_trading_system = None
        if ADVANCED_TRADING_AVAILABLE:
            try:
                # 환경 변수에서 고급 트레이딩 시스템 활성화 여부 확인
                enable_advanced = os.getenv('ENABLE_ADVANCED_TRADING_SYSTEM', 'false').lower() == 'true'
                if enable_advanced:
                    self.advanced_trading_system = AdvancedTradingSystem(
                        data_dir=os.path.join(self.data_dir, 'trading_patterns')
                    )
                    self.logger.info("고급 트레이딩 시스템 초기화 완료")
                else:
                    self.logger.info("고급 트레이딩 시스템이 비활성화되어 있습니다 (ENABLE_ADVANCED_TRADING_SYSTEM=false)")
            except Exception as e:
                self.logger.error(f"고급 트레이딩 시스템 초기화 실패: {e}")
                self.advanced_trading_system = None

        # 하이브리드 컨트롤러 초기화
        try:
            from trading.hybrid_architecture.hybrid_controller import HybridController
            from trading.hybrid_architecture.data_store import DataStore
            from trading.hybrid_architecture.data_logger import DataLogger
            from trading.hybrid_architecture.agents.inca_agent import InCAAgent
            from trading.hybrid_architecture.agents.hiar_factory import create_default_hiar
            from trading.hybrid_architecture.agents.sela_agent import SELAAgent

            # 데이터 스토어 및 로거 초기화
            data_store = DataStore(os.path.join(self.data_dir, 'hybrid_store.db'))
            execution_logger = DataLogger(data_store, self.data_dir)

            # 모델 타입 로깅
            if self.chatts_model:
                model_type = self.chatts_model.get("model_type", "unknown")
                model_name = self.chatts_model.get("model_name", "unknown")
                self.logger.info(f"LLM 모델 사용: {model_type} - {model_name}")

                # 에이전트 초기화 (학습 기능 활성화)
                inca_agent = InCAAgent(
                    llm_model=self.chatts_model,
                    importance_threshold=0.3
                )
                hiar_agent = create_default_hiar(llm_model=self.chatts_model)

                # SELAAgent는 config를 통해 llm_model을 전달
                sela_config = self.config.copy()
                sela_config['llm_model'] = self.chatts_model
                sela_agent = SELAAgent(config=sela_config)

                # 하이브리드 컨트롤러 생성
                self.hybrid_controller = HybridController(
                    inca_agent=inca_agent,
                    hiar_agent=hiar_agent,
                    sela_agent=sela_agent,
                    data_store=data_store,
                    execution_logger=execution_logger,
                    config=self.config,
                    binance_utils=self.binance_utils,
                    vllm_queue=getattr(self, 'vllm_queue', None),
                    continuous_mode=getattr(self, 'continuous_mode', False)
                )
                self.logger.info("하이브리드 컨트롤러 초기화 완료")

                # config에 llm_client 추가 (브릿지에서 사용)
                bridge_config = self.config.copy()
                bridge_config['llm_client'] = self.chatts_model

                # 하이브리드 시뮬레이터 브릿지 초기화
                self.bridge = HybridSimulatorBridge(self.hybrid_controller, bridge_config)
                self.logger.info("하이브리드 시뮬레이터 브릿지 초기화 완료")
            else:
                self.logger.warning("LLM 모델이 설정되지 않았습니다. 하이브리드 컨트롤러 초기화를 건너뜁니다.")
                self.logger.warning("나중에 chatts_model을 설정한 후 initialize_controller() 메서드를 호출하세요.")
        except Exception as e:
            self.logger.error(f"하이브리드 컨트롤러 초기화 실패: {e}")
            self.logger.error(traceback.format_exc())
            raise RuntimeError(f"하이브리드 컨트롤러 초기화 실패: {e}")

        # 시장 데이터 캐시
        self.market_data_cache = {}
        self.last_portfolio_update = 0

        # vLLM 요청 큐 및 연속 모드 초기화
        self.vllm_queue = None  # 나중에 설정됨
        self.continuous_mode = False  # 기본값

    def start(self, continuous_mode=False):
        """
        하이브리드 시스템 시작

        Args:
            continuous_mode: 연속 모드 활성화 여부 (실행 → 학습 → 실행 → 학습 순서로 연속 실행)
        """
        mode_str = "실제 거래" if self.trading_mode == "real" and not self.testnet else \
                 "테스트넷 거래" if self.trading_mode == "real" and self.testnet else "시뮬레이션"
        self.logger.info(f"하이브리드 거래 시스템 시작: {len(self.symbols)} 심볼, 모드: {mode_str}")

        # 시스템 설명 메시지 출력 (한국어로 유지)
        print("\n" + "="*80)
        print("🚀 하이브리드 거래 시스템이 시작되었습니다.")
        print("="*80)
        print("✅ 1. InCA → HiAR → SELA → 실시간 실행")
        print("➤ 지금 당장 시장 반응에 따라 빠르게 판단하고 투자하는 실전 대응 시스템")
        print("\n✅ 2. SELA → HiAR → InCA → 학습 루프")
        print("➤ 다양한 전략을 실험하고, 논리 흐름 정리하고, 과거 사례와 비교해 지속 학습하고 강화하는 연구 루프")
        print("="*80)
        print(f"📊 현재 거래 심볼: {', '.join(self.symbols)}")
        print(f"🔄 거래 모드: {mode_str}")
        if continuous_mode:
            print("🔄 연속 모드: 활성화 (실행 → 학습 → 실행 → 학습 순서로 연속 실행)")
        else:
            print("🔄 연속 모드: 비활성화 (실행 및 학습 독립적 실행)")
        print("="*80 + "\n")

        # LLM 시스템 설명 생성 (영어로)
        self.system_description = """
# Hybrid Trading System Workflow

## 1. Execution Flow (InCA → HiAR → SELA) - Real-time Investment Decisions
- News and price data are received at regular intervals
- InCA determines if the information is significant
- HiAR organizes the reasoning process
- SELA generates strategies and executes investments immediately

## 2. Learning Loop (SELA → HiAR → InCA) - Strategy Evaluation & Enhancement
- SELA generates various experimental strategies
- HiAR organizes reasoning for each strategy
- InCA evaluates strategies based on accumulated execution logs

You are a critical component of this system.
"""

        # 연속 모드 로깅
        if continuous_mode:
            self.logger.info("연속 모드 활성화: 실행 → 학습 → 실행 → 학습 순서로 연속 실행")
        else:
            self.logger.info("독립 모드 활성화: 실행 및 학습 독립적 실행")

        # 초기 시장 데이터 로드
        self.load_initial_market_data()

        # 포트폴리오 상태 출력
        if SHOW_PORTFOLIO_LOG:
            self.portfolio.log_portfolio_status(self.market_data_cache, self.binance_utils)

        # LLM 모델이 설정된 경우 컨트롤러 초기화
        if self.chatts_model:
            # 하이브리드 컨트롤러 초기화
            if not hasattr(self, 'hybrid_controller') or not self.hybrid_controller:
                if not self.initialize_controller():
                    self.logger.error("하이브리드 컨트롤러 초기화 실패. 시스템을 시작할 수 없습니다.")
                    return

            # 🔧 연속 모드 여부에 따라 다른 시작 방식 사용
            if continuous_mode:
                # 연속 모드: 하이브리드 컨트롤러 직접 시작
                self.logger.info("🚀 연속 모드: 하이브리드 컨트롤러 직접 시작")

                # 연속 모드 설정
                if hasattr(self.hybrid_controller, 'continuous_mode'):
                    self.hybrid_controller.continuous_mode = continuous_mode
                    self.logger.info(f"하이브리드 컨트롤러 연속 모드 설정: {self.hybrid_controller.continuous_mode}")
                else:
                    setattr(self.hybrid_controller, 'continuous_mode', continuous_mode)
                    self.logger.info(f"하이브리드 컨트롤러에 continuous_mode 속성 추가: {continuous_mode}")

                # 시뮬레이터 연결 (포지션 실행을 위해)
                if hasattr(self, 'portfolio'):
                    self.hybrid_controller.simulator = self.portfolio
                    self.logger.info("하이브리드 컨트롤러에 포트폴리오 시뮬레이터 연결")

                # 하이브리드 컨트롤러 직접 시작 (연속 모드)
                self.hybrid_controller.start(continuous_mode=True)
                self.logger.info("🚀 하이브리드 컨트롤러 연속 모드 시작 완료")

            else:
                # 기존 방식: 브릿지 시작
                if hasattr(self, 'bridge') and self.bridge:
                    # 연속 모드 설정 전달
                    if hasattr(self.hybrid_controller, 'continuous_mode'):
                        self.hybrid_controller.continuous_mode = continuous_mode
                        self.logger.info(f"하이브리드 컨트롤러 연속 모드 설정: {self.hybrid_controller.continuous_mode}")
                    else:
                        # 속성이 없으면 추가
                        setattr(self.hybrid_controller, 'continuous_mode', continuous_mode)
                        self.logger.info(f"하이브리드 컨트롤러에 continuous_mode 속성 추가: {continuous_mode}")

                    # 브릿지 시작
                    self.bridge.start()
                    self.logger.info("하이브리드 시뮬레이터 브릿지 실행 중")
                else:
                    self.logger.error("브릿지가 초기화되지 않았습니다. 시스템을 시작할 수 없습니다.")
                    return
        else:
            self.logger.warning("LLM 모델이 설정되지 않았습니다. 하이브리드 컨트롤러를 초기화할 수 없습니다.")
            self.logger.warning("chatts_model을 설정한 후 다시 시작하세요.")

    def stop(self):
        """하이브리드 시스템 종료"""
        self.logger.info("하이브리드 거래 시스템 종료 중...")

        # 🔧 연속 모드 여부에 따라 다른 종료 방식 사용
        if hasattr(self, 'hybrid_controller') and self.hybrid_controller:
            if hasattr(self.hybrid_controller, 'continuous_mode') and self.hybrid_controller.continuous_mode:
                # 연속 모드: 하이브리드 컨트롤러 직접 종료
                try:
                    self.hybrid_controller.stop()
                    self.logger.info("🚀 하이브리드 컨트롤러 연속 모드 종료 완료")
                except Exception as e:
                    self.logger.error(f"하이브리드 컨트롤러 종료 중 오류: {e}")
            else:
                # 기존 방식: 브릿지 종료
                if hasattr(self, 'bridge') and self.bridge:
                    try:
                        self.bridge.stop()
                        self.logger.info("하이브리드 시뮬레이터 브릿지 종료 완료")
                    except Exception as e:
                        self.logger.error(f"하이브리드 브릿지 종료 중 오류: {e}")
                else:
                    self.logger.info("브릿지가 초기화되지 않았습니다. 브릿지 종료를 건너뜁니다.")
        else:
            self.logger.info("하이브리드 컨트롤러가 초기화되지 않았습니다.")

        # 최종 포트폴리오 상태 출력
        if hasattr(self, 'portfolio'):
            # 🔥 실제 거래 모드인 경우 최종 포지션 정보 동기화
            if self.trading_mode == 'real' and self.binance_utils:
                try:
                    sync_result = self.portfolio.sync_positions_with_exchange(self.binance_utils, symbols=self.symbols, force=True)

                    if sync_result.get('success', False):
                        if SHOW_SYNC_LOG:
                            self.logger.info("🔥 종료 전 포지션 정보 동기화 완료")
                            self.logger.info(f"🔥 최종 동기화: {sync_result['positions_before']} → {sync_result['positions_after']} 포지션")

                        # 🔥 최종 동기화 변경사항 로깅
                        if sync_result.get('changes_made'):
                            self.logger.info(f"🔥 최종 동기화 변경사항:")
                            for change in sync_result['changes_made']:
                                self.logger.info(f"   - {change}")
                    else:
                        self.logger.warning("🔥 종료 전 포지션 정보 동기화 실패")
                        if sync_result.get('errors'):
                            for error in sync_result['errors']:
                                self.logger.error(f"   - 최종 동기화 오류: {error}")

                except Exception as e:
                    self.logger.error(f"종료 전 포지션 정보 동기화 실패: {e}")

            total_value = self.portfolio.calculate_portfolio_value(self.market_data_cache)

            # 초기 잔액 및 손익 계산
            initial_balance = self.portfolio.performance.get("initial_balance", 10000.0)
            total_pnl = total_value - initial_balance
            total_pnl_pct = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0

            self.logger.info(f"최종 포트폴리오 총 가치: ${total_value:.2f}")
            self.logger.info(f"초기 잔액: ${initial_balance:.2f}")
            self.logger.info(f"총 손익: ${total_pnl:.2f} ({total_pnl_pct:.2f}%)")

            if SHOW_PORTFOLIO_LOG:
                self.portfolio.log_portfolio_status(self.market_data_cache, self.binance_utils)

        self.logger.info("하이브리드 거래 시스템 종료 완료")

    def load_initial_market_data(self):
        """초기 시장 데이터 로드"""
        self.logger.info("초기 시장 데이터 로드 중...")

        try:
            # API 키 상태 로깅 (에러 진단용)
            if self.testnet:
                api_key = os.getenv('BINANCE_TESTNET_API_KEY')
                api_secret = os.getenv('BINANCE_TESTNET_API_SECRET')
                api_type = "테스트넷"
            else:
                api_key = os.getenv('BINANCE_API_KEY')
                api_secret = os.getenv('BINANCE_API_SECRET')
                api_type = "메인넷"

            if not api_key or not api_secret:
                self.logger.warning(f"바이낸스 {api_type} API 키가 설정되지 않았습니다.")
                self.logger.warning(f"{api_type} 환경 변수를 설정하세요.")
                raise ValueError("API 키가 필요합니다. 환경 변수를 설정하세요.")

            # 계정 잔액 정보 가져오기
            if self.binance_utils:
                try:
                    account_balance = self.binance_utils.get_futures_account_balance()
                    if account_balance and isinstance(account_balance, list):
                        usdt_asset = next((asset for asset in account_balance if asset['asset'] == 'USDT'), None)
                        if usdt_asset:
                            wallet_balance = float(usdt_asset.get('walletBalance', 0))
                            available_balance = float(usdt_asset.get('availableBalance', 0))
                            self.logger.info(f"USDT 잔액 정보 (API: {api_type}):")
                            self.logger.info(f"  지갑 잔액: {wallet_balance:.2f} USDT")
                            self.logger.info(f"  사용 가능 잔액: {available_balance:.2f} USDT")

                            # 포트폴리오 잔액 업데이트
                            if hasattr(self, 'portfolio'):
                                # 이전 잔액 저장
                                previous_balance = self.portfolio.balance

                                # 잔액 업데이트
                                self.portfolio.update_balance(wallet_balance)

                                # 업데이트 로그
                                self.logger.info(f"포트폴리오 잔액 업데이트: ${previous_balance:.2f} → ${wallet_balance:.2f} USDT")

                                # 초기 잔액 및 총 가치 계산
                                initial_balance = self.portfolio.performance.get("initial_balance", 10000.0)
                                total_value = wallet_balance  # 현재는 잔액만 있음 (포지션 동기화 전)
                                total_pnl = total_value - initial_balance
                                total_pnl_pct = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0

                                # 포트폴리오 가치 정보 로깅
                                self.logger.info(f"포트폴리오 총 가치: ${total_value:.2f}")
                                self.logger.info(f"초기 잔액: ${initial_balance:.2f}")
                                self.logger.info(f"총 손익: ${total_pnl:.2f} ({total_pnl_pct:.2f}%)")

                                # 🔥 사이클 시작 시 한 번만 포지션 동기화 (실제 거래 모드인 경우)
                                if self.trading_mode == 'real' and symbol == self.symbols[0]:  # 첫 번째 심볼에서만 동기화
                                    self.logger.info("🔄 사이클 시작 - 포지션 동기화 실행")
                                    sync_result = self.portfolio.sync_positions_with_exchange(self.binance_utils, symbols=self.symbols)
                                elif self.trading_mode == 'real':
                                    sync_result = {'success': True, 'skipped': True, 'reason': 'cycle_sync_already_done'}

                                    if sync_result.get('success', False):
                                        if SHOW_SYNC_LOG:
                                            self.logger.info("🔥 포지션 정보 동기화 완료")
                                            self.logger.info(f"🔥 동기화 결과: {sync_result['positions_before']} → {sync_result['positions_after']} 포지션")

                                        # 🔥 동기화 변경사항이 있으면 상세 로깅
                                        if sync_result.get('changes_made'):
                                            self.logger.info(f"🔥 포지션 동기화 변경사항 ({len(sync_result['changes_made'])}개):")
                                            for change in sync_result['changes_made'][:5]:  # 최대 5개만 표시
                                                self.logger.info(f"   - {change}")
                                    else:
                                        self.logger.warning("🔥 포지션 정보 동기화 실패")
                                        if sync_result.get('errors'):
                                            for error in sync_result['errors']:
                                                self.logger.error(f"   - 동기화 오류: {error}")

                                    # 포지션 동기화 후 포트폴리오 총 가치 다시 계산
                                    if self.market_data_cache:
                                        total_value = self.portfolio.calculate_portfolio_value(self.market_data_cache)
                                        total_pnl = total_value - initial_balance
                                        total_pnl_pct = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0

                                        self.logger.info(f"포지션 포함 포트폴리오 총 가치: ${total_value:.2f}")
                                        self.logger.info(f"포지션 포함 총 손익: ${total_pnl:.2f} ({total_pnl_pct:.2f}%)")
                except Exception as e:
                    self.logger.error(f"계정 잔액 정보 가져오기 실패: {e}")
                    self.logger.error(traceback.format_exc())

            # 시장 데이터 로드
            for symbol in self.symbols:
                # 심볼 포맷 정리
                base_symbol = symbol.split('/')[0] if '/' in symbol else symbol

                self.logger.info(f"{base_symbol} 시장 데이터 로드 중...")
                market_data = get_current_market_data(base_symbol, self.binance_utils)

                if market_data:
                    self.market_data_cache[base_symbol] = market_data
                    self.logger.info(f"{base_symbol} 시장 데이터 로드 완료: ${market_data.get('price', 0):.2f}")
                else:
                    self.logger.warning(f"{base_symbol} 시장 데이터 로드 실패")

        except Exception as e:
            self.logger.error(f"초기 시장 데이터 로드 중 오류: {e}")
            traceback.print_exc()

    def initialize_controller(self):
        """하이브리드 컨트롤러 초기화"""
        if not self.chatts_model:
            self.logger.error("LLM 모델이 설정되지 않았습니다. 컨트롤러를 초기화할 수 없습니다.")
            return False

        try:
            from trading.hybrid_architecture.hybrid_controller import HybridController
            from trading.hybrid_architecture.data_store import DataStore
            from trading.hybrid_architecture.data_logger import DataLogger
            from trading.hybrid_architecture.agents.inca_agent import InCAAgent
            from trading.hybrid_architecture.agents.hiar_factory import create_default_hiar
            from trading.hybrid_architecture.agents.sela_agent import SELAAgent

            # 데이터 스토어 및 로거 초기화
            data_store = DataStore(os.path.join(self.data_dir, 'hybrid_store.db'))
            execution_logger = DataLogger(data_store, self.data_dir)

            # 모델 타입 로깅 (객체 또는 딕셔너리 모두 처리)
            if isinstance(self.chatts_model, dict) and "model_type" in self.chatts_model:
                model_type = self.chatts_model.get("model_type", "unknown")
                model_name = self.chatts_model.get("model_name", "unknown")
            else:
                model_type = getattr(self.chatts_model, "model_type", "unknown")
                model_name = getattr(self.chatts_model, "model_name", "unknown")
            self.logger.info(f"LLM 모델 사용: {model_type} - {model_name}")

            # 에이전트 초기화 (시스템 설명 전달, 학습 기능 활성화)
            inca_agent = InCAAgent(
                llm_model=self.chatts_model,
                importance_threshold=0.3
            )
            if hasattr(inca_agent, 'system_description'):
                inca_agent.system_description = self.system_description
            self.logger.info(f"InCA 에이전트에 시스템 설명 전달 완료 (학습 기능 활성화, LLM: {'있음' if self.chatts_model else '없음'})")

            hiar_agent = create_default_hiar(llm_model=self.chatts_model)
            hiar_agent.system_description = self.system_description
            self.logger.info("HiAR 에이전트에 시스템 설명 전달 완료")

            # SELAAgent는 config를 통해 llm_model을 전달
            sela_config = self.config.copy()
            sela_config['llm_model'] = self.chatts_model
            sela_config['system_description'] = self.system_description
            sela_agent = SELAAgent(config=sela_config)
            self.logger.info("SELA 에이전트에 시스템 설명 전달 완료")

            # 하이브리드 컨트롤러 생성
            self.hybrid_controller = HybridController(
                inca_agent=inca_agent,
                hiar_agent=hiar_agent,
                sela_agent=sela_agent,
                data_store=data_store,
                execution_logger=execution_logger,
                config=self.config,
                binance_utils=self.binance_utils,
                portfolio=self.portfolio,
                vllm_queue=self.vllm_queue,
                continuous_mode=self.continuous_mode
            )

            # vLLM 클라이언트를 하이브리드 컨트롤러에 설정
            if hasattr(self.chatts_model, 'vllm_client'):
                self.hybrid_controller.set_vllm_client(self.chatts_model.vllm_client)
                self.logger.info("하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료")
            elif hasattr(self.chatts_model, 'client'):
                self.hybrid_controller.set_vllm_client(self.chatts_model.client)
                self.logger.info("하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (client 속성)")
            else:
                self.logger.warning("vLLM 클라이언트를 찾을 수 없습니다. 큐잉 시스템을 사용할 수 없습니다.")

            self.logger.info("하이브리드 컨트롤러 초기화 완료")

            # config에 llm_client 추가 (브릿지에서 사용)
            bridge_config = self.config.copy()
            bridge_config['llm_client'] = self.chatts_model

            # 하이브리드 시뮬레이터 브릿지 초기화
            self.bridge = HybridSimulatorBridge(self.hybrid_controller, bridge_config)
            self.logger.info("하이브리드 시뮬레이터 브릿지 초기화 완료")

            return True
        except Exception as e:
            self.logger.error(f"하이브리드 컨트롤러 초기화 실패: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def run_cycle(self):
        """단일 거래 주기 실행"""
        cycle_start_time = time.time()
        self.logger.info(f"===== 거래 주기 시작: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====")

        try:
            # 각 심볼별 완전 순차 프로세싱 (vLLM 큐 누적 방지)
            for symbol in self.symbols:
                base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
                self.logger.info(f"🔄 {base_symbol} 완전 순차 처리 시작")

                # 시장 데이터 업데이트
                market_data = get_current_market_data(base_symbol, self.binance_utils)
                if market_data:
                    self.market_data_cache[base_symbol] = market_data

                    # 브릿지에 시장 데이터 전송 (LLM 모델이 설정된 경우에만)
                    if self.chatts_model and hasattr(self, 'bridge') and self.bridge:
                        self.logger.info(f"📊 {base_symbol} 시장 데이터 처리 시작 (순차 모드)")
                        self.bridge.process_market_data(base_symbol, market_data)

                        # 현재 심볼 처리 완료까지 대기 (다음 심볼로 넘어가기 전)
                        self._wait_for_symbol_completion(base_symbol)

                        self.logger.info(f"✅ {base_symbol} 시장 데이터 처리 완료 (순차 모드)")
                    else:
                        self.logger.info(f"{base_symbol} 시장 데이터 캐시 업데이트 완료 (브릿지 처리 건너뜀)")
                else:
                    self.logger.warning(f"{base_symbol} 시장 데이터 가져오기 실패")

            # 기존 포지션 업데이트 (매 주기)
            try:
                self.logger.info("포지션 업데이트 및 거부된 거래 검증 시작")
                # 예측 히스토리 객체 가져오기
                prediction_history = None
                if hasattr(self, 'bridge') and self.bridge and hasattr(self.bridge, 'simulator') and self.bridge.simulator:
                    if hasattr(self.bridge.simulator, 'prediction_history'):
                        prediction_history = self.bridge.simulator.prediction_history
                        self.logger.info("예측 히스토리 객체를 찾았습니다.")

                processed_trades = self.portfolio.update_positions(self.market_data_cache, self.binance_utils, prediction_history)
                self.logger.info("포지션 업데이트 및 거부된 거래 검증 완료")

                if processed_trades:
                    for trade in processed_trades:
                        symbol = trade.get('symbol')
                        exit_type = trade.get('exit_type')
                        pnl = trade.get('realized_pnl', 0)
                        pnl_pct = trade.get('realized_pnl_pct', 0)

                        self.logger.info(f"포지션 청산: {symbol} {exit_type}, "
                                       f"손익: ${pnl:.2f} ({pnl_pct:.2f}%)")
            except Exception as e:
                import traceback
                self.logger.error(f"포지션 업데이트 및 거부된 거래 검증 중 오류 발생: {e}")
                self.logger.error(traceback.format_exc())

            # 시장 가치 업데이트 (5분마다)
            current_time = time.time()
            if current_time - self.last_portfolio_update > 300:  # 5분
                total_value = self.portfolio.calculate_portfolio_value(self.market_data_cache)

                # 초기 잔액 및 손익 계산
                initial_balance = self.portfolio.performance.get("initial_balance", 10000.0)
                total_pnl = total_value - initial_balance
                total_pnl_pct = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0

                self.logger.info(f"포트폴리오 총 가치: ${total_value:.2f}")
                self.logger.info(f"초기 잔액: ${initial_balance:.2f}")
                self.logger.info(f"총 손익: ${total_pnl:.2f} ({total_pnl_pct:.2f}%)")

                # 포트폴리오 상태 로깅 추가
                if SHOW_PORTFOLIO_LOG:
                    self.portfolio.log_portfolio_status(self.market_data_cache, self.binance_utils)

                self.last_portfolio_update = current_time

            # LLM 모델이 설정된 경우에만 실행 결과 확인
            if self.chatts_model and hasattr(self, 'bridge') and self.bridge:
                # 실행 결과 확인 (비동기적으로 처리된 결과)
                results = self.bridge.get_execution_results()
                if results:
                    for symbol, result_obj in results.items():
                        # 결과 객체에서 실제 데이터 추출
                        # result_obj는 {'id': doc_id, 'data': result, 'timestamp': timestamp, 'importance': importance} 형태
                        result = result_obj.get('data', {})

                        if SHOW_DEBUG_LOG:
                            self.logger.info(f"[디버그] {symbol} 실행 결과 객체: {result_obj}")
                            self.logger.info(f"[디버그] {symbol} 실제 결과 데이터: {result}")

                        # 전략 객체 확인
                        strategy = result.get('strategy', {})
                        if not strategy:
                            self.logger.warning(f"{symbol} 전략 객체가 없습니다.")
                            continue

                        if SHOW_DEBUG_LOG:
                            self.logger.info(f"[디버그] {symbol} 전략 객체: {strategy}")

                        # 전략 실행 여부 확인 (여러 필드 이름 처리)
                        if SHOW_STRATEGY_LOG and strategy.get("strategy_executed", False):
                            self.logger.info(f"{symbol} 전략 실행 여부: 실행됨")

                        # direction 필드 확인 (type 필드 우선 - SELA 전략 형식에 맞춤)
                        raw_direction = strategy.get('type',  # SELA 전략의 주요 필드
                                       strategy.get('action',  # 대체 필드
                                       strategy.get('direction',  # 추가 필드
                                       strategy.get('position', 'none'))))  # 최종 대체

                        # SELA 전략의 "buy"/"sell"을 포트폴리오 매니저가 이해하는 "long"/"short"로 변환
                        if raw_direction.lower() == 'buy':
                            direction = 'long'
                        elif raw_direction.lower() == 'sell':
                            direction = 'short'
                        else:
                            direction = raw_direction

                        if SHOW_DEBUG_LOG:
                            self.logger.info(f"[디버그] {symbol} 전략 방향 변환: {raw_direction} → {direction}")

                        # 중립 전략 확인 (short/long은 유효한 전략)
                        neutral_conditions = ['neutral', 'no_trade', 'none', 'hold']
                        if direction.lower() in neutral_conditions:
                            self.logger.info(f"{symbol} 중립 전략 생성됨: 거래 실행 없음 (direction: {direction})")

                            # 🚀 InCA 경험 축적을 위한 강제 가상거래 (중립 전략에서도 실행)
                            force_virtual_trading = os.getenv('FORCE_VIRTUAL_TRADING_FOR_LEARNING', 'false').lower() == 'true'

                            if force_virtual_trading:
                                # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                symbol_str = str(symbol) if symbol is not None else 'N/A'
                                self.logger.info(f"🎯 [{symbol_str}] 중립 전략이지만 InCA 학습을 위한 강제 가상거래 실행")

                                # 중립 전략을 임의의 방향으로 변환 (학습용)
                                import random
                                virtual_direction = random.choice(['long', 'short'])

                                # 가상 전략 생성
                                virtual_strategy = strategy.copy()
                                virtual_strategy['direction'] = virtual_direction
                                virtual_strategy['action'] = virtual_direction
                                virtual_strategy['position'] = virtual_direction
                                virtual_strategy['virtual_mode'] = True
                                virtual_strategy['learning_purpose'] = True
                                virtual_strategy['original_direction'] = direction  # 원래 중립 전략 기록

                                try:
                                    # 가상 거래 실행 (execute_trade 메서드 사용)
                                    virtual_result = self.portfolio.execute_trade(
                                        symbol=symbol,
                                        strategy=virtual_strategy,
                                        market_data=self.market_data_cache.get(symbol, {}),
                                        binance_utils=None,  # 가상거래이므로 None
                                        advanced_trading_system=self.advanced_trading_system
                                    )

                                    if virtual_result.get('success', False):
                                        # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                        symbol_str = str(symbol) if symbol is not None else 'N/A'
                                        trade_id = virtual_result.get('trade_id', 'unknown')
                                        self.logger.info(f"🎯 [{symbol_str}] 중립 전략 강제 가상거래 성공: {trade_id}")
                                    else:
                                        # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                        symbol_str = str(symbol) if symbol is not None else 'N/A'
                                        error_msg = virtual_result.get('error', '알 수 없는 오류') if virtual_result else '결과 없음'
                                        self.logger.warning(f"⚠️ [{symbol_str}] 중립 전략 강제 가상거래 실패: {error_msg}")

                                except Exception as virtual_e:
                                    # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                                    self.logger.error(f"❌ [{symbol_str}] 중립 전략 강제 가상거래 중 오류: {virtual_e}")

                            continue
                        else:
                            self.logger.info(f"{symbol} 유효한 전략으로 판단: {direction}")

                        # 진입 가격 확인 (논문 기반 SELA는 entry_price가 없으므로 현재 시장 가격 사용)
                        entry_price = strategy.get('entry_price', 0)
                        if entry_price <= 0:
                            # 현재 시장 가격을 진입 가격으로 사용
                            market_data = self.market_data_cache.get(symbol, {})
                            current_price = market_data.get('price', 0)
                            if current_price > 0:
                                entry_price = current_price
                                strategy['entry_price'] = entry_price  # 전략 객체에 추가
                                self.logger.info(f"{symbol} 진입 가격을 현재 시장 가격으로 설정: ${entry_price:.2f}")
                            else:
                                self.logger.warning(f"{symbol} 유효하지 않은 진입 가격과 시장 가격: entry_price={entry_price}, market_price={current_price}")
                                continue

                        # 전략 정보 로깅
                        strategy_type = strategy.get('strategy_type', 'unknown')
                        strategy_id = strategy.get('strategy_id', strategy.get('id', 'unknown'))

                        self.logger.info(f"{symbol} 전략 생성됨: {strategy_type} {direction} @ ${entry_price:.2f}, ID: {strategy_id}")

                        # 거래 실행 전 로깅
                        if SHOW_EXECUTION_LOG:
                            self.logger.info(f"[실행] {symbol} 거래 실행 시작: {direction} @ ${entry_price:.2f}")

                        if SHOW_DEBUG_LOG:
                            self.logger.info(f"[디버그] 전략 정보: {result}")
                            self.logger.info(f"[디버그] 시장 데이터: {self.market_data_cache.get(symbol, {})}")

                        # 거래 실행
                        try:
                            trade_result = self.portfolio.execute_trade(
                                symbol=symbol,
                                strategy=strategy,  # result 대신 strategy 객체 사용
                                market_data=self.market_data_cache.get(symbol, {}),
                                binance_utils=self.binance_utils,
                                advanced_trading_system=self.advanced_trading_system
                            )

                            if SHOW_DEBUG_LOG:
                                self.logger.info(f"[디버그] 거래 실행 결과: {str(trade_result)}")

                            if trade_result.get('success'):
                                # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                symbol_str = str(symbol) if symbol is not None else 'N/A'
                                self.logger.info(f"{symbol_str} 거래 실행 성공: {direction} @ ${entry_price:.2f}")
                            else:
                                # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                symbol_str = str(symbol) if symbol is not None else 'N/A'
                                self.logger.error(f"{symbol_str} 거래 실행 실패: {trade_result.get('error')}")

                        except Exception as e:
                            # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                            symbol_str = str(symbol) if symbol is not None else 'N/A'
                            self.logger.error(f"{symbol_str} 거래 실행 중 예외 발생: {str(e)}")
                            if SHOW_DEBUG_LOG:
                                import traceback
                                self.logger.error(f"[디버그] 스택 트레이스: {traceback.format_exc()}")

                        # 🚀 가상거래 업데이트 (거래 성공/실패와 관계없이 항상 실행)
                        try:
                            # 메서드 존재 확인 후 호출
                            if hasattr(self.portfolio, 'update_virtual_positions'):
                                self.portfolio.update_virtual_positions(self.market_data_cache)
                                self.logger.info("🔄 가상거래 업데이트 완료")
                            else:
                                # 대안: 직접 가상거래 완료 처리
                                self._check_and_complete_virtual_trades()
                                self.logger.info("🔄 가상거래 직접 완료 처리")
                        except Exception as virtual_e:
                            self.logger.warning(f"가상거래 업데이트 실패: {virtual_e}")
                            import traceback
                            self.logger.error(f"[디버그] 스택 트레이스: {traceback.format_exc()}")
            else:
                self.logger.info("LLM 모델이 설정되지 않았거나 브릿지가 초기화되지 않았습니다. 전략 생성 및 실행을 건너뜁니다.")

            cycle_duration = time.time() - cycle_start_time
            self.logger.info(f"===== 거래 주기 완료: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 소요시간: {cycle_duration:.2f}초 =====")

        except Exception as e:
            self.logger.error(f"거래 주기 중 오류 발생: {e}")
            self.logger.error(traceback.format_exc())

    def _wait_for_symbol_completion(self, symbol: str, timeout: int = 300):
        """
        특정 심볼의 처리 완료까지 대기 (완전 순차 처리)

        Args:
            symbol: 대기할 심볼
            timeout: 최대 대기 시간 (초)
        """
        start_time = time.time()
        # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
        symbol_str = str(symbol) if symbol is not None else 'N/A'
        self.logger.info(f"⏳ {symbol_str} 처리 완료 대기 시작 (최대 {timeout}초)")

        while time.time() - start_time < timeout:
            try:
                # vLLM 큐 상태 확인
                if hasattr(self, 'bridge') and self.bridge and hasattr(self.bridge, 'hybrid_controller'):
                    queue_status = self.bridge.hybrid_controller.vllm_queue.get_queue_status()

                    # 큐가 비어있고 처리 중인 요청이 없으면 완료
                    if queue_status['queue_size'] == 0 and queue_status['current_processing'] == 0:
                        elapsed = time.time() - start_time
                        self.logger.info(f"✅ {symbol_str} 처리 완료 확인 ({elapsed:.2f}초)")
                        return True

                    # 진행 상황 로깅 및 복구 (10초마다)
                    if int(time.time() - start_time) % 10 == 0:
                        self.logger.info(f"⏳ {symbol_str} 대기 중... 큐: {queue_status['queue_size']}, 처리중: {queue_status['current_processing']}")

                        # 🔧 갇힌 요청 자동 복구 (30초마다)
                        if int(time.time() - start_time) % 30 == 0:
                            from models.vllm_request_queue import vllm_queue
                            recovered = vllm_queue.check_and_recover_stuck_requests()
                            if recovered > 0:
                                self.logger.warning(f"🔧 갇힌 요청 자동 복구: {recovered}개")

                        # 🚨 긴급 상황: 2분 이상 대기 중이고 처리가 멈춘 경우
                        if time.time() - start_time > 120 and queue_status['current_processing'] == 0 and queue_status['queue_size'] > 0:
                            self.logger.error("🚨 큐 시스템 정체 감지 - 강제 리셋 실행")
                            from models.vllm_request_queue import vllm_queue
                            reset_result = vllm_queue.force_reset_queue()
                            self.logger.warning(f"🚨 강제 리셋 결과: {reset_result}")
                            break  # 리셋 후 대기 종료

                time.sleep(1)  # 1초마다 확인

            except Exception as e:
                self.logger.warning(f"{symbol_str} 완료 대기 중 오류: {e}")
                time.sleep(1)

        # 타임아웃 발생
        self.logger.warning(f"⚠️ {symbol_str} 처리 완료 대기 타임아웃 ({timeout}초)")
        return False

    def _check_and_complete_virtual_trades(self):
        """
        🚀 가상거래 완료 직접 처리 (대안 메서드)
        """
        try:
            if not hasattr(self.portfolio, 'virtual_positions') or not self.portfolio.virtual_positions:
                return

            current_time = int(time.time())
            completed_positions = []

            # 가상거래 포지션 확인
            for position in self.portfolio.virtual_positions[:]:  # 복사본으로 순회
                symbol = position.get('symbol', '')
                entry_time = position.get('entry_timestamp', 0)
                direction = position.get('direction', 'long')
                entry_price = position.get('entry_price', 0)

                # 3분(180초) 경과 확인
                elapsed_time = current_time - entry_time
                if elapsed_time >= 180:  # 3분 경과
                    # 현재 가격 가져오기
                    market_data = self.market_data_cache.get(symbol, {})
                    current_price = market_data.get('price', entry_price)

                    # 수익률 계산
                    if direction == 'long':
                        roi_pct = ((current_price - entry_price) / entry_price) * 100
                    else:  # short
                        roi_pct = ((entry_price - current_price) / entry_price) * 100

                    # 가상거래 완료 로그
                    # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    self.logger.info(f"💰 가상 포지션 클로즈 상세:")
                    self.logger.info(f"   심볼: {symbol_str}")
                    self.logger.info(f"   방향: {direction}")
                    self.logger.info(f"   수익률: {roi_pct:.3f}%")
                    self.logger.info(f"   클로즈 사유: 가상 시간 만료")

                    # InCA에 가상거래 완료 피드백 전달
                    try:
                        from trading.hybrid_architecture.agents.inca_agent import InCAAgent

                        inca_feedback = {
                            'symbol': symbol,
                            'prediction': {
                                'action_recommendation': 'buy' if direction == 'long' else 'sell',
                                'trading_direction': direction,
                                'confidence': position.get('confidence', 0.5),
                                'entry_price': entry_price,
                                'strategy_type': 'virtual_trade'
                            },
                            'trade_result': {
                                'profit': roi_pct,
                                'profit_percent': roi_pct,
                                'exit_price': current_price,
                                'success': roi_pct > 0,
                                'status': 'completed'
                            },
                            'market_data': {
                                'symbol': symbol,
                                'timestamp': current_time,
                                'virtual_trade': True
                            }
                        }

                        inca_agent = InCAAgent(llm_model=None, importance_threshold=0.3)
                        inca_result = inca_agent.learn_from_feedback(inca_feedback)

                        self.logger.info(f"🎯 [{symbol_str}] InCA 가상거래 피드백 전달 완료: {inca_result.get('success', False)}")

                    except Exception as inca_e:
                        self.logger.warning(f"⚠️ [{symbol_str}] InCA 피드백 전달 실패: {inca_e}")

                    completed_positions.append(position)
                    # 리스트에서 제거
                    self.portfolio.virtual_positions.remove(position)

                    self.logger.info(f"🔄 [{symbol_str}] 가상거래 3분 완료: {direction} {roi_pct:.3f}% (진입: ${entry_price:.2f} → 종료: ${current_price:.2f})")

            if completed_positions:
                self.logger.info(f"가상거래 직접 완료 처리: {len(completed_positions)}개 포지션 완료")

        except Exception as e:
            self.logger.error(f"가상거래 직접 완료 처리 중 오류: {e}")

def parse_arguments():
    """커맨드라인 인수 파싱"""
    parser = argparse.ArgumentParser(description='하이브리드 암호화폐 거래 시스템')
    parser.add_argument('--symbols', type=str, default="BTC,ETH,SOL,BNB",
                     help='거래 심볼 (쉼표로 구분)')
    parser.add_argument('--interval', type=int, default=180, help='거래 간격 (초), 기본값: 3분')
    parser.add_argument('--iterations', type=int, default=0, help='실행할 반복 횟수 (0 = 무한)')
    parser.add_argument('--testnet', action='store_true', help='바이낸스 테스트넷 사용')
    parser.add_argument('--no-testnet', action='store_false', dest='testnet',
                     help='바이낸스 메인넷 사용 (실제 자산 사용)')
    parser.add_argument('--trading-mode', type=str, choices=['simulation', 'real', 'test'],
                     default='real', help='거래 모드: simulation (거래 없음), real (실제 거래), test (예측만 기록)')
    parser.add_argument('--min-confidence', type=float, default=3.0, help='최소 신뢰도 임계값 (1-10 스케일)')
    parser.add_argument('--min-importance', type=float, default=3.0, help='최소 중요도 임계값 (1-10 스케일)')

    parser.add_argument('--balance', type=float, default=10000.0,
                     help='초기 잔액 (USDT)')

    # 연속 모드 옵션 추가
    parser.add_argument('--continuous-mode', action='store_true',
                     help='연속 모드 활성화 (실행 → 학습 → 실행 → 학습 순서로 연속 실행)')

    # vLLM 관련 인수 추가
    parser.add_argument('--use-vllm', action='store_true',
                     help='vLLM 서버 사용 (기본값: 비활성화)')
    parser.add_argument('--vllm-url', type=str, default='http://localhost:8001/v1',
                     help='vLLM 서버 URL (기본값: http://localhost:8001/v1)')
    parser.add_argument('--vllm-model', type=str, default='Qwen/Qwen3-14B-AWQ',
                     help='vLLM 모델 이름 (기본값: Qwen/Qwen3-14B-AWQ)')

    return parser.parse_args()

def main():
    """메인 함수"""
    # Windows 환경에서 UTF-8 인코딩 설정 (메인 함수에서 다시 한번 설정)
    if sys.platform == 'win32':
        # 환경 변수 설정
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        # 콘솔 코드 페이지를 UTF-8로 변경
        os.system('chcp 65001 > NUL')

        # 표준 출력이 이미 변환되었는지 확인 후 UTF-8로 설정
        import codecs
        import io
        if not isinstance(sys.stdout, io.TextIOWrapper) and hasattr(sys.stdout, 'buffer'):
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        if not isinstance(sys.stderr, io.TextIOWrapper) and hasattr(sys.stderr, 'buffer'):
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

    # 로깅 설정
    logger = setup_logging()
    logger.info("===== 하이브리드 거래 시스템 초기화 =====")

    # 인코딩 설정 확인
    logger.info(f"시스템 인코딩: {sys.getdefaultencoding()}")
    logger.info(f"표준 출력 인코딩: {sys.stdout.encoding}")
    logger.info(f"환경 변수 PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '설정되지 않음')}")

    # 인수 파싱
    args = parse_arguments()

    # 심볼 목록 준비
    symbols = [s.strip() for s in args.symbols.split(',')]

    # .env 파일 로드
    if os.path.exists(".env"):
        load_dotenv()
        logger.info(".env 파일 로드 완료")

    # OpenAI API 키 확인
    if os.environ.get("OPENAI_API_KEY"):
        logger.info("OPENAI_API_KEY 환경 변수가 설정되어 있습니다.")
    else:
        logger.warning("OPENAI_API_KEY가 설정되지 않았습니다. 일부 기능이 제한될 수 있습니다.")

    # vLLM 사용 여부 확인 및 로깅 (환경 변수 또는 커맨드라인 인수)
    use_vllm_env = os.getenv('USE_VLLM', 'false').lower() == 'true'
    use_vllm = args.use_vllm or use_vllm_env
    vllm_server_url = args.vllm_url or os.getenv('VLLM_SERVER_URL', 'http://localhost:8001/v1')
    # 환경변수 우선, 없으면 커맨드라인, 마지막으로 기본값
    vllm_model = os.getenv('VLLM_MODEL') or args.vllm_model or 'Qwen/Qwen3-14B-AWQ'

    logger.info(f"vLLM 환경 변수 확인: USE_VLLM={os.getenv('USE_VLLM')}, 파싱 결과={use_vllm_env}")
    logger.info(f"vLLM 사용 여부: 커맨드라인={args.use_vllm}, 환경변수={use_vllm_env}, 최종={use_vllm}")
    logger.info(f"vLLM 모델 설정: 환경변수={os.getenv('VLLM_MODEL')}, 커맨드라인={args.vllm_model}, 최종={vllm_model}")

    vllm_client = None
    if use_vllm:
        logger.info(f"vLLM 서버 사용: {vllm_server_url}, 모델: {vllm_model}")

        # vLLM 클라이언트 초기화
        try:
            # 향상된 vLLM 클라이언트 팩토리 임포트
            from models.vllm_client_factory import VLLMClientFactory
            logger.info(f"VLLMClientFactory 클래스 임포트 성공")

            # 클라이언트 초기화 전 로깅
            logger.info(f"vLLM 클라이언트 초기화 시작: URL={vllm_server_url}, 모델={vllm_model}")

            # 향상된 클라이언트 생성 (명시적으로 높은 max_tokens 값 지정)
            vllm_client = VLLMClientFactory.create_client(
                client_type="enhanced",
                server_url=vllm_server_url,
                model_name=vllm_model,
                max_tokens=8192  # 높은 max_tokens 값을 지정해 타임아웃 방지
            )

            # 클라이언트 초기화 후 로깅
            logger.info(f"향상된 vLLM 클라이언트 초기화 완료: {vllm_client}")
            logger.info(f"vLLM 클라이언트 타입: {type(vllm_client)}")
            logger.info(f"vLLM 클라이언트 속성: {dir(vllm_client)}")
            logger.info(f"vLLM 클라이언트 서버 URL: {getattr(vllm_client, 'server_url', 'Unknown')}")
            logger.info(f"vLLM 클라이언트 모델명: {getattr(vllm_client, 'model_name', 'Unknown')}")

            # 헬스 체크
            logger.info("vLLM 서버 헬스 체크 시작")
            health_result = vllm_client.health_check()
            logger.info(f"vLLM 서버 헬스 체크 결과: {health_result}")

            if health_result:
                logger.info("vLLM 서버 연결 성공")
            else:
                logger.error("vLLM 서버 연결 실패")
                logger.error("vLLM 서버가 실행 중인지 확인하세요.")
                return

            # 간단한 테스트 프롬프트 실행 (짧은 버전)
            test_prompt = "Hello, I'm testing the vLLM server connection. Please respond with a brief acknowledgment."
            logger.info("vLLM 서버 테스트 중 (짧은 프롬프트)...")

            try:
                # 테스트 (충분한 토큰 수로 응답 생성)
                response = vllm_client.generate(test_prompt, max_tokens=100, temperature=0.0)
                logger.info("vLLM 서버 테스트 성공")
                # response 타입에 따라 다르게 처리
                if isinstance(response, dict):
                    logger.debug(f"테스트 응답: {response.get('text', '')[:100]}...")
                else:
                    logger.debug(f"테스트 응답: {str(response)[:100]}...")
            except Exception as e:
                logger.error(f"vLLM 서버 테스트 실패: {e}")
                logger.warning("vLLM 서버 연결에 문제가 있을 수 있습니다.")
                logger.warning("vLLM 서버가 실행 중인지 확인하세요.")
                return

        except Exception as e:
            logger.error(f"vLLM 클라이언트 초기화 중 오류 발생: {e}")
            logger.error("vLLM 서버 연결에 실패했습니다. vLLM 서버가 실행 중인지 확인하세요.")
            return
    else:
        logger.info("vLLM 서버를 사용하지 않습니다.")

    # 거래 모드에 따른 경고
    if args.trading_mode == 'real' and not args.testnet:
        logger.warning("!!!!! 주의: 실제 거래 모드로 실행합니다 !!!!!")
        logger.warning("!!!!! 실제 자금이 사용됩니다 !!!!!")

        # 환경 변수로 자동 승인 확인
        auto_confirm = os.getenv('AUTO_CONFIRM_REAL_TRADING', 'false').lower() == 'true'

        if auto_confirm:
            logger.info("🔧 AUTO_CONFIRM_REAL_TRADING=true로 자동 승인됨")
        else:
            # 확인 요청
            try:
                confirm = input("실제 거래를 진행하시겠습니까? (y/n): ")
                if confirm.lower() != 'y':
                    logger.info("사용자에 의해 취소됨")
                    return
            except:
                # 비대화형 환경에서는 기본적으로 취소
                logger.info("비대화형 환경에서 실제 거래 모드가 요청됨. 안전을 위해 취소됩니다.")
                logger.info("💡 해결 방법: 환경 변수 AUTO_CONFIRM_REAL_TRADING=true 설정")
                return

    # vLLM 클라이언트 설정
    chatts_model = None
    if use_vllm and vllm_client:
        # ChatTS 모델 대신 vLLM 클라이언트 사용
        logger.info("ChatTS 모델 대신 vLLM 클라이언트를 사용합니다.")

        # 향상된 래퍼 함수 정의 - 팩토리 사용
        logger.info("향상된 vLLM 클라이언트 래퍼 함수 생성")

        # 팩토리를 통해 래퍼 생성
        chatts_model = VLLMClientFactory.create_wrapper(vllm_client)
        logger.info(f"vLLM 모델 래퍼 생성 완료: {chatts_model.model_name}")

        # chatts_model은 이미 팩토리에서 생성됨
        logger.info(f"vLLM 모델 설정 완료: {args.vllm_model}")

    # 하이브리드 시스템 초기화
    trading_system = HybridTrading(
        symbols=symbols,
        trading_mode=args.trading_mode,
        testnet=args.testnet,
        initial_balance=args.balance
    )

    # vLLM 요청 큐 초기화 (vLLM 사용 시)
    if use_vllm and vllm_client:
        try:
            from models.vllm_request_queue import VLLMRequestQueue
            trading_system.vllm_queue = VLLMRequestQueue()
            trading_system.vllm_queue.set_vllm_client(vllm_client)
            logger.info("vLLM 요청 큐 초기화 완료")
        except ImportError as e:
            logger.warning(f"VLLMRequestQueue 임포트 실패: {e}")
            trading_system.vllm_queue = None
        except Exception as e:
            logger.error(f"vLLM 요청 큐 초기화 실패: {e}")
            trading_system.vllm_queue = None

    # 전략 실행 조건 설정 (환경변수가 없을 때만 커맨드라인 값 사용)
    if hasattr(args, 'min_confidence') and 'MIN_CONFIDENCE' not in os.environ:
        os.environ['MIN_CONFIDENCE'] = str(args.min_confidence)
        logger.info(f"최소 신뢰도 임계값 설정 (커맨드라인): {args.min_confidence}")
    elif 'MIN_CONFIDENCE' in os.environ:
        logger.info(f"최소 신뢰도 임계값 설정 (환경변수): {os.environ['MIN_CONFIDENCE']}")

    if hasattr(args, 'min_importance') and 'MIN_IMPORTANCE' not in os.environ:
        os.environ['MIN_IMPORTANCE'] = str(args.min_importance)
        logger.info(f"최소 중요도 임계값 설정 (커맨드라인): {args.min_importance}")
    elif 'MIN_IMPORTANCE' in os.environ:
        logger.info(f"최소 중요도 임계값 설정 (환경변수): {os.environ['MIN_IMPORTANCE']}")



    # 연속 모드 설정 (항상 연속 모드로 실행)
    trading_system.continuous_mode = True
    logger.info("연속 모드 설정 완료")

    # 모델 설정
    if chatts_model:
        trading_system.chatts_model = chatts_model
        logger.info("하이브리드 시스템에 vLLM 모델 설정 완료")

        # 하이브리드 컨트롤러 초기화
        if trading_system.initialize_controller():
            logger.info("하이브리드 컨트롤러 초기화 성공")
        else:
            logger.error("하이브리드 컨트롤러 초기화 실패")
            return

    # 시스템 시작
    try:
        # 연속 모드 설정 확인 (항상 연속 모드로 실행)
        logger.info("연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)")
        # 연속 모드로 시스템 시작
        trading_system.start(continuous_mode=True)

        # 하이브리드 컨트롤러 연속 모드 설정 확인
        if hasattr(trading_system, 'hybrid_controller') and trading_system.hybrid_controller:
            if hasattr(trading_system.hybrid_controller, 'continuous_mode'):
                logger.info(f"하이브리드 컨트롤러 연속 모드 설정: {trading_system.hybrid_controller.continuous_mode}")
            else:
                logger.warning("하이브리드 컨트롤러에 continuous_mode 속성이 없습니다.")
        else:
            logger.warning("하이브리드 컨트롤러가 초기화되지 않았습니다.")

        logger.info(f"거래 간격: {args.interval}초")

        # 거래 루프
        iteration = 0
        while args.iterations == 0 or iteration < args.iterations:
            iteration += 1
            logger.info(f"===== 반복 {iteration}" + (f"/{args.iterations}" if args.iterations > 0 else "") + " =====")

            # 거래 주기 실행
            trading_system.run_cycle()

            # 다음 주기까지 대기
            logger.info(f"{args.interval}초 대기 중...")
            time.sleep(args.interval)

    except KeyboardInterrupt:
        logger.info("사용자에 의해 중단됨")
    except Exception as e:
        logger.error(f"실행 중 오류 발생: {e}")
        traceback.print_exc()
    finally:
        # 시스템 종료
        trading_system.stop()
        logger.info("프로그램 종료")

if __name__ == "__main__":
    main()
