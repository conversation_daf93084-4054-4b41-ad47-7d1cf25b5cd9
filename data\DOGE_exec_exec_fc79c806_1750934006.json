{"execution_id": "exec_fc79c806_1750934006", "symbol": "DOGE", "timestamp": 1750934006, "datetime": "2025-06-26 19:33:26", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "a349caac-f07d-442c-9b87-9f0a00721c8b", "timestamp": 1750934006, "market_data": {"id": "market_DOGE_1750933986", "symbol": "DOGE", "timestamp": 1750933986, "datetime": "2025-06-26 19:33:06", "date": "2025-06-26", "time": "19:33:06", "price": 0.16232, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.16233, "volume": 4944379338.0, "volume_24h": 4944379338.0, "high_24h": 0.16809, "low_24h": 0.16202, "percent_change_24h": -1.897, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8638964, "social_dominance": 0.731, "social_contributors": 86389, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750933986, "news_count": 38, "ema_7": 0.16249714285714287, "ema_14": 0.16251499999999997, "ema_25": 0.1626216, "ema_50": 0.16348459999999998, "ema_99": 0.16407959595959598, "ema_200": 0.1646978499999998, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0207894736842102, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 24381679792.44, "recent_news_titles": ["<PERSON> Threatens to Double Spanish Tariffs Over NATO Spending", "DOGE Price Analysis: Dogecoin Bulls Defend 16 Cent Support Amid <PERSON> Musk’s X Payments Speculation", "DOGE Price News: <PERSON><PERSON><PERSON><PERSON> Higher as Bulls Break 16 Cent Resistance", "Is DOGE doomed to fail? Some experts are ready to call it. &#x2d; Ars Technica", "DOGE layoffs are starting to leave their mark on D.C.’s housing market"], "top_social_posts": [{"text": "Permission for hugs?", "sentiment": 3.5, "platform": "tweet"}, {"text": "🎯", "sentiment": 3, "platform": "tweet"}, {"text": "@elonmusk Off you go🔥", "sentiment": 3, "platform": "tweet"}, {"text": "Nobody cares.", "sentiment": 3.57, "platform": "tweet"}, {"text": "Crowd caused chaos in Santa Ana by getting in the way of police during an arrest.\n\n", "sentiment": 2.52, "platform": "tweet"}, {"text": "An illegal from Iran was arrested in Los Angeles. A bystander yelled at ICE officers during the arrest, and then a woman, said to be Iranian ran out upset.\n\n", "sentiment": 2.47, "platform": "tweet"}, {"text": "‘Liver King’ shows ankle monitor following arrest over threats to <PERSON>.\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "ICE agents caught and arrested an illegal person at a Home Depot in Huntington Park, CA.\n\nThe guy recording and yelling should be arrested too..\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "Friends help make sure his wheelchair doesn’t get in the way of a great wedding.\n\n", "sentiment": 3.6, "platform": "tweet"}, {"text": "Flashback: Maryland parents express worry over <PERSON>'s move to stop \"gender-affirming care\" for minors.\n\n", "sentiment": 2.79, "platform": "tweet"}], "recent_candles": [[1750932840000, "0.162480", "0.162570", "0.162470", "0.162540", "2025923", 1750932899999, "329268.601620", 719, "1329913", "216135.328700", "0"], [1750932900000, "0.162550", "0.162580", "0.162470", "0.162570", "2637955", 1750932959999, "428805.859380", 707, "1613914", "262340.507190", "0"], [1750932960000, "0.162580", "0.162710", "0.162540", "0.162640", "3552647", 1750933019999, "577722.310540", 916, "2155720", "350551.542030", "0"], [1750933020000, "0.162640", "0.162650", "0.162460", "0.162460", "4114208", 1750933079999, "668832.753610", 740, "941563", "153096.146980", "0"], [1750933080000, "0.162450", "0.162480", "0.162380", "0.162410", "2335001", 1750933139999, "379263.521830", 698, "810623", "131668.123440", "0"], [1750933140000, "0.162410", "0.162700", "0.162410", "0.162660", "1768983", 1750933199999, "287579.633610", 844, "1390996", "226101.901450", "0"], [1750933200000, "0.162660", "0.162800", "0.162660", "0.162720", "3015360", 1750933259999, "490769.302620", 707, "1315282", "214044.590860", "0"], [1750933260000, "0.162720", "0.162770", "0.162630", "0.162640", "1526250", 1750933319999, "248367.399190", 573, "510055", "83010.830230", "0"], [1750933320000, "0.162640", "0.162660", "0.162520", "0.162590", "3157903", 1750933379999, "513474.267040", 867, "2071393", "336791.468800", "0"], [1750933380000, "0.162590", "0.162590", "0.162460", "0.162510", "1374985", 1750933439999, "223429.265760", 596, "530918", "86271.642370", "0"], [1750933440000, "0.162510", "0.162520", "0.162430", "0.162470", "1800548", 1750933499999, "292545.797660", 617, "517175", "84030.810240", "0"], [1750933500000, "0.162470", "0.162550", "0.162430", "0.162430", "4094496", 1750933559999, "665380.850820", 695, "2481915", "403329.673970", "0"], [1750933560000, "0.162430", "0.162640", "0.162420", "0.162580", "4181800", 1750933619999, "679488.260180", 789, "2750049", "446859.208380", "0"], [1750933620000, "0.162570", "0.162700", "0.162540", "0.162560", "3676907", 1750933679999, "598020.711490", 751, "1885299", "306636.245330", "0"], [1750933680000, "0.162570", "0.162620", "0.162570", "0.162570", "959020", 1750933739999, "155943.368730", 436, "473497", "76993.654680", "0"], [1750933740000, "0.162570", "0.162630", "0.162520", "0.162530", "887504", 1750933799999, "144291.613390", 474, "458882", "74601.811360", "0"], [1750933800000, "0.162530", "0.162570", "0.162470", "0.162490", "1241386", 1750933859999, "201757.244200", 571, "490527", "79725.570410", "0"], [1750933860000, "0.162490", "0.162530", "0.162470", "0.162520", "853422", 1750933919999, "138682.763260", 430, "446749", "72597.755640", "0"], [1750933920000, "0.162520", "0.162540", "0.162310", "0.162350", "2740284", 1750933979999, "445160.758600", 848, "766901", "124583.670740", "0"], [1750933980000, "0.162340", "0.162340", "0.162320", "0.162340", "183287", 1750934039999, "29752.808910", 74, "66245", "10753.551780", "0"]], "candles_count": 20, "data_timestamp": 1750933986, "has_timeseries_data": true, "short_term_change_pct": -0.1169014951085927, "recent_high": 0.16263, "recent_low": 0.16231}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "Short-term bearish trend confirmed by 5-minute candle analysis with 3 bearish candles out of 5, declining price (-0.117%), and forming a bearish trendline. Volume spikes in 4th candle indicate potential confirmation of downward movement. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish trend", "volume confirmation", "short-term decline"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term bearish trend confirmed by 5-minute candle analysis with 3 bearish candles o...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_66c6b2cb_1750933994"}, "strategy": {"strategy_id": "a349caac-f07d-442c-9b87-9f0a00721c8b", "symbol": "DOGE", "timestamp": 1750934006, "type": "sell", "entry_price": 0.16232, "stop_loss": 0.16495, "take_profit": 0.15748, "reasoning": "InCA 신호는 SELL을 추천하며, 현재 가격이 0.16232에 위치하고 있다. 기술적 지표 분석에 따르면 RSI는 과매수 영역에 진입했으며, MACD는 상승 추세를 끝냈다는 신호를 보이고 있다. 볼린저 밴드는 가격이 상단을 넘어서며 과열 상태를 나타내고 있다. 이러한 요소들을 종합하여 단기적인 하락 투자 전략이 적절하다고 판단된다.", "confidence": 0.78, "reasoning_card_id": "card_66c6b2cb_1750933994", "risk_level": "medium", "key_points": ["InCA 신호는 SELL을 추천", "RSI는 과매수 영역에 진입", "MACD는 상승 추세 종료 신호"], "market_context": {"price": 0.16232, "percent_change_24h": -1.897, "timestamp": 1750933986}, "paper_based": false, "risk_reward": 1.8403041825094846, "importance": 9.102790954801005, "consensus_signal": "sell", "consensus_confidence": 0.78, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.78, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.78, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_fc79c806_1750934006"}}