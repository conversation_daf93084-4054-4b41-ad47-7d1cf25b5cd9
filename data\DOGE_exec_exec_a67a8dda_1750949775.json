{"execution_id": "exec_a67a8dda_1750949775", "symbol": "DOGE", "timestamp": 1750949775, "datetime": "2025-06-26 23:56:15", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "d5c24705-5165-4292-a332-ee47e90b5dd1", "timestamp": 1750949775, "market_data": {"id": "market_DOGE_1750949755", "symbol": "DOGE", "timestamp": 1750949755, "datetime": "2025-06-26 23:55:55", "date": "2025-06-26", "time": "23:55:55", "price": 0.15974, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15971, "volume": 5226556345.0, "volume_24h": 5226556345.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -3.041, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8760646, "social_dominance": 0.746, "social_contributors": 87606, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750949755, "news_count": 45, "ema_7": 0.16015000000000001, "ema_14": 0.15979214285714288, "ema_25": 0.15935920000000003, "ema_50": 0.15978819999999996, "ema_99": 0.1608016161616161, "ema_200": 0.16258160000000005, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.035555555555555, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 24035576882.4, "recent_news_titles": ["Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing", "TSMC to Inject $10 Billion Capital in Unit to Counter FX Swings", "DOGE teen worker 'Big Balls' <PERSON> leaving Trump cost cutting team"], "top_social_posts": [{"text": "Closed $NPC. But holding wouldn't hurt either. Some memes are gonna take a hit early next week as dog<PERSON> is gonna take a hit. <PERSON><PERSON> of Tesla", "sentiment": 2.88, "platform": "tweet"}, {"text": "DOGE & Europe DUMPING Elon Musk Tesla", "sentiment": 3.13, "platform": "youtube-video"}, {"text": "Contracts Update!\n\nOver the last 7 days, agencies have terminated 312 wasteful contracts with a ceiling value of $2.8B and savings of $470M, including a DoD $286k professional and management development contract for an “entrepreneurship course at Harvard University”, and a $485k USAID contract for a “senior general development advisor at USAID Madagascar.\"", "sentiment": 3.03, "platform": "tweet"}, {"text": "President <PERSON>: \"<PERSON><PERSON><PERSON>, the meanest of them all. If he didn't like somebody, it was over\"  \n\nCrowd: \"Sounds like <PERSON>\"\n\n", "sentiment": 3.23, "platform": "tweet"}, {"text": "According to @coingecko research, @bitgetglobal emerged as liquidity leader for top altcoins, $ETH, $DOGE, $XRP, and $SOL in May. While @binance retained the top spot for $BTC, the research shows. By @godbole17.\n\n", "sentiment": 3.05, "platform": "tweet"}, {"text": "<PERSON> praises <PERSON><PERSON>:\n\n\"I'm a huge fan of <PERSON><PERSON> and I think he's got the most incredibly forward-thinking ideas about where we can go technologically. What he's done, nobody else has ever done.\"\n\n", "sentiment": 3.44, "platform": "tweet"}, {"text": "<PERSON><PERSON><PERSON> gets a big hug from AOC after his win... because New York clearly loves making questionable choices.\n\n", "sentiment": 3.78, "platform": "tweet"}, {"text": "No, a team of forensic accountants put to this task should do this, appointed by @POTUS preferably IRS agents of truth! In fact the IRS department should be the follow up teams to move forward on all DOGE findings! Audit every political leader especially this new NYC Mayor! Put the IRS to work for the people!\nI’ll go one further recruit CPA’s in every community to work along side Agents to conduct audits! They will collect the receipts! Clean out the corruption!", "sentiment": 3, "platform": "tweet"}, {"text": "Let that sink in!", "sentiment": 3, "platform": "tweet"}, {"text": "\"What if soldiers in every country just put down their weapons and stopped fighting each other\"\n\nWho's gonna tell her?\n\n", "sentiment": 2.76, "platform": "tweet"}], "recent_candles": [[*************, "0.160280", "0.160560", "0.160270", "0.160520", "9094430", *************, "1458960.344310", 2012, "6047960", "970225.967630", "0"], [*************, "0.160520", "0.160600", "0.160230", "0.160230", "7500500", *************, "1203357.338250", 1881, "3620320", "580985.764140", "0"], [1750948680000, "0.160220", "0.160380", "0.160170", "0.160180", "4837815", 1750948739999, "775394.724520", 1501, "2748638", "440598.268230", "0"], [1750948740000, "0.160190", "0.160190", "0.159940", "0.160100", "8057026", 1750948799999, "1289275.752290", 1585, "2736308", "437900.431430", "0"], [1750948800000, "0.160100", "0.160170", "0.160010", "0.160140", "3028046", 1750948859999, "484749.075730", 1075, "2149401", "344088.717080", "0"], [1750948860000, "0.160150", "0.160390", "0.160140", "0.160270", "5026737", 1750948919999, "805540.169810", 1707, "2849922", "456704.581900", "0"], [1750948920000, "0.160280", "0.160480", "0.160280", "0.160380", "3349895", 1750948979999, "537336.338610", 1447, "1938842", "310998.061580", "0"], [1750948980000, "0.160390", "0.160440", "0.160140", "0.160310", "2799864", 1750949039999, "448732.279830", 1116, "1159096", "185745.083150", "0"], [1750949040000, "0.160310", "0.160430", "0.160210", "0.160270", "3252909", 1750949099999, "521488.732620", 1227, "1678427", "269082.856220", "0"], [1750949100000, "0.160270", "0.160400", "0.160160", "0.160350", "4503470", 1750949159999, "721735.166010", 1927, "2725719", "436834.663800", "0"], [1750949160000, "0.160360", "0.160830", "0.160310", "0.160600", "11517791", 1750949219999, "1849528.020040", 3065, "7790197", "1250891.629690", "0"], [1750949220000, "0.160610", "0.160660", "0.160440", "0.160490", "4215277", 1750949279999, "676674.611300", 1460, "2451127", "393465.391450", "0"], [1750949280000, "0.160480", "0.160540", "0.160340", "0.160480", "2568037", 1750949339999, "412023.945530", 1087, "1570201", "251943.521680", "0"], [1750949340000, "0.160490", "0.160580", "0.160370", "0.160570", "1784991", 1750949399999, "286399.000000", 1031, "1199730", "192504.542590", "0"], [1750949400000, "0.160580", "0.160640", "0.160200", "0.160200", "4958117", 1750949459999, "795167.528260", 1310, "1335829", "214243.917240", "0"], [1750949460000, "0.160200", "0.160220", "0.160030", "0.160180", "3529845", 1750949519999, "565236.123720", 1148, "940604", "150606.584830", "0"], [1750949520000, "0.160180", "0.160190", "0.160000", "0.160000", "1391059", 1750949579999, "222754.506530", 829, "656708", "105163.969900", "0"], [1750949580000, "0.160000", "0.160040", "0.159870", "0.159930", "3744942", 1750949639999, "598987.397290", 1188, "1704646", "272629.329080", "0"], [1750949640000, "0.159920", "0.159920", "0.159730", "0.159860", "2676222", 1750949699999, "427686.241590", 1027, "808348", "129186.664240", "0"], [1750949700000, "0.159860", "0.159890", "0.159680", "0.159680", "2558984", 1750949759999, "408892.834520", 837, "1039984", "166215.403080", "0"]], "candles_count": 20, "data_timestamp": 1750949755, "has_timeseries_data": true, "short_term_change_pct": -0.3121488325633665, "recent_high": 0.16022, "recent_low": 0.15968}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "The short-term trend shows a clear bearish pattern with 4 consecutive bearish candles and a price drop of -0.312%. Volume patterns support the trend, indicating strong bearish momentum. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish", "volume", "candle pattern"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term trend shows a clear bearish pattern with 4 consecutive bearish candles a...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_60d52ea4_1750949762"}, "strategy": {"strategy_id": "d5c24705-5165-4292-a332-ee47e90b5dd1", "symbol": "DOGE", "timestamp": 1750949774, "type": "sell", "entry_price": 0.15974, "stop_loss": 0.16234, "take_profit": 0.15454, "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 $0.15974로, 이 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정했습니다. 기술적 지표 분석에 따르면 RSI는 과열 상태에 있으며, MACD는 하향 추세를 보이고 있어 단기적으로 하락 가능성이 높습니다. 볼린저 밴드는 상단을 돌파한 후 압력이 형성되고 있어, 이는 추가 하락을 예고할 수 있습니다.", "confidence": 0.78, "reasoning_card_id": "card_60d52ea4_1750949762", "risk_level": "medium", "key_points": ["InCA 신호는 SELL로, 현재 시장 데이터를 반영한 분석입니다.", "RSI는 과열 상태로 상승 동력이 약화되고 있습니다.", "MACD와 볼린저 밴드는 하향 추세를 지속하고 있어 단기적 하락 가능성이 높습니다."], "market_context": {"price": 0.15974, "percent_change_24h": -3.041, "timestamp": 1750949755}, "paper_based": false, "risk_reward": 1.9999999999999787, "importance": 9.135311770307354, "consensus_signal": "sell", "consensus_confidence": 0.78, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.78, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.78, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_a67a8dda_1750949775"}}