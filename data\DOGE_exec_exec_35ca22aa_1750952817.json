{"execution_id": "exec_35ca22aa_1750952817", "symbol": "DOGE", "timestamp": 1750952817, "datetime": "2025-06-27 00:46:57", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "830e36f4-58ab-447b-bb2e-4b03e6574dd6", "timestamp": 1750952817, "market_data": {"id": "market_DOGE_1750952799", "symbol": "DOGE", "timestamp": 1750952799, "datetime": "2025-06-27 00:46:39", "date": "2025-06-27", "time": "00:46:39", "price": 0.16007, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.16007, "volume": 5174088790.0, "volume_24h": 5174088790.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -1.804, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8714439, "social_dominance": 0.599, "social_contributors": 87144, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750952799, "news_count": 47, "ema_7": 0.15949857142857143, "ema_14": 0.15984214285714288, "ema_25": 0.1599736, "ema_50": 0.1595598, "ema_99": 0.16036080808080805, "ema_200": 0.16219419999999998, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.041914893617021, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23969764308.91, "recent_news_titles": ["Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing", "TSMC to Inject $10 Billion Capital in Unit to Counter FX Swings"], "top_social_posts": [{"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "🚨$470,000,000.00 saved!  Thank you DOGE!", "sentiment": 3.21, "platform": "tweet"}, {"text": "DOGE just terminated 312 government contracts with a ceiling value of $2.8B and savings of $470M...in just the last week\n\nNotably a $286k professional and management development contract for an “entrepreneurship course at Harvard University”and $485k USAID contract for a “senior general development advisor at USAID Madagascar.\"", "sentiment": 3.17, "platform": "tweet"}, {"text": "Young woman doesn't believe civilians should have firearms and wants the 2nd amendment removed from the constitution.  \n\n", "sentiment": 3.09, "platform": "tweet"}, {"text": "Dr. <PERSON><PERSON> went to buy champagne in anticipation of <PERSON><PERSON>’s win and laughed at the guy who checked her out when he said the race was close.\n\nWhat’s she up to these days?\n\n", "sentiment": 3.32, "platform": "tweet"}, {"text": "Will you be able to resist the temptation OR you HODL BabyDoge and never let go?", "sentiment": 3, "platform": "tweet"}, {"text": "Which #memecoin is printing money today? Shill me the next $DOGE 👀", "sentiment": 3, "platform": "tweet"}, {"text": "Closed $NPC. But holding wouldn't hurt either. Some memes are gonna take a hit early next week as dog<PERSON> is gonna take a hit. <PERSON><PERSON> of Tesla", "sentiment": 2.88, "platform": "tweet"}, {"text": "How it feels to be 27", "sentiment": 3.14, "platform": "tweet"}, {"text": "@DOGE Hey @<PERSON><PERSON><PERSON><PERSON> are you going to codify these DOGE cuts?\n\nOr are you gonna just take more vacation and do nothing at all for the American people???", "sentiment": 2.94, "platform": "tweet"}], "recent_candles": [[1750951620000, "0.160480", "0.160480", "0.160090", "0.160090", "4493279", 1750951679999, "720426.184380", 1395, "1105815", "177257.731990", "0"], [1750951680000, "0.160090", "0.160150", "0.159730", "0.159760", "9361460", 1750951739999, "1496998.492720", 2286, "1891160", "302457.007320", "0"], [1750951740000, "0.159760", "0.159790", "0.159070", "0.159250", "24143521", 1750951799999, "3846728.394690", 4237, "8307324", "1323220.508970", "0"], [1750951800000, "0.159260", "0.159420", "0.159060", "0.159070", "14601393", 1750951859999, "2325598.694500", 3147, "4629637", "737319.350870", "0"], [1750951860000, "0.159080", "0.159180", "0.158840", "0.158910", "20694992", 1750951919999, "3290501.332860", 4019, "8371894", "1331147.720570", "0"], [1750951920000, "0.158910", "0.159130", "0.158500", "0.158950", "30119244", 1750951979999, "4780955.156410", 4947, "15497347", "2459586.811770", "0"], [1750951980000, "0.158950", "0.159030", "0.158720", "0.158740", "23510151", 1750952039999, "3734533.720830", 3275, "12942901", "2056164.070170", "0"], [1750952040000, "0.158740", "0.159150", "0.158740", "0.159050", "5323265", 1750952099999, "846288.963640", 1788, "2491398", "396005.911040", "0"], [1750952100000, "0.159050", "0.159160", "0.158940", "0.159140", "6054393", 1750952159999, "963015.351480", 1565, "1926175", "306327.961980", "0"], [1750952160000, "0.159140", "0.159410", "0.159110", "0.159370", "7012137", 1750952219999, "1117103.197000", 1809, "4262882", "679181.485710", "0"], [1750952220000, "0.159370", "0.159750", "0.159370", "0.159630", "5508781", 1750952279999, "879267.064800", 1753, "2787433", "444896.410490", "0"], [1750952280000, "0.159640", "0.159700", "0.159530", "0.159540", "3463802", 1750952339999, "552890.159260", 1128, "1492737", "238302.252280", "0"], [1750952340000, "0.159530", "0.159830", "0.159510", "0.159820", "1716317", 1750952399999, "274026.772630", 887, "1330772", "212475.374020", "0"], [1750952400000, "0.159830", "0.159930", "0.159650", "0.159860", "4117665", 1750952459999, "658178.792000", 1188, "2386471", "381467.783620", "0"], [1750952460000, "0.159850", "0.159980", "0.159800", "0.159830", "2979603", 1750952519999, "476382.524310", 1199, "1225086", "195858.561120", "0"], [1750952520000, "0.159840", "0.159940", "0.159810", "0.159880", "2455862", 1750952579999, "392607.878840", 860, "1386658", "221683.040920", "0"], [1750952580000, "0.159880", "0.159950", "0.159760", "0.159830", "3154615", 1750952639999, "504216.091900", 963, "745805", "119203.186430", "0"], [1750952640000, "0.159830", "0.159890", "0.159710", "0.159710", "2074308", 1750952699999, "331465.564510", 705, "434943", "69525.071230", "0"], [1750952700000, "0.159710", "0.160140", "0.159680", "0.160100", "5541436", 1750952759999, "886099.546290", 1183, "3857272", "616831.915590", "0"], [1750952760000, "0.160090", "0.160110", "0.159970", "0.160100", "2235190", 1750952819999, "357716.597390", 718, "1350241", "216099.730650", "0"]], "candles_count": 20, "data_timestamp": 1750952799, "has_timeseries_data": true, "short_term_change_pct": 0.13760320240180007, "recent_high": 0.16014, "recent_low": 0.15968}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "neutral", "reasoning": "Short-term candle data shows mixed but neutral trend with weak volume confirmation. No strong patterns or breakouts observed. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral", "volume", "candle"], "raw_response": " NO MARKDOWN. ONLY JSON.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term candle data shows mixed but neutral trend with weak volume confir...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_15e2c142_1750952806"}, "strategy": {"strategy_id": "830e36f4-58ab-447b-bb2e-4b03e6574dd6", "symbol": "DOGE", "timestamp": 1750952817, "type": "none", "entry_price": 0.16007, "stop_loss": 0.15647, "take_profit": 0.15686, "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 기준으로 상하 진동 중이며, MACD는 0을 중심으로 횡보하고 있다. 볼린저 밴드는 가격이 중앙에 위치해 있으며, 단기적인 방향성은 명확하지 않다. InCA의 HOLD 신호와 일치하여 현재는 거래를 자제하는 것이 적절하다.", "confidence": 0.75, "reasoning_card_id": "card_15e2c142_1750952806", "risk_level": "medium", "key_points": ["RSI와 MACD는 중립적인 신호를 보이고 있음", "볼린저 밴드 내부에서 가격이 진동 중", "InCA의 HOLD 신호와 일치하여 거래 자제 권장"], "market_context": {"price": 0.16007, "percent_change_24h": -1.804, "timestamp": 1750952799}, "paper_based": false, "risk_reward": 0.0, "importance": 8.121485220078494, "consensus_signal": "none", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.5, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.75, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_35ca22aa_1750952817"}}