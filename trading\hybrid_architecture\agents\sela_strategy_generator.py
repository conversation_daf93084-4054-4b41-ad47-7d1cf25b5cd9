"""
SELA 전략 생성 모듈

사고 흐름 카드를 기반으로 거래 전략을 생성하고
다양한 실험적 전략을 생성하는 기능을 제공합니다.
Tree Search Enhanced LLM Agents (SELA) 논문 구현.
"""

import logging
import time
import json
import math
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
import uuid
import random

from trading.hybrid_architecture.agents.sela_agent_base import SELAAgentBase

logger = logging.getLogger(__name__)

class MCTSNode:
    """MCTS 노드 클래스 - SELA 논문의 Tree Search 구현"""

    def __init__(self, strategy_data: Dict[str, Any], parent: Optional['MCTSNode'] = None):
        self.strategy_data = strategy_data
        self.parent = parent
        self.children = []
        self.visits = 0
        self.value = 0.0
        self.prior_probability = 1.0
        self.action = strategy_data.get('type', 'none')

    def get_ucb_score(self, exploration_weight: float = 1.4) -> float:
        """Upper Confidence Bound 점수 계산 - SELA importance로 사용"""
        if self.visits == 0:
            return float('inf')

        exploitation = self.value / self.visits
        if self.parent and self.parent.visits > 0:
            exploration = exploration_weight * self.prior_probability * \
                math.sqrt(math.log(self.parent.visits) / (1 + self.visits))
        else:
            exploration = exploration_weight * self.prior_probability

        return exploitation + exploration

    def add_child(self, strategy_data: Dict[str, Any]) -> 'MCTSNode':
        """자식 노드 추가"""
        child = MCTSNode(strategy_data, parent=self)
        self.children.append(child)
        return child

    def update(self, reward: float):
        """노드 업데이트 (백프로파게이션)"""
        self.visits += 1
        self.value += reward

    def is_leaf(self) -> bool:
        """리프 노드인지 확인"""
        return len(self.children) == 0

class SELAStrategyGenerator(SELAAgentBase):
    """
    SELA 전략 생성기 클래스

    사고 흐름 카드를 기반으로 거래 전략을 생성하고
    다양한 실험적 전략을 생성하는 기능을 제공합니다.
    Tree Search Enhanced LLM Agents (SELA) 논문 구현.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.mcts_simulations = 50  # MCTS 시뮬레이션 횟수
        self.exploration_weight = 1.4  # UCB 탐색 가중치
        self.historical_performance = {}  # 과거 성과 데이터 캐시

    def generate_strategy(self,
                         symbol: str,
                         market_data: Dict[str, Any],
                         reasoning_card: Dict[str, Any],
                         inca_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        사고 흐름 카드를 기반으로 전략 생성

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            reasoning_card: 사고 흐름 카드

        Returns:
            Dict[str, Any]: 생성된 전략
        """
        try:
            logger.info(f"🚀 [{symbol}] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)")
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 메서드 진입 성공")
            logger.info(f"🔍 [{symbol}] SELA_IMPL - symbol: {symbol}")
            logger.info(f"🔍 [{symbol}] SELA_IMPL - market_data 타입: {type(market_data)}")
            logger.info(f"🔍 [{symbol}] SELA_IMPL - reasoning_card 타입: {type(reasoning_card)}")

            # 파라미터 타입 체크
            if isinstance(market_data, str):
                logger.warning(f"🔍 [{symbol}] SELA_IMPL 디버깅 - market_data가 문자열로 전달됨: {market_data[:100]}...")
                market_data = {}
            if isinstance(reasoning_card, str):
                logger.warning(f"🔍 [{symbol}] SELA_IMPL 디버깅 - reasoning_card가 문자열로 전달됨: {reasoning_card[:100]}...")
                reasoning_card = {}

        except Exception as e:
            logger.error(f"❌ [{symbol}] SELA_IMPL - 메서드 진입 중 오류: {e}")
            return None

        # 🔍 디버깅: reasoning_card 내용 분석
        logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - reasoning_card 타입: {type(reasoning_card)}")
        logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - reasoning_card 키들: {list(reasoning_card.keys()) if isinstance(reasoning_card, dict) else 'N/A'}")

        if isinstance(reasoning_card, dict):
            # reasoning_card의 주요 필드들 확인
            for key, value in reasoning_card.items():
                if isinstance(value, str) and len(value) > 50:
                    logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - {key}: {value[:100]}...")
                else:
                    logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - {key}: {value}")

        # 🔍 디버깅: InCA 분석 결과 확인 (inca_result 우선, reasoning_card 백업)
        logger.info(f"🔍 [{symbol}] SELA_IMPL InCA 분석 결과:")

        # inca_evaluation 변수를 항상 정의 (하위 호환성)
        inca_evaluation = {}

        # InCA 결과 정보 추출 (안전한 기본값 설정)
        inca_action = 'hold'
        inca_importance = 5

        if inca_result:
            # inca_result 타입 체크
            if isinstance(inca_result, str):
                logger.warning(f"🔍 [{symbol}] SELA_IMPL 디버깅 - inca_result가 문자열로 전달됨: {inca_result[:100]}...")
                inca_evaluation = {}
            else:
                logger.info(f"🔍 [{symbol}] - situation_type: {inca_result.get('situation_type', 'N/A')} (분석용)")
                logger.info(f"🔍 [{symbol}] - action_recommendation: {inca_result.get('action_recommendation', 'N/A')} (분석용)")
                logger.info(f"🔍 [{symbol}] - importance: {inca_result.get('importance', 'N/A')} (분석용)")
                logger.info(f"🔍 [{symbol}] - reasoning: {inca_result.get('reasoning', 'N/A')[:50]}... (분석용)")
                # inca_result를 inca_evaluation 형태로 변환 (하위 호환성)
                inca_evaluation = inca_result

                # InCA 결과 정보 추출 (분석용 - 직접 사용하지 않음)
                inca_action = inca_result.get('action_recommendation', 'hold')
                inca_importance = inca_result.get('importance', 5)

                logger.info(f"🔍 [{symbol}] InCA 결과 분석: {inca_action}, 중요도: {inca_importance} (SELA 독립 분석)")

                # 현재 가격 추출 (참고용)
                current_price = float(market_data.get('price', 1000.0)) if isinstance(market_data, dict) else 1000.0
        else:
            # reasoning_card 타입 체크
            if isinstance(reasoning_card, str):
                logger.warning(f"🔍 [{symbol}] SELA_IMPL 디버깅 - reasoning_card가 문자열로 전달됨: {reasoning_card[:100]}...")
                inca_evaluation = {}
            else:
                inca_evaluation = reasoning_card.get('inca_evaluation', {}) if reasoning_card else {}
                logger.info(f"🔍 [{symbol}] - situation_type: {inca_evaluation.get('situation_type', 'N/A')} (백업 추출)")
                logger.info(f"🔍 [{symbol}] - action_recommendation: {inca_evaluation.get('action_recommendation', 'N/A')} (백업 추출)")
                logger.info(f"🔍 [{symbol}] - importance: {inca_evaluation.get('importance', 'N/A')} (백업 추출)")
                logger.info(f"🔍 [{symbol}] - reasoning: {inca_evaluation.get('reasoning', 'N/A')[:50]}... (백업 추출)")

        # 🔍 디버깅: reasoning_card에서 bullish/bearish 키워드 검색
        reasoning_card_str = str(reasoning_card).lower()
        bullish_found = 'bullish' in reasoning_card_str or '상승' in reasoning_card_str or 'buy' in reasoning_card_str or 'long' in reasoning_card_str
        bearish_found = 'bearish' in reasoning_card_str or '하락' in reasoning_card_str or 'sell' in reasoning_card_str or 'short' in reasoning_card_str

        logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - bullish 키워드 발견: {bullish_found}")
        logger.info(f"🔍 [{symbol}] SELA_IMPL 디버깅 - bearish 키워드 발견: {bearish_found}")

        if inca_evaluation.get('situation_type') == 'bullish':
            logger.info(f"✅ [{symbol}] SELA_IMPL - InCA BULLISH 분석 확인!")
        elif inca_evaluation.get('situation_type') == 'bearish':
            logger.info(f"✅ [{symbol}] SELA_IMPL - InCA BEARISH 분석 확인!")
        elif bullish_found:
            logger.info(f"✅ [{symbol}] SELA_IMPL - BULLISH 키워드 감지됨!")
        elif bearish_found:
            logger.info(f"✅ [{symbol}] SELA_IMPL - BEARISH 키워드 감지됨!")
        else:
            logger.info(f"⚠️ [{symbol}] SELA_IMPL - 명확한 방향성 신호 없음")

        try:
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 프롬프트 생성 시작")

            # 전략 생성을 위한 프롬프트 생성 (InCA 결과 포함)
            strategy_prompt = self._create_strategy_prompt(symbol, market_data, reasoning_card, inca_result)
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 프롬프트 생성 완료: {len(strategy_prompt)}자")

            # LLM 호출하여 전략 생성
            logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 호출 시작")
            start_time = time.time()

            # 🔧 vLLM 호환 파라미터만 사용 (frequency_penalty, presence_penalty 제거)
            generation_kwargs = {
                "temperature": 0.3,  # 적절한 온도로 다양성과 일관성 균형
                "top_p": 0.8,
                "max_tokens": 2048,
                "session_id": f"sela_{symbol}_{int(time.time() * 1000)}_{random.randint(1000, 9999)}",  # 고유 세션 ID
                "agent_type": "sela",  # 🎯 SELA 에이전트 타입 명시적 전달
                "symbol": symbol  # 심볼 정보도 함께 전달
            }

            logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 파라미터: {generation_kwargs}")
            logger.info(f"⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐")
            logger.info(f"⭐ SELA LLM 모델 타입: {type(self.llm_model)}")
            logger.info(f"⭐ SELA 전달 파라미터: kwargs={generation_kwargs}")

            # LLM 모델 타입에 따라 다른 호출 방식 사용
            if hasattr(self.llm_model, 'generate'):
                try:
                    strategy_response = self.llm_model.generate(
                        strategy_prompt,
                        **generation_kwargs
                    )
                    logger.info(f"🔍 [{symbol}] SELA_IMPL - 파라미터 호출 성공")
                except (TypeError, Exception) as e:
                    # 파라미터가 지원되지 않는 경우 기본 호출
                    logger.warning(f"🔍 [{symbol}] SELA_IMPL - 파라미터 지원 안됨, 기본 호출: {e}")
                    strategy_response = self.llm_model.generate(strategy_prompt)
            else:
                # 기본 호출
                logger.info(f"🔍 [{symbol}] SELA_IMPL - 기본 호출 사용")
                strategy_response = self.llm_model.generate(strategy_prompt)
            elapsed_time = time.time() - start_time

            # 🔍 디버깅: strategy_response 타입 확인
            logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 응답 타입: {type(strategy_response)}")

            # 안전한 길이 및 미리보기 처리
            if isinstance(strategy_response, str):
                logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 응답 수신: {len(strategy_response)}자, 소요시간: {elapsed_time:.2f}초")
                logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 응답 미리보기: {strategy_response[:200]}...")
            else:
                logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 응답 수신: {strategy_response}, 소요시간: {elapsed_time:.2f}초")
                logger.info(f"🔍 [{symbol}] SELA_IMPL - LLM 응답 미리보기: {str(strategy_response)[:200]}...")

            # JSON 추출
            logger.info(f"🔍 [{symbol}] SELA_IMPL - JSON 추출 시작")
            strategy_data = self._extract_json(strategy_response)
            logger.info(f"🔍 [{symbol}] SELA_IMPL - JSON 추출 완료: {strategy_data}")

            if not strategy_data:
                logger.error(f"❌ [{symbol}] SELA_IMPL - JSON 추출 실패: 유효한 응답을 받지 못했습니다.")
                raise ValueError(f"{symbol} 전략 생성 실패: 유효한 응답을 받지 못했습니다.")

            # 전략 ID 생성
            strategy_id = str(uuid.uuid4())
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 전략 ID 생성: {strategy_id}")

            # 필수 필드 검증
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 필수 필드 검증 시작")
            required_fields = ["type", "entry_price", "stop_loss", "take_profit", "reasoning"]
            for field in required_fields:
                if field not in strategy_data:
                    logger.error(f"❌ [{symbol}] SELA_IMPL - 필수 필드 누락: {field}")
                    raise ValueError(f"{symbol} 전략 생성 실패: 필수 필드({field})가 누락되었습니다.")
            logger.info(f"🔍 [{symbol}] SELA_IMPL - 필수 필드 검증 완료")

            # 전략 데이터 구성
            strategy = {
                "strategy_id": strategy_id,
                "symbol": symbol,
                "timestamp": int(time.time()),
                "type": strategy_data.get("type", "none"),  # 'buy', 'sell', 'none'
                "entry_price": float(strategy_data.get("entry_price", 0.0)),
                "stop_loss": self._extract_float_from_list_or_value(strategy_data.get("stop_loss", 0.0)),
                "take_profit": self._extract_float_from_list_or_value(strategy_data.get("take_profit", 0.0)),
                "reasoning": strategy_data.get("reasoning", ""),
                "confidence": float(strategy_data.get("confidence", 0.0)),
                "reasoning_card_id": reasoning_card.get("card_id", "") if isinstance(reasoning_card, dict) else "",
                "risk_level": self.risk_level,
                "key_points": strategy_data.get("key_points", []),
                "market_context": {
                    "price": market_data.get("price", 0.0) if isinstance(market_data, dict) else 0.0,
                    "percent_change_24h": market_data.get("percent_change_24h", 0.0) if isinstance(market_data, dict) else 0.0,
                    "timestamp": market_data.get("timestamp", int(time.time())) if isinstance(market_data, dict) else int(time.time())
                },
                "paper_based": self._is_paper_trading_mode()  # 🔧 거래 모드에 따라 동적 설정
            }

            # 위험-보상 비율 계산
            strategy["risk_reward"] = self._calculate_risk_reward(strategy)

            # 🚀 SELA 논문 구현: Tree Search 기반 importance 계산
            strategy["importance"] = self._calculate_tree_search_importance(
                strategy, symbol, market_data, reasoning_card
            )

            # 전략 저장
            self._save_strategy(strategy)

            # 🔍 디버깅: 최종 전략 결과 로깅
            logger.info(f"🎯 [{symbol}] SELA_IMPL 전략 생성 완료!")
            logger.info(f"🎯 [{symbol}] SELA_IMPL 최종 전략 유형: {strategy['type']}")
            logger.info(f"🎯 [{symbol}] SELA_IMPL 전략 신뢰도: {strategy['confidence']}")
            logger.info(f"🎯 [{symbol}] SELA_IMPL 전략 추론: {strategy['reasoning'][:100]}...")
            logger.info(f"🎯 [{symbol}] SELA_IMPL 소요 시간: {elapsed_time:.2f}초")

            # 🔍 디버깅: 전략 방향성 확인
            if strategy['type'] == 'buy':
                logger.info(f"✅ [{symbol}] SELA_IMPL - 최종 결과: BUY 전략 생성됨!")
            elif strategy['type'] == 'sell':
                logger.info(f"✅ [{symbol}] SELA_IMPL - 최종 결과: SELL 전략 생성됨!")
            else:
                logger.info(f"⚠️ [{symbol}] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!")



            return strategy

        except Exception as e:
            logger.error(f"❌ [{symbol}] SELA_IMPL 전략 생성 중 오류 발생: {e}")
            logger.error(f"❌ [{symbol}] SELA_IMPL 오류 타입: {type(e).__name__}")
            logger.error(f"❌ [{symbol}] SELA_IMPL 오류 상세: {str(e)}")
            import traceback
            logger.error(f"❌ [{symbol}] SELA_IMPL 스택 트레이스: {traceback.format_exc()}")

            # 예외를 다시 발생시키지 않고 None 반환
            return None

        except Exception as e:
            logger.error(f"❌ [{symbol}] SELA_IMPL 메서드 전체 오류: {e}")
            logger.error(f"❌ [{symbol}] SELA_IMPL 메서드 전체 오류 타입: {type(e).__name__}")
            logger.error(f"❌ [{symbol}] SELA_IMPL 메서드 전체 오류 상세: {str(e)}")
            import traceback
            logger.error(f"❌ [{symbol}] SELA_IMPL 메서드 전체 스택 트레이스: {traceback.format_exc()}")
            return None

    def generate_diverse_strategies(self,
                                  symbol: str,
                                  count: int = 5,
                                  external_market_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        다양한 실험적 전략 생성

        Args:
            symbol: 코인 심볼
            count: 생성할 전략 개수
            external_market_data: 외부에서 전달된 실제 시장 데이터 (우선 사용)

        Returns:
            List[Dict[str, Any]]: 생성된 전략 목록
        """
        logger.info(f"{symbol} 다양한 전략 {count}개 생성 시작")

        try:
            # 🔥 외부에서 전달된 실제 시장 데이터 우선 사용
            if external_market_data and isinstance(external_market_data, dict):
                logger.info(f"🔍 [{symbol}] 외부 시장 데이터 사용: 가격=${external_market_data.get('price', 'N/A')}")
                market_data = external_market_data
            else:
                # 최신 시장 데이터 조회 (DB에서)
                logger.info(f"🔍 [{symbol}] 내부 시장 데이터 조회 시도")
                market_data = self._get_latest_market_data(symbol)

            if not market_data:
                raise ValueError(f"{symbol} 다양한 전략 생성 실패: 시장 데이터를 찾을 수 없습니다.")

            # 🔥 실제 시장 데이터 확인 로그
            logger.info(f"🔍 [{symbol}] 다양한 전략 생성용 시장 데이터:")
            logger.info(f"🔍 [{symbol}] - 가격: ${market_data.get('price', 'N/A')}")
            logger.info(f"🔍 [{symbol}] - 24h 변동률: {market_data.get('percent_change_24h', market_data.get('change_24h', 'N/A'))}%")
            logger.info(f"🔍 [{symbol}] - 거래량: {market_data.get('volume', market_data.get('volume_24h', 'N/A'))}")

            # 다양한 전략 생성 프롬프트
            diverse_prompt = self._create_diverse_strategies_prompt(symbol, market_data, count)

            # 🔍 디버깅: 실제 전달되는 프롬프트 로그 출력
            logger.info(f"🔍 [{symbol}] SELA 다양한 전략 생성 프롬프트 (처음 200자):")
            logger.info(f"🔍 {diverse_prompt[:200]}...")
            logger.info(f"🔍 [{symbol}] SELA 다양한 전략 생성 프롬프트 (마지막 200자):")
            logger.info(f"🔍 ...{diverse_prompt[-200:]}")

            # 🔧 vLLM 요청 큐를 통한 LLM 호출 (동시성 문제 해결)
            start_time = time.time()

            # vLLM 요청 큐를 통해 호출
            from models.vllm_request_queue import queue_vllm_request

            diverse_response = queue_vllm_request(
                agent_type="sela_diverse",
                symbol=symbol,
                prompt=diverse_prompt,
                params={
                    'max_tokens': 4096,  # 토큰 수 조정 (6144 → 4096)
                    'temperature': 0.3,  # 온도 낮춤 (0.4 → 0.3) - 더 일관된 응답
                    'top_p': 0.8,        # top_p 낮춤 (0.85 → 0.8) - 더 집중된 응답
                    'timeout': 180       # 타임아웃 증가 (120 → 180)
                },
                priority=2,  # 우선순위 높임 (3 → 2)
                timeout=600.0        # 타임아웃 증가 (180 → 600)
            )

            elapsed_time = time.time() - start_time
            logger.info(f"다양한 전략 생성 - vLLM 큐 호출 성공 (소요 시간: {elapsed_time:.2f}초)")

            # 🔍 응답 상세 로깅 추가 (디버깅용)
            logger.info(f"🔍 다양한 전략 생성 LLM 응답 타입: {type(diverse_response)}")
            logger.info(f"🔍 다양한 전략 생성 LLM 응답 전체: {diverse_response}")

            # JSON 추출 (LLM 응답에서 text 필드 먼저 추출)
            if isinstance(diverse_response, dict) and 'text' in diverse_response:
                response_text = diverse_response['text']
            else:
                response_text = str(diverse_response)

            logger.info(f"🔍 다양한 전략 생성 응답 텍스트 길이: {len(response_text)}자")
            logger.info(f"🔍 다양한 전략 생성 응답 텍스트 전체: {response_text}")

            # 🔧 SELA 다양한 전략 생성 전용 </think> 태그 제거
            if "</think>" in response_text:
                logger.info(f"🔧 SELA 다양한 전략 생성에서 </think> 태그 감지됨, 제거 중...")
                think_end = response_text.find("</think>")
                if think_end != -1:
                    response_text = response_text[think_end + 8:].strip()
                    logger.info(f"🔧 SELA 다양한 전략 생성 </think> 태그 제거 완료")
                    logger.info(f"🔧 정리된 응답 텍스트: {response_text}")

            # 🔍 JSON 파싱 전 상세 로깅 (디버깅용)
            logger.info(f"🔍 JSON 파싱 전 텍스트 길이: {len(response_text)}자")
            logger.info(f"🔍 JSON 파싱 전 텍스트 첫 100자: {response_text[:100]}")
            logger.info(f"🔍 JSON 파싱 전 텍스트 마지막 100자: {response_text[-100:]}")

            # 🔍 char 413 위치 확인 (오류 위치 분석)
            if len(response_text) > 413:
                logger.info(f"🔍 char 413 위치 문자: '{response_text[413]}'")
                logger.info(f"🔍 char 410-420 범위: '{response_text[410:420]}'")
            else:
                logger.info(f"🔍 텍스트가 413자보다 짧음: {len(response_text)}자")

            # JSON 파싱 시도
            try:
                diverse_data = self._extract_json(response_text)
                logger.info(f"🔍 JSON 파싱 성공!")
            except Exception as e:
                logger.error(f"🔍 JSON 파싱 실패: {e}")
                logger.error(f"🔍 실패한 텍스트 전체: {response_text}")
                # 🔍 JSON 유효성 직접 검사
                import json
                try:
                    json.loads(response_text)
                    logger.error(f"🔍 표준 JSON 파싱은 성공함 - _extract_json 메서드 문제")
                except json.JSONDecodeError as json_e:
                    logger.error(f"🔍 표준 JSON 파싱도 실패: {json_e}")
                raise

            # LLM 분석 실패 시 기본 전략 생성
            if not diverse_data or "strategies" not in diverse_data:
                logger.warning(f"{symbol} SELA 다양한 전략 생성 LLM 분석 실패 - 기본 전략 생성")
                return self._generate_fallback_diverse_strategies(symbol, market_data, count)

            # 전략 목록 추출
            strategy_list = diverse_data.get("strategies", [])

            # 각 전략에 필요한 정보 추가
            current_time = int(time.time())
            strategies = []

            for i, strategy_data in enumerate(strategy_list):
                try:
                    # 필수 필드 검증
                    required_fields = ["type", "entry_price", "stop_loss", "take_profit", "reasoning"]
                    for field in required_fields:
                        if field not in strategy_data:
                            logger.warning(f"전략 {i+1}에 필수 필드({field})가 누락되었습니다. 건너뜁니다.")
                            continue

                    # 전략 ID 생성
                    strategy_id = str(uuid.uuid4())

                    # 전략 데이터 구성
                    strategy = {
                        "strategy_id": strategy_id,
                        "symbol": symbol,
                        "timestamp": current_time,
                        "type": strategy_data.get("type", "none"),  # 'buy', 'sell', 'none'
                        "entry_price": float(strategy_data.get("entry_price", 0.0)),
                        "stop_loss": self._extract_float_from_list_or_value(strategy_data.get("stop_loss", 0.0)),
                        "take_profit": self._extract_float_from_list_or_value(strategy_data.get("take_profit", 0.0)),
                        "reasoning": strategy_data.get("reasoning", ""),
                        "confidence": float(strategy_data.get("confidence", 0.0)),
                        "reasoning_card_id": "",  # 사고 카드 없음
                        "risk_level": strategy_data.get("risk_level", self.risk_level),
                        "key_points": strategy_data.get("key_points", []),
                        "market_context": {
                            "price": market_data.get("price", 0.0),
                            "percent_change_24h": market_data.get("percent_change_24h", 0.0),
                            "timestamp": market_data.get("timestamp", current_time)
                        },
                        "strategy_index": i,
                        "strategy_type": strategy_data.get("strategy_type", "experimental")
                    }

                    # 위험-보상 비율 계산
                    strategy["risk_reward"] = self._calculate_risk_reward(strategy)

                    # 🚀 SELA 논문 구현: Tree Search 기반 importance 계산 (다양한 전략용)
                    strategy["importance"] = self._calculate_simplified_importance(strategy, symbol, market_data)

                    # 전략 저장
                    self._save_strategy(strategy)

                    strategies.append(strategy)

                except Exception as e:
                    logger.error(f"전략 {i+1} 처리 중 오류 발생: {e}")

            logger.info(f"{symbol} 다양한 전략 생성 완료 (소요 시간: {elapsed_time:.2f}초, 생성된 전략: {len(strategies)}개)")

            return strategies

        except Exception as e:
            logger.error(f"{symbol} 다양한 전략 생성 중 오류 발생: {e}")
            raise

    def _extract_json(self, text) -> Dict[str, Any]:
        """
        텍스트 또는 dict에서 JSON 추출

        Args:
            text: JSON을 포함할 수 있는 텍스트 또는 dict

        Returns:
            Dict[str, Any]: 추출된 JSON
        """
        import json
        import re

        try:
            # 0. 입력 타입 확인 및 전처리
            if isinstance(text, dict):
                logger.info("🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도")
                if 'text' in text:
                    text_content = text['text']
                    logger.info(f"🔍 dict에서 텍스트 추출 성공, 길이: {len(text_content)}자")
                    text = text_content
                else:
                    logger.info("🔍 dict에 'text' 키가 없음, dict 자체를 반환")
                    return text

            if not isinstance(text, str):
                logger.error(f"🔍 지원되지 않는 입력 타입: {type(text)}")
                raise ValueError(f"지원되지 않는 입력 타입: {type(text)}")

            logger.info(f"🔍 JSON 추출 시도, 텍스트 길이: {len(text)}자")

            # 1. 직접 JSON 파싱 시도
            try:
                result = json.loads(text)
                logger.info("🔍 직접 JSON 파싱 성공")
                return result
            except json.JSONDecodeError:
                logger.debug("직접 JSON 파싱 실패, 텍스트 정제 시도")

            # 2. ```json 블록 찾기
            if "```json" in text and "```" in text.split("```json", 1)[1]:
                json_text = text.split("```json", 1)[1].split("```", 1)[0].strip()
                try:
                    result = json.loads(json_text)
                    logger.info("🔍 ```json 블록에서 JSON 파싱 성공")
                    return result
                except json.JSONDecodeError:
                    logger.debug("```json 블록 JSON 파싱 실패")

            # 3. ``` 블록 찾기
            elif "```" in text and "```" in text.split("```", 1)[1]:
                json_text = text.split("```", 1)[1].split("```", 1)[0].strip()
                try:
                    result = json.loads(json_text)
                    logger.info("🔍 ``` 블록에서 JSON 파싱 성공")
                    return result
                except json.JSONDecodeError:
                    logger.debug("``` 블록 JSON 파싱 실패")

            # 4. 중괄호로 둘러싸인 텍스트 찾기
            if '{' in text and '}' in text:
                start_idx = text.find('{')
                end_idx = text.rfind('}')

                if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
                    json_text = text[start_idx:end_idx+1]

                    # 기본 정제
                    json_text = json_text.replace("'", '"')  # 작은따옴표를 큰따옴표로
                    json_text = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', json_text)  # 키에 따옴표 추가

                    try:
                        result = json.loads(json_text)
                        logger.info("🔍 중괄호 기반 JSON 파싱 성공")
                        return result
                    except json.JSONDecodeError:
                        logger.debug("중괄호 기반 JSON 파싱 실패")

            # 5. 정규 표현식으로 JSON 객체 찾기
            json_pattern = r'({[\s\S]*?})'
            matches = re.findall(json_pattern, text)

            if matches:
                for potential_json in matches:
                    try:
                        # 기본 정제
                        cleaned_json = potential_json.replace("'", '"')
                        cleaned_json = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', cleaned_json)

                        result = json.loads(cleaned_json)
                        logger.info("🔍 정규 표현식 기반 JSON 파싱 성공")
                        return result
                    except json.JSONDecodeError:
                        continue

            # 모든 방법 실패
            logger.warning("🔍 모든 JSON 추출 방법 실패")
            raise ValueError("텍스트에서 유효한 JSON을 찾을 수 없습니다")

        except Exception as e:
            logger.error(f"🔍 JSON 추출 중 오류 발생: {e}")
            # 안전한 텍스트 미리보기
            try:
                if isinstance(text, str):
                    preview = text[:200] if len(text) > 200 else text
                    logger.error(f"🔍 원본 텍스트 (첫 200자): {preview}")
                else:
                    logger.error(f"🔍 원본 데이터 타입: {type(text)}, 값: {str(text)[:200]}")
            except Exception as preview_error:
                logger.error(f"🔍 텍스트 미리보기 중 오류: {preview_error}")

            raise ValueError(f"JSON 추출 중 오류 발생: {e}")

    def _extract_float_from_list_or_value(self, value) -> float:
        """
        리스트 또는 단일 값에서 float 추출

        Args:
            value: 리스트 또는 단일 값

        Returns:
            float: 추출된 float 값
        """
        try:
            if isinstance(value, list) and len(value) > 0:
                # 리스트인 경우 첫 번째 값 사용
                return float(value[0])
            elif isinstance(value, (int, float, str)):
                # 단일 값인 경우 직접 변환
                return float(value)
            else:
                # 기타 경우 기본값 반환
                return 0.0
        except (ValueError, TypeError, IndexError):
            # 변환 실패 시 기본값 반환
            return 0.0

    def _generate_fallback_diverse_strategies(self, symbol: str, market_data: Dict[str, Any], count: int = 5) -> List[Dict[str, Any]]:
        """
        기본 다양한 전략 생성 (LLM 분석 실패시 대체용)

        Args:
            symbol: 대상 심볼
            market_data: 시장 데이터
            count: 생성할 전략 수

        Returns:
            List[Dict[str, Any]]: 기본 전략 목록
        """
        logger.info(f"{symbol} 기본 다양한 전략 생성 - {count}개")
        current_price = market_data.get('price', 1000.0)
        current_time = int(time.time())

        strategies = []

        # 전략 유형 목록
        strategy_types = ['buy', 'sell', 'none']

        for i in range(count):
            strategy_type = strategy_types[i % len(strategy_types)]

            # 기본 전략 데이터 구성
            if strategy_type == 'buy':
                entry_price = current_price
                stop_loss = current_price * 0.98
                take_profit = current_price * 1.04
                reasoning = f"{symbol} 상승 추세 예상으로 매수 전략"
            elif strategy_type == 'sell':
                entry_price = current_price
                stop_loss = current_price * 1.02
                take_profit = current_price * 0.96
                reasoning = f"{symbol} 하락 추세 예상으로 매도 전략"
            else:  # none
                entry_price = current_price
                stop_loss = current_price * 0.99
                take_profit = current_price * 1.01
                reasoning = f"{symbol} 방향성 불분명으로 관망 전략"

            strategy = {
                "strategy_id": f"fallback_{symbol}_{current_time}_{i}",
                "symbol": symbol,
                "timestamp": current_time,
                "type": strategy_type,
                "entry_price": float(entry_price),
                "stop_loss": float(stop_loss),
                "take_profit": float(take_profit),
                "reasoning": reasoning,
                "confidence": 0.5,
                "reasoning_card_id": "",
                "risk_level": self.risk_level,
                "key_points": [f"기본 {strategy_type} 전략", "LLM 분석 실패로 생성", "보수적 접근"],
                "market_context": {
                    "price": current_price,
                    "percent_change_24h": market_data.get("percent_change_24h", 0.0),
                    "timestamp": current_time
                },
                "strategy_index": i,
                "strategy_type": "fallback"
            }

            # 위험-보상 비율 계산
            strategy["risk_reward"] = self._calculate_risk_reward(strategy)

            # importance 계산
            strategy["importance"] = 5.0  # 기본값

            strategies.append(strategy)

        logger.info(f"{symbol} 기본 다양한 전략 생성 완료: {len(strategies)}개")
        return strategies

    def _create_strategy_prompt(self,
                              symbol: str,
                              market_data: Dict[str, Any],
                              reasoning_card: Dict[str, Any],
                              inca_result: Dict[str, Any] = None) -> str:
        """
        전략 생성 프롬프트 생성

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            reasoning_card: 사고 흐름 카드

        Returns:
            str: 전략 생성 프롬프트
        """
        # 현재 시간
        current_time = datetime.fromtimestamp(int(time.time())).strftime('%Y-%m-%d %H:%M:%S')

        # 🔥 실제 시장 데이터 사용 확인 및 로깅
        logger.info(f"🔍 [{symbol}] SELA 전략 생성용 시장 데이터 확인:")
        logger.info(f"🔍 [{symbol}] - 가격: ${market_data.get('price', 'N/A')}")
        logger.info(f"🔍 [{symbol}] - 24h 변동률: {market_data.get('percent_change_24h', market_data.get('change_24h', 'N/A'))}%")
        logger.info(f"🔍 [{symbol}] - 거래량: {market_data.get('volume', market_data.get('volume_24h', 'N/A'))}")
        # 시가총액 포맷팅 (달러 단위로 표시)
        market_cap = market_data.get('market_cap', 0)
        if market_cap and market_cap > 0:
            if market_cap >= 1e9:  # 10억 이상
                market_cap_str = f"${market_cap/1e9:.2f}B"
            elif market_cap >= 1e6:  # 100만 이상
                market_cap_str = f"${market_cap/1e6:.2f}M"
            else:
                market_cap_str = f"${market_cap:,.0f}"
        else:
            market_cap_str = "N/A"

        logger.info(f"🔍 [{symbol}] - 시가총액: {market_cap_str}")

        # 시장 데이터 문자열 (실제 데이터 사용)
        price = market_data.get('price', 'N/A')
        change_24h = market_data.get('percent_change_24h', market_data.get('change_24h', 'N/A'))
        volume = market_data.get('volume', market_data.get('volume_24h', 'N/A'))
        market_cap = market_data.get('market_cap', 'N/A')

        market_str = f"""
현재 가격: {price} USD
24시간 변동률: {change_24h}%
거래량: {volume} USD
시가총액: {market_cap} USD
"""

        # 🚀 InCA 분석 결과 추출 (최우선 고려)
        inca_analysis_str = ""
        inca_situation_type = "neutral"
        inca_action_recommendation = "hold"

        # inca_result 파라미터에서 직접 추출
        if inca_result:
            inca_situation_type = inca_result.get('situation_type', 'neutral')
            inca_action_recommendation = inca_result.get('action_recommendation', 'hold')
            inca_importance = inca_result.get('importance', 5)
            inca_reasoning = inca_result.get('reasoning', 'N/A')

            inca_analysis_str = f"""
## 📊 InCA 시장 중요도 분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**

- **InCA 시장 상황**: {inca_situation_type.upper()}
- **InCA 추천 행동**: {inca_action_recommendation.upper()}
- **시장 중요도**: {inca_importance}/10
- **InCA 분석 근거**: {inca_reasoning}

🚨 **CRITICAL TRADING RULES - MUST FOLLOW**:
1. **InCA BEARISH + SELL 신호 → MANDATORY type: "sell"**
2. **InCA BULLISH + BUY 신호 → MANDATORY type: "buy"**
3. **InCA NEUTRAL + HOLD 신호 → 시장 분석 후 적절한 전략 생성 (buy/sell/none)**

⚠️ **CURRENT InCA SIGNAL: {inca_situation_type.upper()} + {inca_action_recommendation.upper()}**

🔥 **MANDATORY ACTION BASED ON InCA**:
- If InCA = BEARISH + SELL → You MUST generate type: "sell"
- If InCA = BULLISH + BUY → You MUST generate type: "buy"
- If InCA = NEUTRAL + HOLD → You MUST generate type: "none"

📊 InCA 분석 결과를 우선 반영하여 전략을 생성하세요:
- InCA 추천: {inca_action_recommendation.upper()} (필수 고려)
- 시장 데이터와 종합하여 최적의 전략 판단

🚨 DO NOT IGNORE InCA SIGNALS! FOLLOW THE MANDATORY RULES ABOVE!
"""
        else:
            # reasoning_card에서 백업 추출 시도
            if reasoning_card:
                inca_evaluation = reasoning_card.get('inca_evaluation', {})
                if inca_evaluation:
                    inca_situation_type = inca_evaluation.get('situation_type', 'neutral')
                    inca_action_recommendation = inca_evaluation.get('action_recommendation', 'hold')
                    inca_importance = inca_evaluation.get('importance', 5)
                    inca_reasoning = inca_evaluation.get('reasoning', 'N/A')

                    inca_analysis_str = f"""
## 📊 InCA 시장 중요도 분석 결과 (필수 준수)
✅ InCA 분석을 적극 활용하여 전략을 생성하세요!
⚠️ **InCA 신호를 반드시 우선 고려하세요.**

- InCA 상황: {inca_situation_type.upper()}
- InCA 추천: {inca_action_recommendation.upper()}
- 중요도: {inca_importance}/10
- 근거: {inca_reasoning}

🚨 **CRITICAL TRADING RULES - MUST FOLLOW**:
1. **InCA BEARISH + SELL 신호 → MANDATORY type: "sell"**
2. **InCA BULLISH + BUY 신호 → MANDATORY type: "buy"**
3. **InCA NEUTRAL + HOLD 신호 → 시장 분석 후 적절한 전략 생성 (buy/sell/none)**

⚠️ **CURRENT InCA SIGNAL: {inca_situation_type.upper()} + {inca_action_recommendation.upper()}**

🔥 **MANDATORY ACTION**: Follow InCA signal exactly - DO NOT IGNORE!
"""

        # 사고 흐름 카드 문자열
        reasoning_str = "사고 흐름 정보 없음"
        if reasoning_card:
            reasoning_str = f"""
컨텍스트: {reasoning_card.get('context', 'N/A')}
관찰: {reasoning_card.get('observations', 'N/A')}
분석: {reasoning_card.get('analysis', 'N/A')}
결론: {reasoning_card.get('conclusion', 'N/A')}
제안 행동: {reasoning_card.get('action', 'N/A')}
확신도: {reasoning_card.get('confidence', 'N/A')}
주요 인사이트: {', '.join(reasoning_card.get('key_insights', ['N/A']))}
"""

        # 위험 수준에 따른 안내 (더 관대하게 수정)
        risk_guidance = {
            "low": "보수적인 전략을 선호합니다. 위험-보상 비율이 최소 1.8 이상이어야 합니다.",
            "medium": "균형 잡힌 전략을 선호합니다. 위험-보상 비율이 최소 1.5 이상이어야 합니다.",
            "high": "공격적인 전략을 수용합니다. 위험-보상 비율이 최소 1.2 이상이어야 합니다."
        }.get(self.risk_level, "균형 잡힌 전략을 선호합니다. 위험-보상 비율이 최소 1.5 이상이어야 합니다.")

        # 🚀 SELA 시간 프레임: 1시간봉 24개 (추세 확인) + 고급 기술적 분석
        hourly_candles = market_data.get("hourly_candles", market_data.get("candles", []))
        candle_data_str = ""
        if hourly_candles and len(hourly_candles) >= 5:  # 최소 5개만 있어도 분석 가능
            # 사용 가능한 캔들 수에 따라 조정
            available_candles = min(len(hourly_candles), 24)
            recent_candles = hourly_candles[-available_candles:]
            candle_data_str = f"## 📊 SELA 고급 기술적 분석 (1시간봉 {available_candles}개)\n"

            # 🔧 고급 기술적 분석 수행
            close_prices = []
            high_prices = []
            low_prices = []
            volumes = []

            for candle in recent_candles:
                if isinstance(candle, list) and len(candle) >= 5:
                    close_prices.append(float(candle[4]))
                    high_prices.append(float(candle[2]))
                    low_prices.append(float(candle[3]))
                    volumes.append(float(candle[5]))

            if len(close_prices) >= 3:
                # 🔧 고급 패턴 분석
                pattern_analysis = self._analyze_hourly_patterns(recent_candles, close_prices)
                volume_analysis = self._analyze_hourly_volume(recent_candles, volumes)
                trendline_analysis = self._analyze_hourly_trendlines(close_prices, high_prices, low_prices)

                # 전체 추세 분석
                if len(close_prices) >= 2:
                    first_close = close_prices[0]
                    last_close = close_prices[-1]
                    trend_change = ((last_close - first_close) / first_close) * 100
                    candle_data_str += f"📈 {available_candles}시간 전체 추세: {trend_change:.2f}% {'상승' if trend_change > 0 else '하락' if trend_change < 0 else '보합'}\n"

                # 고급 분석 결과 추가
                candle_data_str += f"🔧 패턴 분석: {pattern_analysis.get('description', 'N/A')}\n"
                candle_data_str += f"🔧 볼륨 분석: {volume_analysis.get('description', 'N/A')}\n"
                candle_data_str += f"🔧 추세선 분석: {trendline_analysis.get('description', 'N/A')}\n"

                # 종합 신호 강도
                pattern_signal = pattern_analysis.get('signal', 'neutral')
                volume_signal = volume_analysis.get('signal', 'neutral')
                trendline_signal = trendline_analysis.get('signal', 'neutral')

                bullish_signals = sum([s == 'bullish' for s in [pattern_signal, volume_signal, trendline_signal]])
                bearish_signals = sum([s == 'bearish' for s in [pattern_signal, volume_signal, trendline_signal]])

                candle_data_str += f"🎯 종합 신호: 강세 {bullish_signals}/3, 약세 {bearish_signals}/3\n\n"

            # 최근 3개 1시간봉 상세 분석 (간소화)
            candle_data_str += "최근 3개 1시간봉 상세:\n"
            for i, candle in enumerate(recent_candles[-3:]):
                if isinstance(candle, list) and len(candle) >= 5:
                    open_price = float(candle[1])
                    close_price = float(candle[4])
                    volume = float(candle[5])

                    direction = "상승" if close_price > open_price else "하락" if close_price < open_price else "보합"
                    change_pct = ((close_price - open_price) / open_price) * 100 if open_price > 0 else 0
                    candle_data_str += f"1시간봉 {i+1}: {direction} {change_pct:.2f}% (거래량: {volume:.0f})\n"
        else:
            candle_data_str = "## 📊 SELA 분석 데이터\n1시간봉 데이터가 부족합니다 (최소 5개 필요, 현재: {}).\n".format(len(hourly_candles) if hourly_candles else 0)

        # 🎯 단기 변동률 우선 사용 (타임시리즈 변화 중심)
        short_term_change = market_data.get('short_term_change_pct', 0)
        percent_change_24h = market_data.get('percent_change_24h', 0)

        # 🔥 현재 가격 추출 (진입가 설정용)
        current_price = market_data.get('price', 1000.0)
        if isinstance(current_price, str):
            try:
                current_price = float(current_price)
            except:
                current_price = 1000.0

        # 단기 변동률을 퍼센트로 변환 (소수점 형태를 퍼센트로)
        if short_term_change != 0:
            short_term_pct = short_term_change * 100  # 0.001 -> 0.1%
        else:
            short_term_pct = 0

        logger.info(f"🔍 [{symbol}] SELA 변동률 분석:")
        logger.info(f"🔍 [{symbol}] - 현재 가격: ${current_price}")
        logger.info(f"🔍 [{symbol}] - 단기 변동률 (3분봉): {short_term_pct:.3f}%")
        logger.info(f"🔍 [{symbol}] - 24시간 변동률: {percent_change_24h:.2f}%")

        # 📊 단기 거래 우선 전략 가이드 (사용자 요구사항 반영)
        direction_guidance = ""

        # InCA 분석 결과 활용 (안전한 기본값 설정)
        inca_importance = 5
        inca_market_direction = 'neutral'
        # 🔧 이 변수는 사용하지 않음 (inca_action_recommendation 사용)

        if inca_result and isinstance(inca_result, dict):
            inca_importance = inca_result.get('importance', 5)
            inca_market_direction = inca_result.get('situation_type', 'neutral')
            # 🔧 inca_action_recommendation은 이미 위에서 설정됨

        # InCA 신호를 포함한 시장 분석 정보 제공
        direction_guidance = f"""📊 시장 분석 정보:
- 시장 중요도: {inca_importance}/10
- 시장 방향성: {inca_market_direction}
- InCA 추천 행동: {inca_action_recommendation}

🎯 중요: InCA 신호와 시장 데이터를 종합하여 최적 전략을 생성하세요:

📈 시장 분석 기준 (단타 중심):
- 🔥 실시간 단기 변동률: {short_term_pct:.3f}% (3분봉 기준)
- 📊 24시간 변동률: {percent_change_24h:.2f}% (참고용)
- 🎯 타임시리즈 변화: 최근 캔들 패턴과 모멘텀 중심 분석
- 📈 기술적 지표와 캔들 패턴 분석
- 📊 시장 모멘텀과 거래량 분석

🎯 전략 선택 가이드 (단타 최적화 - 강화된 신호 포착):
- 'buy': 단기 상승 신호가 있을 때 선택 (롱 포지션) - 약한 신호도 포함
- 'sell': 단기 하락 신호가 있을 때 선택 (숏 포지션) - 약한 신호도 포함
- 'none': 정말 신호가 없거나 상반된 신호가 혼재할 때만 선택

⚠️ 중요: 'none'을 너무 자주 선택하지 마세요. 미세한 신호라도 포착하여 거래 기회를 만드세요.

💡 적극적 단타 분석: 실시간 타임시리즈 변화를 중심으로 거래 기회를 포착하세요!
🔥 중요: 24시간 변동률보다 실시간 단기 변동률과 캔들 패턴을 우선 고려하세요!"""



        # 🔧 vLLM 캐싱 방지를 위한 강력한 고유 식별자 추가
        import uuid
        import random
        import hashlib
        unique_id = str(uuid.uuid4())[:8]
        random_seed = random.randint(1000, 9999)
        timestamp_ms = int(time.time() * 1000)

        # 심볼별 고유 해시 생성
        symbol_hash = hashlib.md5(f"{symbol}_{timestamp_ms}_{random_seed}".encode()).hexdigest()[:8]

        # 최종 프롬프트 조합
        prompt = f"""🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 {symbol} 전문 암호화폐 트레이딩 전략 생성 전문가입니다.
현재 시간: {current_time}
🎯 분석 대상: {symbol} (심볼 해시: {symbol_hash})
🆔 요청 ID: {unique_id}
⏰ 타임스탬프: {timestamp_ms}
🎲 시드: {random_seed}
🔑 고유키: {symbol}_{unique_id}_{random_seed}

{inca_analysis_str}

## 시장 데이터
{market_str}

{candle_data_str}

## 📊 시장 중요도 참고 정보 (InCA 평가)
{direction_guidance}

## 사고 흐름 분석
{reasoning_str}

## 위험 선호도
{risk_guidance}

🎯 **단기 시장 예측 중심 전략 생성**

위 정보를 바탕으로 {symbol}의 **다음 1-3시간 내 가격 움직임을 예측**하여 거래 전략을 생성해 주세요.

📊 **단기 예측 분석 요소 (단타 최적화)**:
1. **🔥 실시간 캔들 패턴**: 지난 3-5개 3분봉의 방향성과 모멘텀 (최우선)
2. **📈 단기 변동률**: {short_term_pct:+.3f}% (3분봉 기준 실시간 변화)
3. **📊 InCA 중요도 참고**: InCA가 평가한 시장 중요도와 방향성 (참고용)
4. **🎯 볼륨 분석**: 거래량 변화가 시사하는 단기 방향성
5. **📉 24시간 변동률**: {percent_change_24h:+.2f}% (장기 트렌드 참고용)

🔮 **전략 생성 가이드라인 (단타 중심)**:

💡 **단기 시장 분석**: 실시간 타임시리즈 변화를 최우선으로 분석
💡 **적극적 판단**: 작은 단기 변화도 거래 기회로 인식
💡 **빠른 리스크 관리**: 단타에 적합한 손절가와 목표가 설정

**🔥 현재 {symbol} 실시간 단기 변동률: {short_term_pct:+.3f}% (3분봉 기준)**
**📊 24시간 변동률: {percent_change_24h:+.2f}% (참고용)**
**단기 변동률을 우선으로 최적의 전략을 선택하세요!**

💡 **분석 방법**: 실시간 타임시리즈 변화 + InCA 분석 결과 종합 판단!
📊 **InCA 분석 참고**:
- InCA 추천: {inca_action_recommendation} (참고용)
- 실시간 시장 데이터와 종합하여 최적의 전략 판단

📊 **시장 데이터 우선순위 분석**:
- **🔥 1순위: {short_term_pct:+.3f}% 실시간 단기 변동률** → **즉시 전략 선택**
- **📊 2순위: 최근 캔들 패턴과 모멘텀** → **방향성 확인**
- **📈 3순위: {percent_change_24h:+.2f}% 24시간 변동률** → **장기 트렌드 참고**
- **🎯 단타 최적화**: 실시간 변화를 중심으로 빠른 판단

중요 지침:
1. 반드시 유효한 JSON 객체만으로 응답해야 합니다.
2. JSON 앞뒤에 다른 텍스트를 포함하지 마세요.
3. ```json 또는 ``` 같은 마크다운 형식을 포함하지 마세요.
4. 설명, 소개, 주석을 포함하지 마세요.
5. 응답 전체가 단일 유효 JSON 객체여야 합니다.
6. 아래 예시의 모든 필드는 필수입니다. 어떤 필드도 생략하지 마세요.
7. 모든 키와 문자열 값은 반드시 큰따옴표(")로 감싸야 합니다.
8. 작은따옴표나 따옴표 없는 키를 사용하지 마세요.
9. JSON에 주석을 포함하지 마세요.
10. 리스크/리워드 비율이 최소 1.2 이상이 되도록 stop_loss와 take_profit 값을 설정하세요.
11. 리스크/리워드 비율 = |take_profit - entry_price| / |entry_price - stop_loss|
12. **MANDATORY**: {symbol} 실시간 단기 변동률 {short_term_pct:+.3f}% → 단기 변동률 우선 전략 선택!
13. **참고**: 24시간 변동률 {percent_change_24h:+.2f}% → 장기 트렌드 참고용

**🎯 전략 선택 가이드:**
- 📈 **BUY**: 상승 신호가 명확할 때 선택 (롱 포지션)
- 📉 **SELL**: 하락 신호가 명확할 때 선택 (숏 포지션)
- ⏸️ **NONE**: 신호가 불분명하거나 관망이 적절할 때

**🔥 현재 {symbol} 단기 변동률: {short_term_pct:+.3f}% (3분봉 기준)**
**📊 24시간 변동률: {percent_change_24h:+.2f}% (참고용)**
**💰 현재 가격: ${current_price} (진입가 기준)**

**🚨 분석 요구사항:**
1. 실시간 단기 변동률 {short_term_pct:+.3f}%를 최우선으로 분석
2. 캔들 패턴과 시장 데이터를 종합하여 전략 선택
3. 구체적인 분석 근거를 reasoning에 명시
4. 진입가는 반드시 현재 가격 ${current_price}를 사용

**📋 JSON 응답 형식:**
{{
  "type": "[buy|sell|none]",
  "direction": "[long|short|neutral]",
  "entry_price": {current_price},
  "stop_loss": [현재가 ${current_price}의 ±1-3% 범위],
  "take_profit": [현재가 ${current_price}의 ±2-5% 범위],
  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",
  "confidence": "[0.6-0.9 범위]",
  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]
}}

🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:
- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행
- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정
- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)

🎯 **SELA 역할**:
- **전략적 분석**: 단기/중기 시장 트렌드 분석
- **리스크 관리**: 적절한 진입/청산 타이밍 결정
- **종합 판단**: InCA + 시장데이터 + 기술분석 종합
- **균형**: 보수성과 적극성의 적절한 균형 유지

⚠️ **현재 InCA 신호: {inca_action_recommendation.upper()}** - 참고하되 SELA 독립 분석으로 최종 결정

**📊 SELA 분석 체크리스트**:
1. **시장 트렌드**: 단기/중기 방향성 확인
2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단
3. **InCA 신호 검토**: 동의/반대 여부 및 근거
4. **리스크 관리**: 손실 제한 vs 수익 기회
5. **최종 결정**: none/buy/sell 중 최적 선택

**🎯 리스크/리워드 비율 1.2 이상 유지**

CRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.

START WITH {{ AND END WITH }}. NO OTHER TEXT.
"""

        return prompt

    def _create_diverse_strategies_prompt(self,
                                        symbol: str,
                                        market_data: Dict[str, Any],
                                        count: int = 5) -> str:
        """
        다양한 전략 생성 프롬프트 생성

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            count: 생성할 전략 개수

        Returns:
            str: 다양한 전략 생성 프롬프트
        """
        # 현재 시간
        current_time = datetime.fromtimestamp(int(time.time())).strftime('%Y-%m-%d %H:%M:%S')

        # 🔥 실제 시장 데이터 사용 확인 및 로깅
        logger.info(f"🔍 [{symbol}] SELA 다양한 전략 생성용 시장 데이터 확인:")
        logger.info(f"🔍 [{symbol}] - 가격: ${market_data.get('price', 'N/A')}")
        logger.info(f"🔍 [{symbol}] - 24h 변동률: {market_data.get('percent_change_24h', market_data.get('change_24h', 'N/A'))}%")
        logger.info(f"🔍 [{symbol}] - 거래량: {market_data.get('volume', market_data.get('volume_24h', 'N/A'))}")
        # 시가총액 포맷팅 (달러 단위로 표시)
        market_cap = market_data.get('market_cap', 0)
        if market_cap and market_cap > 0:
            if market_cap >= 1e9:  # 10억 이상
                market_cap_str = f"${market_cap/1e9:.2f}B"
            elif market_cap >= 1e6:  # 100만 이상
                market_cap_str = f"${market_cap/1e6:.2f}M"
            else:
                market_cap_str = f"${market_cap:,.0f}"
        else:
            market_cap_str = "N/A"

        logger.info(f"🔍 [{symbol}] - 시가총액: {market_cap_str}")

        # 시장 데이터 문자열 (실제 데이터 사용)
        price = market_data.get('price', 'N/A')
        change_24h = market_data.get('percent_change_24h', market_data.get('change_24h', 'N/A'))
        volume = market_data.get('volume', market_data.get('volume_24h', 'N/A'))
        market_cap = market_data.get('market_cap', 'N/A')

        market_str = f"""
현재 가격: {price} USD
24시간 변동률: {change_24h}%
거래량: {volume} USD
시가총액: {market_cap} USD
"""

        # 소셜 데이터 문자열 추가
        social_str = ""
        if 'social_data' in market_data:
            social_data = market_data.get('social_data', {})
            social_count = social_data.get('count', 0)
            social_titles = social_data.get('titles', [])
            avg_sentiment = social_data.get('avg_sentiment', 0)

            # 소셜 데이터 문자열 생성
            social_str = f"""
## 소셜 데이터
소셜 게시물 수: {social_count}
평균 감성 점수: {avg_sentiment:.2f} (0~5 사이, 3이 중립)
"""

            # 소셜 게시물 제목 추가 (최대 5개)
            if social_titles:
                social_str += "\n소셜 게시물 제목 예시:\n"
                for i, title in enumerate(social_titles[:5]):
                    social_str += f"{i+1}. {title}\n"
                social_str += "\n"

        # 🔥 SELA 요구사항에 맞는 최적화된 프롬프트
        # 🔥 변동률 표시 개선 (여러 필드에서 시도)
        change_24h = market_data.get('percent_change_24h')
        if change_24h is None or change_24h == 0:
            change_24h = market_data.get('change_24h')
        if change_24h is None:
            change_24h = market_data.get('priceChangePercent')

        # 변동률 포맷팅
        if change_24h is not None and change_24h != 0:
            change_str = f"{change_24h:.2f}%"
        else:
            change_str = "0.00%"

        # 🔥 현재 가격 추출 (진입가 설정용)
        current_price = market_data.get('price', 1000.0)
        if isinstance(current_price, str):
            try:
                current_price = float(current_price)
            except:
                current_price = 1000.0

        prompt = f"""Generate {count} trading strategies for {symbol}.

Market: {symbol} ${current_price} ({change_str})

IMPORTANT: Use current price ${current_price} as base for all entry_price calculations.

Required JSON format:
{{"strategies": [
  {{
    "type": "buy",
    "entry_price": {current_price},
    "stop_loss": {current_price * 0.98:.2f},
    "take_profit": {current_price * 1.04:.2f},
    "reasoning": "detailed analysis",
    "confidence": 0.8,
    "risk_level": "medium",
    "strategy_type": "trend_following",
    "key_points": ["point1", "point2"]
  }}
]}}

Create {count} different strategies with varied types (buy/sell/none), risk levels (low/medium/high), and strategy types.
All entry_price values must be based on current price ${current_price}."""

        return prompt

    def _calculate_tree_search_importance(self, strategy: Dict[str, Any], symbol: str,
                                        market_data: Dict[str, Any], reasoning_card: Dict[str, Any]) -> float:
        """
        SELA 논문 구현: Tree Search 기반 importance 계산

        Args:
            strategy: 생성된 전략 데이터
            symbol: 코인 심볼
            market_data: 시장 데이터
            reasoning_card: 사고 흐름 카드

        Returns:
            float: Tree Search 기반 importance 점수 (0.0 ~ 10.0)
        """
        try:
            logger.info(f"🌳 [{symbol}] SELA Tree Search importance 계산 시작")

            # 1. 루트 노드 생성 (현재 전략)
            root_node = MCTSNode(strategy)

            # 2. 과거 성과 데이터 로드
            historical_performance = self._get_historical_performance(symbol)

            # 3. MCTS 시뮬레이션 수행
            for simulation in range(self.mcts_simulations):
                # Selection & Expansion
                node = self._select_and_expand(root_node, symbol, market_data)

                # Simulation (전략 성과 예측)
                reward = self._simulate_strategy_performance(
                    node.strategy_data, symbol, market_data, historical_performance
                )

                # Backpropagation
                self._backpropagate(node, reward)

            # 4. UCB 점수를 importance로 변환 (0.0 ~ 10.0 스케일)
            ucb_score = root_node.get_ucb_score(self.exploration_weight)

            # UCB 점수를 0~10 범위로 정규화
            if ucb_score == float('inf'):
                importance = 8.0  # 새로운 전략에 높은 importance 부여
            else:
                # UCB 점수를 sigmoid 함수로 0~10 범위로 변환
                normalized_score = 1 / (1 + math.exp(-ucb_score))
                importance = normalized_score * 10.0

            # 5. 시장 상황 기반 조정
            importance = self._adjust_importance_by_market_conditions(
                importance, market_data, reasoning_card
            )

            # 6. 최종 importance 범위 제한 (1.0 ~ 10.0)
            importance = max(1.0, min(importance, 10.0))

            logger.info(f"🎯 [{symbol}] SELA Tree Search importance 완료: {importance:.2f}")
            logger.info(f"🎯 [{symbol}] - UCB 점수: {ucb_score}")
            logger.info(f"🎯 [{symbol}] - 방문 횟수: {root_node.visits}")
            logger.info(f"🎯 [{symbol}] - 평균 보상: {root_node.value / max(root_node.visits, 1):.4f}")

            return importance

        except Exception as e:
            logger.error(f"❌ [{symbol}] Tree Search importance 계산 중 오류: {e}")
            raise ValueError(f"SELA({symbol}) Tree Search importance 계산 실패: {e}")

    def _select_and_expand(self, root_node: MCTSNode, symbol: str, market_data: Dict[str, Any]) -> MCTSNode:
        """MCTS Selection & Expansion 단계"""
        node = root_node

        # Selection: UCB가 가장 높은 노드 선택
        while node.children and not node.is_leaf():
            best_child = max(node.children, key=lambda c: c.get_ucb_score(self.exploration_weight))
            node = best_child

        # Expansion: 새로운 자식 노드 생성 (전략 변형)
        if node.visits > 0:  # 이미 방문한 노드라면 확장
            variations = self._generate_strategy_variations(node.strategy_data, symbol, market_data)
            for variation in variations[:3]:  # 최대 3개 변형 생성
                child = node.add_child(variation)
                if child.visits == 0:  # 새로운 노드 반환
                    return child

        return node

    def _simulate_strategy_performance(self, strategy_data: Dict[str, Any], symbol: str,
                                     market_data: Dict[str, Any], historical_performance: Dict) -> float:
        """전략 성과 시뮬레이션"""
        try:
            # 1. 기본 보상 계산 (위험-보상 비율 기반)
            risk_reward = strategy_data.get('risk_reward', 1.0)
            base_reward = min(risk_reward / 3.0, 1.0)  # 정규화

            # 2. 과거 성과 기반 조정
            strategy_type = strategy_data.get('type', 'none')
            historical_success_rate = historical_performance.get(strategy_type, {}).get('success_rate', 0.5)
            performance_adjustment = (historical_success_rate - 0.5) * 0.5  # -0.25 ~ +0.25

            # 3. 시장 조건 기반 조정
            market_adjustment = self._calculate_market_alignment(strategy_data, market_data)

            # 4. 신뢰도 기반 조정
            confidence = strategy_data.get('confidence', 0.5)
            confidence_adjustment = (confidence - 0.5) * 0.3  # -0.15 ~ +0.15

            # 5. 최종 보상 계산
            total_reward = base_reward + performance_adjustment + market_adjustment + confidence_adjustment

            # 6. 노이즈 추가 (탐색 다양성)
            noise = random.uniform(-0.1, 0.1)
            total_reward += noise

            return max(-1.0, min(total_reward, 1.0))  # -1.0 ~ 1.0 범위 제한

        except Exception as e:
            logger.warning(f"전략 성과 시뮬레이션 중 오류: {e}")
            return 0.0

    def _backpropagate(self, node: MCTSNode, reward: float):
        """MCTS Backpropagation 단계"""
        while node:
            node.update(reward)
            node = node.parent

    def _get_historical_performance(self, symbol: str) -> Dict:
        """과거 성과 데이터 조회"""
        if symbol in self.historical_performance:
            return self.historical_performance[symbol]

        # 실제 구현에서는 데이터베이스에서 조회
        # 여기서는 기본값 반환
        performance_data = {
            'buy': {'success_rate': 0.6, 'avg_return': 0.02},
            'sell': {'success_rate': 0.55, 'avg_return': 0.015},
            'none': {'success_rate': 0.5, 'avg_return': 0.0}
        }

        self.historical_performance[symbol] = performance_data
        return performance_data

    def _generate_strategy_variations(self, base_strategy: Dict[str, Any], symbol: str,
                                    market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """전략 변형 생성 (Tree Search Expansion용)"""
        variations = []

        try:
            # 1. 가격 조정 변형 (±1-3%)
            for price_adj in [0.98, 0.99, 1.01, 1.02]:
                variation = base_strategy.copy()
                variation['entry_price'] = base_strategy['entry_price'] * price_adj
                variation['stop_loss'] = base_strategy['stop_loss'] * price_adj
                variation['take_profit'] = base_strategy['take_profit'] * price_adj
                variations.append(variation)

            # 2. 신뢰도 조정 변형
            for conf_adj in [0.9, 1.1]:
                variation = base_strategy.copy()
                variation['confidence'] = min(1.0, base_strategy['confidence'] * conf_adj)
                variations.append(variation)

        except Exception as e:
            logger.warning(f"전략 변형 생성 중 오류: {e}")

        return variations[:5]  # 최대 5개 변형 반환

    def _analyze_hourly_patterns(self, candles: list, close_prices: list) -> dict:
        """🔧 1시간봉 고급 패턴 분석"""
        try:
            if len(close_prices) < 3:
                return {"signal": "neutral", "description": "데이터 부족", "strength_multiplier": 1.0}

            # 더블 바텀/탑 패턴 감지 (1시간봉용)
            if len(close_prices) >= 7:
                # 최근 7개 캔들에서 W 또는 M 패턴 찾기
                lows = [min(close_prices[i:i+2]) for i in range(len(close_prices)-1)]
                highs = [max(close_prices[i:i+2]) for i in range(len(close_prices)-1)]

                # 더블 바텀 패턴 (W 형태)
                if len(lows) >= 5:
                    recent_lows = lows[-5:]
                    if (recent_lows[0] < recent_lows[1] and recent_lows[1] > recent_lows[2] and
                        recent_lows[2] < recent_lows[3] and recent_lows[3] > recent_lows[4] and
                        abs(recent_lows[0] - recent_lows[2]) / recent_lows[0] < 0.02):  # 2% 이내 유사
                        return {
                            "signal": "bullish",
                            "description": "더블 바텀 패턴 (강한 상승 신호)",
                            "strength_multiplier": 2.5
                        }

                # 더블 탑 패턴 (M 형태)
                if len(highs) >= 5:
                    recent_highs = highs[-5:]
                    if (recent_highs[0] > recent_highs[1] and recent_highs[1] < recent_highs[2] and
                        recent_highs[2] > recent_highs[3] and recent_highs[3] < recent_highs[4] and
                        abs(recent_highs[0] - recent_highs[2]) / recent_highs[0] < 0.02):  # 2% 이내 유사
                        return {
                            "signal": "bearish",
                            "description": "더블 탑 패턴 (강한 하락 신호)",
                            "strength_multiplier": 2.5
                        }

            # 상승/하락 웨지 패턴
            if len(close_prices) >= 5:
                # 최근 5개 캔들의 고점과 저점 추세
                recent_highs = close_prices[-5:]
                recent_lows = close_prices[-5:]

                # 상승 웨지 (고점은 상승, 저점도 상승하지만 더 가파름)
                high_trend = (recent_highs[-1] - recent_highs[0]) / recent_highs[0] * 100
                low_trend = (recent_lows[-1] - recent_lows[0]) / recent_lows[0] * 100

                if high_trend > 1 and low_trend > high_trend * 1.5:
                    return {
                        "signal": "bearish",
                        "description": f"상승 웨지 패턴 (조정 예상, 고점 +{high_trend:.1f}%, 저점 +{low_trend:.1f}%)",
                        "strength_multiplier": 1.8
                    }
                elif high_trend < -1 and low_trend < high_trend * 1.5:
                    return {
                        "signal": "bullish",
                        "description": f"하락 웨지 패턴 (반등 예상, 고점 {high_trend:.1f}%, 저점 {low_trend:.1f}%)",
                        "strength_multiplier": 1.8
                    }

            # 연속 상승/하락 패턴 (1시간봉용)
            consecutive_up = 0
            consecutive_down = 0
            for i in range(1, len(close_prices)):
                if close_prices[i] > close_prices[i-1]:
                    consecutive_up += 1
                    consecutive_down = 0
                elif close_prices[i] < close_prices[i-1]:
                    consecutive_down += 1
                    consecutive_up = 0

            if consecutive_up >= 4:
                return {
                    "signal": "bullish",
                    "description": f"연속 상승 패턴 ({consecutive_up}시간)",
                    "strength_multiplier": 1.6
                }
            elif consecutive_down >= 4:
                return {
                    "signal": "bearish",
                    "description": f"연속 하락 패턴 ({consecutive_down}시간)",
                    "strength_multiplier": 1.6
                }

            return {"signal": "neutral", "description": "명확한 패턴 없음", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"1시간봉 패턴 분석 실패: {e}")
            return {"signal": "neutral", "description": "분석 실패", "strength_multiplier": 1.0}

    def _analyze_hourly_volume(self, candles: list, volumes: list) -> dict:
        """🔧 1시간봉 볼륨 분석"""
        try:
            if len(volumes) < 3:
                return {"signal": "neutral", "description": "볼륨 데이터 부족", "strength_multiplier": 1.0}

            # 최근 볼륨과 평균 볼륨 비교
            recent_volume = volumes[-1]
            avg_volume = sum(volumes[:-1]) / len(volumes[:-1])
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

            # 가격 변화 계산
            price_changes = []
            for candle in candles[-3:]:
                if isinstance(candle, list) and len(candle) >= 5:
                    open_price = float(candle[1])
                    close_price = float(candle[4])
                    price_change = (close_price - open_price) / open_price * 100 if open_price > 0 else 0
                    price_changes.append(price_change)

            recent_price_change = price_changes[-1] if price_changes else 0

            # 볼륨 급증 분석 (1시간봉용)
            if volume_ratio > 3.0 and recent_price_change > 0.5:
                return {
                    "signal": "bullish",
                    "description": f"대량 매수 ({volume_ratio:.1f}배 볼륨, +{recent_price_change:.2f}%)",
                    "strength_multiplier": 2.2
                }
            elif volume_ratio > 3.0 and recent_price_change < -0.5:
                return {
                    "signal": "bearish",
                    "description": f"대량 매도 ({volume_ratio:.1f}배 볼륨, {recent_price_change:.2f}%)",
                    "strength_multiplier": 2.2
                }
            elif volume_ratio > 2.0 and recent_price_change > 0.2:
                return {
                    "signal": "bullish",
                    "description": f"볼륨 증가 상승 ({volume_ratio:.1f}배)",
                    "strength_multiplier": 1.5
                }
            elif volume_ratio < 0.5 and abs(recent_price_change) > 0.5:
                return {
                    "signal": "neutral",
                    "description": f"저볼륨 움직임 (신뢰도 낮음)",
                    "strength_multiplier": 0.7
                }

            return {"signal": "neutral", "description": "일반적인 볼륨 패턴", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"1시간봉 볼륨 분석 실패: {e}")
            return {"signal": "neutral", "description": "볼륨 분석 실패", "strength_multiplier": 1.0}

    def _analyze_hourly_trendlines(self, close_prices: list, high_prices: list, low_prices: list) -> dict:
        """🔧 1시간봉 추세선 분석"""
        try:
            if len(close_prices) < 4:
                return {"signal": "neutral", "description": "추세선 분석 데이터 부족", "strength_multiplier": 1.0}

            current_price = close_prices[-1]
            prev_price = close_prices[-2]
            price_change = (current_price - prev_price) / prev_price * 100

            # 주요 지지/저항선 계산 (1시간봉용)
            recent_high = max(high_prices[-6:]) if len(high_prices) >= 6 else max(close_prices[-6:])
            recent_low = min(low_prices[-6:]) if len(low_prices) >= 6 else min(close_prices[-6:])

            # 이전 주요 레벨
            prev_high = max(high_prices[:-6]) if len(high_prices) > 6 else recent_high
            prev_low = min(low_prices[:-6]) if len(low_prices) > 6 else recent_low

            # 주요 저항선 돌파 (1시간봉용)
            if current_price > recent_high and price_change > 1.0:
                return {
                    "signal": "bullish",
                    "description": f"주요 저항선 돌파 (${recent_high:.2f} → ${current_price:.2f})",
                    "strength_multiplier": 2.0
                }

            # 주요 지지선 이탈 (1시간봉용)
            if current_price < recent_low and price_change < -1.0:
                return {
                    "signal": "bearish",
                    "description": f"주요 지지선 이탈 (${recent_low:.2f} → ${current_price:.2f})",
                    "strength_multiplier": 2.0
                }

            # 상승 추세선 형성 (1시간봉용)
            if len(close_prices) >= 8:
                # 최근 8시간의 저점들이 상승 추세
                lows = [min(close_prices[i:i+2]) for i in range(len(close_prices)-7, len(close_prices)-1)]
                if len(lows) >= 4 and all(lows[i] <= lows[i+1] for i in range(len(lows)-1)):
                    return {
                        "signal": "bullish",
                        "description": "상승 추세선 형성 (고점 갱신 중)",
                        "strength_multiplier": 1.7
                    }

                # 하락 추세선 형성
                highs = [max(close_prices[i:i+2]) for i in range(len(close_prices)-7, len(close_prices)-1)]
                if len(highs) >= 4 and all(highs[i] >= highs[i+1] for i in range(len(highs)-1)):
                    return {
                        "signal": "bearish",
                        "description": "하락 추세선 형성 (저점 갱신 중)",
                        "strength_multiplier": 1.7
                    }

            return {"signal": "neutral", "description": "명확한 추세선 없음", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"1시간봉 추세선 분석 실패: {e}")
            return {"signal": "neutral", "description": "추세선 분석 실패", "strength_multiplier": 1.0}

    def _calculate_market_alignment(self, strategy_data: Dict[str, Any], market_data: Dict[str, Any]) -> float:
        """전략과 시장 상황의 일치도 계산"""
        try:
            strategy_type = strategy_data.get('type', 'none')
            price_change_24h = market_data.get('percent_change_24h', 0.0)

            # 전략 방향과 시장 트렌드 일치도
            if strategy_type == 'buy' and price_change_24h > 0:
                return 0.2  # 상승 시장에서 매수 전략
            elif strategy_type == 'sell' and price_change_24h < 0:
                return 0.2  # 하락 시장에서 매도 전략
            elif strategy_type == 'none':
                return 0.0  # 중립 전략
            else:
                return -0.1  # 시장과 반대 방향

        except Exception:
            return 0.0

    def _adjust_importance_by_market_conditions(self, base_importance: float,
                                              market_data: Dict[str, Any],
                                              reasoning_card: Dict[str, Any]) -> float:
        """시장 상황에 따른 importance 조정"""
        try:
            adjusted_importance = base_importance

            # 1. 변동성 기반 조정
            price_change = abs(market_data.get('percent_change_24h', 0.0))
            if price_change > 5.0:  # 높은 변동성
                adjusted_importance *= 1.2
            elif price_change < 1.0:  # 낮은 변동성
                adjusted_importance *= 0.9

            # 2. 사고 카드 임팩트 기반 조정
            impact = reasoning_card.get('impact', 50)
            if impact > 70:  # 높은 임팩트
                adjusted_importance *= 1.1
            elif impact < 30:  # 낮은 임팩트
                adjusted_importance *= 0.95

            # 3. 신뢰도 기반 조정
            confidence = reasoning_card.get('confidence', 0.5)
            if confidence > 0.8:  # 높은 신뢰도
                adjusted_importance *= 1.05
            elif confidence < 0.3:  # 낮은 신뢰도
                adjusted_importance *= 0.9

            return adjusted_importance

        except Exception as e:
            logger.warning(f"시장 조건 기반 importance 조정 중 오류: {e}")
            return base_importance

    def _calculate_simplified_importance(self, strategy: Dict[str, Any], symbol: str,
                                       market_data: Dict[str, Any]) -> float:
        """
        간소화된 importance 계산 (다양한 전략 생성용)
        전체 Tree Search 대신 빠른 휴리스틱 기반 계산
        """
        try:
            # 1. 기본 importance (신뢰도 기반)
            confidence = strategy.get('confidence', 0.5)
            base_importance = 3.0 + (confidence * 4.0)  # 3.0 ~ 7.0

            # 2. 위험-보상 비율 기반 조정
            risk_reward = strategy.get('risk_reward', 1.0)
            if risk_reward > 2.0:
                base_importance += 1.0
            elif risk_reward > 1.5:
                base_importance += 0.5
            elif risk_reward < 1.0:
                base_importance -= 0.5

            # 3. 시장 조건 기반 조정
            strategy_type = strategy.get('type', 'none')
            price_change_24h = market_data.get('percent_change_24h', 0.0)

            if strategy_type == 'buy' and price_change_24h > 2.0:
                base_importance += 0.5  # 상승 시장에서 매수
            elif strategy_type == 'sell' and price_change_24h < -2.0:
                base_importance += 0.5  # 하락 시장에서 매도
            elif strategy_type == 'none':
                base_importance -= 0.3  # 관망 전략은 낮은 importance

            # 4. 변동성 기반 조정
            volatility = abs(price_change_24h)
            if volatility > 5.0:
                base_importance += 0.3  # 높은 변동성에서 기회

            # 5. 최종 범위 제한
            importance = max(1.0, min(base_importance, 10.0))

            logger.debug(f"🎯 [{symbol}] 간소화된 importance: {importance:.2f} (신뢰도: {confidence:.2f})")

            return importance

        except Exception as e:
            logger.error(f"간소화된 importance 계산 중 오류: {e}")
            raise ValueError(f"SELA({symbol}) 간소화된 importance 계산 실패: {e}")

    def generate_execution_strategy(self, symbol: str, market_data: Dict[str, Any],
                                  reasoning_card: Dict[str, Any]) -> Dict[str, Any]:
        """
        실행 플로우용 전략 생성 (generate_strategy와 동일)

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            reasoning_card: 사고 흐름 카드

        Returns:
            Dict[str, Any]: 생성된 전략 (Tree Search importance 포함)
        """
        logger.info(f"🚀 [{symbol}] SELA Tree Search 기반 실행 전략 생성 시작")
        return self.generate_strategy(symbol, market_data, reasoning_card, None)

    def generate_learning_strategies(self, symbol: str, count: int = 5,
                                   external_market_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        학습 루프용 다양한 전략 생성 (generate_diverse_strategies와 동일)

        Args:
            symbol: 코인 심볼
            count: 생성할 전략 수
            external_market_data: 외부에서 전달된 실제 시장 데이터 (우선 사용)

        Returns:
            List[Dict[str, Any]]: 생성된 전략 목록 (간소화된 importance 포함)
        """
        logger.info(f"🚀 [{symbol}] SELA Tree Search 기반 학습 전략 {count}개 생성 시작")
        return self.generate_diverse_strategies(symbol, count, external_market_data)

    def _is_paper_trading_mode(self) -> bool:
        """
        현재 거래 모드가 가상 거래인지 확인 (단기 거래 우선 - 실제 거래 모드)

        Returns:
            bool: True if paper trading, False if real trading
        """
        import os

        # 🚀 단기 거래 우선: 환경 변수 기반 거래 모드 설정
        trading_mode = os.getenv('TRADING_MODE', 'real')  # 기본값을 'real'로 설정
        testnet = os.getenv('TESTNET', 'false').lower() == 'true'  # 기본값을 'false'로 변경

        # real 모드이고 testnet이 false인 경우만 실제 거래
        is_real_trading = (trading_mode == 'real' and not testnet)

        return not is_real_trading

    def _calculate_risk_reward(self, strategy: Dict[str, Any]) -> float:
        """
        위험-보상 비율 계산

        Args:
            strategy: 전략 데이터

        Returns:
            float: 위험-보상 비율
        """
        strategy_type = strategy.get("type", "none")
        entry_price = strategy.get("entry_price", 0.0)
        stop_loss = strategy.get("stop_loss", 0.0)
        take_profit = strategy.get("take_profit", 0.0)

        # 기본값
        risk_reward = 0.0

        if strategy_type != "none" and entry_price > 0:
            if strategy_type == "buy" and take_profit > entry_price and stop_loss < entry_price:
                risk = entry_price - stop_loss
                reward = take_profit - entry_price
                risk_reward = reward / risk if risk > 0 else 0.0
            elif strategy_type == "sell" and take_profit < entry_price and stop_loss > entry_price:
                risk = stop_loss - entry_price
                reward = entry_price - take_profit
                risk_reward = reward / risk if risk > 0 else 0.0

        return risk_reward

    def _get_latest_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        최신 시장 데이터 조회 (임시 구현)

        실제 구현에서는 데이터 스토어에서 조회해야 함

        Args:
            symbol: 코인 심볼

        Returns:
            Dict[str, Any]: 시장 데이터
        """
        # 임시 구현 (실제로는 데이터 스토어에서 조회)
        # TODO: 데이터 스토어 통합 시 이 부분 수정

        # 데이터 스토어가 있는 경우 실제 데이터 사용
        if hasattr(self, 'data_store') and self.data_store is not None:
            try:
                # 통합 데이터 조회 시도
                if hasattr(self.data_store, 'get_latest_combined_data'):
                    combined_data = self.data_store.get_latest_combined_data(symbol)
                    if combined_data and isinstance(combined_data, dict) and "price" in combined_data:
                        # 통합 데이터에서 시장 데이터 추출
                        return {
                            "price": combined_data.get("price", 0.0),
                            "volume": combined_data.get("volume", 0.0),
                            "percent_change_24h": 0.0,  # 변화율은 계산해야 함
                            "timestamp": combined_data.get("timestamp", int(time.time())),
                            "social_data": {
                                "count": combined_data.get("social_count", 0),
                                "titles": combined_data.get("social_titles", []),
                                "avg_sentiment": combined_data.get("avg_sentiment", 0.0)
                            }
                        }

                # 타임시리즈 데이터 조회 시도
                if hasattr(self.data_store, 'get_latest_timeseries_data'):
                    timeseries_data = self.data_store.get_latest_timeseries_data(symbol)
                    if timeseries_data and isinstance(timeseries_data, dict):
                        # 타임시리즈 데이터에서 시장 데이터 추출
                        return {
                            "price": timeseries_data.get("close", 0.0),
                            "volume": timeseries_data.get("volume", 0.0),
                            "percent_change_24h": timeseries_data.get("change_24h", 0.0),
                            "timestamp": timeseries_data.get("timestamp", int(time.time()))
                        }
            except Exception as e:
                logger.warning(f"{symbol} 데이터 스토어에서 데이터 조회 실패: {e}")

        # 데이터 스토어가 없거나 조회 실패 시 심볼별 적절한 기본값 사용
        symbol_defaults = {
            "BTC": {"price": 45000.0, "market_cap": 850000000000.0},
            "ETH": {"price": 2500.0, "market_cap": 300000000000.0},
            "SOL": {"price": 150.0, "market_cap": 70000000000.0},
            "DOGE": {"price": 0.18, "market_cap": 25000000000.0},
            "BNB": {"price": 600.0, "market_cap": 90000000000.0}
        }

        defaults = symbol_defaults.get(symbol, {"price": 1000.0, "market_cap": 10000000000.0})

        return {
            "price": defaults["price"],
            "volume": 1000000000.0,  # 기본 거래량
            "market_cap": defaults["market_cap"],
            "percent_change_24h": 0.0,  # 중립적 변화율
            "timestamp": int(time.time()),
            "social_data": {
                "count": 10,
                "titles": [
                    "Bitcoin shows strong momentum as institutional interest grows",
                    "Analysts predict Bitcoin to reach new highs this quarter",
                    "Market sentiment turns positive for major cryptocurrencies"
                ],
                "avg_sentiment": 3.8
            }
        }


# 테스트 코드
if __name__ == "__main__":
    # 로깅 설정
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

    # Mock LLM 모델
    class MockLLM:
        def generate(self, prompt):
            if "다양한" in prompt:
                return '''
```json
{
  "strategies": [
    {
      "type": "buy",
      "entry_price": 50000.0,
      "stop_loss": 49000.0,
      "take_profit": 52000.0,
      "reasoning": "상승 추세와 거래량 증가",
      "confidence": 0.8,
      "risk_level": "medium",
      "strategy_type": "trend-following",
      "key_points": ["상승 추세", "거래량 증가"]
    },
    {
      "type": "sell",
      "entry_price": 50000.0,
      "stop_loss": 51000.0,
      "take_profit": 48000.0,
      "reasoning": "기술적 지표 과매수",
      "confidence": 0.7,
      "risk_level": "medium",
      "strategy_type": "counter-trend",
      "key_points": ["과매수 지표", "단기 조정 예상"]
    }
  ]
}
```
'''
            else:
                return '''
```json
{
  "type": "buy",
  "entry_price": 50000.0,
  "stop_loss": 49000.0,
  "take_profit": 52000.0,
  "reasoning": "시장의 상승 추세와 긍정적인 뉴스 영향으로 추가 상승이 예상됩니다.",
  "confidence": 0.8,
  "key_points": [
    "상승 추세 확인",
    "거래량 증가",
    "긍정적인 뉴스 영향"
  ]
}
```
'''

    # 테스트 데이터
    market_data = {
        "price": 50000.0,
        "volume": 10000000000.0,
        "market_cap": 950000000000.0,
        "percent_change_24h": 2.5,
        "timestamp": int(time.time())
    }

    reasoning_card = {
        "card_id": "test-card-id",
        "context": "비트코인이 상승세",
        "observations": "가격 상승, 거래량 증가",
        "analysis": "상승 추세 확인됨",
        "conclusion": "추가 상승 가능성 높음",
        "action": "buy",
        "confidence": 0.8,
        "key_insights": ["상승 추세", "거래량 증가", "긍정적 뉴스"]
    }

    # 테스트 실행
    try:
        # 전략 생성기 생성
        generator = SELAStrategyGenerator(MockLLM(), risk_level="medium")

        # 단일 전략 생성 테스트
        strategy = generator.generate_strategy("BTC", market_data, reasoning_card, None)
        print("\n단일 전략:")
        print(json.dumps(strategy, indent=2, ensure_ascii=False))

        # 다양한 전략 생성 테스트
        diverse_strategies = generator.generate_diverse_strategies("BTC", 2)
        print("\n다양한 전략:")
        for i, strategy in enumerate(diverse_strategies):
            print(f"\n전략 {i+1}:")
            print(json.dumps(strategy, indent=2, ensure_ascii=False))


    except Exception as e:
        logger.error(f"테스트 중 오류 발생: {e}")
