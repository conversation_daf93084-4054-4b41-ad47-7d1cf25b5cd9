{"execution_id": "exec_3dd6cebd_1750954155", "symbol": "DOGE", "timestamp": 1750954155, "datetime": "2025-06-27 01:09:15", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "a75a9c4f-70c9-4302-976b-1d59424da184", "timestamp": 1750954155, "market_data": {"id": "market_DOGE_1750954136", "symbol": "DOGE", "timestamp": 1750954136, "datetime": "2025-06-27 01:08:56", "date": "2025-06-27", "time": "01:08:56", "price": 0.1596, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15956, "volume": 5167587147.0, "volume_24h": 5167587147.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -2.458, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8617271, "social_dominance": 0.803, "social_contributors": 86172, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750954136, "news_count": 47, "ema_7": 0.15990285714285715, "ema_14": 0.15969499999999998, "ema_25": 0.15987839999999998, "ema_50": 0.15961480000000003, "ema_99": 0.1602179797979798, "ema_200": 0.16204545000000004, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.041914893617021, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23958731737.46, "recent_news_titles": ["Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing", "TSMC to Inject $10 Billion Capital in Unit to Counter FX Swings"], "top_social_posts": [{"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "🚨$470,000,000.00 saved!  Thank you DOGE!", "sentiment": 3.21, "platform": "tweet"}, {"text": "DOGE just terminated 312 government contracts with a ceiling value of $2.8B and savings of $470M...in just the last week\n\nNotably a $286k professional and management development contract for an “entrepreneurship course at Harvard University”and $485k USAID contract for a “senior general development advisor at USAID Madagascar.\"", "sentiment": 3.17, "platform": "tweet"}, {"text": "Young woman doesn't believe civilians should have firearms and wants the 2nd amendment removed from the constitution.  \n\n", "sentiment": 3.09, "platform": "tweet"}, {"text": "Dr. <PERSON><PERSON> went to buy champagne in anticipation of <PERSON><PERSON>’s win and laughed at the guy who checked her out when he said the race was close.\n\nWhat’s she up to these days?\n\n", "sentiment": 3.32, "platform": "tweet"}, {"text": "Will you be able to resist the temptation OR you HODL BabyDoge and never let go?", "sentiment": 3, "platform": "tweet"}, {"text": "Which #memecoin is printing money today? Shill me the next $DOGE 👀", "sentiment": 3, "platform": "tweet"}, {"text": "Closed $NPC. But holding wouldn't hurt either. Some memes are gonna take a hit early next week as dog<PERSON> is gonna take a hit. <PERSON><PERSON> of Tesla", "sentiment": 2.88, "platform": "tweet"}, {"text": "How it feels to be 27", "sentiment": 3.14, "platform": "tweet"}, {"text": "@DOGE Hey @<PERSON><PERSON><PERSON><PERSON> are you going to codify these DOGE cuts?\n\nOr are you gonna just take more vacation and do nothing at all for the American people???", "sentiment": 2.94, "platform": "tweet"}], "recent_candles": [[1750952940000, "0.159900", "0.159970", "0.159850", "0.159950", "2050364", 1750952999999, "327843.311230", 729, "796429", "127360.946980", "0"], [1750953000000, "0.159940", "0.159980", "0.159810", "0.159900", "1947652", 1750953059999, "311399.804630", 986, "1235228", "197493.826230", "0"], [1750953060000, "0.159900", "0.160010", "0.159850", "0.159910", "3325987", 1750953119999, "531951.937030", 891, "2242459", "358641.649790", "0"], [1750953120000, "0.159900", "0.159960", "0.159830", "0.159930", "1412306", 1750953179999, "225836.690460", 697, "684781", "109492.919500", "0"], [1750953180000, "0.159930", "0.160070", "0.159920", "0.160030", "1751442", 1750953239999, "280240.691900", 640, "1336250", "213818.022600", "0"], [1750953240000, "0.160040", "0.160130", "0.160000", "0.160030", "1698828", 1750953299999, "271950.745660", 733, "1094269", "175178.961190", "0"], [1750953300000, "0.160040", "0.160050", "0.159850", "0.159920", "1270780", 1750953359999, "203306.284360", 544, "661561", "105857.092230", "0"], [1750953360000, "0.159920", "0.160130", "0.159890", "0.160080", "1630183", 1750953419999, "260857.200030", 1360, "844640", "135164.493510", "0"], [1750953420000, "0.160080", "0.160170", "0.160060", "0.160170", "1200654", 1750953479999, "192247.644690", 519, "678993", "108726.434940", "0"], [1750953480000, "0.160160", "0.160160", "0.159950", "0.159950", "1932449", 1750953539999, "309343.449600", 785, "464775", "74409.359450", "0"], [1750953540000, "0.159960", "0.160200", "0.159960", "0.160010", "3321028", 1750953599999, "531624.872160", 1036, "1593754", "255166.718210", "0"], [1750953600000, "0.160010", "0.160100", "0.159970", "0.159980", "2887896", 1750953659999, "462154.929550", 1895, "1522312", "243621.765290", "0"], [1750953660000, "0.159980", "0.160160", "0.159980", "0.160040", "3657230", 1750953719999, "585385.085290", 1321, "2412955", "386238.158890", "0"], [1750953720000, "0.160050", "0.160100", "0.159980", "0.160020", "5724962", 1750953779999, "916096.980220", 931, "4331570", "693092.405270", "0"], [1750953780000, "0.160020", "0.160080", "0.159870", "0.159940", "2942007", 1750953839999, "470714.460460", 879, "1109330", "177523.122200", "0"], [1750953840000, "0.159950", "0.160020", "0.159880", "0.159990", "1342655", 1750953899999, "214745.369520", 660, "314482", "50299.752440", "0"], [1750953900000, "0.159990", "0.159990", "0.159640", "0.159720", "3287281", 1750953959999, "525186.422910", 929, "1202789", "192121.761670", "0"], [1750953960000, "0.159720", "0.159720", "0.159520", "0.159530", "3777784", 1750954019999, "602950.839980", 1415, "435852", "69571.133270", "0"], [1750954020000, "0.159540", "0.159590", "0.159420", "0.159580", "4451635", 1750954079999, "709993.416890", 1107, "2104565", "335704.909860", "0"], [1750954080000, "0.159570", "0.159610", "0.159490", "0.159560", "1403164", 1750954139999, "223862.456150", 759, "716805", "114356.726080", "0"]], "candles_count": 20, "data_timestamp": 1750954136, "has_timeseries_data": true, "short_term_change_pct": -0.2687667979248615, "recent_high": 0.16002, "recent_low": 0.15942}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "neutral", "reasoning": "Short-term candle data shows a neutral trend with mixed price movements and low strength. No clear technical patterns or breakouts observed, and volume does not confirm a strong direction. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral trend", "no breakout", "low volume confirmation"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term candle data shows a neutral trend with mixed price movements and low strengt...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_95278375_1750954143"}, "strategy": {"strategy_id": "a75a9c4f-70c9-4302-976b-1d59424da184", "symbol": "DOGE", "timestamp": 1750954155, "type": "none", "entry_price": 0.1596, "stop_loss": 0.1564, "take_profit": 0.1558, "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. 볼린저 밴드는 가격이 중간 수준에 머물고 있으며, MACD는 상승 및 하락 신호가 혼재되어 있다. InCA의 HOLD 신호와 일치하여 단기적인 방향성이 명확하지 않다.", "confidence": 0.72, "reasoning_card_id": "card_95278375_1750954143", "risk_level": "medium", "key_points": ["RSI는 중립 영역에 머물고 있음", "볼린저 밴드는 가격이 중간 수준에 머물고 있음", "MACD는 혼합 신호 발생"], "market_context": {"price": 0.1596, "percent_change_24h": -2.458, "timestamp": 1750954136}, "paper_based": false, "risk_reward": 0.0, "importance": 8.124738006121229, "consensus_signal": "none", "consensus_confidence": 0.72, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.5, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.72, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.72, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_3dd6cebd_1750954155"}}