{"execution_id": "exec_cedccde4_1750932840", "symbol": "DOGE", "timestamp": 1750932840, "datetime": "2025-06-26 19:14:00", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "87e62c8c-150f-4788-bcb8-a2d60d3cdee7", "timestamp": 1750932840, "market_data": {"id": "market_DOGE_1750932821", "symbol": "DOGE", "timestamp": 1750932821, "datetime": "2025-06-26 19:13:41", "date": "2025-06-26", "time": "19:13:41", "price": 0.16247, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.16248, "volume": 4992126777.0, "volume_24h": 4992126777.0, "high_24h": 0.16809, "low_24h": 0.16202, "percent_change_24h": -1.384, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8688997, "social_dominance": 0.661, "social_contributors": 86889, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750932821, "news_count": 38, "ema_7": 0.1625242857142857, "ema_14": 0.16258142857142857, "ema_25": 0.16292240000000002, "ema_50": 0.1638332, "ema_99": 0.16420353535353538, "ema_200": 0.1647789499999999, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0207894736842102, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 24396097120.5, "recent_news_titles": ["<PERSON> Threatens to Double Spanish Tariffs Over NATO Spending", "DOGE Price Analysis: Dogecoin Bulls Defend 16 Cent Support Amid <PERSON> Musk’s X Payments Speculation", "DOGE Price News: <PERSON><PERSON><PERSON><PERSON> Higher as Bulls Break 16 Cent Resistance", "Is DOGE doomed to fail? Some experts are ready to call it. &#x2d; Ars Technica", "DOGE layoffs are starting to leave their mark on D.C.’s housing market"], "top_social_posts": [{"text": "Permission for hugs?", "sentiment": 3.5, "platform": "tweet"}, {"text": "🎯", "sentiment": 3, "platform": "tweet"}, {"text": "@elonmusk Off you go🔥", "sentiment": 3, "platform": "tweet"}, {"text": "Nobody cares.", "sentiment": 3.57, "platform": "tweet"}, {"text": "Crowd caused chaos in Santa Ana by getting in the way of police during an arrest.\n\n", "sentiment": 2.52, "platform": "tweet"}, {"text": "An illegal from Iran was arrested in Los Angeles. A bystander yelled at ICE officers during the arrest, and then a woman, said to be Iranian ran out upset.\n\n", "sentiment": 2.47, "platform": "tweet"}, {"text": "‘Liver King’ shows ankle monitor following arrest over threats to <PERSON>.\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "ICE agents caught and arrested an illegal person at a Home Depot in Huntington Park, CA.\n\nThe guy recording and yelling should be arrested too..\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "Friends help make sure his wheelchair doesn’t get in the way of a great wedding.\n\n", "sentiment": 3.6, "platform": "tweet"}, {"text": "Flashback: Maryland parents express worry over <PERSON>'s move to stop \"gender-affirming care\" for minors.\n\n", "sentiment": 2.79, "platform": "tweet"}], "recent_candles": [[1750931640000, "0.162440", "0.162480", "0.162310", "0.162380", "2744907", 1750931699999, "445728.015060", 686, "1586669", "257612.844420", "0"], [1750931700000, "0.162380", "0.162490", "0.162380", "0.162380", "1770825", 1750931759999, "287637.766320", 662, "1215235", "197397.035120", "0"], [1750931760000, "0.162380", "0.162400", "0.162310", "0.162380", "1838441", 1750931819999, "298480.871440", 727, "814965", "132318.557180", "0"], [1750931820000, "0.162380", "0.162490", "0.162300", "0.162380", "3314817", 1750931879999, "538166.247350", 858, "750493", "121903.864580", "0"], [1750931880000, "0.162390", "0.162390", "0.162310", "0.162320", "1819421", 1750931939999, "295414.517620", 485, "742261", "120525.336290", "0"], [1750931940000, "0.162310", "0.162450", "0.162310", "0.162360", "1198486", 1750931999999, "194606.972710", 833, "631695", "102577.083760", "0"], [1750932000000, "0.162370", "0.162470", "0.162310", "0.162460", "3331613", 1750932059999, "541109.215300", 1127, "1633560", "265316.236460", "0"], [1750932060000, "0.162450", "0.162480", "0.162360", "0.162370", "2708847", 1750932119999, "439993.244030", 884, "1390961", "225936.979920", "0"], [1750932120000, "0.162370", "0.162460", "0.162330", "0.162380", "6816538", 1750932179999, "1106986.601260", 1428, "4196332", "681495.689390", "0"], [1750932180000, "0.162390", "0.162420", "0.162020", "0.162330", "21605328", 1750932239999, "3504645.603630", 2730, "10324926", "1675031.926610", "0"], [1750932240000, "0.162330", "0.162530", "0.162260", "0.162530", "4285842", 1750932299999, "696070.304430", 1412, "2784994", "452306.620690", "0"], [1750932300000, "0.162530", "0.162790", "0.162520", "0.162670", "12224472", 1750932359999, "1988779.562210", 1990, "9194071", "1495778.039750", "0"], [1750932360000, "0.162660", "0.162810", "0.162660", "0.162790", "4775747", 1750932419999, "777081.821510", 1281, "2892153", "470595.285110", "0"], [1750932420000, "0.162780", "0.162870", "0.162710", "0.162770", "7659873", 1750932479999, "1247119.196920", 1440, "3081541", "501745.832500", "0"], [1750932480000, "0.162760", "0.162770", "0.162680", "0.162720", "2317171", 1750932539999, "377039.848710", 669, "1423279", "231579.483620", "0"], [1750932540000, "0.162730", "0.162830", "0.162730", "0.162780", "6416364", 1750932599999, "1044529.092470", 1252, "4394926", "715457.064460", "0"], [1750932600000, "0.162770", "0.162820", "0.162700", "0.162710", "3890568", 1750932659999, "633244.892670", 827, "2112400", "343834.435610", "0"], [1750932660000, "0.162700", "0.162790", "0.162680", "0.162680", "1961267", 1750932719999, "319155.646900", 748, "1301256", "211758.582350", "0"], [1750932720000, "0.162690", "0.162690", "0.162500", "0.162520", "2403444", 1750932779999, "390704.972390", 826, "1073214", "174453.026280", "0"], [1750932780000, "0.162510", "0.162510", "0.162450", "0.162480", "1605462", 1750932839999, "260842.980030", 471, "554290", "90055.120880", "0"]], "candles_count": 20, "data_timestamp": 1750932821, "has_timeseries_data": true, "short_term_change_pct": -0.18429782528565836, "recent_high": 0.16283, "recent_low": 0.16245}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "Short-term bearish trend confirmed by 5 consecutive bearish candles with volume confirmation. Continuous decline in price with strong downward momentum indicates immediate sell pressure. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish trend", "volume confirmation", "downward momentum"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term bearish trend confirmed by 5 consecutive bearish candles with volume confirm...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_58be7a53_1750932828"}, "strategy": {"strategy_id": "87e62c8c-150f-4788-bcb8-a2d60d3cdee7", "symbol": "DOGE", "timestamp": 1750932839, "type": "sell", "entry_price": 0.16247, "stop_loss": 0.1645, "take_profit": 0.1574, "reasoning": "InCA 신호는 SELL을 권장하며, 현재 가격이 0.16247에 위치하고 있다. 기술적 지표 분석에 따르면 RSI는 과열 상태에 있으며, MACD는 하향 추세를 보이고 있다. 볼린저 밴드는 상단을 돌파한 후 하락세로 전환되었으며, 이는 단기적인 하락 가능성을 시사한다. 이러한 모든 요소를 종합하여 SELL 전략을 추천한다.", "confidence": 0.82, "reasoning_card_id": "card_58be7a53_1750932828", "risk_level": "medium", "key_points": ["InCA 신호는 SELL을 권장", "RSI 과열 상태", "MACD 하향 추세", "볼린저 밴드 상단 돌파 후 하락세"], "market_context": {"price": 0.16247, "percent_change_24h": -1.384, "timestamp": 1750932821}, "paper_based": false, "risk_reward": 2.****************, "importance": 9.168273035060777, "consensus_signal": "sell", "consensus_confidence": 0.82, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.82, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.82, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_cedccde4_1750932840"}}