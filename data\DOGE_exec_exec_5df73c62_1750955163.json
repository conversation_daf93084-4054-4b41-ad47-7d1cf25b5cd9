{"execution_id": "exec_5df73c62_1750955163", "symbol": "DOGE", "timestamp": 1750955163, "datetime": "2025-06-27 01:26:03", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "3d946dd3-402b-43b9-a4b5-4bb91a660169", "timestamp": 1750955163, "market_data": {"id": "market_DOGE_1750955145", "symbol": "DOGE", "timestamp": 1750955145, "datetime": "2025-06-27 01:25:45", "date": "2025-06-27", "time": "01:25:45", "price": 0.15947, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15947, "volume": 5151214484.0, "volume_24h": 5151214484.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -2.975, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8488789, "social_dominance": 0.607, "social_contributors": 84887, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750955145, "news_count": 47, "ema_7": 0.15946285714285713, "ema_14": 0.15971357142857143, "ema_25": 0.1597436, "ema_50": 0.15966079999999994, "ema_99": 0.1600936363636364, "ema_200": 0.16189034999999996, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.041914893617021, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23949240961.34, "recent_news_titles": ["Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing", "TSMC to Inject $10 Billion Capital in Unit to Counter FX Swings"], "top_social_posts": [{"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "🚨$470,000,000.00 saved!  Thank you DOGE!", "sentiment": 3.21, "platform": "tweet"}, {"text": "DOGE just terminated 312 government contracts with a ceiling value of $2.8B and savings of $470M...in just the last week\n\nNotably a $286k professional and management development contract for an “entrepreneurship course at Harvard University”and $485k USAID contract for a “senior general development advisor at USAID Madagascar.\"", "sentiment": 3.17, "platform": "tweet"}, {"text": "Young woman doesn't believe civilians should have firearms and wants the 2nd amendment removed from the constitution.  \n\n", "sentiment": 3.09, "platform": "tweet"}, {"text": "Dr. <PERSON><PERSON> went to buy champagne in anticipation of <PERSON><PERSON>’s win and laughed at the guy who checked her out when he said the race was close.\n\nWhat’s she up to these days?\n\n", "sentiment": 3.32, "platform": "tweet"}, {"text": "Will you be able to resist the temptation OR you HODL BabyDoge and never let go?", "sentiment": 3, "platform": "tweet"}, {"text": "Which #memecoin is printing money today? Shill me the next $DOGE 👀", "sentiment": 3, "platform": "tweet"}, {"text": "Closed $NPC. But holding wouldn't hurt either. Some memes are gonna take a hit early next week as dog<PERSON> is gonna take a hit. <PERSON><PERSON> of Tesla", "sentiment": 2.88, "platform": "tweet"}, {"text": "How it feels to be 27", "sentiment": 3.14, "platform": "tweet"}, {"text": "@DOGE Hey @<PERSON><PERSON><PERSON><PERSON> are you going to codify these DOGE cuts?\n\nOr are you gonna just take more vacation and do nothing at all for the American people???", "sentiment": 2.94, "platform": "tweet"}], "recent_candles": [[1750953960000, "0.159720", "0.159720", "0.159520", "0.159530", "3777784", 1750954019999, "602950.839980", 1415, "435852", "69571.133270", "0"], [1750954020000, "0.159540", "0.159590", "0.159420", "0.159580", "4451635", 1750954079999, "709993.416890", 1107, "2104565", "335704.909860", "0"], [1750954080000, "0.159570", "0.159610", "0.159490", "0.159570", "1455180", 1750954139999, "232162.178730", 776, "721767", "115148.512420", "0"], [1750954140000, "0.159560", "0.159570", "0.159470", "0.159560", "1633720", 1750954199999, "260625.070590", 664, "504834", "80535.376560", "0"], [1750954200000, "0.159560", "0.159580", "0.159300", "0.159350", "6099528", 1750954259999, "972525.645340", 1083, "1196848", "190768.966460", "0"], [1750954260000, "0.159350", "0.159360", "0.159230", "0.159230", "1707870", 1750954319999, "272041.089690", 971, "498462", "79405.318490", "0"], [1750954320000, "0.159230", "0.159480", "0.159230", "0.159330", "1186214", 1750954379999, "189069.763460", 867, "610490", "97301.006660", "0"], [1750954380000, "0.159330", "0.159390", "0.159330", "0.159340", "871816", 1750954439999, "138929.634530", 625, "505977", "80632.606830", "0"], [1750954440000, "0.159350", "0.159580", "0.159350", "0.159560", "983577", 1750954499999, "156842.792820", 681, "670328", "106910.123540", "0"], [1750954500000, "0.159560", "0.159560", "0.159360", "0.159380", "2538680", 1750954559999, "404748.239540", 607, "1499732", "239078.541170", "0"], [1750954560000, "0.159390", "0.159490", "0.159180", "0.159190", "2797075", 1750954619999, "445619.622080", 997, "654678", "104305.118380", "0"], [1750954620000, "0.159180", "0.159420", "0.159120", "0.159380", "3214851", 1750954679999, "511743.496460", 959, "1045282", "166403.411020", "0"], [1750954680000, "0.159380", "0.159380", "0.159180", "0.159180", "1655176", 1750954739999, "263630.200030", 908, "469486", "74781.037120", "0"], [1750954740000, "0.159170", "0.159250", "0.159130", "0.159130", "1051832", 1750954799999, "167436.620160", 640, "385373", "61348.637230", "0"], [1750954800000, "0.159130", "0.159240", "0.159120", "0.159230", "1996654", 1750954859999, "317794.908040", 828, "1426075", "226979.572190", "0"], [1750954860000, "0.159230", "0.159320", "0.159190", "0.159210", "1004000", 1750954919999, "159883.586200", 625, "615671", "98037.625540", "0"], [1750954920000, "0.159220", "0.159300", "0.159180", "0.159270", "1770319", 1750954979999, "281916.746020", 759, "992452", "158042.384730", "0"], [1750954980000, "0.159270", "0.159830", "0.159270", "0.159800", "5189103", 1750955039999, "828499.427180", 1628, "3334509", "532304.457610", "0"], [1750955040000, "0.159810", "0.159880", "0.159610", "0.159630", "2630638", 1750955099999, "420329.450470", 944, "1423214", "227448.814200", "0"], [1750955100000, "0.159640", "0.159710", "0.159460", "0.159480", "991189", 1750955159999, "158188.323750", 504, "378409", "60404.809710", "0"]], "candles_count": 20, "data_timestamp": 1750955145, "has_timeseries_data": true, "short_term_change_pct": 0.1695873374788143, "recent_high": 0.15988, "recent_low": 0.15918}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "neutral", "reasoning": "The short-term 5-minute candle data shows a neutral trend with mixed price movements and moderate volume. No strong technical patterns or breakouts are evident, and the overall trend strength is low (1.7/100). Social and news sentiment are neutral, further supporting a neutral stance. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral trend", "moderate volume", "no strong patterns"], "raw_response": " NO MARKDOWN. ONLY JSON.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term 5-minute candle data shows a neutral trend with mixed price m...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_f69b7415_1750955152"}, "strategy": {"strategy_id": "3d946dd3-402b-43b9-a4b5-4bb91a660169", "symbol": "DOGE", "timestamp": 1750955163, "type": "none", "entry_price": 0.15947, "stop_loss": 0.15628, "take_profit": 0.15588, "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RS<PERSON>(14)는 52.3으로 중립 수준을 유지하고 있다. MACD는 0.0042로 상승 추세를 보이지만, 볼린저 밴드 상단에 근접해 있어 과열 가능성에 주의가 필요하다. InCA의 HOLD 신호와 일치하여 단기적인 방향성은 명확하지 않다.", "confidence": 0.72, "reasoning_card_id": "card_f69b7415_1750955152", "risk_level": "medium", "key_points": ["RSI는 중립 수준 유지", "MACD는 약한 상승 추세", "볼린저 밴드 상단 근접"], "market_context": {"price": 0.15947, "percent_change_24h": -2.975, "timestamp": 1750955145}, "paper_based": false, "risk_reward": 0.0, "importance": 8.133667615666493, "consensus_signal": "none", "consensus_confidence": 0.72, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.5, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.72, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.72, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_5df73c62_1750955163"}}