{"execution_id": "exec_9e8b6732_1750985955", "symbol": "DOGE", "timestamp": 1750985955, "datetime": "2025-06-27 09:59:15", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "5d7fd8ae-b32c-4d15-a335-7c5c91e275fa", "timestamp": 1750985955, "market_data": {"id": "market_DOGE_1750985935", "symbol": "DOGE", "timestamp": 1750985935, "datetime": "2025-06-27 09:58:55", "date": "2025-06-27", "time": "09:58:55", "price": 0.15903, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15903, "volume": 5339011305.0, "volume_24h": 5339011305.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -3.372, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 9194609, "social_dominance": 0.847, "social_contributors": 91946, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750985935, "news_count": 52, "ema_7": 0.1593642857142857, "ema_14": 0.1597242857142857, "ema_25": 0.15980439999999999, "ema_50": 0.1597336, "ema_99": 0.160410303030303, "ema_200": 0.16020705000000002, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0788461538461545, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23854448527.93, "recent_news_titles": ["Bitwise Adds In-Kind Redemptions to DOGE, APT ETF Filings", "Dogecoin ETF Nears But DOGE Down 5% In 7 Days: Bear Market Incoming?  - Benzinga", "<PERSON><PERSON><PERSON>n (DOGE) News: ETF Approval Draws Closer", "Coinbase Shares Hit First Record Since 2021 on Stablecoin Fervor", "Dems drill into Russell Vought over DOGE cuts, accuse Trump admin of killing thousands | Fox News"], "top_social_posts": [{"text": "DOGE Team RESIGNS as <PERSON>-<PERSON><PERSON> Beef CLEARS HOUSE?!?", "sentiment": 3, "platform": "youtube-video"}, {"text": "President <PERSON> is a man of God.\n\n", "sentiment": 3.17, "platform": "tweet"}, {"text": "She is running interference for the GOP doing the uniparty business so the GOP can pretend they’re not in charge of her. \n\nAnother civics lesson for Americans. The GOP hates you as much if not more than the DNC.", "sentiment": 3.07, "platform": "tweet"}, {"text": "Add this to the list too.", "sentiment": 2.86, "platform": "tweet"}, {"text": "<PERSON> shares what <PERSON> said to him after choosing him for Defense Secretary.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "My view on Omead Afshar’s departure from $TSLA:  2Q sales are going to suck.  Omead was in charge of TSLA US/Europe sales and 2Q delivs are likely -15% YoY, which will be even worse than TSLA’s 1Q delivs decline of -13% YoY. Elon likely doesn’t accept that at least some of the 2Q sales weakness in the U.S./Europe is brand taint associated with his right wing political rhetoric and DOGE activities.", "sentiment": 2.95, "platform": "tweet"}, {"text": "An overweight man in a <PERSON> shirt was pulled off a plane after asking for an emergency exit seat because of his weight.\n\n", "sentiment": 2.79, "platform": "tweet"}, {"text": "A \"fireball\" was seen in Georgia, South Carolina, and Tennessee.\n\n", "sentiment": 3, "platform": "tweet"}, {"text": "Liberal Woman: \"If you support <PERSON> you would have supported <PERSON>.\"\n\n", "sentiment": 3.44, "platform": "tweet"}, {"text": "Who wants to see emails where the @US_OSC admits the following in writing:\n🤡they don’t properly investigate federal complaints\n🤡sent me someone else’s determination letter and said “disregard please”\n@FederalWD @POTUS @JDVance @DeptofDefense @elonmusk @DOGE (How efficient is whistleblowing to clean house?) Lmao. 💅🏻", "sentiment": 3.26, "platform": "tweet"}], "recent_candles": [[1750984740000, "0.160390", "0.160620", "0.160370", "0.160480", "6816841", 1750984799999, "1094346.693720", 1314, "4414260", "708720.008990", "0"], [1750984800000, "0.160480", "0.160540", "0.160410", "0.160410", "2065336", 1750984859999, "331474.824490", 694, "747128", "119930.521190", "0"], [1750984860000, "0.160410", "0.160420", "0.160170", "0.160170", "1943157", 1750984919999, "311448.362430", 1023, "819426", "131331.024270", "0"], [1750984920000, "0.160170", "0.160170", "0.159650", "0.159650", "5434952", 1750984979999, "868670.782230", 1663, "1563644", "249954.336560", "0"], [1750984980000, "0.159660", "0.159660", "0.159520", "0.159570", "8717886", 1750985039999, "1391084.812830", 1667, "2993392", "477668.436330", "0"], [1750985040000, "0.159580", "0.159600", "0.159440", "0.159540", "3905440", 1750985099999, "623041.914040", 1113, "1434566", "228909.257920", "0"], [1750985100000, "0.159530", "0.159660", "0.159460", "0.159620", "3970550", 1750985159999, "633589.482580", 1857, "2440083", "389403.176080", "0"], [1750985160000, "0.159620", "0.159710", "0.159580", "0.159700", "485634", 1750985219999, "77534.978490", 524, "235316", "37569.250530", "0"], [1750985220000, "0.159700", "0.159780", "0.159550", "0.159550", "1446825", 1750985279999, "231053.142430", 946, "331455", "52935.907980", "0"], [1750985280000, "0.159550", "0.159640", "0.159530", "0.159630", "950063", 1750985339999, "151596.404510", 678, "247781", "39536.677670", "0"], [1750985340000, "0.159630", "0.159630", "0.159240", "0.159270", "4617265", 1750985399999, "735932.415240", 1448, "489237", "77960.859080", "0"], [1750985400000, "0.159270", "0.159360", "0.159120", "0.159260", "6383192", 1750985459999, "1016460.817480", 1353, "2373838", "378075.602290", "0"], [1750985460000, "0.159250", "0.159270", "0.159110", "0.159110", "2311420", 1750985519999, "367924.710050", 894, "275672", "43891.452080", "0"], [1750985520000, "0.159110", "0.159190", "0.158900", "0.158980", "11549369", 1750985579999, "1836665.940350", 1988, "2538985", "403772.331520", "0"], [1750985580000, "0.158970", "0.159130", "0.158820", "0.159000", "6054587", 1750985639999, "962524.000330", 1582, "2935232", "466686.395680", "0"], [1750985640000, "0.158990", "0.159060", "0.158970", "0.159050", "3480275", 1750985699999, "553439.531940", 864, "2680485", "426256.977400", "0"], [1750985700000, "0.159050", "0.159120", "0.158990", "0.159080", "3438029", 1750985759999, "546814.227320", 999, "2104809", "334763.608040", "0"], [1750985760000, "0.159080", "0.159130", "0.159000", "0.159000", "1839621", 1750985819999, "292600.826650", 941, "962205", "153040.010340", "0"], [1750985820000, "0.159000", "0.159120", "0.158990", "0.159080", "2359535", 1750985879999, "375239.626330", 671, "1451311", "230798.617110", "0"], [1750985880000, "0.159080", "0.159170", "0.159030", "0.159030", "1602349", 1750985939999, "254965.998060", 740, "1253471", "199449.892030", "0"]], "candles_count": 20, "data_timestamp": 1750985935, "has_timeseries_data": true, "short_term_change_pct": -0.01257466205595237, "recent_high": 0.15917, "recent_low": 0.15897}, "importance": {"importance": 4, "is_important": true, "importance_score": 0.4, "situation_type": "neutral", "reasoning": "Short-term candle patterns show minimal directional bias with slight consolidation. No strong technical signals or volume spikes observed in the 5-minute timeframe. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "hold", "trading_direction": "neutral", "signal_direction": "neutral", "keywords": ["neutral trend", "volume confirmation", "no clear pattern"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 6,\n  \"is_important\": true,\n  \"importance_score\": 0.65,\n  \"reasoning\": \"Short-term candle patterns show minimal directional bias with slight consolidation. No ...", "confidence": 0.35, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_7018d760_1750985942"}, "strategy": {"strategy_id": "5d7fd8ae-b32c-4d15-a335-7c5c91e275fa", "symbol": "DOGE", "timestamp": 1750985955, "type": "none", "entry_price": 0.15903, "stop_loss": 0.15544, "take_profit": 0.15544, "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. MACD는 0을 기준으로 상하 진동하며, 볼린저 밴드는 가격이 중간 수준에 머물고 있다. InCA의 HOLD 신호와 일치하여 현재는 명확한 추세가 없으며, 단기적인 변동성은 제한적이다.", "confidence": 0.75, "reasoning_card_id": "card_7018d760_1750985942", "risk_level": "medium", "key_points": ["RSI는 중립 영역에 머물고 있음", "MACD는 0 기준으로 진동 중", "볼린저 밴드는 중간 수준에 위치"], "market_context": {"price": 0.15903, "percent_change_24h": -3.372, "timestamp": 1750985935}, "paper_based": false, "risk_reward": 0.0, "importance": 8.14998813885027, "consensus_signal": "none", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "hold", "situation": "neutral", "importance": 0.4, "confidence": 0.35, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "none", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "none", "consensus_confidence": 0.75, "should_execute": false, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_9e8b6732_1750985955"}}