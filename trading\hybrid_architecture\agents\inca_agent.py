"""
InCA (Importance Calculation Agent) - 중요도 계산 에이전트
"""

import logging
import time
import json
import os
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional

from trading.hybrid_architecture.interfaces import InCAAgentInterface
# 중요도 평가 모듈 임포트
from trading.hybrid_architecture.utils.importance_evaluator import evaluate_importance_with_llm
# JSON 추출 유틸리티 임포트
from trading.hybrid_architecture.utils.json_extractor import extract_json_from_text
# vLLM 요청 큐 임포트
from models.vllm_request_queue import queue_vllm_request

# 로거 설정
logger = logging.getLogger(__name__)


class InCAAgent(InCAAgentInterface):
    """
    InCA (Importance Calculation Agent) - 중요도 계산 에이전트
    """

    def __init__(self, llm_model=None, importance_threshold: float = 0.3):
        """
        InCA 에이전트 초기화

        Args:
            llm_model: LLM 모델
            importance_threshold: 중요도 임계값 (0.0 ~ 1.0)
        """
        self.llm_model = llm_model
        self.importance_threshold = importance_threshold
        self.patterns_db = []  # 패턴 데이터베이스
        self.is_initialized = False
        self.success_count = 0  # 성공 횟수
        self.failure_count = 0  # 실패 횟수
        self.recent_results = []  # 최근 결과 (최대 100개)
        self.learning_rate = 0.1  # 학습률 (임계값 조정에 사용)

        # 🔧 llm_client 설정 (evaluate_importance_sync에서 사용)
        self.llm_client = None
        self._setup_llm_client()

        # 성과 기반 학습 데이터 (실시간 업데이트)
        self.hold_duration_performance = {}
        self.symbol_performance = {}

        # 🎯 벡터 DB 초기화 (ChromaDB)
        self.vector_db = None
        self.vector_collection = None
        self._init_vector_db()

        logger.info(f"InCA 에이전트 초기화 완료 (중요도 임계값: {importance_threshold})")

    def _setup_llm_client(self):
        """llm_client 설정 (evaluate_importance_sync에서 사용)"""
        try:
            # 올바른 vLLM 클라이언트 가져오기
            from simulator.utils.vllm_client import VLLMClient
            import os

            # 환경변수에서 vLLM 설정 가져오기
            vllm_url = os.getenv('VLLM_URL', 'http://localhost:8001/v1')
            vllm_model = os.getenv('VLLM_MODEL', 'Qwen/Qwen3-14B-AWQ')

            # VLLMClient 생성
            self.llm_client = VLLMClient(
                api_base=vllm_url,
                model_name=vllm_model,
                temperature=0.1,
                max_tokens=512
            )

            logger.info(f"InCA 에이전트 llm_client 설정 완료: {vllm_model}")

        except Exception as e:
            logger.warning(f"InCA 에이전트 llm_client 설정 중 오류: {e}")
            self.llm_client = None

    def _init_vector_db(self):
        """벡터 DB (ChromaDB) 초기화"""
        try:
            import chromadb
            from trading.hybrid_architecture.utils.chromadb_utils import safe_get_collection, get_safe_embedding_function

            # ChromaDB 클라이언트 초기화
            self.vector_db = chromadb.PersistentClient(path="data/inca_db/chroma")

            # SentenceTransformer 임베딩 함수 사용
            embedding_func = get_safe_embedding_function("all-MiniLM-L6-v2")

            # 벡터 컬렉션 생성/로드
            self.vector_collection = safe_get_collection(
                self.vector_db,
                "inca_experiences_v3",  # 새로운 컬렉션 이름
                embedding_function=embedding_func
            )

            if self.vector_collection:
                collection_count = self.vector_collection.count()
                logger.info(f"InCA 벡터 DB 초기화 완료: {collection_count}개 경험 데이터")
            else:
                logger.warning("InCA 벡터 DB 컬렉션 초기화 실패")

        except Exception as e:
            logger.warning(f"InCA 벡터 DB 초기화 실패: {e}")
            self.vector_db = None
            self.vector_collection = None

    def initialize(self) -> bool:
        """
        에이전트 초기화

        필요한 리소스를 로드하고 에이전트를 사용 가능한 상태로 준비합니다.

        Returns:
            bool: 초기화 성공 여부
        """
        try:
            # 캐시 디렉토리 생성
            os.makedirs("cache/inca", exist_ok=True)

            # 패턴 데이터베이스 로드 (있는 경우)
            self.load_state()

            self.is_initialized = True
            logger.info("InCA 에이전트 초기화 성공")
            return True
        except Exception as e:
            logger.error(f"InCA 에이전트 초기화 실패: {e}")
            logger.error(traceback.format_exc())
            return False

    def process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        데이터 처리

        시장 데이터를 처리하고 중요도를 평가합니다.

        Args:
            data: 처리할 입력 데이터

        Returns:
            Dict[str, Any]: 처리 결과
        """
        try:
            # 중요도 평가
            importance_result = self.evaluate_importance(data)

            # 유사한 패턴 찾기
            similar_patterns = self.find_similar_patterns(data)

            # 결과 결합
            result = {
                "importance_evaluation": importance_result,
                "similar_patterns": similar_patterns,
                "timestamp": datetime.now().isoformat()
            }

            # 패턴 데이터베이스에 현재 상황 추가
            self._add_to_patterns_db(data, importance_result)

            return result
        except Exception as e:
            logger.error(f"데이터 처리 중 오류 발생: {e}")
            logger.error(traceback.format_exc())
            # 기본 결과 반환
            return {
                "importance_evaluation": {
                    "importance": 1,
                    "situation_type": "error",
                    "is_important": False,
                    "importance_score": 0.1,
                    "error": str(e)
                },
                "similar_patterns": [],
                "timestamp": datetime.now().isoformat()
            }

    def evaluate_importance(self, market_data: Dict[str, Any], mode: str = "execution") -> Dict[str, Any]:
        """
        시장 데이터의 중요도 평가

        Args:
            market_data: 시장 데이터

        Returns:
            Dict[str, Any]: 중요도 평가 결과
        """
        # 🔧 market_data None 체크 추가
        if market_data is None:
            logger.error("❌ evaluate_importance: market_data가 None입니다")
            return {
                "importance_score": 0.5,
                "situation_type": "neutral",
                "action_recommendation": "hold",
                "signal_direction": "neutral",
                "reasoning": "market_data 없음",
                "error": "market_data is None"
            }

        # 최대 재시도 횟수
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 프롬프트 생성 (기존 로직 유지)
                prompt = self._create_importance_prompt(market_data)

                # LLM 호출 (모드 전달)
                result = self._generate_importance_with_llm(prompt, mode=mode)

                # 🚀 성과 데이터 기반 미세 조정 (기존 결과에 살짝만 반영)
                symbol = market_data.get("symbol", "UNKNOWN")
                result = self._apply_subtle_performance_adjustment(result, symbol)

                # 🎯 과거 경험 기반 결과 조정
                result = self._apply_historical_experience_adjustment(result, market_data)

                return result

            except Exception as e:
                logger.error(f"중요도 평가 중 오류 발생: {e}")
                logger.error(traceback.format_exc())

                # 재시도
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"오류로 인해 재시도합니다. (시도 {retry_count}/{max_retries})")
                    time.sleep(1)  # 잠시 대기 후 재시도
                else:
                    logger.error(f"최대 재시도 횟수({max_retries})를 초과했습니다.")
                    raise

    def find_similar_patterns(self, situation: Dict[str, Any], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        유사한 과거 상황/패턴 검색

        Args:
            situation: 현재 상황 데이터
            top_k: 반환할 최대 결과 수

        Returns:
            List[Dict[str, Any]]: 유사한 상황 목록 (유사도 포함)
        """
        if not self.patterns_db:
            return []

        # 간단한 유사도 계산 (실제로는 더 복잡한 임베딩 기반 유사도를 사용할 수 있음)
        similar_patterns = []

        # 현재 심볼 추출
        current_symbol = situation.get("symbol", "UNKNOWN")

        # 같은 심볼의 패턴만 필터링
        symbol_patterns = [p for p in self.patterns_db if p.get("symbol") == current_symbol]

        # 패턴이 없으면 빈 리스트 반환
        if not symbol_patterns:
            return []

        # 간단한 유사도 계산 (가격 변동률 기반)
        for pattern in symbol_patterns:
            try:
                # 가격 변동률 차이 계산
                current_change = situation.get("percent_change_24h", 0)
                pattern_change = pattern.get("percent_change_24h", 0)
                price_diff = abs(current_change - pattern_change)

                # 거래량 차이 계산
                current_volume = situation.get("volume_24h", 0)
                pattern_volume = pattern.get("volume_24h", 0)
                volume_ratio = min(current_volume, pattern_volume) / max(current_volume, pattern_volume) if max(current_volume, pattern_volume) > 0 else 0

                # 유사도 점수 계산 (낮을수록 유사)
                similarity_score = price_diff * (1 - volume_ratio)

                similar_patterns.append({
                    "pattern": pattern,
                    "similarity_score": similarity_score,
                    "timestamp": pattern.get("timestamp", "")
                })
            except Exception as e:
                logger.error(f"유사도 계산 중 오류: {e}")
                continue

        # 유사도 기준으로 정렬
        similar_patterns.sort(key=lambda x: x["similarity_score"])

        # 상위 k개 반환
        return similar_patterns[:top_k]

    def learn_from_feedback(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """
        피드백을 기반으로 학습

        실행 결과나 다른 에이전트의 피드백을 기반으로 내부 모델을 업데이트합니다.
        성공/실패 모두에서 학습하며, 손익 정도에 따른 가중치를 부여합니다.

        Args:
            feedback: 피드백 데이터

        Returns:
            Dict[str, Any]: 학습 결과
        """
        try:
            # 피드백에서 필요한 정보 추출
            strategy_result = feedback.get("strategy_result", {})
            market_data = feedback.get("market_data", {})
            importance_evaluation = feedback.get("importance_evaluation", {})

            # 성공 여부 및 손익 추출
            is_success = strategy_result.get("success", False)
            profit_loss = float(strategy_result.get("profit_loss", 0.0))
            profit_loss_pct = float(strategy_result.get("profit_loss_pct", 0.0))

            # 학습 가중치 계산 (손익 정도에 따라 가중치 부여)
            learning_weight = self._calculate_learning_weight(is_success, profit_loss, profit_loss_pct)

            # 성공/실패 모두에서 학습 (가중치 적용)
            self._add_to_patterns_db(
                market_data,
                importance_evaluation,
                strategy_result,
                learning_weight=learning_weight
            )

            # 중요도 임계값 동적 조정 (성공률에 따라)
            self._adjust_importance_threshold(is_success, importance_evaluation)

            # 🎯 벡터 DB에 경험 저장 (의미 있는 데이터만)
            try:
                if self.vector_collection and self._is_meaningful_experience(profit_loss_pct, feedback):
                    experience_data = {
                        'symbol': feedback.get('symbol', market_data.get('symbol', 'UNKNOWN')),
                        'timestamp': int(time.time()),
                        'prediction': {
                            'action_recommendation': importance_evaluation.get('action_recommendation', 'hold'),
                            'trading_direction': importance_evaluation.get('trading_direction', 'neutral'),
                            'confidence': importance_evaluation.get('confidence', 0.5),
                            'importance': importance_evaluation.get('importance', 5),
                            'situation_type': importance_evaluation.get('situation_type', 'neutral')
                        },
                        'trade_result': {
                            'profit': profit_loss_pct,
                            'profit_percent': profit_loss_pct,
                            'success': is_success,
                            'status': 'completed'
                        },
                        'market_data': market_data
                    }
                    self._save_to_vector_db(experience_data)
                    logger.info(f"InCA 의미 있는 경험을 벡터 DB에 저장 완료: {experience_data['symbol']} (수익: {profit_loss_pct:.3f}%)")
                else:
                    logger.debug(f"InCA 무의미한 경험 필터링: {feedback.get('symbol', 'UNKNOWN')} (수익: {profit_loss_pct:.3f}%)")
            except Exception as e:
                logger.warning(f"InCA 벡터 DB 저장 실패: {e}")

            # 학습 결과 반환
            return {
                "success": True,
                "message": f"피드백 학습 완료 (가중치: {learning_weight:.2f})",
                "patterns_count": len(self.patterns_db),
                "learning_weight": learning_weight,
                "adjusted_threshold": self.importance_threshold
            }
        except Exception as e:
            logger.error(f"피드백 학습 중 오류 발생: {e}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"피드백 학습 실패: {e}",
                "patterns_count": len(self.patterns_db)
            }

    def learn_from_virtual_position(self, virtual_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        가상 포지션 결과를 통한 학습 (중립 신호 학습 포함)

        Args:
            virtual_result: 가상 포지션 평가 결과

        Returns:
            Dict[str, Any]: 학습 결과
        """
        try:
            virtual_position = virtual_result.get("virtual_position", {})
            symbol = virtual_position.get("symbol", "UNKNOWN")
            prediction_type = virtual_position.get("prediction_type", "neutral")
            confidence = virtual_position.get("confidence", 0.5)
            importance = virtual_position.get("importance", 5)

            price_change_pct = virtual_result.get("price_change_pct", 0.0)
            prediction_accuracy = virtual_result.get("prediction_accuracy", "unknown")
            learning_weight = virtual_result.get("learning_weight", 0.5)
            actual_behavior = virtual_result.get("actual_market_behavior", "neutral")

            logger.info(f"🔮 [{symbol}] 가상 포지션 학습 시작: {prediction_type} 예측 → {actual_behavior} "
                       f"({price_change_pct:.3f}%) = {prediction_accuracy}")

            # 가상 거래 결과를 실제 거래와 유사한 형태로 변환
            synthetic_market_data = virtual_position.get("market_data", {})
            synthetic_market_data["symbol"] = symbol

            synthetic_importance_evaluation = {
                "action_recommendation": prediction_type if prediction_type != "neutral" else "hold",
                "trading_direction": prediction_type if prediction_type != "neutral" else "neutral",
                "confidence": confidence,
                "importance": importance,
                "situation_type": prediction_type if prediction_type != "neutral" else "neutral"
            }

            synthetic_strategy_result = {
                "success": prediction_accuracy in ["correct", "perfect", "good"],
                "profit_loss": 0.0,  # 가상 거래이므로 실제 손익 없음
                "profit_loss_pct": 0.0,
                "prediction_accuracy": prediction_accuracy,
                "virtual_trade": True,
                "actual_price_change": price_change_pct
            }

            # 패턴 DB에 학습 데이터 추가 (가중치 적용)
            self._add_to_patterns_db(
                synthetic_market_data,
                synthetic_importance_evaluation,
                synthetic_strategy_result,
                learning_weight=learning_weight
            )

            # 중요도 임계값 동적 조정 (가상 거래도 고려)
            self._adjust_importance_threshold(synthetic_strategy_result["success"], synthetic_importance_evaluation)

            # 🎯 벡터 DB에 가상 경험 저장
            try:
                if self.vector_collection:
                    experience_data = {
                        'symbol': symbol,
                        'timestamp': int(time.time()),
                        'prediction': {
                            'action_recommendation': synthetic_importance_evaluation['action_recommendation'],
                            'trading_direction': synthetic_importance_evaluation['trading_direction'],
                            'confidence': confidence,
                            'importance': importance,
                            'situation_type': synthetic_importance_evaluation['situation_type']
                        },
                        'trade_result': {
                            'profit': 0.0,
                            'profit_percent': 0.0,
                            'success': synthetic_strategy_result['success'],
                            'status': 'virtual_completed',
                            'prediction_accuracy': prediction_accuracy,
                            'actual_price_change': price_change_pct
                        },
                        'market_data': synthetic_market_data,
                        'virtual_trade': True
                    }
                    self._save_to_vector_db(experience_data)
                    logger.info(f"🔮 InCA 가상 경험을 벡터 DB에 저장 완료: {symbol} "
                               f"({prediction_type} → {actual_behavior}, 정확도: {prediction_accuracy})")
            except Exception as e:
                logger.warning(f"InCA 가상 경험 벡터 DB 저장 실패: {e}")

            # 학습 결과 반환
            return {
                "success": True,
                "message": f"가상 포지션 학습 완료 (가중치: {learning_weight:.2f})",
                "patterns_count": len(self.patterns_db),
                "learning_weight": learning_weight,
                "prediction_accuracy": prediction_accuracy,
                "virtual_trade": True
            }
        except Exception as e:
            logger.error(f"가상 포지션 학습 중 오류 발생: {e}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"가상 포지션 학습 실패: {e}",
                "patterns_count": len(self.patterns_db)
            }

    def save_state(self, path: Optional[str] = None) -> bool:
        """
        에이전트 상태 저장

        현재 에이전트의 상태를 디스크에 저장합니다.

        Args:
            path: 저장 경로 (None일 경우 기본 경로 사용)

        Returns:
            bool: 저장 성공 여부
        """
        try:
            # 기본 경로 설정
            if path is None:
                base_path = "cache/inca"
                patterns_path = f"{base_path}/patterns_db.json"
                stats_path = f"{base_path}/learning_stats.json"
            else:
                patterns_path = path
                stats_path = os.path.join(os.path.dirname(path), "learning_stats.json")

            # 디렉토리 생성
            os.makedirs(os.path.dirname(patterns_path), exist_ok=True)

            # 패턴 데이터베이스 저장
            with open(patterns_path, 'w', encoding='utf-8') as f:
                json.dump(self.patterns_db, f, ensure_ascii=False, indent=2)

            # 학습 통계 저장 (성과 데이터 포함)
            learning_stats = {
                "success_count": self.success_count,
                "failure_count": self.failure_count,
                "importance_threshold": self.importance_threshold,
                "learning_rate": self.learning_rate,
                "recent_results_count": len(self.recent_results),
                "last_updated": int(time.time()),
                "total_patterns": len(self.patterns_db),
                "success_rate": self.success_count / (self.success_count + self.failure_count) if (self.success_count + self.failure_count) > 0 else 0,
                "hold_duration_performance": self.hold_duration_performance,
                "symbol_performance": self.symbol_performance,
                "performance_learning_enabled": True
            }

            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(learning_stats, f, ensure_ascii=False, indent=2)

            logger.info(f"InCA 에이전트 상태 저장 완료: {patterns_path} (패턴 수: {len(self.patterns_db)}, 성공/실패: {self.success_count}/{self.failure_count})")
            return True
        except Exception as e:
            logger.error(f"InCA 에이전트 상태 저장 실패: {e}")
            logger.error(traceback.format_exc())
            return False

    def load_state(self, path: Optional[str] = None) -> bool:
        """
        에이전트 상태 로드

        이전에 저장된 에이전트 상태를 로드합니다.

        Args:
            path: 로드할 파일 경로 (None일 경우 기본 경로에서 로드)

        Returns:
            bool: 로드 성공 여부
        """
        try:
            # 기본 경로 설정
            if path is None:
                base_path = "cache/inca"
                patterns_path = f"{base_path}/patterns_db.json"
                stats_path = f"{base_path}/learning_stats.json"
            else:
                patterns_path = path
                stats_path = os.path.join(os.path.dirname(path), "learning_stats.json")

            # 패턴 데이터베이스 로드
            if os.path.exists(patterns_path):
                with open(patterns_path, 'r', encoding='utf-8') as f:
                    self.patterns_db = json.load(f)
                logger.info(f"InCA 에이전트 패턴 데이터베이스 로드 완료: {patterns_path} (패턴 수: {len(self.patterns_db)})")
            else:
                logger.info(f"InCA 에이전트 패턴 데이터베이스 파일이 없습니다. 새로운 패턴 데이터베이스를 생성합니다.")
                self.patterns_db = []

            # 학습 통계 로드
            if os.path.exists(stats_path):
                with open(stats_path, 'r', encoding='utf-8') as f:
                    learning_stats = json.load(f)

                # 학습 통계 적용
                self.success_count = learning_stats.get("success_count", 0)
                self.failure_count = learning_stats.get("failure_count", 0)
                self.importance_threshold = learning_stats.get("importance_threshold", self.importance_threshold)
                self.learning_rate = learning_stats.get("learning_rate", 0.1)

                # 🚀 성과 데이터 로드 (저장된 데이터가 있으면 업데이트)
                if "hold_duration_performance" in learning_stats:
                    self.hold_duration_performance.update(learning_stats["hold_duration_performance"])
                    logger.info("저장된 보유기간별 성과 데이터 로드 완료")

                if "symbol_performance" in learning_stats:
                    self.symbol_performance.update(learning_stats["symbol_performance"])
                    logger.info("저장된 심볼별 성과 데이터 로드 완료")

                logger.info(f"InCA 에이전트 학습 통계 로드 완료: {stats_path} (성공/실패: {self.success_count}/{self.failure_count})")
                logger.info("성과 기반 학습 시스템 활성화됨")
            else:
                logger.info(f"InCA 에이전트 학습 통계 파일이 없습니다. 기본값을 사용합니다.")

            return True
        except Exception as e:
            logger.error(f"InCA 에이전트 상태 로드 실패: {e}")
            logger.error(traceback.format_exc())
            self.patterns_db = []
            return False

    def update_performance_data(self, portfolio_path: str = "data/portfolio.json") -> bool:
        """
        실제 거래 성과 데이터를 분석하여 성과 데이터 업데이트

        Args:
            portfolio_path: 포트폴리오 파일 경로

        Returns:
            bool: 업데이트 성공 여부
        """
        try:
            if not os.path.exists(portfolio_path):
                logger.warning(f"포트폴리오 파일이 없습니다: {portfolio_path}")
                return False

            # 포트폴리오 데이터 로드
            with open(portfolio_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            trades = data.get('trade_history', [])

            # 단타거래 관련 거래 필터링
            scalping_trades = []
            for trade in trades:
                if (trade.get('strategy_type') == 'scalping' or
                    'min_hold_time' in trade or
                    'hold_duration_minutes' in trade):
                    scalping_trades.append(trade)

            if not scalping_trades:
                logger.info("단타거래 데이터가 없어 성과 업데이트를 건너뜁니다.")
                return True

            # 보유기간별 성과 재계산
            hold_duration_stats = {
                "1분 이하": {"success": 0, "total": 0, "pnl_sum": 0},
                "1-3분": {"success": 0, "total": 0, "pnl_sum": 0},
                "3-5분": {"success": 0, "total": 0, "pnl_sum": 0},
                "5분 초과": {"success": 0, "total": 0, "pnl_sum": 0}
            }

            # 심볼별 성과 재계산
            symbol_stats = {}

            for trade in scalping_trades:
                entry_time = trade.get('entry_timestamp', trade.get('timestamp', 0))
                exit_time = trade.get('exit_timestamp', 0)
                symbol = trade.get('symbol', 'UNKNOWN')

                if entry_time and exit_time and exit_time > entry_time:
                    actual_hold_minutes = (exit_time - entry_time) / 60

                    # 보유기간 구간 분류
                    if actual_hold_minutes <= 1:
                        category = "1분 이하"
                    elif actual_hold_minutes <= 3:
                        category = "1-3분"
                    elif actual_hold_minutes <= 5:
                        category = "3-5분"
                    else:
                        category = "5분 초과"

                    # 성공 여부 판단
                    is_success = False
                    pnl = 0

                    if 'success' in trade:
                        is_success = trade['success']
                    elif 'current_pnl' in trade:
                        pnl = trade['current_pnl']
                        is_success = pnl > 0

                    # 보유기간별 통계 업데이트
                    if category in hold_duration_stats:
                        hold_duration_stats[category]["total"] += 1
                        if is_success:
                            hold_duration_stats[category]["success"] += 1
                        hold_duration_stats[category]["pnl_sum"] += pnl

                    # 심볼별 통계 업데이트
                    if symbol not in symbol_stats:
                        symbol_stats[symbol] = {"success": 0, "total": 0, "hold_time_sum": 0}

                    symbol_stats[symbol]["total"] += 1
                    if is_success:
                        symbol_stats[symbol]["success"] += 1
                    symbol_stats[symbol]["hold_time_sum"] += actual_hold_minutes

            # 성과 데이터 업데이트
            for category, stats in hold_duration_stats.items():
                if stats["total"] > 0:
                    success_rate = stats["success"] / stats["total"]
                    avg_pnl = stats["pnl_sum"] / stats["total"]

                    self.hold_duration_performance[category] = {
                        "success_rate": success_rate,
                        "avg_pnl": avg_pnl,
                        "trade_count": stats["total"]
                    }

            # 심볼별 성과 데이터 업데이트
            for symbol, stats in symbol_stats.items():
                if stats["total"] > 0:
                    success_rate = stats["success"] / stats["total"]
                    avg_hold_time = stats["hold_time_sum"] / stats["total"]

                    self.symbol_performance[symbol] = {
                        "success_rate": success_rate,
                        "avg_hold_time": avg_hold_time
                    }

            # 상태 저장
            self.save_state()

            logger.info("성과 데이터 업데이트 완료")
            logger.info(f"보유기간별 성과: {self.hold_duration_performance}")
            logger.info(f"심볼별 성과: {self.symbol_performance}")

            return True

        except Exception as e:
            logger.error(f"성과 데이터 업데이트 중 오류: {e}")
            return False

    def _create_importance_prompt(self, market_data: Dict[str, Any]) -> str:
        """
        중요도 평가 프롬프트 생성

        Args:
            market_data: 시장 데이터

        Returns:
            str: 중요도 평가 프롬프트
        """
        # 심볼 추출
        symbol = market_data.get("symbol", "UNKNOWN")

        # 🔍 디버깅: 받은 시장 데이터 구조 로깅
        logger.info(f"InCA 프롬프트 생성 - {symbol} 시장 데이터 키: {list(market_data.keys())}")

        # 현재 가격 추출 (다양한 필드명 지원)
        current_price = (
            market_data.get("current_price") or
            market_data.get("price") or
            market_data.get("lastPrice") or
            0
        )

        # 🚫 24시간 변동률 제거 (단타에서는 불필요)
        # 캔들 패턴 분석만 사용

        # 거래량 추출 (다양한 필드명 지원)
        volume_24h = (
            market_data.get("volume_24h") or
            market_data.get("volume") or
            market_data.get("quoteVolume") or
            0
        )

        # 🔍 디버깅: 추출된 값들 로깅 (24시간 변동률 제거)
        logger.info(f"InCA 프롬프트 생성 - {symbol} 추출된 값: 가격=${current_price}, 거래량={volume_24h}")

        # 🚀 InCA 시간 프레임: 1분봉 5개 (즉시 반응성) + 단기 추세 분석
        candles = market_data.get("recent_candles", market_data.get("candles", []))
        candle_data = ""
        short_term_trend = "neutral"
        trend_strength = 0

        if candles:
            # 최근 5개 1분봉 캔들 사용 (InCA 전용 시간 프레임)
            recent_candles = candles[-5:]
            candle_data += f"📊 InCA 분석 데이터 (1분봉 5개 - 즉시 반응성):\n"

            # 🎯 단기 추세 분석 추가
            close_prices = []
            high_prices = []
            low_prices = []

            for i, candle in enumerate(recent_candles):
                if isinstance(candle, list) and len(candle) >= 5:
                    # 바이낸스 캔들 형식: [timestamp, open, high, low, close, volume, ...]
                    open_price = float(candle[1])
                    high_price = float(candle[2])
                    low_price = float(candle[3])
                    close_price = float(candle[4])
                    volume = float(candle[5])

                    close_prices.append(close_price)
                    high_prices.append(high_price)
                    low_prices.append(low_price)

                    candle_data += f"1분봉 {i+1}: Open: {open_price}, High: {high_price}, Low: {low_price}, Close: {close_price}, Volume: {volume}\n"
                elif isinstance(candle, dict):
                    close_price = candle.get('close', 0)
                    high_price = candle.get('high', 0)
                    low_price = candle.get('low', 0)

                    close_prices.append(close_price)
                    high_prices.append(high_price)
                    low_prices.append(low_price)

                    candle_data += f"1분봉 {i+1}: Open: {candle.get('open', 0)}, High: {high_price}, Low: {low_price}, Close: {close_price}, Volume: {candle.get('volume', 0)}\n"

            # 🎯 고급 기술적 분석 (추세선, 패턴, 볼륨 분석)
            if len(close_prices) >= 3:
                # 기본 추세 계산
                first_price = close_prices[0]
                last_price = close_prices[-1]
                price_change_pct = ((last_price - first_price) / first_price) * 100 if first_price > 0 else 0

                # 🔧 고급 패턴 분석
                pattern_analysis = self._analyze_advanced_patterns(recent_candles, close_prices)

                # 🔧 볼륨 분석
                volume_analysis = self._analyze_volume_pattern(recent_candles)

                # 🔧 추세선 분석
                trendline_analysis = self._analyze_trendlines(close_prices, high_prices, low_prices)

                # 연속성 확인 (상승/하락 캔들 개수)
                up_candles = 0
                down_candles = 0
                for i in range(1, len(close_prices)):
                    if close_prices[i] > close_prices[i-1]:
                        up_candles += 1
                    elif close_prices[i] < close_prices[i-1]:
                        down_candles += 1

                # 🔧 강화된 추세 강도 계산 (패턴, 볼륨, 추세선 고려)
                base_strength = abs(price_change_pct) * 10  # 기본 강도
                pattern_boost = pattern_analysis.get('strength_multiplier', 1.0)
                volume_boost = volume_analysis.get('strength_multiplier', 1.0)
                trendline_boost = trendline_analysis.get('strength_multiplier', 1.0)

                trend_strength = base_strength * pattern_boost * volume_boost * trendline_boost
                trend_strength = min(100, trend_strength)

                # 🔧 강화된 추세 방향 결정 (패턴과 추세선 고려)
                pattern_signal = pattern_analysis.get('signal', 'neutral')
                trendline_signal = trendline_analysis.get('signal', 'neutral')
                volume_signal = volume_analysis.get('signal', 'neutral')

                # 종합 신호 판단
                bullish_signals = sum([
                    price_change_pct > 0.05,
                    up_candles > down_candles,
                    pattern_signal == 'bullish',
                    trendline_signal == 'bullish',
                    volume_signal == 'bullish'
                ])

                bearish_signals = sum([
                    price_change_pct < -0.05,
                    down_candles > up_candles,
                    pattern_signal == 'bearish',
                    trendline_signal == 'bearish',
                    volume_signal == 'bearish'
                ])

                if bullish_signals >= 3:
                    short_term_trend = "bullish"
                elif bearish_signals >= 3:
                    short_term_trend = "bearish"
                else:
                    short_term_trend = "neutral"

                candle_data += f"\n🎯 고급 기술적 분석 (5분간):\n"
                candle_data += f"- 추세 방향: {short_term_trend.upper()}\n"
                candle_data += f"- 가격 변화: {price_change_pct:.3f}%\n"
                candle_data += f"- 추세 강도: {trend_strength:.1f}/100\n"
                candle_data += f"- 상승 캔들: {up_candles}개, 하락 캔들: {down_candles}개\n"
                candle_data += f"- 패턴 분석: {pattern_analysis.get('description', 'N/A')}\n"
                candle_data += f"- 볼륨 분석: {volume_analysis.get('description', 'N/A')}\n"
                candle_data += f"- 추세선 분석: {trendline_analysis.get('description', 'N/A')}\n"
                candle_data += f"- 종합 신호: 강세 {bullish_signals}/5, 약세 {bearish_signals}/5\n"

        # 뉴스 데이터 추출 (다양한 필드명 지원)
        news = market_data.get("news", [])
        recent_news_titles = market_data.get("recent_news_titles", [])
        news_sentiment = market_data.get("news_sentiment", 0)

        news_data = ""
        if news and isinstance(news, list):
            # 최근 3개 뉴스만 사용
            recent_news = news[:3]
            for i, news_item in enumerate(recent_news):
                title = news_item.get('title', '')
                sentiment = news_item.get('sentiment', 'neutral')
                news_data += f"News {i+1}: {title} (Sentiment: {sentiment})\n"
        elif recent_news_titles and isinstance(recent_news_titles, list):
            # recent_news_titles 사용
            for i, title in enumerate(recent_news_titles[:3]):
                news_data += f"News {i+1}: {title}\n"

        if news_sentiment and news_sentiment != 0:
            news_data += f"Overall News Sentiment Score: {news_sentiment}\n"

        # 소셜 미디어 데이터 추출 (다양한 필드명 지원)
        social = market_data.get("social", [])
        top_social_posts = market_data.get("top_social_posts", [])
        bullish_sentiment = market_data.get("bullish_sentiment", 0)
        bearish_sentiment = market_data.get("bearish_sentiment", 0)

        social_data = ""
        if social and isinstance(social, list):
            # 최근 3개 소셜 미디어 데이터만 사용
            recent_social = social[:3]
            for i, social_item in enumerate(recent_social):
                text = social_item.get('text', '')
                sentiment = social_item.get('sentiment', 'neutral')
                social_data += f"Social {i+1}: {text[:100]}... (Sentiment: {sentiment})\n"
        elif top_social_posts and isinstance(top_social_posts, list):
            # top_social_posts 사용
            for i, post in enumerate(top_social_posts[:3]):
                text = post.get('text', '')
                sentiment = post.get('sentiment', 'neutral')
                social_data += f"Social {i+1}: {text[:100]}... (Sentiment: {sentiment})\n"

        if bullish_sentiment or bearish_sentiment:
            social_data += f"Bullish Sentiment: {bullish_sentiment}, Bearish Sentiment: {bearish_sentiment}\n"

        # 프롬프트 생성 (단기 추세 강조)
        prompt = f"""
/no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR {symbol} CRYPTOCURRENCY ONLY.

Analyze ALL available data for {symbol} and provide comprehensive importance evaluation.

MARKET DATA:
Symbol: {symbol}
Current Price: ${current_price}
Volume: {volume_24h}

CANDLE DATA (CRITICAL - ANALYZE SHORT-TERM TREND):
{candle_data if candle_data else "No candle data available."}

NEWS DATA (CRITICAL - ANALYZE SENTIMENT):
{news_data if news_data else "No news data available."}

SOCIAL MEDIA DATA (CRITICAL - ANALYZE SENTIMENT):
{social_data if social_data else "No social data available."}

🔧 ENHANCED TECHNICAL ANALYSIS INSTRUCTIONS:
1. 🎯 TECHNICAL PATTERNS ARE CRITICAL: V-shaped reversals, breakouts, volume spikes are STRONG signals
2. 🎯 TRENDLINE ANALYSIS: Support/resistance breakouts indicate major direction changes
3. 🎯 VOLUME CONFIRMATION: Price moves with high volume are more reliable than low-volume moves
4. 🎯 PATTERN RECOGNITION: Look for double tops/bottoms, wedges, consecutive candles
5. 🎯 SHORT-TERM TREND PRIORITY: Recent 5-minute patterns override 24h changes
6. 🎯 BREAKOUT SIGNALS: When price breaks above resistance or below support with volume = STRONG signal
7. 🎯 REVERSAL PATTERNS: V-shaped reversals from lows/highs with volume = immediate action needed
8. News and social sentiment are secondary to clear technical patterns
9. If technical analysis shows STRONG patterns (strength >50), prioritize over other factors

PRIORITY ORDER:
1. Short-term trend direction (from candle data) - HIGHEST PRIORITY
2. Trend strength and consistency
3. News sentiment
4. Social sentiment
5. 24h change (lowest priority)

🚨 **CRITICAL TRADING SIGNAL RULES**:
- If market_direction is "bearish" → action_recommendation MUST be "sell"
- If market_direction is "bullish" → action_recommendation MUST be "buy"
- If market_direction is "neutral" → action_recommendation MUST be "hold"
- situation_type should match market_direction (bearish/bullish/neutral)

RESPOND WITH ONLY A JSON OBJECT:
{{
  "importance": <1-10 integer>,
  "is_important": <true/false>,
  "importance_score": <0.0-1.0 float>,
  "reasoning": "<brief explanation prioritizing short-term trend>",
  "market_direction": "bullish"/"bearish"/"neutral",
  "situation_type": "bullish"/"bearish"/"neutral",
  "action_recommendation": "buy"/"sell"/"hold",
  "trading_direction": "long"/"short"/"neutral",
  "confidence": <0.0-1.0 float>,
  "keywords": ["<keyword1>", "<keyword2>", "<keyword3>"]
}}

Rate importance:
1-3: Low activity, 4-6: Moderate activity, 7-10: High activity

CRITICAL: Return ONLY valid JSON, no markdown, no explanations.
"""

        return prompt

    def _apply_subtle_performance_adjustment(self, result: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """
        성과 데이터 기반 미세 조정 (기존 로직에 살짝만 반영)

        Args:
            result: LLM 평가 결과
            symbol: 거래 심볼

        Returns:
            Dict[str, Any]: 미세 조정된 평가 결과
        """
        try:
            # 성과 데이터가 없으면 조정하지 않음
            if not self.symbol_performance:
                return result

            # 심볼별 성과 가져오기
            symbol_perf = self.symbol_performance.get(symbol, {})
            if not symbol_perf:
                return result

            symbol_success_rate = symbol_perf.get("success_rate", 0.3)

            # 기존 중요도 가져오기
            original_importance = result.get("importance", 5)

            # 성과가 좋은 심볼은 중요도를 살짝 상향 조정 (최대 +1)
            if symbol_success_rate > 0.5:  # 50% 이상 승률
                adjustment = 1
            elif symbol_success_rate > 0.4:  # 40% 이상 승률
                adjustment = 0
            else:  # 40% 미만 승률
                adjustment = -1

            # 조정된 중요도 (1~10 범위 유지)
            adjusted_importance = min(10, max(1, original_importance + adjustment))

            # 결과에 반영 (기존 필드는 그대로 유지)
            result["importance"] = adjusted_importance

            # 성과 정보 추가
            result["symbol_success_rate"] = symbol_success_rate
            result["performance_adjusted"] = True

            if adjustment != 0:
                logger.debug(f"[{symbol}] 성과 기반 미세 조정: 중요도 {original_importance}→{adjusted_importance} (승률: {symbol_success_rate*100:.1f}%)")

            return result

        except Exception as e:
            logger.error(f"성과 미세 조정 중 오류: {e}")
            return result

    def _generate_importance_with_llm(self, prompt: str, mode: str = "execution") -> Dict[str, Any]:
        """
        LLM을 사용하여 중요도 평가 생성 (vLLM 큐 사용)

        Args:
            prompt: 중요도 평가 프롬프트

        Returns:
            Dict[str, Any]: 중요도 평가 결과
        """
        # 현재 처리 중인 심볼 추출 (실제 프롬프트 구조에 맞게 수정)
        symbol = "UNKNOWN"

        # 1. "Symbol: {symbol}" 패턴 확인
        if "Symbol:" in prompt:
            symbol_lines = [line for line in prompt.split('\n') if "Symbol:" in line]
            if symbol_lines:
                symbol_line = symbol_lines[0]
                if "Symbol:" in symbol_line:
                    symbol = symbol_line.split("Symbol:")[1].strip()
                    logger.info(f"InCA 에이전트 심볼 추출 (Symbol:): {symbol}")

        # 2. "THIS ANALYSIS IS SPECIFICALLY FOR {symbol}" 패턴 확인
        elif "THIS ANALYSIS IS SPECIFICALLY FOR" in prompt:
            import re
            match = re.search(r'THIS ANALYSIS IS SPECIFICALLY FOR (\w+)', prompt)
            if match:
                symbol = match.group(1)
                logger.info(f"InCA 에이전트 심볼 추출 (ANALYSIS FOR): {symbol}")

        # 3. 기존 "Analysis target:" 패턴 (호환성 유지)
        elif "Analysis target:" in prompt:
            symbol_line = [line for line in prompt.split('\n') if "Analysis target:" in line]
            if symbol_line:
                symbol = symbol_line[0].split("Analysis target:")[1].strip()
                logger.info(f"InCA 에이전트 심볼 추출 (Analysis target:): {symbol}")

        try:
            # vLLM 큐를 통해 요청 처리
            logger.info(f"InCA({symbol}) vLLM 큐에 요청 추가")

            # 요청 파라미터 (🔧 Temperature 0.1 → 0.4로 증가)
            params = {
                'max_tokens': 512,
                'temperature': 0.4,  # 0.1 → 0.4 (다양한 응답 생성)
                'timeout': 600  # 45초 → 600초 (10분)
            }

            # vLLM 큐를 통해 요청 (모드별 에이전트 타입 사용)
            agent_type = f"inca_{mode}"  # inca_execution 또는 inca_learning
            priority = 1 if mode == "execution" else 4  # 실행: 1, 학습: 4

            result = queue_vllm_request(
                agent_type=agent_type,
                symbol=symbol,
                prompt=prompt,
                params=params,
                priority=priority,
                timeout=600.0,  # 30초 → 600초 (10분)
                metadata={"mode": mode}  # 모드 정보 전달
            )

            if result and result.get('finish_reason') != 'timeout':
                # 응답에서 JSON 추출
                response_text = result.get('text', '')
                logger.info(f"InCA({symbol}) vLLM 응답 수신: {len(response_text)}자")

                # JSON 파싱
                json_data = extract_json_from_text(response_text)

                if json_data:
                    # 🎯 LLM 직접 실행 결과 저장 (나중에 evaluate_importance에서 사용)
                    self._last_llm_result = json_data.copy()
                    logger.info(f"🎯 [{symbol}] LLM 직접 실행 결과 저장: market_direction={json_data.get('market_direction', 'neutral')}")
                    # 기본값 설정
                    importance = json_data.get("importance", 5)
                    is_important = json_data.get("is_important", importance >= 5)

                    # 🎯 신호 방향 추출 (SELA 포지션 관리용)
                    # market_direction을 action_recommendation으로 매핑
                    market_direction = json_data.get("market_direction", "neutral")
                    if market_direction == "bullish":
                        action_recommendation = "buy"
                    elif market_direction == "bearish":
                        action_recommendation = "sell"
                    else:
                        action_recommendation = "hold"

                    # market_direction을 situation_type으로 매핑
                    situation_type = json_data.get("situation_type", market_direction)

                    # action_recommendation과 situation_type을 기반으로 신호 방향 결정
                    signal_direction = "neutral"  # 기본값
                    if action_recommendation == "buy" or situation_type == "bullish":
                        signal_direction = "bullish"
                    elif action_recommendation == "sell" or situation_type == "bearish":
                        signal_direction = "bearish"
                    elif action_recommendation == "hold" or situation_type == "neutral":
                        signal_direction = "neutral"

                    return {
                        "importance": importance,
                        "is_important": is_important,
                        "importance_score": importance / 10.0,
                        "situation_type": situation_type,
                        "reasoning": json_data.get("reasoning", "LLM 분석 결과"),
                        "action_recommendation": action_recommendation,
                        "trading_direction": json_data.get("trading_direction", "neutral"),
                        "signal_direction": signal_direction,  # 🎯 SELA용 신호 방향 추가
                        "keywords": json_data.get("keywords", []),
                        "raw_response": response_text[:200] + "..." if len(response_text) > 200 else response_text
                    }
                else:
                    logger.warning(f"InCA({symbol}) JSON 추출 실패, 기본값 사용")
            else:
                logger.warning(f"InCA({symbol}) vLLM 요청 실패 또는 타임아웃")

        except Exception as e:
            logger.error(f"InCA({symbol}) vLLM 큐 요청 중 오류: {e}")

        # 기본값 반환 (오류 시)
        return {
            "importance": 5,
            "is_important": False,
            "importance_score": 0.5,
            "situation_type": "neutral",
            "reasoning": "vLLM 요청 실패로 기본값 사용",
            "action_recommendation": "hold",
            "trading_direction": "neutral",
            "keywords": [],
            "error": "vLLM 큐 요청 실패"
        }

    def _calculate_learning_weight(self, is_success: bool, profit_loss_value: float, profit_loss_pct: float) -> float:
        """
        학습 가중치 계산

        성공/실패 여부와 손익 정도에 따라 학습 가중치를 계산합니다.
        가중치가 높을수록 패턴의 중요도가 높아집니다.

        Args:
            is_success: 성공 여부
            profit_loss_value: 손익 (절대값)
            profit_loss_pct: 손익률 (%)

        Returns:
            float: 학습 가중치 (0.1 ~ 2.0)
        """
        # 기본 가중치
        base_weight = 1.0

        # 성공/실패에 따른 가중치 조정
        if is_success:
            # 성공한 경우 손익률에 따라 가중치 증가
            success_factor = min(abs(profit_loss_pct) / 5.0, 1.0)  # 최대 5% 수익률까지 고려
            weight = base_weight + success_factor

            # 매우 높은 수익률인 경우 추가 가중치
            if profit_loss_pct > 10.0:
                weight += 0.5

            # 절대 손익 금액이 큰 경우 추가 가중치
            if abs(profit_loss_value) > 50.0:  # 50 USDT 이상
                weight += 0.2
        else:
            # 실패한 경우 손익률에 따라 가중치 감소
            failure_factor = min(abs(profit_loss_pct) / 10.0, 0.9)  # 최대 10% 손실률까지 고려
            weight = base_weight - failure_factor

            # 손실이 매우 적은 경우 (거의 성공에 가까운 실패)
            if profit_loss_pct > -1.0:
                weight = base_weight * 0.8  # 기본 가중치의 80%

        # 가중치 범위 제한 (0.1 ~ 2.0)
        weight = max(0.1, min(2.0, weight))

        logger.info(f"학습 가중치 계산: 성공={is_success}, 손익={profit_loss_value:.2f}, 손익률={profit_loss_pct:.2f}%, 가중치={weight:.2f}")
        return weight

    def _adjust_importance_threshold(self, is_success: bool, importance_evaluation: Dict[str, Any]) -> None:
        """
        중요도 임계값 동적 조정

        성공/실패 결과에 따라 중요도 임계값을 동적으로 조정합니다.

        Args:
            is_success: 성공 여부
            importance_evaluation: 중요도 평가 결과
        """
        # 현재 중요도 점수
        importance_score = importance_evaluation.get("importance_score", 0.0)

        # 성공/실패 카운트 업데이트
        if is_success:
            self.success_count += 1
        else:
            self.failure_count += 1

        # 최근 결과 업데이트 (최대 100개)
        self.recent_results.append({
            "success": is_success,
            "importance_score": importance_score,
            "timestamp": int(time.time())
        })

        if len(self.recent_results) > 100:
            self.recent_results = self.recent_results[-100:]

        # 최소 10개 이상의 결과가 있을 때만 임계값 조정
        if len(self.recent_results) >= 10:
            # 최근 성공률 계산
            recent_success_count = sum(1 for r in self.recent_results if r["success"])
            recent_success_rate = recent_success_count / len(self.recent_results)

            # 성공한 거래의 평균 중요도 점수
            successful_scores = [r["importance_score"] for r in self.recent_results if r["success"]]
            avg_successful_score = sum(successful_scores) / len(successful_scores) if successful_scores else 0.5

            # 실패한 거래의 평균 중요도 점수
            failed_scores = [r["importance_score"] for r in self.recent_results if not r["success"]]
            avg_failed_score = sum(failed_scores) / len(failed_scores) if failed_scores else 0.5

            # 임계값 조정 로직
            # 실패한 거래의 중요도가 성공한 거래보다 높으면 임계값 조정에 활용
            if avg_failed_score > avg_successful_score and failed_scores:
                logger.info(f"실패한 거래의 중요도({avg_failed_score:.2f})가 성공한 거래({avg_successful_score:.2f})보다 높음")
            if recent_success_rate < 0.4:  # 성공률이 낮은 경우
                # 임계값을 성공한 거래의 평균 중요도 쪽으로 조정
                new_threshold = self.importance_threshold * (1 - self.learning_rate) + avg_successful_score * self.learning_rate
            elif recent_success_rate > 0.7:  # 성공률이 높은 경우
                # 임계값을 약간 높여서 더 중요한 상황에만 집중
                new_threshold = self.importance_threshold * (1 - self.learning_rate) + (self.importance_threshold * 1.1) * self.learning_rate
            else:  # 적절한 성공률인 경우
                # 현재 임계값 유지하되 약간의 조정
                new_threshold = self.importance_threshold * (1 - self.learning_rate * 0.5) + avg_successful_score * (self.learning_rate * 0.5)

            # 임계값 범위 제한 (0.2 ~ 0.8)
            new_threshold = max(0.2, min(0.8, new_threshold))

            # 변화가 크지 않은 경우에만 적용 (급격한 변화 방지)
            if abs(new_threshold - self.importance_threshold) < 0.1:
                old_threshold = self.importance_threshold
                self.importance_threshold = new_threshold
                logger.info(f"중요도 임계값 조정: {old_threshold:.2f} → {new_threshold:.2f} (성공률: {recent_success_rate:.2f})")

    def _find_similar_pattern_index(self, new_pattern: Dict[str, Any]) -> Optional[int]:
        """
        유사한 패턴 인덱스 찾기

        새로운 패턴과 유사한 기존 패턴의 인덱스를 찾습니다.

        Args:
            new_pattern: 새로운 패턴

        Returns:
            Optional[int]: 유사한 패턴의 인덱스 또는 None
        """
        if not self.patterns_db:
            return None

        # 현재 심볼
        symbol = new_pattern.get("symbol", "UNKNOWN")

        # 같은 심볼의 패턴만 필터링
        symbol_patterns = [(i, p) for i, p in enumerate(self.patterns_db) if p.get("symbol") == symbol]

        if not symbol_patterns:
            return None

        # 유사도 점수 계산
        similarity_scores = []

        for idx, pattern in symbol_patterns:
            # 1. 캔들 패턴 유사도 (24시간 변동률 대신)
            current_pattern = new_pattern.get("candle_pattern", "neutral")
            pattern_candle = pattern.get("candle_pattern", "neutral")
            pattern_match = 1.0 if current_pattern == pattern_candle else 0.0

            # 2. 거래량 유사도
            current_volume = new_pattern.get("volume_24h", 0)
            pattern_volume = pattern.get("volume_24h", 0)
            volume_ratio = min(current_volume, pattern_volume) / max(current_volume, pattern_volume) if max(current_volume, pattern_volume) > 0 else 0

            # 3. 상황 유형 유사도
            situation_match = 1.0 if new_pattern.get("situation_type") == pattern.get("situation_type") else 0.0

            # 4. 중요도 유사도
            importance_diff = abs(new_pattern.get("importance", 5) - pattern.get("importance", 5)) / 10.0

            # 5. 행동 추천 유사도
            action_match = 1.0 if new_pattern.get("action_recommendation") == pattern.get("action_recommendation") else 0.0

            # 종합 유사도 점수 (캔들 패턴 기반, 낮을수록 유사)
            similarity_score = (
                (1 - pattern_match) * 0.4 +  # 캔들 패턴 (40%)
                (1 - volume_ratio) * 0.2 +  # 거래량 (20%)
                (1 - situation_match) * 0.2 +  # 상황 유형 (20%)
                importance_diff * 0.1 +  # 중요도 (10%)
                (1 - action_match) * 0.1  # 행동 추천 (10%)
            )

            similarity_scores.append((idx, similarity_score))

        # 가장 유사한 패턴 찾기 (유사도 점수가 낮을수록 유사)
        similarity_scores.sort(key=lambda x: x[1])

        # 유사도 임계값 (0.3 이하면 유사하다고 판단)
        if similarity_scores and similarity_scores[0][1] <= 0.3:
            return similarity_scores[0][0]

        return None

    def _update_existing_pattern(self, pattern_idx: int, new_pattern: Dict[str, Any], learning_weight: float) -> None:
        """
        기존 패턴 업데이트

        유사한 기존 패턴을 새로운 패턴과 가중 평균하여 업데이트합니다.

        Args:
            pattern_idx: 업데이트할 패턴의 인덱스
            new_pattern: 새로운 패턴
            learning_weight: 학습 가중치
        """
        existing_pattern = self.patterns_db[pattern_idx]

        # 기존 패턴의 가중치 (없으면 1.0)
        existing_weight = existing_pattern.get("learning_weight", 1.0)

        # 업데이트 횟수 증가
        update_count = existing_pattern.get("update_count", 0) + 1

        # 가중 평균 계산을 위한 가중치 정규화
        total_weight = existing_weight + learning_weight
        existing_ratio = existing_weight / total_weight
        new_ratio = learning_weight / total_weight

        # 수치형 필드 가중 평균 계산
        for field in ["importance", "base_importance", "news_factor", "volatility_factor",
                     "social_factor", "importance_score"]:
            if field in new_pattern and field in existing_pattern:
                existing_value = existing_pattern[field]
                new_value = new_pattern[field]

                # 가중 평균 계산
                updated_value = existing_value * existing_ratio + new_value * new_ratio
                existing_pattern[field] = updated_value

        # 성공/실패 정보 업데이트
        if "strategy_success" in new_pattern:
            # 성공 횟수 업데이트
            success_count = existing_pattern.get("success_count", 0)
            if new_pattern["strategy_success"]:
                success_count += 1
            existing_pattern["success_count"] = success_count

            # 성공률 업데이트
            existing_pattern["success_rate"] = success_count / update_count

        # 손익 이력 업데이트
        if "profit_loss" in new_pattern:
            profit_loss_history = existing_pattern.get("profit_loss_history", [])
            profit_loss_history.append(new_pattern["profit_loss"])

            # 최대 20개 유지
            if len(profit_loss_history) > 20:
                profit_loss_history = profit_loss_history[-20:]

            existing_pattern["profit_loss_history"] = profit_loss_history

            # 평균 손익 계산
            existing_pattern["avg_profit_loss"] = sum(profit_loss_history) / len(profit_loss_history)

        # 메타데이터 업데이트
        existing_pattern["update_count"] = update_count
        existing_pattern["last_updated"] = int(time.time())
        existing_pattern["learning_weight"] = (existing_weight + learning_weight) / 2  # 가중치 평균

    def _add_to_patterns_db(self, market_data: Dict[str, Any], importance_evaluation: Dict[str, Any],
                         strategy_result: Dict[str, Any] = None, learning_weight: float = 1.0) -> None:
        """
        패턴 데이터베이스에 새로운 패턴 추가

        성공/실패 모두에서 학습하며, 손익 정도에 따른 가중치를 부여합니다.
        가중치가 높을수록 패턴의 중요도가 높아집니다.

        Args:
            market_data: 시장 데이터
            importance_evaluation: 중요도 평가 결과
            strategy_result: 전략 실행 결과 (선택 사항)
            learning_weight: 학습 가중치 (0.0 ~ 2.0, 기본값 1.0)
        """
        try:
            # 새로운 패턴 생성
            new_pattern = {
                "symbol": market_data.get("symbol", "UNKNOWN"),
                "timestamp": datetime.now().isoformat(),
                "current_price": market_data.get("current_price", 0),
                "candle_pattern": self._get_candle_pattern_summary(market_data.get('recent_candles', [])),
                "volume_24h": market_data.get("volume_24h", 0),
                "importance": importance_evaluation.get("importance", 1),
                "situation_type": importance_evaluation.get("situation_type", "unknown"),
                "keywords": importance_evaluation.get("keywords", []),
                "action_recommendation": importance_evaluation.get("action_recommendation", "hold"),
                "learning_weight": learning_weight,  # 학습 가중치 추가
                "learned_at": int(time.time())  # 학습 시간 추가
            }

            # 전략 결과가 있는 경우 추가 정보 포함
            if strategy_result:
                # 성공 여부 및 손익 정보 추가
                new_pattern.update({
                    "strategy_success": strategy_result.get("success", False),
                    "profit_loss": strategy_result.get("profit_loss", 0),
                    "profit_loss_pct": strategy_result.get("profit_loss_pct", 0),
                    "strategy_type": strategy_result.get("strategy_type", "unknown"),
                    "holding_time": strategy_result.get("holding_time", 0),
                    "entry_price": strategy_result.get("entry_price", 0),
                    "exit_price": strategy_result.get("exit_price", 0)
                })

                # 실패 원인 추가 (실패한 경우)
                if not strategy_result.get("success", False) and "failure_reason" in strategy_result:
                    new_pattern["failure_reason"] = strategy_result["failure_reason"]

            # 유사한 패턴이 있는지 확인
            similar_pattern_idx = self._find_similar_pattern_index(new_pattern)

            if similar_pattern_idx is not None:
                # 유사한 패턴이 있으면 업데이트 (가중 평균)
                self._update_existing_pattern(similar_pattern_idx, new_pattern, learning_weight)
                logger.info(f"유사한 패턴 업데이트: {new_pattern['symbol']} ({learning_weight:.2f})")
            else:
                # 새로운 패턴 추가
                self.patterns_db.append(new_pattern)
                logger.info(f"새로운 패턴 추가: {new_pattern['symbol']} ({learning_weight:.2f})")

            # 데이터베이스 크기 제한 (최대 1000개)
            if len(self.patterns_db) > 1000:
                # 가중치가 낮은 패턴부터 제거
                self.patterns_db.sort(key=lambda p: p.get("learning_weight", 0))
                self.patterns_db = self.patterns_db[-1000:]
                logger.info("패턴 데이터베이스 크기 제한 적용 (1000개)")

            # 주기적으로 상태 저장 (10개마다)
            if len(self.patterns_db) % 10 == 0:
                self.save_state()
                logger.info(f"패턴 데이터베이스 저장 완료 (패턴 수: {len(self.patterns_db)})")

        except Exception as e:
            logger.error(f"패턴 데이터베이스 추가 중 오류 발생: {e}")
            logger.error(traceback.format_exc())

    def evaluate_position_importance(self, symbol: str, position: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        포지션 보유 중요도 평가 (SELA 포지션 관리용)

        Args:
            symbol: 코인 심볼
            position: 현재 포지션 정보
            market_data: 현재 시장 데이터

        Returns:
            Dict: 포지션 중요도 평가 결과
        """
        try:
            # 🔧 market_data None 체크 추가
            if market_data is None:
                logger.error(f"❌ [{symbol}] evaluate_position_importance: market_data가 None입니다")
                return {
                    "importance_score": 0.5,
                    "situation_type": "neutral",
                    "action_recommendation": "hold",
                    "signal_direction": "neutral",
                    "reasoning": "market_data 없음",
                    "error": "market_data is None"
                }
            logger.info(f"🔍 [{symbol}] InCA 포지션 중요도 평가 시작")

            # 포지션 정보 추출
            position_direction = position.get("direction", "unknown")
            entry_price = position.get("entry_price", 0)
            current_price = market_data.get("price", 0)
            hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60

            # 실제 PnL 계산 (포지션에서 가져오거나 직접 계산)
            current_pnl = position.get("current_pnl_pct", 0)
            if current_pnl == 0 and entry_price > 0 and current_price > 0:
                # 포지션에 PnL이 없으면 직접 계산
                if position_direction == "long":
                    current_pnl = (current_price - entry_price) / entry_price * 100
                elif position_direction == "short":
                    current_pnl = (entry_price - current_price) / entry_price * 100
                logger.info(f"🔍 [{symbol}] PnL 직접 계산: {current_pnl:.2f}% (진입가: ${entry_price:.2f}, 현재가: ${current_price:.2f})")

            # 🔧 포지션 평가용 보수적 시장 분석 (강제 중립 우선)
            market_evaluation = self._conservative_market_analysis_for_position(market_data)

            # 🎯 포지션 방향과 시장 신호 비교
            market_signal = market_evaluation.get("signal_direction", "neutral")
            action_recommendation = market_evaluation.get("action_recommendation", "hold")
            situation_type = market_evaluation.get("situation_type", "neutral")

            # 포지션 유지/종료 판단
            should_close = False
            hold_importance = 7  # 기본값: 보유 권장

            # 🔧 손절 기준 비활성화 (5분 + 0.2% 수익 조건만 사용)
            # stop_loss_threshold = -0.15  # 기존 손절 기준 비활성화
            # 🎯 극단적 손실 감지 (SELA에게 알려주기만 함)
            stop_loss_threshold = -2.0  # -2.0% 극단적 손절 기준
            if current_pnl <= stop_loss_threshold:
                should_close = False  # 🔧 InCA는 클로즈 결정 안함
                hold_importance = 2
                situation_type = "extreme_loss"  # 🔧 시장 상황만 알려줌
                logger.warning(f"🚨 [{symbol}] 극단적 손실 감지: {current_pnl:.2f}% <= {stop_loss_threshold}%")
            elif position_direction == "long":
                if market_signal == "bearish" or action_recommendation == "sell":
                    should_close = False  # 🔧 InCA는 클로즈 결정 안함
                    hold_importance = 3
                    situation_type = "market_bearish"  # 🔧 시장 상황만 알려줌
                elif market_signal == "neutral":
                    should_close = False
                    hold_importance = 6
                    situation_type = "market_neutral"  # 🔧 시장 상황만 알려줌
                else:  # bullish
                    should_close = False
                    hold_importance = 8
                    situation_type = "market_bullish"  # 🔧 시장 상황만 알려줌

            elif position_direction == "short":
                if market_signal == "bullish" or action_recommendation == "buy":
                    should_close = False  # 🔧 InCA는 클로즈 결정 안함
                    hold_importance = 3
                    situation_type = "market_bullish"  # 🔧 시장 상황만 알려줌
                elif market_signal == "neutral":
                    should_close = False
                    hold_importance = 6
                    situation_type = "market_neutral"  # 🔧 시장 상황만 알려줌
                else:  # bearish
                    should_close = False
                    hold_importance = 8
                    situation_type = "market_bearish"  # 🔧 시장 상황만 알려줌

            # PnL 기반 조정
            if current_pnl > 2.0:  # 2% 이상 수익
                hold_importance = max(3, hold_importance - 2)  # 익절 고려
                situation_type = "high_profit"  # 🔧 시장 상황만 알려줌
            elif current_pnl < -1.5:  # 1.5% 이상 손실
                should_close = False  # 🔧 InCA는 클로즈 결정 안함
                hold_importance = 3
                situation_type = "high_loss"  # 🔧 시장 상황만 알려줌

            # 보유 시간 기반 조정 (단기 트레이딩)
            if hold_time_minutes > 30:
                hold_importance = max(3, hold_importance - 1)

            result = {
                "hold_importance": hold_importance,
                "situation_type": situation_type,
                "should_close": should_close,
                "signal_direction": market_signal,  # 🎯 SELA용 신호 방향
                "reasoning": f"포지션: {position_direction}, 시장신호: {market_signal}, PnL: {current_pnl:.2f}%, 보유시간: {hold_time_minutes:.1f}분",
                "confidence": market_evaluation.get("importance_score", 0.7),
                "market_evaluation": market_evaluation,
                "pnl_pct": current_pnl,  # 🎯 5분마다 0.2% 수익 실현용
                "hold_time_minutes": hold_time_minutes,  # 🎯 5분마다 0.2% 수익 실현용
                "timestamp": int(time.time())
            }

            logger.info(f"🔍 [{symbol}] InCA 포지션 평가 완료: {situation_type} (중요도: {hold_importance}, 신호: {market_signal})")
            return result

        except Exception as e:
            logger.error(f"❌ [{symbol}] InCA 포지션 평가 실패: {e}")
            # Fallback - 기본 PnL과 보유시간 계산 시도
            try:
                current_pnl = position.get("current_pnl_pct", 0)
                hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60
            except:
                current_pnl = 0
                hold_time_minutes = 0

            return {
                "hold_importance": 5,
                "situation_type": "neutral",
                "should_close": False,
                "signal_direction": "neutral",
                "reasoning": f"평가 실패: {str(e)}",
                "confidence": 0.3,
                "pnl_pct": current_pnl,  # 🎯 5분마다 0.2% 수익 실현용
                "hold_time_minutes": hold_time_minutes,  # 🎯 5분마다 0.2% 수익 실현용
                "error": str(e),
                "timestamp": int(time.time())
            }

    def evaluate_importance_sync(self, market_data: Dict[str, Any], mode: str = "execution") -> Dict[str, Any]:
        """
        동기 버전의 중요도 평가 (vLLM 큐 우회)

        Args:
            market_data: 시장 데이터
            mode: 실행 모드

        Returns:
            Dict: 중요도 평가 결과
        """
        try:
            # 🔧 market_data None 체크 추가
            if market_data is None:
                logger.error("❌ evaluate_importance_sync: market_data가 None입니다")
                return {
                    "importance_score": 0.5,
                    "situation_type": "neutral",
                    "action_recommendation": "hold",
                    "signal_direction": "neutral",
                    "reasoning": "market_data 없음",
                    "error": "market_data is None"
                }
            symbol = market_data.get("symbol", "UNKNOWN")
            logger.info(f"🔍 [{symbol}] InCA 동기 중요도 평가 시작")

            # 프롬프트 생성 (기존 로직 재사용)
            prompt = self._create_importance_prompt(market_data)

            # vLLM 클라이언트 직접 호출 (큐 우회)
            if hasattr(self, 'llm_client') and self.llm_client:
                # 고정 세션 사용 (에이전트별 1개)
                session_id = f"inca_position_evaluation_{symbol}"

                # 직접 vLLM 호출
                response = self.llm_client.generate_fast(
                    prompt=prompt,
                    max_tokens=512,
                    timeout=30,  # 30초 타임아웃
                    session_id=session_id
                )

                if response and response.get("text"):
                    # JSON 파싱
                    result = self._parse_importance_response(response["text"])
                    logger.info(f"✅ [{symbol}] InCA 동기 평가 완료: 중요도 {result.get('importance_score', 0.0)}")
                    return result
                else:
                    logger.warning(f"⚠️ [{symbol}] InCA 동기 평가 응답 없음")

            # 실패 시 기본 로직 사용
            logger.warning(f"⚠️ [{symbol}] InCA 동기 평가 실패, 기본 로직 사용")
            return self._fallback_importance_logic(symbol, market_data, {})

        except Exception as e:
            logger.error(f"❌ [{symbol}] InCA 동기 평가 중 오류: {e}")
            return self._fallback_importance_logic(symbol, market_data, {})

    def _conservative_market_analysis_for_position(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        포지션 평가용 캔들 기반 시장 분석 (24시간 데이터 제거)

        Args:
            market_data: 시장 데이터

        Returns:
            Dict: 캔들 패턴 기반 시장 평가 결과
        """
        try:
            # 🔧 market_data None 체크 추가
            if market_data is None:
                logger.error("❌ market_data가 None입니다")
                return {
                    "importance_score": 0.5,
                    "situation_type": "neutral",
                    "action_recommendation": "hold",
                    "signal_direction": "neutral",
                    "reasoning": "market_data 없음",
                    "conservative_analysis": True,
                    "error": "market_data is None"
                }

            symbol = market_data.get("symbol", "UNKNOWN")
            logger.info(f"🔍 [{symbol}] 포지션 평가용 캔들 기반 시장 분석")

            # 기본값: 중립
            situation_type = "neutral"
            action_recommendation = "hold"
            signal_direction = "neutral"
            importance_score = 0.5

            # 🎯 캔들 패턴 분석 (24시간 데이터 무시) - 강화된 로직
            candles = market_data.get('recent_candles', market_data.get('candles', []))
            candle_data = ""
            if len(candles) >= 3:  # 최소 3개 캔들로 완화
                recent_candles = candles[-3:]  # 최근 3개 캔들
                close_prices = []

                for i, candle in enumerate(recent_candles):
                    if isinstance(candle, list) and len(candle) >= 5:
                        # 바이낸스 캔들 형식: [timestamp, open, high, low, close, volume, ...]
                        open_price = float(candle[1])
                        high_price = float(candle[2])
                        low_price = float(candle[3])
                        close_price = float(candle[4])
                        volume = float(candle[5])

                        close_prices.append(close_price)
                    elif isinstance(candle, dict):
                        close_price = candle.get('close', 0)
                        high_price = candle.get('high', 0)
                        low_price = candle.get('low', 0)

                        close_prices.append(close_price)

                # 🎯 고급 기술적 분석 (추세선, 패턴, 볼륨 분석)
                if len(close_prices) >= 3:
                    # 기본 추세 계산
                    first_price = close_prices[0]
                    last_price = close_prices[-1]
                    price_change_pct = ((last_price - first_price) / first_price) * 100 if first_price > 0 else 0

                    # 🔧 고급 패턴 분석
                    pattern_analysis = self._analyze_advanced_patterns(recent_candles, close_prices)

                    # 🔧 볼륨 분석
                    volume_analysis = self._analyze_volume_pattern(recent_candles)

                    # 🔧 추세선 분석
                    trendline_analysis = self._analyze_trendlines(close_prices, [high_price for _, high_price, _, _ in recent_candles], [low_price for _, _, low_price, _ in recent_candles])

                    # 연속성 확인 (상승/하락 캔들 개수)
                    up_candles = 0
                    down_candles = 0
                    for i in range(1, len(close_prices)):
                        if close_prices[i] > close_prices[i-1]:
                            up_candles += 1
                        elif close_prices[i] < close_prices[i-1]:
                            down_candles += 1

                    # 🔧 강화된 추세 강도 계산 (패턴, 볼륨, 추세선 고려)
                    base_strength = abs(price_change_pct) * 10  # 기본 강도
                    pattern_boost = pattern_analysis.get('strength_multiplier', 1.0)
                    volume_boost = volume_analysis.get('strength_multiplier', 1.0)
                    trendline_boost = trendline_analysis.get('strength_multiplier', 1.0)

                    trend_strength = base_strength * pattern_boost * volume_boost * trendline_boost
                    trend_strength = min(100, trend_strength)

                    # 🔧 강화된 추세 방향 결정 (패턴과 추세선 고려)
                    pattern_signal = pattern_analysis.get('signal', 'neutral')
                    trendline_signal = trendline_analysis.get('signal', 'neutral')
                    volume_signal = volume_analysis.get('signal', 'neutral')

                    # 종합 신호 판단
                    bullish_signals = sum([
                        price_change_pct > 0.05,
                        up_candles > down_candles,
                        pattern_signal == 'bullish',
                        trendline_signal == 'bullish',
                        volume_signal == 'bullish'
                    ])

                    bearish_signals = sum([
                        price_change_pct < -0.05,
                        down_candles > up_candles,
                        pattern_signal == 'bearish',
                        trendline_signal == 'bearish',
                        volume_signal == 'bearish'
                    ])

                    if bullish_signals >= 3:
                        situation_type = "bullish"
                        action_recommendation = "buy"
                        signal_direction = "bullish"
                        importance_score = 0.7
                        logger.info(f"🔍 [{symbol}] 캔들 상승 패턴: {pattern_analysis.get('reason', '분석 없음')}")
                    elif bearish_signals >= 3:
                        situation_type = "bearish"
                        action_recommendation = "sell"
                        signal_direction = "bearish"
                        importance_score = 0.7
                        logger.info(f"🔍 [{symbol}] 캔들 하락 패턴: {pattern_analysis.get('reason', '분석 없음')}")
                    else:
                        logger.info(f"🔍 [{symbol}] 캔들 중립 패턴: {pattern_analysis.get('reason', '분석 없음')}")
                else:
                    # 캔들 데이터가 부족한 경우 가격 변동률로 대체 분석
                    logger.warning(f"🔍 [{symbol}] 캔들 데이터 부족 ({len(candles)}개), 가격 변동률 분석으로 대체")

                    # 단기 가격 변동률 확인
                    short_term_change = market_data.get('short_term_change_pct', 0)
                    if abs(short_term_change) > 0.5:  # 0.5% 이상 변동
                        if short_term_change > 0:
                            situation_type = "bullish"
                            action_recommendation = "buy"
                            signal_direction = "bullish"
                            importance_score = 0.6
                            logger.info(f"🔍 [{symbol}] 가격 상승 신호: {short_term_change:.2f}%")
                        else:
                            situation_type = "bearish"
                            action_recommendation = "sell"
                            signal_direction = "bearish"
                            importance_score = 0.6
                            logger.info(f"🔍 [{symbol}] 가격 하락 신호: {short_term_change:.2f}%")
                    else:
                        logger.info(f"🔍 [{symbol}] 가격 변동 미미 (중립 처리): {short_term_change:.2f}%")

            return {
                "importance_score": importance_score,
                "situation_type": situation_type,
                "action_recommendation": action_recommendation,
                "signal_direction": signal_direction,
                "reasoning": f"캔들 패턴 분석: {pattern_analysis.get('reason', '데이터 부족') if 'pattern_analysis' in locals() else '데이터 부족'}",
                "conservative_analysis": True
            }

        except Exception as e:
            logger.error(f"❌ [{symbol}] 캔들 기반 시장 분석 실패: {e}")
            return {
                "importance_score": 0.5,
                "situation_type": "neutral",
                "action_recommendation": "hold",
                "signal_direction": "neutral",
                "reasoning": f"분석 실패: {str(e)}",
                "conservative_analysis": True,
                "error": str(e)
            }

    def _analyze_candle_patterns(self, candles: list) -> Dict[str, str]:
        """
        캔들 패턴 분석 (단타용) - 강화된 분석 로직

        Args:
            candles: 캔들 데이터 리스트

        Returns:
            Dict: 패턴 분석 결과
        """
        try:
            # 캔들 데이터 검증 강화
            if not candles:
                error_msg = "❌ 캔들 데이터 없음 - 분석 불가능"
                logger.error(error_msg)
                raise ValueError(error_msg)

            if len(candles) < 2:  # 🔧 조건 완화: 3개 → 2개
                error_msg = f"❌ 캔들 데이터 부족 ({len(candles)}개, 최소 2개 필요) - 실행 중단"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 사용 가능한 캔들 수에 따라 분석 범위 조정
            analysis_count = min(len(candles), 5)
            recent_candles = candles[-analysis_count:]

            # 연속 상승/하락 캔들 감지
            consecutive_green = 0
            consecutive_red = 0
            close_prices = []
            valid_candles_count = 0

            for candle in recent_candles:
                try:
                    # 캔들 데이터 파싱 (리스트 또는 딕셔너리 형태 모두 지원)
                    if isinstance(candle, list):
                        open_price = float(candle[1])
                        close_price = float(candle[4])
                        volume = float(candle[5]) if len(candle) > 5 else 0
                    else:
                        open_price = float(candle.get('open', candle.get('o', 0)))
                        close_price = float(candle.get('close', candle.get('c', 0)))
                        volume = float(candle.get('volume', candle.get('v', 0)))

                    # 유효성 검증
                    if open_price <= 0 or close_price <= 0:
                        logger.warning(f"유효하지 않은 캔들 가격: open={open_price}, close={close_price}")
                        continue

                    close_prices.append(close_price)
                    valid_candles_count += 1

                    if close_price > open_price:  # 상승 캔들
                        consecutive_green += 1
                        consecutive_red = 0
                    else:  # 하락 캔들
                        consecutive_red += 1
                        consecutive_green = 0

                except (ValueError, KeyError, IndexError) as e:
                    logger.warning(f"캔들 데이터 파싱 오류: {e}")
                    continue

            # 유효한 캔들이 있는지 최종 검증
            if valid_candles_count < 2:
                error_msg = f"❌ 유효한 캔들 부족 ({valid_candles_count}개, 최소 2개 필요) - 실행 중단"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 가격 추세 분석 - 최소 2개 캔들로 완화
            if len(close_prices) >= 2:
                first_price = close_prices[0]
                last_price = close_prices[-1]
                price_trend = ((last_price - first_price) / first_price) * 100 if first_price > 0 else 0

                # 패턴 판단 (연속 캔들 + 가격 추세) - 임계값 완화
                if consecutive_red >= 2 or price_trend < -0.3:
                    strength = "강한" if consecutive_red >= 3 or price_trend < -0.5 else "약한"
                    return {"signal": "bearish", "reason": f"{consecutive_red}연속 하락 캔들 ({strength}), 추세: {price_trend:.2f}%"}
                elif consecutive_green >= 2 or price_trend > 0.3:
                    strength = "강한" if consecutive_green >= 3 or price_trend > 0.5 else "약한"
                    return {"signal": "bullish", "reason": f"{consecutive_green}연속 상승 캔들 ({strength}), 추세: {price_trend:.2f}%"}
                else:
                    return {"signal": "neutral", "reason": f"혼재 패턴 (상승:{consecutive_green}, 하락:{consecutive_red}), 추세: {price_trend:.2f}%"}
            else:
                return {"signal": "neutral", "reason": f"가격 데이터 부족 (유효 캔들: {valid_candles_count}개)"}

        except Exception as e:
            logger.error(f"캔들 패턴 분석 오류: {e}")
            return {"signal": "neutral", "reason": f"분석 오류: {str(e)}"}

    def _get_candle_pattern_summary(self, candles: list) -> str:
        """캔들 패턴 요약 (패턴 저장용)"""
        try:
            if len(candles) >= 3:
                pattern_result = self._analyze_candle_patterns(candles)
                return f"{pattern_result.get('signal', 'neutral')}_{pattern_result.get('reason', 'unknown').replace(' ', '_')}"
            else:
                return "insufficient_data"
        except Exception as e:
            return f"error_{str(e).replace(' ', '_')}"

    def evaluate_strategy(self, symbol: str, strategy: Dict[str, Any],
                         reasoning_card: Dict[str, Any], market_data: Dict[str, Any],
                         past_performances: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        학습 루프에서 전략 평가 (우선순위 4 - 학습 루프에서 InCA가 첫 번째)

        Args:
            symbol: 코인 심볼
            strategy: 평가할 전략
            reasoning_card: HiAR의 사고 카드
            market_data: 시장 데이터
            past_performances: 과거 성과 데이터

        Returns:
            Dict[str, Any]: 평가 결과
        """
        try:
            # 프롬프트 생성
            prompt = self._create_strategy_evaluation_prompt(
                symbol, strategy, reasoning_card, market_data, past_performances
            )

            # vLLM 큐를 통해 요청 (우선순위 4 - 학습 루프에서 InCA가 첫 번째)
            result = queue_vllm_request(
                agent_type="inca_learning",
                symbol=symbol,
                prompt=prompt,
                params={'max_tokens': 512, 'temperature': 0.4, 'timeout': 600},  # 0.1 → 0.4 (다양한 응답)
                priority=4,  # 학습 루프: InCA(4) → SELA(5) → HiAR(6)
                timeout=600.0  # 30초 → 600초 (10분)
            )

            if result and result.get('finish_reason') != 'timeout':
                # JSON 추출 및 파싱
                evaluation_text = result.get('text', '')
                evaluation_data = extract_json_from_text(evaluation_text)

                if evaluation_data:
                    logger.info(f"{symbol} 전략 평가 완료 (학습 루프)")
                    return evaluation_data
                else:
                    logger.warning(f"{symbol} 전략 평가 JSON 파싱 실패, 기본 평가 사용")
                    return self._create_fallback_evaluation(strategy)
            else:
                logger.warning(f"{symbol} 전략 평가 타임아웃, 기본 평가 사용")
                return self._create_fallback_evaluation(strategy)

        except Exception as e:
            logger.error(f"{symbol} 전략 평가 중 오류: {e}")
            logger.error(traceback.format_exc())
            return self._create_fallback_evaluation(strategy)

    def _calculate_virtual_trade_pnl(self, symbol: str, strategy: Dict[str, Any], market_data: Dict[str, Any]) -> float:
        """
        🔥 가상거래 PnL 계산 (핵심 수정!)

        Args:
            symbol: 코인 심볼
            strategy: 전략 데이터
            market_data: 시장 데이터

        Returns:
            float: 예상 PnL 퍼센트 (-100.0 ~ 100.0)
        """
        try:
            # 전략 정보 추출
            strategy_type = strategy.get('strategy_type', 'none')
            target_profit = strategy.get('target_profit', 1.0)
            stop_loss = strategy.get('stop_loss', -1.0)
            confidence = strategy.get('confidence', 0.5)

            # 🎯 캔들 패턴 기반 PnL 계산 (24시간 데이터 제거)
            candles = market_data.get('recent_candles', market_data.get('candles', []))
            base_pnl = 0.0

            if len(candles) >= 3:
                # 캔들 패턴 분석
                pattern_result = self._analyze_candle_patterns(candles)
                pattern_signal = pattern_result.get('signal', 'neutral')

                if strategy_type == 'buy':
                    # 매수 전략: 상승 패턴 시 수익
                    if pattern_signal == 'bullish':
                        base_pnl = min(target_profit, 1.0)  # 최대 1% 수익 가정
                    elif pattern_signal == 'bearish':
                        base_pnl = max(stop_loss, -0.5)  # 최대 0.5% 손실
                    else:
                        base_pnl = 0.1  # 중립 시 소폭 수익

                elif strategy_type == 'sell':
                    # 매도 전략: 하락 패턴 시 수익
                    if pattern_signal == 'bearish':
                        base_pnl = min(target_profit, 1.0)  # 최대 1% 수익 가정
                    elif pattern_signal == 'bullish':
                        base_pnl = max(stop_loss, -0.5)  # 최대 0.5% 손실
                    else:
                        base_pnl = 0.1  # 중립 시 소폭 수익

                else:  # none or hold
                    # 중립 전략: 패턴에 관계없이 소폭 수익
                    base_pnl = 0.2

            else:
                # 캔들 데이터 부족 시 기본값
                base_pnl = 0.1

            # 🔥 신뢰도 기반 조정
            confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 ~ 1.0
            adjusted_pnl = base_pnl * confidence_multiplier

            # 🔥 캔들 기반 거래량 조정
            if len(candles) >= 2:
                try:
                    # 최근 2개 캔들의 거래량 비교
                    recent_volume = float(candles[-1][5]) if isinstance(candles[-1], list) else candles[-1].get('volume', 0)
                    prev_volume = float(candles[-2][5]) if isinstance(candles[-2], list) else candles[-2].get('volume', 0)

                    if prev_volume > 0:
                        volume_ratio = recent_volume / prev_volume
                        if volume_ratio > 2.0:  # 거래량 2배 증가
                            adjusted_pnl *= 1.3
                        elif volume_ratio > 1.5:  # 거래량 50% 증가
                            adjusted_pnl *= 1.1
                        elif volume_ratio < 0.5:  # 거래량 50% 감소
                            adjusted_pnl *= 0.9
                except (ValueError, KeyError, IndexError):
                    pass  # 거래량 데이터 오류 시 무시

            # 🔥 노이즈 추가 (현실적 변동성)
            import random
            noise = random.uniform(-0.2, 0.2)  # ±0.2% 노이즈
            final_pnl = adjusted_pnl + noise

            # 범위 제한
            final_pnl = max(-10.0, min(10.0, final_pnl))

            logger.debug(f"[{symbol}] 가상거래 PnL 계산: {strategy_type} → {final_pnl:.2f}% (기본: {base_pnl:.2f}%, 신뢰도: {confidence:.2f})")

            return final_pnl

        except Exception as e:
            logger.error(f"가상거래 PnL 계산 실패: {e}")
            return 0.0

    def _create_strategy_evaluation_prompt(self, symbol: str, strategy: Dict[str, Any],
                                         reasoning_card: Dict[str, Any], market_data: Dict[str, Any],
                                         past_performances: List[Dict[str, Any]]) -> str:
        """전략 평가 프롬프트 생성 (가상거래 PnL 포함)"""

        # 🔥 가상거래 PnL 계산
        virtual_pnl = self._calculate_virtual_trade_pnl(symbol, strategy, market_data)

        # 과거 성과 요약
        performance_summary = ""
        if past_performances:
            win_count = sum(1 for p in past_performances if p.get("win", False))
            total_count = len(past_performances)
            avg_pnl = sum(p.get("pnl_percentage", 0) for p in past_performances) / total_count
            performance_summary = f"과거 성과: {win_count}/{total_count} 승률 ({win_count/total_count*100:.1f}%), 평균 수익률: {avg_pnl:.2f}%"

        # 🔧 다양한 필드명 지원으로 시장 데이터 추출
        current_price = (
            market_data.get('price') or
            market_data.get('current_price') or
            market_data.get('lastPrice') or
            0
        )

        # 🚫 24시간 변동률 제거 (단타에서는 불필요)
        # 캔들 패턴 분석 결과만 사용
        candles = market_data.get('recent_candles', market_data.get('candles', []))

        # 🔧 디버깅: 캔들 데이터 상태 로깅
        logger.warning(f"  - recent_candles 개수: {len(market_data.get('recent_candles', []))}")
        logger.warning(f"  - candles 개수: {len(market_data.get('candles', []))}")
        logger.warning(f"  - 최종 candles 개수: {len(candles)}")

        candle_analysis = ""
        if len(candles) >= 2:  # 🔧 조건 완화: 3개 → 2개
            pattern_result = self._analyze_candle_patterns(candles)
            candle_analysis = f"캔들 패턴: {pattern_result.get('signal', 'neutral')} - {pattern_result.get('reason', '분석 없음')}"
        elif len(candles) == 1:
            # 🔧 캔들 1개라도 기본 분석 제공
            candle = candles[0]
            open_price = 0
            close_price = 0

            if isinstance(candle, dict):
                # 딕셔너리 형태
                open_price = float(candle.get('open', 0))
                close_price = float(candle.get('close', 0))
            elif isinstance(candle, list) and len(candle) >= 5:
                # 리스트 형태 [timestamp, open, high, low, close, ...]
                open_price = float(candle[1])
                close_price = float(candle[4])

            if open_price > 0 and close_price > 0:
                if close_price > open_price:
                    candle_analysis = "캔들 패턴: bullish - 단일 양봉"
                elif close_price < open_price:
                    candle_analysis = "캔들 패턴: bearish - 단일 음봉"
                else:
                    candle_analysis = "캔들 패턴: neutral - 단일 도지"
            else:
                candle_analysis = "캔들 패턴: neutral - 단일 캔들 (데이터 부족)"
        else:
            # 🔧 캔들 데이터가 없어도 현재가 기반 분석 제공
            candle_analysis = f"캔들 패턴: neutral - 현재가 ${current_price} 기준 분석"

        volume = (
            market_data.get('volume') or
            market_data.get('volume_24h') or
            market_data.get('quoteVolume') or
            0
        )

        # 🔥 캔들 데이터 디버깅 로그 추가
        logger.warning(f"🔍 [{symbol}] 전략 평가 프롬프트 캔들 데이터 상태:")
        logger.warning(f"  - market_data 키들: {list(market_data.keys())}")
        logger.warning(f"  - recent_candles 개수: {len(market_data.get('recent_candles', []))}")
        logger.warning(f"  - candles 개수: {len(market_data.get('candles', []))}")
        logger.warning(f"  - 최종 candles 개수: {len(candles)}")
        if len(candles) > 0:
            logger.warning(f"  - 첫 캔들 샘플: {candles[0] if candles else 'None'}")

        prompt = f"""
{symbol} 전략 평가 요청

=== 시장 데이터 ===
현재가: ${current_price}
캔들 분석: {candle_analysis}
거래량: {volume:,.0f}

=== 평가할 전략 ===
전략 ID: {strategy.get('strategy_id', 'unknown')}
전략 유형: {strategy.get('strategy_type', 'unknown')}
진입 조건: {strategy.get('entry_condition', 'unknown')}
목표 수익률: {strategy.get('target_profit', 1.0)}%
손절 기준: {strategy.get('stop_loss', -1.0)}%

=== 🔥 가상거래 시뮬레이션 결과 ===
예상 PnL: {virtual_pnl:.2f}%
시뮬레이션 성공: {"예" if virtual_pnl > 0 else "아니오"}

=== HiAR 사고 분석 ===
결론: {reasoning_card.get('conclusion', 'unknown')}
신뢰도: {reasoning_card.get('verification', {}).get('overall_score', 0):.2f}

=== 과거 성과 ===
{performance_summary if performance_summary else "과거 성과 데이터 없음"}

🔥 가상거래 시뮬레이션 결과를 중요하게 고려하여 전략을 평가하고 JSON 형식으로 응답하세요:

{{
  "overall_score": 0.75,
  "logic_score": 0.8,
  "market_alignment_score": 0.7,
  "risk_reward_score": 0.8,
  "historical_performance_score": 0.7,
  "virtual_trade_pnl": {virtual_pnl:.2f},
  "virtual_trade_success": {str(virtual_pnl > 0).lower()},
  "recommendation": "accept/consider/reject",
  "reasoning": "평가 근거 설명 (가상거래 결과 포함)",
  "strengths": ["강점1", "강점2"],
  "weaknesses": ["약점1", "약점2"],
  "expected_success_rate": 0.65
}}
"""
        return prompt

    def _create_fallback_evaluation(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """기본 평가 결과 생성 (가상거래 PnL 포함)"""
        # 🔥 기본 가상거래 PnL 계산
        strategy_type = strategy.get('strategy_type', 'none')
        confidence = strategy.get('confidence', 0.5)

        # 간단한 PnL 추정
        if strategy_type == 'buy':
            virtual_pnl = confidence * 1.0  # 신뢰도 기반 수익
        elif strategy_type == 'sell':
            virtual_pnl = confidence * 0.8
        else:
            virtual_pnl = confidence * 0.3

        # 🔥 가상거래 결과에 따른 점수 조정
        base_score = 0.5
        if virtual_pnl > 0.5:
            base_score = 0.6
        elif virtual_pnl > 0:
            base_score = 0.55

        return {
            "overall_score": base_score,
            "logic_score": 0.5,
            "market_alignment_score": 0.5,
            "risk_reward_score": 0.5,
            "historical_performance_score": 0.5,
            "virtual_trade_pnl": virtual_pnl,  # 🔥 가상거래 PnL 추가
            "virtual_trade_success": virtual_pnl > 0,  # 🔥 가상거래 성공 여부
            "recommendation": "accept" if virtual_pnl > 0.5 else "consider",
            "reasoning": f"LLM 평가 실패로 기본 평가 적용 (가상 PnL: {virtual_pnl:.2f}%)",
            "strengths": ["기본 전략 구조"] + (["가상거래 수익 예상"] if virtual_pnl > 0 else []),
            "weaknesses": ["평가 데이터 부족"] + (["가상거래 손실 예상"] if virtual_pnl <= 0 else []),
            "expected_success_rate": 0.6 if virtual_pnl > 0 else 0.4,
            "fallback": True
        }

    def evaluate_market_importance_direct(self, symbol: str, market_data: dict, news_data: dict) -> dict:
        """
        vLLM 큐를 우회하여 직접 시장 중요도 평가 (순차 실행용)

        Args:
            symbol: 심볼
            market_data: 시장 데이터
            news_data: 뉴스 데이터

        Returns:
            중요도 평가 결과
        """
        try:
            logger.info(f"InCA 직접 실행 시작: {symbol}")

            # 프롬프트 생성 (기존 로직 재사용)
            prompt = self._create_importance_prompt(market_data)

            # vLLM 클라이언트 직접 호출 (큐 우회)
            if hasattr(self, 'llm_client') and self.llm_client:
                # 고정 세션 사용 (에이전트별 1개)
                session_id = f"inca_agent_session"

                # 직접 vLLM 호출
                response = self.llm_client.generate_fast(
                    prompt=prompt,
                    max_tokens=512,
                    timeout=600,  # 60초 → 600초 (10분)
                    session_id=session_id
                )

                if response and response.get("text"):
                    # JSON 파싱
                    result = self._parse_importance_response(response["text"])
                    logger.info(f"InCA 직접 실행 완료: {symbol}, 중요도 {result.get('importance_score', 0.0)}")
                    return result
                else:
                    logger.warning(f"InCA 직접 실행 응답 없음: {symbol}")

            # 실패 시 기본 로직 사용
            logger.warning(f"InCA 직접 실행 실패, 기본 로직 사용: {symbol}")
            return self._fallback_importance_logic(symbol, market_data, news_data)

        except Exception as e:
            logger.error(f"InCA 직접 실행 중 오류: {e}")
            return self._fallback_importance_logic(symbol, market_data, news_data)

    def _fallback_importance_logic(self, symbol: str, market_data: dict, news_data: dict) -> dict:
        """InCA 실패 시 기본 로직 (캔들 패턴 기반)"""
        try:
            # 🚫 24시간 변동률 제거
            volume = market_data.get("volume_24h", 0)
            news_count = len(news_data.get("news", []))

            # 🎯 단기 추세 분석 (캔들 데이터 기반)
            candles = market_data.get("recent_candles", market_data.get("candles", []))
            short_term_trend = "neutral"
            trend_strength = 0

            if candles and len(candles) >= 3:
                recent_candles = candles[-3:]  # 최근 3개 캔들
                close_prices = []

                for i, candle in enumerate(recent_candles):
                    if isinstance(candle, list) and len(candle) >= 5:
                        close_prices.append(float(candle[4]))  # close price
                    elif isinstance(candle, dict):
                        close_prices.append(candle.get('close', 0))

                if len(close_prices) >= 2:
                    # 단기 추세 계산
                    first_price = close_prices[0]
                    last_price = close_prices[-1]
                    short_term_change = ((last_price - first_price) / first_price) * 100 if first_price > 0 else 0
                    trend_strength = abs(short_term_change) * 10

                    if short_term_change > 0.1:
                        short_term_trend = "bullish"
                    elif short_term_change < -0.1:
                        short_term_trend = "bearish"

            # 기본 중요도 계산
            importance_score = 0.5

            # 🎯 단기 추세 우선 고려
            if trend_strength > 30:  # 강한 추세
                importance_score += 0.3
            elif trend_strength > 15:  # 중간 추세
                importance_score += 0.2

            # 🚫 24h 변동률 제거 (단타에서는 불필요)
            # 캔들 패턴 기반 중요도만 사용

            if volume > 1000000000:
                importance_score += 0.1

            if news_count > 10:
                importance_score += 0.1

            importance_score = min(1.0, importance_score)

            # 🎯 LLM 직접 실행 결과 우선 사용 (982-988번 라인에서 이미 처리됨)
            # LLM이 이미 market_direction을 분석했으므로 그 결과를 존중
            if hasattr(self, '_last_llm_result') and self._last_llm_result:
                llm_market_direction = self._last_llm_result.get("market_direction", "neutral")
                if llm_market_direction == "bullish":
                    situation_type = "bullish"
                    action_recommendation = "buy"
                elif llm_market_direction == "bearish":
                    situation_type = "bearish"
                    action_recommendation = "sell"
                else:
                    situation_type = "neutral"
                    action_recommendation = "hold"
                logger.info(f"🎯 [{symbol}] LLM 직접 실행 결과 사용: {llm_market_direction} → {action_recommendation}")
            else:
                # LLM 결과가 없는 경우에만 기존 로직 사용
                if short_term_trend == "bearish" and trend_strength > 20:
                    situation_type = "bearish"
                    action_recommendation = "sell"
                elif short_term_trend == "bullish" and trend_strength > 20:
                    situation_type = "bullish"
                    action_recommendation = "buy"
                # 🎯 미세한 신호도 포착하는 강화된 로직
                elif abs(trend_strength) > 5:  # 5% 이상 변동 시 신호 생성
                    if trend_strength > 5:
                        situation_type = "weak_bullish"
                        action_recommendation = "buy"
                        importance_score = 0.6
                    else:
                        situation_type = "weak_bearish"
                        action_recommendation = "sell"
                        importance_score = 0.6
                    logger.info(f"🎯 [{symbol}] 약한 신호 포착: {situation_type} → {action_recommendation}")
                else:
                    # 정말 중립인 경우에만 중립 신호
                    situation_type = "neutral"
                    action_recommendation = "hold"
                logger.info(f"🎯 [{symbol}] 강화된 로직 사용: {situation_type} → {action_recommendation}")

            # 🎯 신호 강도 계산 (0.1 ~ 1.0)
            signal_strength = self._calculate_signal_strength(
                trend_strength, volume, news_count, candles
            )

            return {
                "importance_score": importance_score,
                "situation_type": situation_type,
                "action_recommendation": action_recommendation,
                "signal_strength": signal_strength,  # 새로운 필드
                "reasoning": f"캔들 기반 로직: 단기추세={short_term_trend}({trend_strength:.1f}), 거래량={volume:,.0f}, 뉴스={news_count}개, 신호강도={signal_strength:.2f}",
                "fallback": True
            }
        except Exception as e:
            logger.error(f"InCA 기본 로직 실패: {e}")
            return {
                "importance_score": 0.5,
                "situation_type": "neutral",
                "signal_strength": 0.1,
                "reasoning": f"완전 실패: {str(e)}",
                "fallback": True,
                "error": str(e)
            }

    def _calculate_signal_strength(self, trend_strength: float, volume: float,
                                 news_count: int, candles: list) -> float:
        """
        신호 강도 계산 (0.1 ~ 1.0)

        Args:
            trend_strength: 추세 강도
            volume: 거래량
            news_count: 뉴스 개수
            candles: 캔들 데이터

        Returns:
            float: 신호 강도 (0.1 ~ 1.0)
        """
        try:
            strength_factors = []

            # 1. 추세 강도 (0.0 ~ 0.4)
            trend_factor = min(0.4, abs(trend_strength) / 100)
            strength_factors.append(trend_factor)

            # 2. 거래량 (0.0 ~ 0.3)
            volume_factor = min(0.3, volume / 1000000) if volume > 0 else 0
            strength_factors.append(volume_factor)

            # 3. 뉴스 영향 (0.0 ~ 0.2)
            news_factor = min(0.2, news_count * 0.05)
            strength_factors.append(news_factor)

            # 4. 캔들 패턴 (0.0 ~ 0.1)
            candle_factor = 0.1 if len(candles) >= 5 else 0.05
            strength_factors.append(candle_factor)

            # 총 신호 강도 계산
            total_strength = sum(strength_factors)

            # 0.1 ~ 1.0 범위로 정규화
            normalized_strength = max(0.1, min(1.0, total_strength))

            return normalized_strength

        except Exception as e:
            logger.warning(f"신호 강도 계산 실패: {e}")
            return 0.5  # 기본값

    def _analyze_advanced_patterns(self, candles: list, close_prices: list) -> dict:
        """🔧 고급 캔들 패턴 분석"""
        try:
            if len(close_prices) < 3:
                return {"signal": "neutral", "description": "데이터 부족", "strength_multiplier": 1.0}

            # V자 반등 패턴 감지
            if len(close_prices) >= 5:
                # 하락 후 급상승 패턴
                mid_idx = len(close_prices) // 2
                first_half = close_prices[:mid_idx]
                second_half = close_prices[mid_idx:]

                # 첫 번째 절반이 하락, 두 번째 절반이 상승
                first_trend = (first_half[-1] - first_half[0]) / first_half[0] * 100
                second_trend = (second_half[-1] - second_half[0]) / second_half[0] * 100

                if first_trend < -0.2 and second_trend > 0.3:
                    return {
                        "signal": "bullish",
                        "description": f"V자 반등 패턴 (하락 {first_trend:.2f}% → 상승 {second_trend:.2f}%)",
                        "strength_multiplier": 2.0
                    }
                elif first_trend > 0.2 and second_trend < -0.3:
                    return {
                        "signal": "bearish",
                        "description": f"역V자 패턴 (상승 {first_trend:.2f}% → 하락 {second_trend:.2f}%)",
                        "strength_multiplier": 2.0
                    }

            # 연속 상승/하락 패턴
            consecutive_up = 0
            consecutive_down = 0
            for i in range(1, len(close_prices)):
                if close_prices[i] > close_prices[i-1]:
                    consecutive_up += 1
                    consecutive_down = 0
                elif close_prices[i] < close_prices[i-1]:
                    consecutive_down += 1
                    consecutive_up = 0

            if consecutive_up >= 3:
                return {
                    "signal": "bullish",
                    "description": f"연속 상승 패턴 ({consecutive_up}개 캔들)",
                    "strength_multiplier": 1.5
                }
            elif consecutive_down >= 3:
                return {
                    "signal": "bearish",
                    "description": f"연속 하락 패턴 ({consecutive_down}개 캔들)",
                    "strength_multiplier": 1.5
                }

            return {"signal": "neutral", "description": "명확한 패턴 없음", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"패턴 분석 실패: {e}")
            return {"signal": "neutral", "description": "분석 실패", "strength_multiplier": 1.0}

    def _analyze_volume_pattern(self, candles: list) -> dict:
        """🔧 볼륨 패턴 분석"""
        try:
            if len(candles) < 3:
                return {"signal": "neutral", "description": "볼륨 데이터 부족", "strength_multiplier": 1.0}

            volumes = []
            price_changes = []

            for i, candle in enumerate(candles):
                if isinstance(candle, list) and len(candle) >= 6:
                    volume = float(candle[5])  # volume
                    open_price = float(candle[1])
                    close_price = float(candle[4])
                    price_change = (close_price - open_price) / open_price * 100

                    volumes.append(volume)
                    price_changes.append(price_change)
                elif isinstance(candle, dict):
                    volume = candle.get('volume', 0)
                    open_price = candle.get('open', 0)
                    close_price = candle.get('close', 0)
                    price_change = (close_price - open_price) / open_price * 100 if open_price > 0 else 0

                    volumes.append(volume)
                    price_changes.append(price_change)

            if len(volumes) < 2:
                return {"signal": "neutral", "description": "볼륨 데이터 부족", "strength_multiplier": 1.0}

            # 최근 볼륨과 이전 볼륨 비교
            recent_volume = volumes[-1]
            avg_volume = sum(volumes[:-1]) / len(volumes[:-1])
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

            # 최근 가격 변화
            recent_price_change = price_changes[-1] if price_changes else 0

            # 볼륨 급증 + 상승 = 강한 매수 신호
            if volume_ratio > 2.0 and recent_price_change > 0.1:
                return {
                    "signal": "bullish",
                    "description": f"볼륨 급증 상승 ({volume_ratio:.1f}배, +{recent_price_change:.2f}%)",
                    "strength_multiplier": 1.8
                }
            # 볼륨 급증 + 하락 = 강한 매도 신호
            elif volume_ratio > 2.0 and recent_price_change < -0.1:
                return {
                    "signal": "bearish",
                    "description": f"볼륨 급증 하락 ({volume_ratio:.1f}배, {recent_price_change:.2f}%)",
                    "strength_multiplier": 1.8
                }
            # 볼륨 증가 + 상승
            elif volume_ratio > 1.5 and recent_price_change > 0.05:
                return {
                    "signal": "bullish",
                    "description": f"볼륨 증가 상승 ({volume_ratio:.1f}배)",
                    "strength_multiplier": 1.3
                }
            # 볼륨 감소 + 상승 = 약한 신호
            elif volume_ratio < 0.7 and recent_price_change > 0:
                return {
                    "signal": "neutral",
                    "description": f"볼륨 감소 상승 (약한 신호)",
                    "strength_multiplier": 0.8
                }

            return {"signal": "neutral", "description": "일반적인 볼륨 패턴", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"볼륨 분석 실패: {e}")
            return {"signal": "neutral", "description": "볼륨 분석 실패", "strength_multiplier": 1.0}

    def _analyze_trendlines(self, close_prices: list, high_prices: list, low_prices: list) -> dict:
        """🔧 추세선 분석 (지지/저항선 돌파 감지)"""
        try:
            if len(close_prices) < 4:
                return {"signal": "neutral", "description": "추세선 분석 데이터 부족", "strength_multiplier": 1.0}

            # 최근 가격 움직임
            current_price = close_prices[-1]
            prev_price = close_prices[-2]
            price_change = (current_price - prev_price) / prev_price * 100

            # 최근 고점/저점 찾기
            recent_high = max(high_prices[-3:]) if len(high_prices) >= 3 else max(close_prices[-3:])
            recent_low = min(low_prices[-3:]) if len(low_prices) >= 3 else min(close_prices[-3:])

            # 이전 고점/저점
            prev_high = max(high_prices[:-3]) if len(high_prices) > 3 else recent_high
            prev_low = min(low_prices[:-3]) if len(low_prices) > 3 else recent_low

            # 저항선 돌파 감지
            if current_price > recent_high and price_change > 0.2:
                return {
                    "signal": "bullish",
                    "description": f"저항선 돌파 (${recent_high:.2f} → ${current_price:.2f})",
                    "strength_multiplier": 1.7
                }

            # 지지선 이탈 감지
            if current_price < recent_low and price_change < -0.2:
                return {
                    "signal": "bearish",
                    "description": f"지지선 이탈 (${recent_low:.2f} → ${current_price:.2f})",
                    "strength_multiplier": 1.7
                }

            # 상승 추세선 (연속적인 고점 상승)
            if len(close_prices) >= 5:
                highs = [max(close_prices[i:i+2]) for i in range(len(close_prices)-1)]
                if len(highs) >= 3 and highs[-1] > highs[-2] > highs[-3]:
                    return {
                        "signal": "bullish",
                        "description": "상승 추세선 형성",
                        "strength_multiplier": 1.4
                    }

                # 하락 추세선 (연속적인 저점 하락)
                lows = [min(close_prices[i:i+2]) for i in range(len(close_prices)-1)]
                if len(lows) >= 3 and lows[-1] < lows[-2] < lows[-3]:
                    return {
                        "signal": "bearish",
                        "description": "하락 추세선 형성",
                        "strength_multiplier": 1.4
                    }

            return {"signal": "neutral", "description": "명확한 추세선 없음", "strength_multiplier": 1.0}

        except Exception as e:
            logger.error(f"추세선 분석 실패: {e}")
            return {"signal": "neutral", "description": "추세선 분석 실패", "strength_multiplier": 1.0}

    def _save_to_vector_db(self, experience: Dict[str, Any]):
        """경험을 벡터 DB에 저장"""
        if not self.vector_collection:
            return

        try:
            import json
            import random
            from datetime import datetime

            # 경험 데이터에서 정보 추출
            symbol = experience.get('symbol', 'UNKNOWN')
            timestamp = experience.get('timestamp', int(time.time()))
            prediction = experience.get('prediction', {})
            trade_result = experience.get('trade_result', {})
            market_data = experience.get('market_data', {})

            # 문서 텍스트 생성 (검색 가능한 형태)
            action = prediction.get('action_recommendation', 'hold')
            direction = prediction.get('trading_direction', 'neutral')
            confidence = prediction.get('confidence', 0.5)
            importance = prediction.get('importance', 5)
            profit = trade_result.get('profit', 0)
            success = trade_result.get('success', False)

            # 시장 데이터 추출 (24시간 변동률 제거)
            price = market_data.get('price', market_data.get('current_price', 0))
            volume = market_data.get('volume_24h', 0)

            # 캔들 패턴 분석 결과 추가
            candles = market_data.get('recent_candles', [])
            candle_pattern = "neutral"
            if len(candles) >= 3:
                pattern_result = self._analyze_candle_patterns(candles)
                candle_pattern = pattern_result.get('signal', 'neutral')

            # 고유한 문서 텍스트 생성
            document_text = f"""
INCA_EXPERIENCE_{symbol}_{timestamp}_{random.randint(1000, 9999)}:
DECISION: {action}_{direction} confidence_{confidence:.2f} importance_{importance}
MARKET: price_${price:.6f} pattern_{candle_pattern} volume_{volume:,.0f}
OUTCOME: profit_{profit:.3f}% success_{success}
CONTEXT: {symbol} {datetime.fromtimestamp(timestamp).strftime('%Y%m%d_%H%M')}
CLASSIFICATION: {action}_{direction}_{confidence:.1f}_{importance}_{profit:.2f}
"""

            # 메타데이터 준비 (ChromaDB 호환)
            metadata = {
                'symbol': symbol,
                'timestamp': timestamp,
                'action_recommendation': action,
                'trading_direction': direction,
                'profit': float(profit),
                'success': bool(success),
                'confidence': float(confidence),
                'importance': int(importance),
                'source': 'inca_feedback',
                # JSON 직렬화
                'prediction_json': json.dumps(prediction),
                'trade_result_json': json.dumps(trade_result),
                'market_data_json': json.dumps(market_data)
            }

            # 벡터 DB에 저장
            experience_id = f"inca_feedback_{symbol}_{timestamp}"
            self.vector_collection.add(
                ids=[experience_id],
                metadatas=[metadata],
                documents=[document_text]
            )

            logger.info(f"InCA 경험을 벡터 DB에 저장 완료: {experience_id}")

        except Exception as e:
            logger.warning(f"InCA 벡터 DB 저장 중 오류: {e}")

    def _is_meaningful_experience(self, profit_loss_pct: float, feedback: Dict[str, Any]) -> bool:
        """경험이 학습에 의미가 있는지 판단 (완화된 기준)"""
        try:
            # 🔧 모든 실제 거래 결과를 의미 있는 경험으로 저장
            trade_result = feedback.get('trade_result', {})
            status = trade_result.get('status', '')

            # 1. 실제 거래나 가상거래 완료인 경우 (우선 조건)
            if status in ['completed', 'virtual_completed', 'closed']:
                logger.info(f"의미 있는 경험: 거래 완료 (status={status}, PnL={profit_loss_pct:.3f}%)")
                return True

            # 2. 수익률이 있는 경우 (기준 완화: 0.001% 이상)
            if abs(profit_loss_pct) > 0.001:  # 0.001% 이상 변동 (기존 0.01%에서 완화)
                logger.info(f"의미 있는 경험: 수익률 변동 (PnL={profit_loss_pct:.3f}%)")
                return True

            # 3. 포지션 종료 관련 피드백인 경우
            symbol = feedback.get('symbol', 'UNKNOWN')
            if 'position' in str(feedback).lower() or 'close' in str(feedback).lower():
                logger.info(f"의미 있는 경험: 포지션 관련 피드백 ({symbol})")
                return True

            # 4. 높은 신뢰도의 예측인 경우 (기준 완화: 0.6 이상)
            strategy_result = feedback.get('strategy_result', {})
            prediction = feedback.get('prediction', {})
            confidence = prediction.get('confidence', strategy_result.get('confidence', 0.5))
            if confidence >= 0.6:  # 기존 0.7에서 0.6으로 완화
                logger.info(f"의미 있는 경험: 높은 신뢰도 (confidence={confidence:.2f})")
                return True

            # 5. 중요도가 높은 시장 상황인 경우 (기준 완화: 6 이상)
            importance_evaluation = feedback.get('importance_evaluation', {})
            importance = importance_evaluation.get('importance', 5)
            if importance >= 6:  # 기존 7에서 6으로 완화
                logger.info(f"의미 있는 경험: 높은 중요도 (importance={importance})")
                return True

            # 6. 학습 루프 평가에서 극단적 점수인 경우
            if status == 'learning_evaluation':
                evaluation_details = trade_result.get('evaluation_details', {})
                overall_score = evaluation_details.get('overall_score', 0.5)
                if overall_score <= 0.3 or overall_score >= 0.7:  # 매우 좋거나 나쁜 평가
                    logger.info(f"의미 있는 경험: 극단적 평가 (score={overall_score:.2f})")
                    return True

            # 7. 🔧 기본적으로 모든 피드백을 저장 (학습 데이터 확보)
            logger.info(f"의미 있는 경험: 기본 저장 정책 (PnL={profit_loss_pct:.3f}%, status={status})")
            return True

        except Exception as e:
            logger.warning(f"의미 있는 경험 판단 중 오류: {e}")
            return True  # 오류 시 저장

    def _search_similar_experiences(self, symbol: str, current_situation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """벡터 DB에서 유사한 과거 경험 검색"""
        if not self.vector_collection:
            return []

        try:
            from trading.hybrid_architecture.utils.chromadb_utils import safe_query_collection

            # 현재 상황에서 검색 쿼리 생성
            action = current_situation.get('action_recommendation', 'hold')
            direction = current_situation.get('trading_direction', 'neutral')
            importance = current_situation.get('importance', 5)
            price_change = current_situation.get('price_change', 0)

            # 시장 상황 분류
            if abs(price_change) < 1:
                volatility = "low_volatility"
            elif abs(price_change) < 3:
                volatility = "medium_volatility"
            else:
                volatility = "high_volatility"

            # 검색 쿼리 텍스트 생성
            query_text = f"{symbol} {action} {direction} importance_{importance} {volatility} trading experience"

            # 벡터 DB에서 검색
            results = safe_query_collection(
                collection=self.vector_collection,
                query_texts=[query_text],
                n_results=10,  # 최대 10개 결과
                where={"symbol": symbol}  # 같은 심볼만 검색
            )

            similar_experiences = []
            for result in results:
                try:
                    metadata = result.get('metadata', {})
                    distance = result.get('distance', 1.0)

                    # 거리를 유사도로 변환 (0~1 범위)
                    similarity = max(0, 1.0 - (distance / 2.0))

                    # 경험 데이터 재구성
                    experience = {
                        'symbol': metadata.get('symbol'),
                        'timestamp': metadata.get('timestamp'),
                        'prediction': {
                            'action_recommendation': metadata.get('action_recommendation'),
                            'trading_direction': metadata.get('trading_direction'),
                            'confidence': metadata.get('confidence'),
                            'importance': metadata.get('importance')
                        },
                        'trade_result': {
                            'profit': metadata.get('profit'),
                            'success': metadata.get('success')
                        }
                    }

                    similar_experiences.append({
                        'experience': experience,
                        'similarity': similarity,
                        'source': 'vector_db'
                    })

                    logger.info(f"벡터 DB 유사 경험: {symbol} 유사도 {similarity:.3f}, 수익 {metadata.get('profit', 0):.2f}%")

                except Exception as e:
                    logger.warning(f"벡터 DB 결과 처리 중 오류: {e}")
                    continue

            # 유사도 순으로 정렬
            similar_experiences.sort(key=lambda x: x['similarity'], reverse=True)

            logger.info(f"{symbol} 벡터 DB에서 {len(similar_experiences)}개 유사 경험 발견")
            return similar_experiences[:5]  # 상위 5개만 반환

        except Exception as e:
            logger.warning(f"벡터 DB 검색 중 오류: {e}")
            return []

    def _apply_historical_experience_adjustment(self, result: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """과거 경험을 바탕으로 중요도 평가 결과 조정"""
        try:
            symbol = market_data.get("symbol", "UNKNOWN")

            # 현재 상황 정보 추출
            current_situation = {
                'action_recommendation': result.get('action_recommendation', 'hold'),
                'trading_direction': result.get('trading_direction', 'neutral'),
                'importance': result.get('importance', 5),
                'candle_pattern': self._get_candle_pattern_summary(market_data.get('recent_candles', []))
            }

            # 유사한 과거 경험 검색
            similar_experiences = self._search_similar_experiences(symbol, current_situation)

            if not similar_experiences:
                logger.info(f"{symbol} 과거 경험 없음, 원본 결과 유지")
                return result

            # 과거 경험 분석
            total_experiences = len(similar_experiences)
            successful_experiences = [exp for exp in similar_experiences
                                    if exp['experience']['trade_result']['success']]
            success_rate = len(successful_experiences) / total_experiences if total_experiences > 0 else 0.5

            # 평균 수익률 계산
            profits = [exp['experience']['trade_result']['profit'] for exp in similar_experiences]
            avg_profit = sum(profits) / len(profits) if profits else 0

            logger.info(f"{symbol} 과거 경험 분석: {total_experiences}개 경험, 성공률 {success_rate:.1%}, 평균 수익 {avg_profit:.2f}%")

            # 🎯 과거 성과 기반 조정
            original_importance = result.get('importance', 5)
            original_confidence = result.get('confidence', 0.5)

            # 성공률이 매우 낮은 경우 (30% 미만) 신호 약화 (방향은 유지)
            if success_rate < 0.3 and total_experiences >= 3:
                logger.warning(f"{symbol} 성공률 낮음 ({success_rate:.1%}), 신호 강도 조정 (방향 유지)")

                # 중요도 감소
                adjusted_importance = max(1, original_importance - 2)

                # 신뢰도 감소
                adjusted_confidence = original_confidence * 0.7

                # 🎯 LLM이 명확한 신호를 감지했다면 방향은 유지하고 강도만 조정
                original_situation = result.get('situation_type', 'neutral')
                if original_situation in ['bullish', 'bearish'] and success_rate == 0.0:
                    # 성공률 0%여도 LLM의 명확한 신호는 존중하되 신뢰도만 더 낮춤
                    adjusted_confidence = original_confidence * 0.5
                    logger.warning(f"{symbol} 성공률 0%이지만 LLM 신호({original_situation}) 방향 유지, 신뢰도만 감소")
                elif success_rate < 0.1 and original_situation == 'neutral':
                    # 🎯 성공률 기준 완화: 10% 미만일 때만 중립 유지 (기존 20% → 10%)
                    result['action_recommendation'] = 'hold'
                    result['trading_direction'] = 'neutral'
                    result['situation_type'] = 'neutral'
                    logger.warning(f"{symbol} 성공률 매우 낮음 ({success_rate:.1%}), 중립 신호 유지")
                elif success_rate < 0.2:
                    # 🎯 성공률 10-20%: 약한 신호라도 생성 (기존에는 중립으로 강제)
                    logger.info(f"{symbol} 성공률 낮음 ({success_rate:.1%})이지만 약한 신호 생성 허용")

            # 성공률이 높은 경우 (70% 이상) 신호 강화
            elif success_rate > 0.7 and total_experiences >= 3:
                logger.info(f"{symbol} 성공률 높음 ({success_rate:.1%}), 신호 강화")

                # 중요도 증가
                adjusted_importance = min(10, original_importance + 1)

                # 신뢰도 증가
                adjusted_confidence = min(1.0, original_confidence * 1.2)

            else:
                # 보통 성과인 경우 약간 조정
                adjusted_importance = original_importance
                adjusted_confidence = original_confidence * (0.8 + success_rate * 0.4)

            # 결과 업데이트
            result['importance'] = int(adjusted_importance)
            result['importance_score'] = adjusted_importance / 10.0
            result['confidence'] = adjusted_confidence
            result['historical_analysis'] = {
                'total_experiences': total_experiences,
                'success_rate': success_rate,
                'avg_profit': avg_profit,
                'adjustment_applied': True
            }

            # 추론에 과거 경험 정보 추가
            if 'reasoning' in result:
                result['reasoning'] += f" (과거 유사 상황 {total_experiences}개: 성공률 {success_rate:.1%}, 평균 수익 {avg_profit:.2f}%)"

            logger.info(f"{symbol} 과거 경험 기반 조정 완료: 중요도 {original_importance}→{adjusted_importance}, 신뢰도 {original_confidence:.2f}→{adjusted_confidence:.2f}")

            return result

        except Exception as e:
            logger.warning(f"과거 경험 기반 조정 실패: {e}")
            return result