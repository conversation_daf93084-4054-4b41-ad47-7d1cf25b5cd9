2025-06-26 18:00:43 - hybrid_simulator - INFO - 로깅 시스템 초기화 완료 - 타임스탬프 테스트
2025-06-26 18:00:43 - hybrid_simulator - INFO - ===== 하이브리드 거래 시스템 초기화 =====
2025-06-26 18:00:43 - hybrid_simulator - INFO - 시스템 인코딩: utf-8
2025-06-26 18:00:43 - hybrid_simulator - INFO - 표준 출력 인코딩: utf-8
2025-06-26 18:00:43 - hybrid_simulator - INFO - 환경 변수 PYTHONIOENCODING: utf-8
2025-06-26 18:00:43 - hybrid_simulator - INFO - .env 파일 로드 완료
2025-06-26 18:00:43 - hybrid_simulator - INFO - OPENAI_API_KEY 환경 변수가 설정되어 있습니다.
2025-06-26 18:00:43 - hybrid_simulator - INFO - vLLM 환경 변수 확인: USE_VLLM=true, 파싱 결과=True
2025-06-26 18:00:43 - hybrid_simulator - INFO - vLLM 사용 여부: 커맨드라인=True, 환경변수=True, 최종=True
2025-06-26 18:00:43 - hybrid_simulator - INFO - vLLM 모델 설정: 환경변수=Qwen/Qwen3-14B-AWQ, 커맨드라인=Qwen/Qwen3-14B-AWQ, 최종=Qwen/Qwen3-14B-AWQ
2025-06-26 18:00:43 - hybrid_simulator - INFO - vLLM 서버 사용: http://localhost:8001/v1, 모델: Qwen/Qwen3-14B-AWQ
2025-06-26 18:00:43 - hybrid_simulator - INFO - VLLMClientFactory 클래스 임포트 성공
2025-06-26 18:00:43 - hybrid_simulator - INFO - vLLM 클라이언트 초기화 시작: URL=http://localhost:8001/v1, 모델=Qwen/Qwen3-14B-AWQ
2025-06-26 18:00:43 - models.vllm_client_factory - INFO - Using timeout from environment: 600 seconds
2025-06-26 18:00:43 - models.vllm_client_factory - INFO - Creating enhanced VLLM client (model: Qwen/Qwen3-14B-AWQ, timeout: 600s, max_tokens: 8192)
2025-06-26 18:00:43 - models.vllm_client_enhanced - INFO - Qwen3 모델 감지됨: Qwen/Qwen3-14B-AWQ
2025-06-26 18:00:45 - models.vllm_client_enhanced - INFO - VLLM 서버 설정 확인: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': 1750928445, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-6868c92fa5484fcf9eb100867808ddc9', 'object': 'model_permission', 'created': 1750928445, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-26 18:00:45 - models.vllm_client_enhanced - INFO - VLLM 서버 max_model_len: 9096
2025-06-26 18:00:45 - models.vllm_client_enhanced - INFO - VLLM 클라이언트 설정: 타임아웃=600초, 최대토큰=8192
2025-06-26 18:00:45 - models.vllm_session_manager - INFO - VLLMSessionManager 초기화 완료
2025-06-26 18:00:45 - models.vllm_client_enhanced - INFO - Qwen3 모델 최적화 설정 적용: temperature=0.6, top_p=0.95, top_k=20, presence_penalty=1.5
2025-06-26 18:00:45 - models.vllm_client_enhanced - INFO - Enhanced VLLM client initialized (server: http://localhost:8001/v1, model: Qwen/Qwen3-14B-AWQ)
2025-06-26 18:00:45 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 초기화 완료: <models.vllm_client_enhanced.VLLMClientEnhanced object at 0x000001FF4FA0DBD0>
2025-06-26 18:00:45 - hybrid_simulator - INFO - vLLM 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 18:00:45 - hybrid_simulator - INFO - vLLM 클라이언트 속성: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_initialized', '_instance', 'default_presence_penalty', 'default_temperature', 'default_top_k', 'default_top_p', 'generate', 'generate_fast', 'generate_json', 'health_check', 'is_qwen3', 'max_tokens', 'model_name', 'prompt_processor', 'response_parser', 'server_url', 'session_manager', 'timeout']
2025-06-26 18:00:45 - hybrid_simulator - INFO - vLLM 클라이언트 서버 URL: http://localhost:8001/v1
2025-06-26 18:00:45 - hybrid_simulator - INFO - vLLM 클라이언트 모델명: Qwen/Qwen3-14B-AWQ
2025-06-26 18:00:45 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 시작
