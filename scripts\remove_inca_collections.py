#!/usr/bin/env python3
"""
InCA 컬렉션만 제거하는 스크립트
- inca_trading_experiences
- inca_execution_logs
"""

import chromadb
import logging

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def remove_inca_collections():
    """InCA 컬렉션들만 제거"""
    try:
        # 🔧 실제 InCA 에이전트와 동일한 경로 사용
        client = chromadb.PersistentClient(path="data/inca_db/chroma")
        
        # 제거할 InCA 컬렉션 목록 (실제 존재하는 것들)
        inca_collection_names = [
            "inca_experiences",
            "inca_experiences_v2",
            "inca_experiences_v3"  # 🔧 현재 사용 중인 컬렉션
        ]
        
        # 현재 컬렉션 목록 확인 (ChromaDB v0.6.0+ 호환)
        all_collections = client.list_collections()
        existing_collections = all_collections  # v0.6.0에서는 이미 이름 리스트

        logger.info("=== 벡터 DB 현황 ===")
        logger.info(f"전체 컬렉션: {existing_collections}")

        removed_count = 0
        preserved_count = 0

        # InCA 컬렉션 제거
        for collection_name in inca_collection_names:
            if collection_name in existing_collections:
                try:
                    # 컬렉션 문서 수 확인
                    collection = client.get_collection(collection_name)
                    results = collection.get()
                    doc_count = len(results['ids']) if results and results.get('ids') else 0

                    # 컬렉션 삭제
                    client.delete_collection(collection_name)
                    logger.info(f"✅ 제거됨: {collection_name} ({doc_count}개 문서)")
                    removed_count += 1

                except Exception as e:
                    logger.error(f"❌ {collection_name} 제거 실패: {e}")
            else:
                logger.info(f"⚠️  {collection_name} 컬렉션이 존재하지 않음")

        # 보존된 컬렉션 확인 (큰 컬렉션은 건너뛰기)
        remaining_collections = client.list_collections()
        skip_large_collections = ['combined_data', 'social_data']  # 큰 컬렉션은 건너뛰기

        for collection_name in remaining_collections:
            if collection_name in skip_large_collections:
                logger.info(f"🔒 보존됨: {collection_name} (큰 컬렉션 - 문서 수 조회 건너뛰기)")
                preserved_count += 1
            else:
                try:
                    collection = client.get_collection(collection_name)
                    results = collection.get()
                    doc_count = len(results['ids']) if results and results.get('ids') else 0
                    logger.info(f"🔒 보존됨: {collection_name} ({doc_count}개 문서)")
                    preserved_count += 1
                except Exception as e:
                    logger.warning(f"컬렉션 {collection_name} 정보 조회 실패: {e}")
                    preserved_count += 1
        
        logger.info(f"\n=== 제거 완료 ===")
        logger.info(f"제거된 InCA 컬렉션: {removed_count}개")
        logger.info(f"보존된 기타 컬렉션: {preserved_count}개")
        
        return True
        
    except Exception as e:
        logger.error(f"제거 중 오류: {e}")
        return False

def initialize_inca_data():
    """InCA 데이터 완전 초기화 (컬렉션 + 캐시 파일)"""
    try:
        logger.info("=== InCA 데이터 완전 초기화 시작 ===")

        # 1. ChromaDB 컬렉션 제거
        logger.info("1단계: ChromaDB 컬렉션 제거")
        remove_inca_collections()

        # 2. 캐시 및 데이터 파일 제거
        import os
        import shutil

        logger.info("2단계: 캐시 및 데이터 파일 제거")

        cache_paths = [
            "cache/inca",           # InCA 캐시
            "data/inca_db",         # InCA 벡터 DB
            "data/llm_cache",       # LLM 캐시
        ]

        for cache_path in cache_paths:
            if os.path.exists(cache_path):
                shutil.rmtree(cache_path)
                logger.info(f"✅ {cache_path} 디렉토리 제거 완료")
            else:
                logger.info(f"⚠️ {cache_path} 디렉토리가 존재하지 않음")

        logger.info("=== InCA 데이터 완전 초기화 완료 ===")
        logger.info("시스템을 재시작하면 완전히 새로운 학습이 시작됩니다.")
        return True

    except Exception as e:
        logger.error(f"InCA 데이터 초기화 중 오류 발생: {e}")
        return False

def main():
    """메인 함수"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--full-reset":
        # 완전 초기화 모드
        print("=== InCA 데이터 완전 초기화 ===")
        print("제거 대상:")
        print("- ChromaDB 컬렉션 (inca_experiences, inca_experiences_v2, inca_experiences_v3)")
        print("- 캐시 디렉토리 (cache/inca, data/inca_db, data/llm_cache)")
        print("- 모든 학습 데이터가 삭제됩니다!\n")

        response = input("정말로 모든 InCA 데이터를 초기화하시겠습니까? (y/N): ")

        if response.lower() == 'y':
            print("\nInCA 데이터 완전 초기화 중...")
            success = initialize_inca_data()

            if success:
                print("\n🎉 InCA 데이터 완전 초기화 완료!")
                print("시스템을 재시작하면 완전히 새로운 학습이 시작됩니다.")
            else:
                print("\n❌ 초기화 중 오류가 발생했습니다.")
        else:
            print("취소되었습니다.")
    else:
        # 컬렉션만 제거 모드
        print("=== InCA 컬렉션 제거 도구 ===")
        print("제거 대상:")
        print("- inca_experiences")
        print("- inca_experiences_v2")
        print("- inca_experiences_v3")
        print("기타 컬렉션은 보존됩니다.\n")
        print("💡 완전 초기화를 원하면: python remove_inca_collections.py --full-reset\n")

        response = input("정말로 InCA 컬렉션들을 제거하시겠습니까? (y/N): ")

        if response.lower() == 'y':
            print("\nInCA 컬렉션 제거 중...")
            success = remove_inca_collections()

            if success:
                print("\n🎉 InCA 컬렉션 제거 완료!")
                print("이제 시스템을 재시작하면 새로운 학습이 시작됩니다.")
            else:
                print("\n❌ 제거 중 오류가 발생했습니다.")
        else:
            print("취소되었습니다.")

if __name__ == "__main__":
    main()
