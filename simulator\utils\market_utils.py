"""
시장 데이터 유틸리티 - 실제 거래소 데이터 수집 및 변환
"""
import time
import logging
import os
import sys
from typing import Dict, Any, Optional, List
from datetime import datetime

# 프로젝트 루트 디렉토리 추가
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
if project_root not in sys.path:
    sys.path.append(project_root)

# dotenv 로드 추가
from dotenv import load_dotenv
load_dotenv()  # .env 파일 로드

# 로거 설정
logger = logging.getLogger(__name__)

# LunarMonitor 사용 (기존 LunarCrush 대신)
try:
    from lunar_monitor import LunarMonitor
    from data_collector.lunarcrush_collector import LunarCrushCollector

    # LunarCrush API 키 가져오기
    LUNARCRUSH_API_KEY = os.getenv('LUNARCRUSH_API_KEY')
    logger.info(f"LunarCrush API 키 상태: {'설정됨' if LUNARCRUSH_API_KEY else '설정되지 않음'}")

    lunar_monitor = None
    lunar_collector = None

    if LUNARCRUSH_API_KEY:
        try:
            lunar_monitor = LunarMonitor(LUNARCRUSH_API_KEY)
            lunar_collector = LunarCrushCollector(LUNARCRUSH_API_KEY)
            logger.info("LunarMonitor API & Collector 연결 초기화 완료")
        except Exception as e:
            logger.error(f"LunarMonitor API 초기화 실패: {e}")
            logger.warning("LunarMonitor API 초기화 실패로 인해 소셜 데이터가 제한될 수 있습니다.")
    else:
        logger.error("LunarCrush API 키가 설정되지 않았습니다.")
        logger.warning("LunarCrush API 키가 없어 소셜 데이터가 제한될 수 있습니다.")
except ImportError as e:
    logger.error(f"LunarMonitor 모듈 임포트 실패: {e}")
    logger.warning("LunarMonitor 모듈 임포트 실패로 인해 소셜 데이터가 제한될 수 있습니다.")

def get_current_market_data(symbol: str, binance_utils, include_news: bool = True) -> Optional[Dict[str, Any]]:
    """
    바이낸스 API를 통해 현재 시장 데이터 가져오기

    Args:
        symbol: 코인 심볼 (예: BTC, ETH)
        binance_utils: 바이낸스 유틸리티 객체
        include_news: 뉴스 데이터 포함 여부

    Returns:
        시장 데이터 딕셔너리 또는 가져오기 실패 시 None
    """
    # 심볼 형식 정리 (슬래시 없이 BTCUSDT 형식으로)
    if '/' in symbol:
        base_symbol = symbol.split('/')[0]
        trading_symbol = f"{base_symbol}USDT"
    else:
        base_symbol = symbol
        trading_symbol = f"{symbol}USDT"

    logger.info(f"{symbol} 실시간 시장 데이터 수집 중...")

    # 종합 시장 데이터 가져오기 (신규 메서드 사용)
    market_result = binance_utils.get_market_data(trading_symbol, interval='3m', days=1)
    if not market_result:
        logger.warning(f"{symbol} 시장 데이터 수집 실패")
        return None  # 오류 반환

    # 현재 가격 정보
    current_price = market_result.get('price', 0)

    # 24시간 변화율 직접 가져오기
    try:
        ticker_24h = binance_utils.get_ticker(trading_symbol)
        if ticker_24h and 'priceChangePercent' in ticker_24h:
            change_24h_pct = float(ticker_24h.get('priceChangePercent', 0))
            logger.info(f"{symbol} 24h 변화율: {change_24h_pct:.2f}%")
        else:
            change_24h_pct = market_result.get('change_24h', 0.0)
            logger.warning(f"{symbol} 24h 변화율 API 실패, 기본값 사용: {change_24h_pct:.2f}%")
    except Exception as e:
        change_24h_pct = market_result.get('change_24h', 0.0)
        logger.warning(f"{symbol} 24h 변화율 계산 실패: {e}, 기본값 사용: {change_24h_pct:.2f}%")

    logger.info(f"{symbol} 현재 가격: {current_price}, 24h 변화율: {change_24h_pct:.2f}%")

    # 현재 시각
    timestamp = int(time.time())
    dt_now = datetime.now()

    # 기본 시장 데이터 구성
    market_data = {
        "id": f"market_{base_symbol}_{timestamp}",
        "symbol": base_symbol,
        "timestamp": timestamp,
        "datetime": dt_now.strftime("%Y-%m-%d %H:%M:%S"),
        "date": dt_now.strftime("%Y-%m-%d"),
        "time": dt_now.strftime("%H:%M:%S"),

        # 핵심 가격 데이터
        "price": float(current_price),
        "open": float(market_result.get('opens', [0])[-1] if market_result.get('opens') else 0),
        "high": float(market_result.get('highs', [0])[-1] if market_result.get('highs') else 0),
        "low": float(market_result.get('lows', [0])[-1] if market_result.get('lows') else 0),
        "close": float(market_result.get('prices', [0])[-1] if market_result.get('prices') else 0),

        # 24시간 데이터 - 바이낸스 API에서 직접 가져온 거래량 사용
        "volume": market_result.get('volume_24h', 0),
        "volume_24h": market_result.get('volume_24h', 0),

        # 고가/저가 - 바이낸스 API에서 직접 가져온 24시간 고가/저가 사용
        "high_24h": market_result.get('high_24h', 0),
        "low_24h": market_result.get('low_24h', 0),

        # 변동성 및 기술 지표 - 바이낸스 API에서 직접 가져온 24시간 변동률 사용
        "percent_change_24h": change_24h_pct,  # 직접 계산한 24h 변화율 사용
        "volatility": market_result.get('volatility', 0.0),
        "rsi": market_result.get('rsi_14', 50.0),

        # 소셜 & 감성 데이터 (기본값으로 초기화)
        "average_sentiment": 0.5,  # 중립
        "sentiment_score": 0.5,    # 중립
        "social_volume": 0,        # 소셜 언급량
        "social_dominance": 0,     # 소셜 점유율
        "social_contributors": 0,  # 소셜 기여자 수
        "bullish_sentiment": 0.5,  # 강세 감성 비율
        "bearish_sentiment": 0.5,  # 약세 감성 비율

        # 메타데이터
        "data_source": "binance_api",
        "is_real_data": True,
        "has_news": include_news,
        "execution_timestamp": timestamp,  # 실행 시점
        "news_count": 0  # 뉴스 개수
    }

    # 이동평균 계산
    prices = market_result.get('prices', [])
    current_price = market_data['price']

    if prices and len(prices) > 0:
        # 이동평균 추가
        if len(prices) >= 7:
            market_data['ema_7'] = sum(prices[-7:]) / 7
        else:
            market_data['ema_7'] = current_price  # 데이터 부족 시 현재 가격 사용

        if len(prices) >= 14:
            market_data['ema_14'] = sum(prices[-14:]) / 14
        else:
            market_data['ema_14'] = current_price

        if len(prices) >= 25:
            market_data['ema_25'] = sum(prices[-25:]) / 25
        else:
            market_data['ema_25'] = current_price

        if len(prices) >= 50:
            market_data['ema_50'] = sum(prices[-50:]) / 50
        else:
            market_data['ema_50'] = current_price

        if len(prices) >= 99:
            market_data['ema_99'] = sum(prices[-99:]) / 99
        else:
            market_data['ema_99'] = current_price

        if len(prices) >= 200:
            market_data['ema_200'] = sum(prices[-200:]) / 200
        else:
            market_data['ema_200'] = current_price
    else:
        # prices 데이터가 없으면 현재 가격을 모든 EMA로 사용
        market_data['ema_7'] = current_price
        market_data['ema_14'] = current_price
        market_data['ema_25'] = current_price
        market_data['ema_50'] = current_price
        market_data['ema_99'] = current_price
        market_data['ema_200'] = current_price

    logger.info(f"{symbol} 시장 데이터 수집 완료: 현재가 ${market_data['price']:.2f}, 변동률 {market_data['percent_change_24h']:.2f}%")

    # 뉴스 데이터 수집
    if include_news:
        try:
            # 최신 뉴스 가져오기
            market_data['news'] = get_latest_news_for_symbol(base_symbol)
            market_data['news_count'] = len(market_data['news'])

            # 뉴스 기반 감성 계산
            if market_data['news_count'] > 0:
                sentiment_sum = sum(news.get('sentiment', 0.5) for news in market_data['news'])
                market_data['news_sentiment'] = sentiment_sum / market_data['news_count']
            else:
                # 뉴스가 없으면 오류 로깅
                logger.error(f"{symbol} 뉴스 데이터가 없습니다.")
                market_data['news'] = []  # 빈 뉴스 목록 설정
        except Exception as news_err:
            logger.error(f"{symbol} 뉴스 데이터 수집 중 오류: {news_err}")
            market_data['news'] = []  # 빈 뉴스 목록 설정
            market_data['news_count'] = 0

    # 소셜 데이터 가져오기 (LunarCrushCollector 사용)
    try:
        social_data = get_social_data_for_symbol(base_symbol)
        if not social_data:
            logger.error(f"{symbol} 소셜 데이터를 가져오지 못했습니다.")
            # 기본 소셜 데이터 설정
            social_data = {
                "average_sentiment": 0.5,
                "sentiment_score": 0.5,
                "social_dominance": 0.01,
                "social_contributors": 100,
                "bullish_sentiment": 0.5,
                "bearish_sentiment": 0.5,
                "social_volume": 1000,
                "news_count": 0,
                "post_count": 0
            }

        # 소셜 데이터 통합
        market_data.update(social_data)

        # 소셜 볼륨 검증
        if 'social_volume' not in social_data or social_data['social_volume'] == 0:
            logger.error(f"{symbol} 소셜 볼륨 데이터가 없거나 0입니다.")
            # 기본값 설정
            market_data['social_volume'] = 1000

        logger.info(f"{symbol} 소셜 데이터 결합 완료: 바이낸스 거래량={int(market_data['volume'])}, 소셜볼륨={market_data['social_volume']}")

    except Exception as social_err:
        logger.error(f"{symbol} 소셜 데이터 수집 중 오류: {social_err}")
        # 기본 소셜 데이터 설정
        default_social_data = {
            "average_sentiment": 0.5,
            "sentiment_score": 0.5,
            "social_dominance": 0.01,
            "social_contributors": 100,
            "bullish_sentiment": 0.5,
            "bearish_sentiment": 0.5,
            "social_volume": 1000,
            "news_count": 0,
            "post_count": 0
        }
        market_data.update(default_social_data)

    # 🚀 타임시리즈 데이터 추가 (각 에이전트가 개별 분석용)
    try:
        # 캔들스틱 데이터 추가 (최근 몇 개만) - 강화된 수집 로직
        recent_candles = []
        if binance_utils:
            # 🔧 더 많은 인터벌로 시도하여 데이터 확보 (성공률 향상)
            intervals_to_try = ["1m", "3m", "5m", "15m", "30m", "1h"]

            for interval in intervals_to_try:
                try:
                    logger.info(f"{symbol} {interval} 캔들 데이터 수집 시도...")

                    # 🔧 바이낸스 유틸리티 상태 확인
                    if not binance_utils:
                        logger.error(f"{symbol} binance_utils가 None입니다!")
                        continue

                    # 🔧 더 많은 캔들 요청 (30 → 50)
                    candles = binance_utils.get_klines(f"{base_symbol}USDT", interval, 50)

                    # 🔧 디버깅: 캔들 데이터 상태 로깅
                    logger.info(f"{symbol} {interval} 캔들 응답: {type(candles)}, 길이: {len(candles) if candles else 0}")
                    if candles and len(candles) > 0:
                        logger.info(f"{symbol} {interval} 첫 번째 캔들 샘플: {candles[0] if len(candles) > 0 else 'None'}")
                    else:
                        logger.warning(f"{symbol} {interval} 캔들 데이터가 비어있거나 None입니다!")

                    if candles and len(candles) >= 2:  # 🔧 조건 완화: 5개 → 2개
                        # 유효한 캔들 데이터 검증
                        valid_candles = []
                        for candle in candles:
                            try:
                                # 캔들 데이터 유효성 검증
                                open_price = float(candle[1])
                                high_price = float(candle[2])
                                low_price = float(candle[3])
                                close_price = float(candle[4])
                                volume = float(candle[5])

                                # 기본 검증: 가격이 0이 아니고 거래량이 있는지
                                if (open_price > 0 and high_price > 0 and
                                    low_price > 0 and close_price > 0 and volume > 0):
                                    valid_candles.append(candle)

                            except (ValueError, IndexError) as e:
                                logger.warning(f"{symbol} 캔들 데이터 파싱 오류: {e}")
                                continue

                        if len(valid_candles) >= 2:  # 🔧 조건 완화: 5개 → 2개
                            recent_candles = valid_candles[-20:]  # 최근 20개만
                            logger.info(f"✅ {symbol} {interval} 캔들스틱 데이터 수집 성공: {len(recent_candles)}개")
                            break
                        else:
                            logger.warning(f"⚠️ {symbol} {interval} 유효한 캔들 부족: {len(valid_candles)}개")

                except Exception as e:
                    logger.warning(f"❌ {symbol} {interval} 캔들스틱 데이터 수집 실패: {e}")
                    # 🔧 디버깅: 상세 오류 정보 로깅
                    import traceback
                    logger.debug(f"{symbol} {interval} 캔들 수집 오류 상세: {traceback.format_exc()}")
                    continue

            # 🔧 모든 인터벌 시도 후에도 데이터가 없으면 직접 API 호출 시도
            if not recent_candles:
                logger.warning(f"🚨 {symbol} 모든 인터벌에서 캔들 데이터 수집 실패! 직접 API 호출 시도...")

                try:
                    import requests
                    # 바이낸스 API 직접 호출
                    url = "https://fapi.binance.com/fapi/v1/klines"
                    params = {
                        "symbol": f"{base_symbol}USDT",
                        "interval": "1m",
                        "limit": 10
                    }

                    response = requests.get(url, params=params, timeout=10)
                    if response.status_code == 200:
                        direct_candles = response.json()
                        if direct_candles and len(direct_candles) > 0:
                            # 간단한 형태로 변환
                            recent_candles = []
                            for candle in direct_candles[-5:]:  # 최근 5개만
                                recent_candles.append({
                                    'open': float(candle[1]),
                                    'high': float(candle[2]),
                                    'low': float(candle[3]),
                                    'close': float(candle[4]),
                                    'volume': float(candle[5])
                                })
                            logger.info(f"✅ {symbol} 직접 API 호출로 캔들 데이터 {len(recent_candles)}개 수집 성공!")
                        else:
                            logger.error(f"❌ {symbol} 직접 API 호출 응답이 비어있음")
                    else:
                        logger.error(f"❌ {symbol} 직접 API 호출 실패: {response.status_code}")

                except Exception as e:
                    logger.error(f"❌ {symbol} 직접 API 호출 중 오류: {e}")

                # 여전히 데이터가 없으면 로그만 남기고 빈 리스트 유지
                if not recent_candles:
                    logger.error(f"🚨 {symbol} 모든 캔들 데이터 수집 방법 실패!")

        # 타임시리즈 관련 데이터 추가
        market_data.update({
            'recent_candles': recent_candles,
            'candles_count': len(recent_candles),
            'data_timestamp': timestamp,
            'has_timeseries_data': len(recent_candles) > 0
        })

        # 최근 가격 변동 계산 (캔들 데이터가 있는 경우)
        if recent_candles and len(recent_candles) >= 5:
            try:
                # 최근 5개 캔들의 종가
                recent_closes = [float(candle[4]) for candle in recent_candles[-5:]]

                # 단기 변동률 계산 (최근 5개 캔들)
                if len(recent_closes) >= 2:
                    short_term_change = ((recent_closes[-1] - recent_closes[0]) / recent_closes[0]) * 100
                    market_data['short_term_change_pct'] = short_term_change

                # 최근 캔들 고/저가
                recent_highs = [float(candle[2]) for candle in recent_candles[-5:]]
                recent_lows = [float(candle[3]) for candle in recent_candles[-5:]]
                market_data['recent_high'] = max(recent_highs)
                market_data['recent_low'] = min(recent_lows)

                logger.info(f"{symbol} 단기 변동률: {short_term_change:.3f}%")
            except Exception as e:
                logger.warning(f"{symbol} 단기 변동률 계산 실패: {e}")
                market_data['short_term_change_pct'] = 0
                market_data['recent_high'] = market_data['price']
                market_data['recent_low'] = market_data['price']
        else:
            market_data['short_term_change_pct'] = 0
            market_data['recent_high'] = market_data['price']
            market_data['recent_low'] = market_data['price']

    except Exception as e:
        logger.error(f"{symbol} 타임시리즈 데이터 추가 실패: {e}")
        # 실패 시 기본값 설정
        market_data.update({
            'recent_candles': [],
            'candles_count': 0,
            'data_timestamp': timestamp,
            'has_timeseries_data': False,
            'short_term_change_pct': 0,
            'recent_high': market_data['price'],
            'recent_low': market_data['price']
        })

    logger.info(f"{symbol} 시장 데이터 수집 완료: 현재가 ${market_data['price']:.2f}, 24h변동률 {market_data['percent_change_24h']:.2f}%, 단기변동률 {market_data.get('short_term_change_pct', 0):.3f}%, 캔들 {market_data['candles_count']}개")

    return market_data

def calculate_volatility(prices: List[float], window: int = 14) -> float:
    """
    가격 변동성 계산

    Args:
        prices: 가격 목록
        window: 계산 기간

    Returns:
        변동성 (표준편차 기반)
    """
    if len(prices) < window:
        return 0.0

    # 최근 window 기간 동안의 가격
    recent_prices = prices[-window:]

    # 일일 변화율 계산
    pct_changes = [(recent_prices[i] / recent_prices[i-1] - 1) * 100
                  for i in range(1, len(recent_prices))]

    # 표준편차 계산
    if not pct_changes:
        return 0.0

    mean = sum(pct_changes) / len(pct_changes)
    variance = sum((x - mean) ** 2 for x in pct_changes) / len(pct_changes)
    return (variance ** 0.5)  # 표준편차

def calculate_rsi(prices: List[float]) -> float:
    """
    RSI (Relative Strength Index) 계산

    Args:
        prices: 가격 목록

    Returns:
        RSI 값
    """
    # RSI 계산 (간단한 구현)
    gains = []
    losses = []

    for i in range(1, len(prices)):
        diff = prices[i] - prices[i-1]
        if diff > 0:
            gains.append(diff)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(abs(diff))

    avg_gain = sum(gains) / len(gains)
    avg_loss = sum(losses) / len(losses)

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi

def get_latest_news_for_symbol(symbol: str) -> List[Dict[str, Any]]:
    """
    최신 뉴스 가져오기 (현재는 더미 데이터)

    Args:
        symbol: 코인 심볼

    Returns:
        뉴스 목록
    """
    # 더미 뉴스 데이터
    news = [
        {"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8},
        {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2},
    ]

    return news

def get_social_data_for_symbol(symbol: str) -> Dict[str, Any]:
    """
    소셜 데이터 가져오기 (LunarCrushCollector 사용)

    Args:
        symbol: 코인 심볼

    Returns:
        소셜 데이터
    """
    logger.info(f"{symbol} 소셜 데이터 수집 중...")

    # 1. 소셜 데이터를 가져오기 위해 LunarCrush 수집기 사용
    api_key = os.getenv("LUNARCRUSH_API_KEY")
    if not api_key:
        logger.error("LunarCrush API 키가 설정되지 않았습니다. .env 파일을 확인하세요.")
        return None

    lunar_collector = LunarCrushCollector(api_key)

    # 1. 코인 소셜 데이터 가져오기
    coin_data = lunar_collector.get_coin_data(symbol)
    if not coin_data:
        logger.error(f"{symbol} 코인 데이터가 없습니다.")
        return None

    # 2. 코인 뉴스 데이터 가져오기 (파라미터 없이 호출)
    news_data = lunar_collector.get_coin_news(symbol)
    if not news_data:
        logger.error(f"{symbol} 뉴스 데이터가 없습니다.")
        # 빈 뉴스 데이터 설정
        news_data = []

    # 3. 소셜 포스트 데이터 가져오기 (최대 100개)
    posts_data = lunar_collector.get_coin_posts(symbol, limit=100)
    if not posts_data:
        logger.error(f"{symbol} 소셜 포스트 데이터가 없습니다.")
        # 빈 포스트 데이터 설정
        posts_data = []

    # 데이터 추출
    # 1. 소셜 볼륨 및 감성 점수
    social_volume = coin_data.get('social_volume', 0)
    social_dominance = coin_data.get('social_dominance', 0.01)
    social_contributors = coin_data.get('social_contributors', 100)
    sentiment_score = coin_data.get('average_sentiment', 0.5)  # 중립값은 0.5

    # 감성 점수가 없거나 0인 경우 기본값 설정
    if sentiment_score == 0:
        logger.error(f"{symbol} 감성 점수 데이터 없음")
        sentiment_score = 0.5  # 중립값으로 설정

    # 정규화된 감성 (0.5를 기준으로 정규화)
    normalized_sentiment = max(0.1, min(0.9, sentiment_score))  # 0.1~0.9 범위로 제한

    # 감성 비율 계산
    if normalized_sentiment >= 0.5:
        bullish_ratio = (normalized_sentiment - 0.5) * 2  # 0.5~1.0 -> 0~1.0
        bearish_ratio = 0
    else:
        bullish_ratio = 0
        bearish_ratio = (0.5 - normalized_sentiment) * 2  # 0~0.5 -> 0~1.0

    # 뉴스 데이터 집계
    news_count = len(news_data)
    post_count = len(posts_data)

    # 데이터 검증 - 뉴스 또는 포스트가 없으면 로깅
    if news_count == 0 or post_count == 0:
        logger.error(f"{symbol} 뉴스 또는 소셜 포스트 데이터 없음: 뉴스={news_count}개, 포스트={post_count}개")
        # 계속 진행

    # 뉴스 감성 평균 (뉴스가 있을 경우)
    if news_data:
        news_sentiment = sum([news.get('sentiment', 0.5) for news in news_data]) / len(news_data)
    else:
        news_sentiment = 0.5  # 뉴스가 없으면 중립값 사용

    # 결과 조합
    social_data = {
        "average_sentiment": sentiment_score,
        "sentiment_score": sentiment_score,
        "social_dominance": social_dominance,
        "social_contributors": social_contributors,
        "bullish_sentiment": max(0.5, normalized_sentiment),
        "bearish_sentiment": max(0.5, 1 - normalized_sentiment),
        "news_count": news_count,
        "post_count": post_count,
        "news_sentiment": news_sentiment,
        "bullish_ratio": bullish_ratio,
        "bearish_ratio": bearish_ratio,
        "galaxy_score": coin_data.get('galaxy_score', 0),
        "alt_rank": coin_data.get('alt_rank', 0),
        "market_cap": coin_data.get('market_cap', 0),  # 시가총액 추가

        # 최근 주요 뉴스 제목 (최대 5개)
        "recent_news_titles": [news.get('title', '') for news in news_data[:5]] if news_data else [],

        # 주요 소셜 미디어 언급 (최대 10개)
        "top_social_posts": [
            {
                "text": post.get('text', ''),
                "sentiment": post.get('sentiment', 0),
                "platform": post.get('platform', '')
            }
            for post in posts_data[:10]
        ] if posts_data else [],

        "social_volume": social_volume  # 소셜 볼륨 필드 복원
    }

    # 로그 메시지에 소셜 볼륨 정보 추가
    logger.info(f"{symbol} 소셜 데이터 수집 완료: 감성={sentiment_score:.2f}, 소셜볼륨={social_volume}, 뉴스={news_count}개, 포스트={post_count}개")
    return social_data

# 글로벌 LLM 분석 함수 제거됨 - 각 에이전트가 개별적으로 타임시리즈 데이터를 분석
