{"execution_id": "exec_afffa96b_1750799729", "symbol": "SOL", "timestamp": 1750799729, "datetime": "2025-06-25 06:15:29", "type": "execution_result", "data": {"symbol": "SOL", "strategy_id": "5bca0443-2d4b-4dd8-8a63-b7dc84da39c9", "timestamp": 1750799729, "market_data": {"id": "market_SOL_1750799711", "symbol": "SOL", "timestamp": 1750799711, "datetime": "2025-06-25 06:15:11", "date": "2025-06-25", "time": "06:15:11", "price": 144.49, "open": 0.0, "high": 0.0, "low": 0.0, "close": 144.49, "volume": 24601101.35, "volume_24h": 24601101.35, "high_24h": 146.88, "low_24h": 140.42, "percent_change_24h": 2.716, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 45555219, "social_dominance": 0.202, "social_contributors": 455552, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750799711, "news_count": 24, "ema_7": 144.6342857142857, "ema_14": 144.53357142857143, "ema_25": 144.1168, "ema_50": 143.80300000000003, "ema_99": 144.21373737373736, "ema_200": 144.27374999999995, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.04125, "post_count": 99, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 76911672288.48, "recent_news_titles": ["Crypto Price Analysis 6-24: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, ARBITRUM: ARB, UNISWAP: UNI, FILECOIN: FIL - Crypto Daily", "Solana (SOL) Surges 8% to Over $145, Up 146% in One Month", "Altcoin News: ETH, SOL, ADA Spike 7% as <PERSON> Claims Israel-Iran Ceasefire", "Crypto Price Analysis 6-23: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, LITECOIN: LTC, OPTIMISM: OP - Crypto Daily", "Venezuelan TV Mogul Raul <PERSON>lls <PERSON> Island Condo"], "top_social_posts": [{"text": "JUST IN: $SOL CME FUTURES VOLUME HITS RECORD 1.75M CONTRACTS AS PRICE CLIMBS TO $145\n\nSource: @cointelegraph", "sentiment": 3.13, "platform": "tweet"}, {"text": "HEAR ME OUT!!!\n\nLet me teach you something.\n\nIf you have less than $50K to your name and you’re buying coins like Bitcoin, ETH, or even SOL\n\nYou’re doing it all wrong.\n\nYou want to make millions, don’t you?\n\nIf that’s the case, you need to buy coins that can 20x at minimum.\n\nCoins most people don’t even know exist.\n\nIs it risky? Of course.\n\nDo you know what’s even riskier? Not trying at all.\n\nNobody gets rich without risk.\n\nNOBODY. Do you understand?\n\nBUT THERE’S A TWIST.\n\nYou can significantly reduce your risk by doing your own research (seriously).\n\nFor example, when i bought Kaspa in 2022 before it did a 200x, i knew something most people didn’t.\n\nI knew that the Kasp<PERSON> founder worked on Bitcoin very early on.\n\nI didn’t engage in insider trading, it was public information, people just didn’t dig deep enough\n\nBy the time the masses found out, it was already at a $500M market cap and i was up 50x.\n\nI took alot of risks, but the reward was BIG.\n\nThat being said, if you REALLY want to get rich, avoid any coins in the top 50.\n\nThose are for people who are already rich.\n\nWhen i find a new coin with 100x potential, i’ll post it here for everyone to see.\n\nIf you’re still not following me, you’re going to regret it MASSIVELY.", "sentiment": 2.98, "platform": "tweet"}, {"text": "⏳ Our latest round of the SOL App Campaign ends on 2 Jul\n\nThe top 2,000 SOL Net Buyers will each win a US$10 $BTC bonus! Be rewarded for building up your $SOL holdings 🦾\n\nFull details and T&Cs 👇\n\n\n@solana", "sentiment": 3.44, "platform": "tweet"}, {"text": "The volume of futures trading on $SOL on the @CMEGroup exchange is a at all time high - @glassnode \n\n", "sentiment": 3, "platform": "tweet"}, {"text": "<PERSON><PERSON>, <PERSON> 10no Yunus azim azim azdirdi bu üçlü", "sentiment": 3, "platform": "tweet"}, {"text": "I think this fruit named $SOLAMI has runner legs.\n\n<PERSON><PERSON><PERSON>'s a well-known giga degen, and it looks like the $SOL crew just wanted to help birth a solid meme on-chain. \n\nThat’s probably why they engaged under his post, that too with a different and 'Viral-worthy' name. \n\nFeels similar to what we saw with $TST or $KNIGHT on BNB — early memes with organic heat.\n\nTeam didn’t launch it themselves, but the vibes are there. Could cook. \n\n$SOLAMI \n\n", "sentiment": 3.31, "platform": "tweet"}, {"text": "#signos #astrologia #signosdoszodíaco♈♉♊♋♌♍♎♏♐♑♒♓ #previsoesastrologicas #astrologiatiktok #cancer♋️ #capricornio♑️ #aries #libra ", "sentiment": 2.96, "platform": "tiktok-video"}, {"text": "🔥 BULLISH: $SOL CME futures volume hit an all-time high of 1.75 million contracts, reflecting rising institutional interest as the price climbs back to $145.", "sentiment": 3.2, "platform": "tweet"}, {"text": "I am printing on @solpumpcom\n\nfor a free sol case and many cool features like no kyc and instant cashouts. And dont forget about the hourly solana airdrops!", "sentiment": 3.24, "platform": "tweet"}, {"text": "Which #memecoin $SOL will make you a Millionaire this year?👇 \n\n$MASK or $DOG or $TRUMP or $BONK or $PENGU or  $WIF or $POPCAT or $CAT or #chillguy or $HOUSE or $VINE 🤔", "sentiment": 3.08, "platform": "tweet"}], "recent_candles": [[1750798560000, "144.7800", "144.8100", "144.7000", "144.8000", "5216.57", 1750798619999, "755139.376600", 682, "3664.58", "530501.298800", "0"], [1750798620000, "144.8000", "144.9900", "144.7700", "144.9800", "10850.54", 1750798679999, "1572389.587100", 1229, "8472.18", "1227712.211700", "0"], [1750798680000, "144.9800", "145.0300", "144.9300", "144.9800", "15419.70", 1750798739999, "2235585.751200", 4822, "7648.25", "1108873.128800", "0"], [1750798740000, "144.9800", "145.0700", "144.8800", "144.9700", "9287.31", 1750798799999, "1346505.876400", 4374, "3975.83", "576476.619600", "0"], [1750798800000, "144.9700", "145.0200", "144.8100", "144.8100", "11154.09", 1750798859999, "1616274.171300", 1430, "4628.03", "670705.820300", "0"], [1750798860000, "144.8100", "144.9500", "144.7500", "144.9100", "10297.06", 1750798919999, "1491190.378100", 1503, "4641.18", "672221.439800", "0"], [1750798920000, "144.9000", "144.9100", "144.6300", "144.6300", "10319.55", 1750798979999, "1493654.046400", 1498, "1767.39", "255837.691300", "0"], [1750798980000, "144.6300", "144.7500", "144.5600", "144.7400", "7176.01", 1750799039999, "1037866.122000", 1147, "2779.48", "401977.879700", "0"], [1750799040000, "144.7300", "144.9200", "144.5400", "144.5400", "13947.83", 1750799099999, "2018609.874000", 1912, "3353.23", "485499.386000", "0"], [1750799100000, "144.5500", "144.6400", "144.5100", "144.5200", "7137.43", 1750799159999, "1031944.672800", 1196, "2743.40", "396648.770500", "0"], [1750799160000, "144.5100", "144.6600", "144.3200", "144.6200", "18210.56", 1750799219999, "2630015.808800", 2156, "9291.02", "1341903.432100", "0"], [1750799220000, "144.6100", "144.6100", "144.5200", "144.5500", "4721.39", 1750799279999, "682580.648400", 779, "1817.19", "262705.338700", "0"], [1750799280000, "144.5500", "144.7400", "144.5500", "144.7300", "6005.25", 1750799339999, "868704.423500", 856, "4938.34", "714317.452300", "0"], [1750799340000, "144.7300", "144.7900", "144.6200", "144.6300", "3431.69", 1750799399999, "496541.819100", 755, "1425.49", "206276.077800", "0"], [1750799400000, "144.6400", "144.6500", "144.5300", "144.5800", "6330.40", 1750799459999, "915337.320000", 991, "3442.32", "497745.409000", "0"], [1750799460000, "144.5900", "144.6200", "144.5700", "144.6000", "3572.54", 1750799519999, "516576.570100", 469, "2457.87", "355403.959400", "0"], [1750799520000, "144.5900", "144.7300", "144.5900", "144.6700", "3726.63", 1750799579999, "539137.435800", 747, "1912.43", "276637.093600", "0"], [1750799580000, "144.6800", "144.7200", "144.6400", "144.6600", "2348.64", 1750799639999, "339795.302800", 661, "1259.05", "182154.129600", "0"], [1750799640000, "144.6600", "144.6600", "144.4300", "144.5000", "3747.36", 1750799699999, "541483.460500", 988, "1727.07", "249515.708900", "0"], [1750799700000, "144.5000", "144.5000", "144.4500", "144.4600", "242.91", 1750799759999, "35095.467400", 631, "55.06", "7955.087400", "0"]], "candles_count": 20, "data_timestamp": 1750799711, "has_timeseries_data": true, "short_term_change_pct": -0.09681881051174714, "recent_high": 144.73, "recent_low": 144.43}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "The short-term 5-minute candle data shows a bearish trend with 3 consecutive bearish candles and a weak price decline of -0.097%. The volume pattern is typical but does not confirm a strong reversal. The overall technical analysis indicates a bearish direction with a strength of 2.0/100. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish", "volume", "candle pattern"], "raw_response": " NO MARKDOWN.\n\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term 5-minute candle data shows a bearish trend with 3 consecutive bearish c...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_1cd663a1_1750799718"}, "strategy": {"strategy_id": "5bca0443-2d4b-4dd8-8a63-b7dc84da39c9", "symbol": "SOL", "timestamp": 1750799729, "type": "sell", "entry_price": 144.49, "stop_loss": 147.83, "take_profit": 138.14, "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(70)와 MACD(0 이상)의 과열 상태와 볼린저 밴드 상단 돌파 후의 조정 가능성을 고려할 때 단기적으로 하락 흐름이 예상됨. 현재 가격 대비 1-3% 범위의 스톱로스와 2-5% 범위의 테이크프로핏을 설정하여 리스크/리워드 비율 1.2 이상을 유지.", "confidence": 0.75, "reasoning_card_id": "card_1cd663a1_1750799718", "risk_level": "medium", "key_points": ["InCA 신호와 SELA 분석 모두 SELL 추천", "RSI 과열 및 MACD 상승 종료 신호", "볼린저 밴드 상단 돌파 후 조정 예상"], "market_context": {"price": 144.49, "percent_change_24h": 2.716, "timestamp": 1750799711}, "paper_based": false, "risk_reward": 1.901197604790424, "importance": 8.842663833099637, "consensus_signal": "sell", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.75, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - SOL"}, "execution_id": "exec_afffa96b_1750799729"}}