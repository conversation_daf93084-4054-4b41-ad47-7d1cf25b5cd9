"""
실행 플로우 모듈

하이브리드 시스템의 실행 흐름(InCA → HiAR → SELA → 실행)을 구현합니다.
3분마다 뉴스와 시장 데이터를 수집하고, 중요성 평가, 사고 흐름 생성, 전략 실행을 수행합니다.
"""

import logging
import time
import threading
import json
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import uuid

from trading.hybrid_architecture.data_store import DataStore
from trading.hybrid_architecture.event_manager import EventManager, EventType, Event, EventsBuilder
from trading.hybrid_architecture.config_manager import HybridConfigManager
from trading.hybrid_architecture.interfaces import InCAAgentInterface, HiARAgentInterface, SELAAgentInterface

logger = logging.getLogger(__name__)

class ExecutionFlow:
    """
    실행 플로우 클래스

    InCA → HiAR → SELA 순서로 실시간 데이터 처리 및 전략 실행을 수행합니다.
    """

    def __init__(self,
                 inca_agent: InCAAgentInterface,
                 hiar_agent: HiARAgentInterface,
                 sela_agent: SELAAgentInterface,
                 data_store: DataStore,
                 event_manager: EventManager,
                 config_manager: HybridConfigManager):
        """
        실행 플로우 초기화

        Args:
            inca_agent: InCA 에이전트 (중요성 평가)
            hiar_agent: HiAR 에이전트 (사고 흐름 생성)
            sela_agent: SELA 에이전트 (전략 생성)
            data_store: 데이터 저장소
            event_manager: 이벤트 관리자
            config_manager: 설정 관리자
        """
        self.inca_agent = inca_agent
        self.hiar_agent = hiar_agent
        self.sela_agent = sela_agent
        self.data_store = data_store
        self.event_manager = event_manager
        self.config_manager = config_manager

        # 설정 로드
        self.symbols = self.config_manager.get("trading.symbols", ["BTC"])
        self.collection_interval = self.config_manager.get("trading.collection_interval", 180)  # 3분(초 단위)
        self.trading_mode = self.config_manager.get("trading.mode", "simulation")
        self.is_testnet = self.config_manager.get("trading.testnet", True)

        # 실행 상태
        self.is_running = False
        self.execution_thread = None
        self.last_execution_time = 0
        self.execution_count = 0



        # 이벤트 구독 설정
        self._setup_event_subscribers()

    def _setup_event_subscribers(self):
        """이벤트 구독 설정"""
        # 뉴스 데이터 이벤트 구독
        self.event_manager.subscribe(
            EventType.NEWS_RECEIVED,
            self._handle_news_event,
            "execution_flow"
        )

        # 시장 데이터 이벤트 구독
        self.event_manager.subscribe(
            EventType.MARKET_DATA_RECEIVED,
            self._handle_market_data_event,
            "execution_flow"
        )

        # 중요성 평가 이벤트 구독
        self.event_manager.subscribe(
            EventType.IMPORTANCE_EVALUATED,
            self._handle_importance_event,
            "execution_flow"
        )

        # 사고 흐름 이벤트 구독
        self.event_manager.subscribe(
            EventType.REASONING_GENERATED,
            self._handle_reasoning_event,
            "execution_flow"
        )

        # 전략 생성 이벤트 구독
        self.event_manager.subscribe(
            EventType.STRATEGY_GENERATED,
            self._handle_strategy_event,
            "execution_flow"
        )

        # 실행 완료 이벤트 구독
        self.event_manager.subscribe(
            EventType.EXECUTION_COMPLETED,
            self._handle_execution_completed_event,
            "execution_flow"
        )

    def start(self):
        """실행 플로우 시작"""
        if self.is_running:
            logger.warning("실행 플로우가 이미 실행 중입니다.")
            return

        self.is_running = True
        self.execution_thread = threading.Thread(target=self._execution_loop, daemon=True)
        self.execution_thread.start()
        logger.info("실행 플로우 시작됨")

    def stop(self):
        """실행 플로우 중지"""
        if not self.is_running:
            logger.warning("실행 플로우가 이미 중지되었습니다.")
            return

        self.is_running = False
        if self.execution_thread and self.execution_thread.is_alive():
            self.execution_thread.join(timeout=5.0)
        logger.info(f"실행 플로우 중지됨 (실행 횟수: {self.execution_count})")

    def _execution_loop(self):
        """실행 루프"""
        logger.info("실행 루프 시작됨")

        while self.is_running:
            try:
                current_time = time.time()
                elapsed_time = current_time - self.last_execution_time

                # 수집 간격 확인
                if elapsed_time >= self.collection_interval:
                    self.last_execution_time = current_time
                    self._process_execution_cycle()
                    self.execution_count += 1

                # 잠시 대기 (CPU 부하 방지)
                time.sleep(1)

            except Exception as e:
                logger.error(f"실행 루프 중 오류 발생: {e}")
                # 모의나 fallback 없이 오류 발생 시 중단
                self.is_running = False
                raise

    def _process_execution_cycle(self):
        """실행 사이클 처리"""
        logger.info(f"실행 사이클 시작 (실행 횟수: {self.execution_count + 1})")
        logger.info(f"처리할 심볼 목록: {self.symbols}")

        # 각 심볼에 대해 처리
        processed_symbols = []
        failed_symbols = []

        for symbol in self.symbols:
            try:
                logger.info(f"📊 {symbol} 처리 시작")
                self._process_symbol(symbol)
                processed_symbols.append(symbol)
                logger.info(f"✅ {symbol} 처리 완료")
            except Exception as e:
                logger.error(f"❌ {symbol} 처리 중 오류 발생: {e}")
                logger.error(f"🔄 {symbol} 건너뛰고 다음 심볼 처리 계속")
                failed_symbols.append(symbol)
                # 🔧 개별 심볼 오류 시 전체 중단하지 않고 계속 진행
                continue

        # 사이클 완료 로깅
        logger.info(f"🎯 실행 사이클 완료 - 성공: {len(processed_symbols)}개, 실패: {len(failed_symbols)}개")
        if processed_symbols:
            logger.info(f"✅ 성공한 심볼: {processed_symbols}")
        if failed_symbols:
            logger.warning(f"❌ 실패한 심볼: {failed_symbols}")

    def _process_symbol(self, symbol: str):
        """
        심볼별 처리

        Args:
            symbol: 코인 심볼
        """
        logger.info(f"{symbol} 처리 시작")

        # 1. 최신 데이터 수집
        market_data = self._collect_market_data(symbol)
        news_data = self._collect_news_data(symbol)

        if not market_data:
            logger.error(f"{symbol} 시장 데이터가 없습니다.")
            raise ValueError(f"{symbol} 시장 데이터가 없습니다.")

        # 2. InCA에 데이터 전달하여 중요성 평가
        importance_result = self.inca_agent.evaluate_importance(symbol, market_data, news_data)

        # 중요성 평가 결과 이벤트 발행
        importance_event = EventsBuilder.importance_evaluated(
            symbol=symbol,
            evaluation=importance_result,
            sender="execution_flow"
        )
        self.event_manager.publish(importance_event)

        # 중요도가 임계값을 넘지 않으면 처리 중단
        if not importance_result.get("is_important", False):
            logger.info(f"{symbol} 중요도가 낮아 처리를 중단합니다.")
            return

        # 3. HiAR에 데이터 전달하여 사고 흐름 생성
        reasoning_card = self.hiar_agent.generate_reasoning(
            symbol=symbol,
            market_data=market_data,
            news_data=news_data,
            importance_data=importance_result
        )

        # 사고 흐름 생성 결과 이벤트 발행
        reasoning_event = EventsBuilder.reasoning_generated(
            symbol=symbol,
            reasoning_card=reasoning_card,
            sender="execution_flow"
        )
        self.event_manager.publish(reasoning_event)

        # 4. SELA에 데이터 전달하여 전략 생성
        strategy = self.sela_agent.generate_strategy(
            symbol=symbol,
            market_data=market_data,
            reasoning_card=reasoning_card
        )

        # 전략 생성 결과 이벤트 발행
        strategy_event = EventsBuilder.strategy_generated(
            symbol=symbol,
            strategy=strategy,
            sender="execution_flow"
        )
        self.event_manager.publish(strategy_event)

        # 5. 전략 실행
        execution_result = self._execute_strategy(symbol, strategy, market_data)

        # 6. 실행 로그 생성 및 저장
        execution_log = self._create_execution_log(
            symbol=symbol,
            market_data=market_data,
            news_data=news_data,
            importance_result=importance_result,
            reasoning_card=reasoning_card,
            strategy=strategy,
            execution_result=execution_result
        )

        # 로그 저장
        log_id = self.data_store.save_execution_log(symbol, execution_log)

        # 실행 로그 생성 이벤트 발행
        log_event = EventsBuilder.execution_log_created(
            symbol=symbol,
            log_data={"log_id": log_id, **execution_log},
            sender="execution_flow"
        )
        self.event_manager.publish(log_event)

        logger.info(f"{symbol} 처리 완료")

    def _collect_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        최신 시장 데이터 수집

        Args:
            symbol: 코인 심볼

        Returns:
            Dict[str, Any]: 시장 데이터
        """
        try:
            # 🔧 BinanceUtils를 사용해서 최신 캔들 데이터 포함한 시장 데이터 수집
            from trading.utils.binance_utils import BinanceUtils
            
            # 바이낸스에서 최신 시장 데이터 + 캔들 데이터 수집
            logger.info(f"[ExecutionFlow] {symbol} 최신 시장 데이터 및 캔들 데이터 수집 중...")
            market_data = BinanceUtils.get_market_data(symbol)
            
            # 🔧 Debug logging
            recent_candles = market_data.get('recent_candles', [])
            candles = market_data.get('candles', [])
            logger.info(f"[ExecutionFlow] {symbol} 시장 데이터 수집 완료: recent_candles={len(recent_candles)}, candles={len(candles)}")
            
            if len(recent_candles) == 0 and len(candles) == 0:
                logger.warning(f"[ExecutionFlow] {symbol} BinanceUtils에서 캔들 데이터가 없음!")
            
            # 데이터 스토어에 저장
            self.data_store.save_market_data(symbol, market_data)
            logger.info(f"[ExecutionFlow] {symbol} 시장 데이터를 DataStore에 저장 완료")
            
            return market_data
            
        except Exception as e:
            logger.error(f"[ExecutionFlow] {symbol} BinanceUtils에서 시장 데이터 수집 실패: {e}")
            
            # 🔧 Fallback: 데이터 스토어에서 기존 데이터 조회
            logger.warning(f"[ExecutionFlow] {symbol} Fallback: DataStore에서 기존 데이터 조회")
            market_data = self.data_store.get_latest_market_data(symbol)

            # 데이터가 없거나 오래된 경우 오류 발생
            if not market_data:
                raise ValueError(f"{symbol} 시장 데이터가 없습니다.")

            # 데이터 수집 시간 확인
            data_timestamp = market_data.get("timestamp", 0)
            current_time = time.time()

            # 오래된 데이터 확인 (5분 이상 지난 데이터는 오래된 것으로 간주)
            if current_time - data_timestamp > 300:  # 5분
                logger.warning(f"{symbol} 시장 데이터가 오래되었습니다. ({int(current_time - data_timestamp)}초 전)")
                raise ValueError(f"{symbol} 시장 데이터가 오래되었습니다.")

            return market_data

    def _collect_news_data(self, symbol: str) -> List[Dict[str, Any]]:
        """
        최신 뉴스 데이터 수집

        Args:
            symbol: 코인 심볼

        Returns:
            List[Dict[str, Any]]: 뉴스 데이터 목록
        """
        # 데이터 스토어에서 최신 뉴스 데이터 조회 (최근 30분 이내)
        current_time = time.time()
        start_time = current_time - 1800  # 30분

        news_data = self.data_store.get_news_data(symbol, start_time, current_time)

        # 뉴스가 없어도 오류는 아님 (뉴스가 없는 경우 빈 리스트 반환)
        if not news_data:
            logger.info(f"{symbol} 관련 최근 뉴스가 없습니다.")

        return news_data

    def _execute_strategy(self, symbol: str, strategy: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        전략 실행

        Args:
            symbol: 코인 심볼
            strategy: 전략 데이터
            market_data: 시장 데이터

        Returns:
            Dict[str, Any]: 실행 결과
        """
        # 실행 ID 생성
        execution_id = str(uuid.uuid4())

        # 실행 시점 시간 및 가격
        execution_time = time.time()
        entry_price = market_data.get("price", 0.0)

        # 거래 타입
        trade_type = strategy.get("type", "none")  # 'buy', 'sell', 'none'

        # 실행 결과 초기화
        execution_result = {
            "execution_id": execution_id,
            "symbol": symbol,
            "strategy_id": strategy.get("strategy_id", ""),
            "timestamp": execution_time,
            "entry_time": execution_time,
            "entry_price": entry_price,
            "exit_time": None,
            "exit_price": None,
            "profit_loss": 0.0,
            "profit_loss_percent": 0.0,
            "status": "initiated",
            "trade_type": trade_type
        }

        # 실제 거래 실행 (시뮬레이션 또는 실제 거래)
        if trade_type in ["buy", "sell"]:
            if self.trading_mode == "simulation":
                # 시뮬레이션 모드
                execution_result["status"] = "simulated"
                logger.info(f"{symbol} 전략 시뮬레이션: {trade_type.upper()} @ {entry_price}")
            else:
                # 실제 거래 모드
                try:
                    # TODO: 실제 거래 실행 코드 구현
                    execution_result["status"] = "executed"
                    logger.info(f"{symbol} 전략 실행: {trade_type.upper()} @ {entry_price}")
                except Exception as e:
                    execution_result["status"] = "failed"
                    execution_result["error"] = str(e)
                    logger.error(f"{symbol} 전략 실행 실패: {e}")
        else:
            # 관망 전략
            execution_result["status"] = "no_action"
            logger.info(f"{symbol} 관망 전략 (조치 없음)")

        # 실행 결과 저장
        self.data_store.save_execution_result(symbol, execution_result)

        # 실행 완료 이벤트 발행
        execution_event = EventsBuilder.execution_completed(
            symbol=symbol,
            result=execution_result,
            sender="execution_flow"
        )
        self.event_manager.publish(execution_event)

        return execution_result

    def _create_execution_log(self,
                             symbol: str,
                             market_data: Dict[str, Any],
                             news_data: List[Dict[str, Any]],
                             importance_result: Dict[str, Any],
                             reasoning_card: Dict[str, Any],
                             strategy: Dict[str, Any],
                             execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        실행 로그 생성

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            news_data: 뉴스 데이터
            importance_result: 중요성 평가 결과
            reasoning_card: 사고 흐름 카드
            strategy: 생성된 전략
            execution_result: 실행 결과

        Returns:
            Dict[str, Any]: 실행 로그
        """
        # 현재 시간
        timestamp = time.time()

        # 실행 로그 생성
        execution_log = {
            "log_id": str(uuid.uuid4()),
            "symbol": symbol,
            "timestamp": timestamp,
            "market_situation": importance_result,
            "reasoning_card": reasoning_card,
            "strategy": strategy,
            "execution_result": execution_result,
            "market_data": market_data,
            "news_data": news_data,
            "created_at": timestamp
        }

        return execution_log

    # ===== 이벤트 핸들러 =====

    def _handle_news_event(self, event: Event):
        """뉴스 이벤트 핸들러"""
        # 필요한 경우 여기서 뉴스 이벤트 처리
        symbol = event.symbol
        news_data = event.data.get("news", {})
        logger.debug(f"뉴스 이벤트 수신: {symbol} - {news_data.get('title', '')}")

    def _handle_market_data_event(self, event: Event):
        """시장 데이터 이벤트 핸들러"""
        # 필요한 경우 여기서 시장 데이터 이벤트 처리
        symbol = event.symbol
        market_data = event.data.get("market_data", {})
        logger.debug(f"시장 데이터 이벤트 수신: {symbol} - 가격: {market_data.get('price', 0)}")

    def _handle_importance_event(self, event: Event):
        """중요성 평가 이벤트 핸들러"""
        # 필요한 경우 여기서 중요성 평가 이벤트 처리
        pass

    def _handle_reasoning_event(self, event: Event):
        """사고 흐름 이벤트 핸들러"""
        # 필요한 경우 여기서 사고 흐름 이벤트 처리
        pass

    def _handle_strategy_event(self, event: Event):
        """전략 생성 이벤트 핸들러"""
        # 필요한 경우 여기서 전략 생성 이벤트 처리
        pass

    def _handle_execution_completed_event(self, event: Event):
        """실행 완료 이벤트 핸들러"""
        # 필요한 경우 여기서 실행 완료 이벤트 처리
        pass


# 테스트 코드
if __name__ == "__main__":
    # 로깅 설정
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

    # 필요한 의존성이 모두 Mock으로 제공되는 경우에만 테스트 진행
    logger.info("실행 플로우 테스트는 의존성이 제공되는 경우에만 가능합니다.")
