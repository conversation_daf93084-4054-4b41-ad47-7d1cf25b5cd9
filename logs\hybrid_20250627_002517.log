2025-06-27 00:25:17 - hybrid_simulator - INFO - 로깅 시스템 초기화 완료 - 타임스탬프 테스트
2025-06-27 00:25:17 - hybrid_simulator - INFO - ===== 하이브리드 거래 시스템 초기화 =====
2025-06-27 00:25:17 - hybrid_simulator - INFO - 시스템 인코딩: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - 표준 출력 인코딩: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - 환경 변수 PYTHONIOENCODING: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - .env 파일 로드 완료
2025-06-27 00:25:17 - hybrid_simulator - INFO - OPENAI_API_KEY 환경 변수가 설정되어 있습니다.
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 환경 변수 확인: USE_VLLM=true, 파싱 결과=True
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 사용 여부: 커맨드라인=True, 환경변수=True, 최종=True
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 모델 설정: 환경변수=Qwen/Qwen3-14B-AWQ, 커맨드라인=Qwen/Qwen3-14B-AWQ, 최종=Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 서버 사용: http://localhost:8001/v1, 모델: Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - hybrid_simulator - INFO - VLLMClientFactory 클래스 임포트 성공
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 클라이언트 초기화 시작: URL=http://localhost:8001/v1, 모델=Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - models.vllm_client_factory - INFO - Using timeout from environment: 600 seconds
2025-06-27 00:25:17 - models.vllm_client_factory - INFO - Creating enhanced VLLM client (model: Qwen/Qwen3-14B-AWQ, timeout: 600s, max_tokens: 8192)
2025-06-27 00:25:17 - models.vllm_client_enhanced - INFO - Qwen3 모델 감지됨: Qwen/Qwen3-14B-AWQ
