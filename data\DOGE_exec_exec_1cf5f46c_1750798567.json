{"execution_id": "exec_1cf5f46c_1750798567", "symbol": "DOGE", "timestamp": 1750798567, "datetime": "2025-06-25 05:56:07", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "5d5bb6ee-5cbd-4e45-8039-3e4f18a4ad3f", "timestamp": 1750798567, "market_data": {"id": "market_DOGE_1750798546", "symbol": "DOGE", "timestamp": 1750798546, "datetime": "2025-06-25 05:55:46", "date": "2025-06-25", "time": "05:55:46", "price": 0.16514, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.16514, "volume": 6370244166.0, "volume_24h": 6370244166.0, "high_24h": 0.16763, "low_24h": 0.1581, "percent_change_24h": 4.076, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 12728978, "social_dominance": 0.272, "social_contributors": 127289, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750798546, "news_count": 27, "ema_7": 0.16492285714285715, "ema_14": 0.16447785714285712, "ema_25": 0.1640272, "ema_50": 0.1640257999999999, "ema_99": 0.*****************, "ema_200": 0.*****************, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.***************, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": ***********.19, "recent_news_titles": ["Job Hunting Firm CareerBuilder + Monster Files Bankruptcy", "China on Cusp of Seeing Over 100 DeepSeeks, Ex-Top Official Says", "Truce Between Israel and Iran Back On After Trump Lashes Out", "Oil Holds Slump as Mideast Conflict Shifts to Fragile Ceasefire", "<PERSON> administration scrambles to rehire key federal workers after DOGE firings | CNN Politics"], "top_social_posts": [{"text": "Lmao. Democrat Rep <PERSON> brought a poll to try to prove that DOGE and @elonmusk are unpopular but the poll was FALSE! 😂", "sentiment": 3.14, "platform": "tweet"}, {"text": "Dogecoin up over 5%. For a coin started as a joke, DOGE is getting the last laugh. Bark twice if you turned your rent into a Lambo.", "sentiment": 3.19, "platform": "tweet"}, {"text": "DOGE hearing erupts into gavel-slamming showdown: 'You're not recognized!' #shorts #politics #us", "sentiment": 2.79, "platform": "youtube-video"}, {"text": "BREAKING: DOGE Hearing Grinds To A Complete Halt When <PERSON> Calls To Subpoena Elon Musk", "sentiment": 3.1, "platform": "youtube-video"}, {"text": "You're late son", "sentiment": 3, "platform": "tweet"}, {"text": "Sen. @HawleyMO on the BBB: \"We need to root out the climate change, Green New Deal garbage that Biden spent $4 trillion on. We need to make permanent all the DOGE cuts.\"", "sentiment": 2.9, "platform": "tweet"}, {"text": "<PERSON> says MAGA is living with their own decisions.\n\n", "sentiment": 3.4, "platform": "tweet"}, {"text": "Mom upset son wants a job, worried it’ll affect her Section 8.\n\n", "sentiment": 2.88, "platform": "tweet"}, {"text": "🔥 @TopperbyUphold's latest Top 10 assets purchased is here!\n\n🔀 We've got a shakeup:\n\n$SOL claims 2nd\n$USDC jumps to 4th\n$USDT falls to 5th\n$LTC falls to 7th\n$KAS climbs to 8th\n$XLM + $SUI join at 9th + 10th\n$DOGE + $AVAX 👊 off\n\n$BTC + $XRP unchanged\n\n💬  Which alt moves next?", "sentiment": 3.1, "platform": "tweet"}, {"text": "Part 2 | Prepare for the crypto bull market 2025. Altcoins vs Bitcoin. K1 NG) Potential coins. #bitcoin #crypto #altcoin #rp #ethereum #solana #ondo #cfg #chainlink #money #investing #cryptocurrency #trump #btc #eth #investment #doge", "sentiment": 3.04, "platform": "tiktok-video"}], "recent_candles": [[1750797360000, "0.164290", "0.164410", "0.164270", "0.164410", "957260", 1750797419999, "157321.376470", 473, "812217", "133491.073670", "0"], [1750797420000, "0.164410", "0.164480", "0.164390", "0.164440", "1294678", 1750797479999, "212894.091540", 582, "1009892", "166062.964360", "0"], [1750797480000, "0.164430", "0.164510", "0.164430", "0.164500", "915411", 1750797539999, "150559.444550", 595, "474534", "78048.601650", "0"], [1750797540000, "0.164510", "0.164650", "0.164510", "0.164590", "2934179", 1750797599999, "482953.311550", 969, "1453134", "239173.312250", "0"], [1750797600000, "0.164590", "0.164690", "0.164590", "0.164670", "1075981", 1750797659999, "177151.659780", 630, "848011", "139615.113550", "0"], [1750797660000, "0.164660", "0.165070", "0.164660", "0.164960", "36345609", 1750797719999, "5992620.903410", 3340, "32474126", "5354440.788120", "0"], [1750797720000, "0.164950", "0.164980", "0.164860", "0.164860", "2607823", 1750797779999, "430131.177790", 729, "827400", "136478.720310", "0"], [1750797780000, "0.164870", "0.164880", "0.164770", "0.164770", "3988809", 1750797839999, "657459.755920", 855, "985494", "162453.012460", "0"], [1750797840000, "0.164770", "0.164840", "0.164760", "0.164830", "916155", 1750797899999, "150974.007950", 433, "498159", "82093.980980", "0"], [1750797900000, "0.164840", "0.165120", "0.164840", "0.165090", "4039462", 1750797959999, "666451.062510", 1745, "3211209", "529797.746130", "0"], [1750797960000, "0.165080", "0.165120", "0.164940", "0.165090", "2837987", 1750798019999, "468324.240170", 840, "943539", "155728.072660", "0"], [1750798020000, "0.165090", "0.165090", "0.164990", "0.165080", "1807965", 1750798079999, "298359.331150", 624, "665938", "109904.122170", "0"], [1750798080000, "0.165090", "0.165140", "0.165040", "0.165070", "1125632", 1750798139999, "185826.532020", 541, "711659", "117485.283290", "0"], [1750798140000, "0.165060", "0.165300", "0.165060", "0.165230", "2246768", 1750798199999, "371174.294810", 880, "1692779", "279650.550560", "0"], [1750798200000, "0.165230", "0.165240", "0.164860", "0.164880", "2602081", 1750798259999, "429410.604950", 1020, "1298014", "214167.146620", "0"], [1750798260000, "0.164880", "0.165080", "0.164820", "0.165060", "957238", 1750798319999, "157891.537840", 625, "638553", "105324.068290", "0"], [1750798320000, "0.165050", "0.165110", "0.164980", "0.165090", "2539300", 1750798379999, "419050.835050", 578, "585636", "96649.800110", "0"], [1750798380000, "0.165100", "0.165240", "0.165060", "0.165070", "3262102", 1750798439999, "538700.455140", 701, "1640364", "270899.883960", "0"], [1750798440000, "0.165060", "0.165120", "0.165030", "0.165120", "1151407", 1750798499999, "190050.322580", 499, "840941", "138805.730730", "0"], [1750798500000, "0.165120", "0.165200", "0.165080", "0.165200", "2209434", 1750798559999, "364858.897600", 490, "1555552", "256880.391470", "0"]], "candles_count": 20, "data_timestamp": 1750798546, "has_timeseries_data": true, "short_term_change_pct": 0.0848176420695512, "recent_high": 0.16524, "recent_low": 0.16482}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bullish", "reasoning": "The short-term trend shows a bullish direction with 3 out of 5 candles closing higher, forming an ascending pattern. The price has increased by 0.085% in the last 5 minutes, and the volume is consistent with the upward movement, indicating strong buying pressure. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "buy", "trading_direction": "long", "signal_direction": "bullish", "keywords": ["bullish", "volume confirmation", "short-term trend"], "raw_response": " NO MARKDOWN.\n\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term trend shows a bullish direction with 3 out of 5 candles closing higher,...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_95d103b3_1750798553"}, "strategy": {"strategy_id": "5d5bb6ee-5cbd-4e45-8039-3e4f18a4ad3f", "symbol": "DOGE", "timestamp": 1750798565, "type": "buy", "entry_price": 0.16514, "stop_loss": 0.16184, "take_profit": 0.17339, "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 구매 신호를 제공하고 있습니다. 현재 가격은 $0.16514로, RSI(14)는 52.3으로 중립 영역에 위치해 있으며, MACD는 상승 추세를 보이고 있습니다. 볼린저 밴드는 상단으로 확장되며, 가격이 중앙선을 돌파한 후 상승 추세를 이어가고 있습니다. 이러한 기술적 지표와 InCA의 구매 신호를 종합적으로 고려할 때, 단기적인 상승 가능성이 높습니다.", "confidence": 0.78, "reasoning_card_id": "card_95d103b3_1750798553", "risk_level": "medium", "key_points": ["InCA 시스템의 구매 신호", "MACD 상승 추세", "볼린저 밴드 상단 확장"], "market_context": {"price": 0.16514, "percent_change_24h": 4.076, "timestamp": 1750798546}, "paper_based": false, "risk_reward": 2.***************, "importance": 9.168273035060777, "consensus_signal": "buy", "consensus_confidence": 0.78, "consensus_breakdown": {"short_term": {"action": "buy", "situation": "bullish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "buy", "importance": 0.5, "confidence": 0.78, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "buy", "consensus_confidence": 0.78, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_1cf5f46c_1750798567"}}