2025-06-26 19:52:59 - database.news_vectordb - INFO - huggingface_hub에 cached_download 함수가 없어 패치 적용
2025-06-26 19:52:59 - database.news_vectordb - INFO - huggingface_hub.cached_download 패치 완료
2025-06-26 19:53:02 - database.market_data - INFO - SentenceTransformer 모듈 로드 성공
2025-06-26 19:53:02 - simulator.utils.market_utils - INFO - LunarCrush API 키 상태: 설정됨
2025-06-26 19:53:02 - lunar_monitor - INFO - LunarCrush API initialized with API key
2025-06-26 19:53:02 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-26 19:53:02 - database.news_vectordb - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-26 19:53:02 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-26 19:53:04 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-26 19:53:04 - database.news_vectordb - INFO - Successfully loaded model: all-MiniLM-L6-v2
2025-06-26 19:53:04 - lunar_monitor - INFO - Successfully initialized NewsVectorDB
2025-06-26 19:53:04 - database.market_data - INFO - Initializing MarketDataDB
2025-06-26 19:53:04 - database.market_data - INFO - ChromaDB client initialized successfully
2025-06-26 19:53:04 - database.market_data - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-26 19:53:04 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-26 19:53:04 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-26 19:53:04 - database.market_data - INFO - SentenceTransformer model loaded successfully
2025-06-26 19:53:04 - database.market_data - INFO - Successfully accessed collection: topic_metrics
2025-06-26 19:53:04 - database.market_data - INFO - Successfully accessed collection: time_series
2025-06-26 19:53:04 - database.market_data - INFO - MarketDataDB initialized successfully
2025-06-26 19:53:04 - lunar_monitor - INFO - Successfully initialized MarketDataDB
2025-06-26 19:53:04 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:04 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:04 - simulator.utils.market_utils - INFO - LunarMonitor API & Collector 연결 초기화 완료
2025-06-26 19:53:04 - simulator.trading.portfolio - INFO - 🎯 통합 생각카드 시스템 로드 성공
2025-06-26 19:53:04 - hybrid_simulator - INFO - PyTorch 임포트 성공
2025-06-26 19:53:04 - hybrid_simulator - INFO - ChatTS 클라이언트 임포트 성공
2025-06-26 19:53:04 - hybrid_simulator - INFO - 로깅 시스템 초기화 완료 - 타임스탬프 테스트
2025-06-26 19:53:04 - hybrid_simulator - INFO - ===== 하이브리드 거래 시스템 초기화 =====
2025-06-26 19:53:04 - hybrid_simulator - INFO - 시스템 인코딩: utf-8
2025-06-26 19:53:04 - hybrid_simulator - INFO - 표준 출력 인코딩: utf-8
2025-06-26 19:53:04 - hybrid_simulator - INFO - 환경 변수 PYTHONIOENCODING: utf-8
2025-06-26 19:53:04 - hybrid_simulator - INFO - .env 파일 로드 완료
2025-06-26 19:53:04 - hybrid_simulator - INFO - OPENAI_API_KEY 환경 변수가 설정되어 있습니다.
2025-06-26 19:53:04 - hybrid_simulator - INFO - vLLM 환경 변수 확인: USE_VLLM=true, 파싱 결과=True
2025-06-26 19:53:04 - hybrid_simulator - INFO - vLLM 사용 여부: 커맨드라인=True, 환경변수=True, 최종=True
2025-06-26 19:53:04 - hybrid_simulator - INFO - vLLM 모델 설정: 환경변수=Qwen/Qwen3-14B-AWQ, 커맨드라인=Qwen/Qwen3-14B-AWQ, 최종=Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:04 - hybrid_simulator - INFO - vLLM 서버 사용: http://localhost:8001/v1, 모델: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:04 - hybrid_simulator - INFO - VLLMClientFactory 클래스 임포트 성공
2025-06-26 19:53:04 - hybrid_simulator - INFO - vLLM 클라이언트 초기화 시작: URL=http://localhost:8001/v1, 모델=Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:04 - models.vllm_client_factory - INFO - Using timeout from environment: 600 seconds
2025-06-26 19:53:04 - models.vllm_client_factory - INFO - Creating enhanced VLLM client (model: Qwen/Qwen3-14B-AWQ, timeout: 600s, max_tokens: 8192)
2025-06-26 19:53:04 - models.vllm_client_enhanced - INFO - Qwen3 모델 감지됨: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:06 - models.vllm_client_enhanced - INFO - VLLM 서버 설정 확인: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': 1750935186, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-e1ac7f99e0464c5cbb2e3c70f0733c07', 'object': 'model_permission', 'created': 1750935186, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-26 19:53:06 - models.vllm_client_enhanced - INFO - VLLM 서버 max_model_len: 9096
2025-06-26 19:53:06 - models.vllm_client_enhanced - INFO - VLLM 클라이언트 설정: 타임아웃=600초, 최대토큰=8192
2025-06-26 19:53:06 - models.vllm_session_manager - INFO - VLLMSessionManager 초기화 완료
2025-06-26 19:53:06 - models.vllm_client_enhanced - INFO - Qwen3 모델 최적화 설정 적용: temperature=0.6, top_p=0.95, top_k=20, presence_penalty=1.5
2025-06-26 19:53:06 - models.vllm_client_enhanced - INFO - Enhanced VLLM client initialized (server: http://localhost:8001/v1, model: Qwen/Qwen3-14B-AWQ)
2025-06-26 19:53:06 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 초기화 완료: <models.vllm_client_enhanced.VLLMClientEnhanced object at 0x000001A7F6E8DBD0>
2025-06-26 19:53:06 - hybrid_simulator - INFO - vLLM 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:06 - hybrid_simulator - INFO - vLLM 클라이언트 속성: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_initialized', '_instance', 'default_presence_penalty', 'default_temperature', 'default_top_k', 'default_top_p', 'generate', 'generate_fast', 'generate_json', 'health_check', 'is_qwen3', 'max_tokens', 'model_name', 'prompt_processor', 'response_parser', 'server_url', 'session_manager', 'timeout']
2025-06-26 19:53:06 - hybrid_simulator - INFO - vLLM 클라이언트 서버 URL: http://localhost:8001/v1
2025-06-26 19:53:06 - hybrid_simulator - INFO - vLLM 클라이언트 모델명: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:06 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 시작
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - VLLM server health check successful: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': **********, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-314626cefed446e88beda84c3c6bd67a', 'object': 'model_permission', 'created': **********, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-26 19:53:08 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 결과: True
2025-06-26 19:53:08 - hybrid_simulator - INFO - vLLM 서버 연결 성공
2025-06-26 19:53:08 - hybrid_simulator - INFO - vLLM 서버 테스트 중 (짧은 프롬프트)...
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 90자
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-26 19:53:08 - models.vllm_prompt_processor - WARNING - 프롬프트에서 심볼을 추출할 수 없습니다.
2025-06-26 19:53:08 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 2/8)
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-26 19:53:08 - models.vllm_session_manager - INFO - Created new session: cca72ca8-d6a1-49a1-bf9e-7f53577523d4 for sela agent
2025-06-26 19:53:08 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: sela_execution_default → cca72ca8-d6a1-49a1-bf9e-7f53577523d4
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - sela execution 세션 ID: cca72ca8-d6a1-49a1-bf9e-7f53577523d4 (기본)
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 504
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Session ID: cca72ca8-d6a1-49a1-bf9e-7f53577523d4
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think



market data, HiAR organizes reasoning, SELA executes strategies.






CRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT U...
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\n\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 100, 'temperature': 0.0, 'top_p': 0.7, 'stop': []}
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 126
2025-06-26 19:53:08 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - Text generation complete (time: 3.59s)
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "analysis": "The provided statement outlines a system where market data is processed by HiAR for reasoning, and SELA is responsible for executing strategies. This suggests a division of labor betw...
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 480자):
2025-06-26 19:53:12 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {
  "analysis": "The provided statement outlines a system where market data is processed by HiAR for reasoning, and SELA is responsible for executing strategies. This suggests a division of labor between analysis and execution, which is common in automated trading systems.",
  "confidence": 0.85,
  "reasoning": "The system appears to separate high-level analysis (HiAR) from strategy execution (SELA), which aligns with typical architectures in financial systems. The confidence
2025-06-26 19:53:12 - models.vllm_client_enhanced - WARNING - 🔍 HiAR 응답에 불완전한 JSON 구조 발견 (토큰 제한으로 잘림)
2025-06-26 19:53:12 - hybrid_simulator - INFO - vLLM 서버 테스트 성공
2025-06-26 19:53:12 - hybrid_simulator - WARNING - !!!!! 주의: 실제 거래 모드로 실행합니다 !!!!!
2025-06-26 19:53:12 - hybrid_simulator - WARNING - !!!!! 실제 자금이 사용됩니다 !!!!!
2025-06-26 19:53:13 - hybrid_simulator - INFO - ChatTS 모델 대신 vLLM 클라이언트를 사용합니다.
2025-06-26 19:53:13 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 래퍼 함수 생성
2025-06-26 19:53:13 - models.vllm_client_factory - INFO - Created wrapper for Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:13 - hybrid_simulator - INFO - vLLM 모델 래퍼 생성 완료: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:13 - hybrid_simulator - INFO - vLLM 모델 설정 완료: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:13 - hybrid_simulator - INFO - LLM 모델 설정 준비 완료 (vLLM 사용 시 나중에 설정됨)
2025-06-26 19:53:13 - binance.binance_utils - INFO - 프로덕션 모드 사용 중: https://fapi.binance.com
2025-06-26 19:53:13 - binance.binance_utils - INFO - API 키: iVljo...EypUp
2025-06-26 19:53:13 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:13 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935192549}
2025-06-26 19:53:13 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935192549
2025-06-26 19:53:13 - binance.binance_utils - INFO - ✅ 바이낸스 서버 시간 동기화 성공: 1750935192549, 로컬 시간: 1750935193691, 차이: 1142ms
2025-06-26 19:53:13 - binance.binance_utils - WARNING - ⚠️ 로컬 시간과 서버 시간의 차이가 큽니다: 1142ms
2025-06-26 19:53:13 - binance.binance_utils - INFO - 바이낸스 API 연결 테스트 성공
2025-06-26 19:53:13 - binance.binance_utils - INFO - API 키: 설정됨
2025-06-26 19:53:13 - hybrid_simulator - INFO - 바이낸스 API 초기화 완료 (테스트넷: False)
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - ✅ Portfolio 초기화: 바이낸스 유틸리티 연결됨 (<class 'binance.binance_utils.BinanceUtils'>)
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 실제 거래 모드가 사용됩니다. 바이낸스 API를 통해 잔액을 동기화하세요.
2025-06-26 19:53:13 - simulator.trading.portfolio - WARNING - !!!!! 실제 거래 모드로 실행 중입니다 !!!!!
2025-06-26 19:53:13 - simulator.trading.portfolio - WARNING - !!!!! 실제 자금이 사용됩니다 !!!!!
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 기존 포트폴리오 로드 완료: 잔액 $610.98 USDT
2025-06-26 19:53:13 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750934424_1750935193
2025-06-26 19:53:13 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BTC] 통합 생각카드 생성: card_trade_BTC_1750934599_1750935193
2025-06-26 19:53:13 - trading.thinking_cards.position_card_manager - INFO - 🔧 최근 카드 로딩 완료: 2개 로드, 10개 스킵 (1시간 이내)
2025-06-26 19:53:13 - trading.thinking_cards.position_card_manager - INFO - 🎯 포지션 카드 매니저 초기화 완료 (활성 카드: 2개, 완료 카드: 0개)
2025-06-26 19:53:13 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750934424_1750935193
2025-06-26 19:53:13 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BTC] 통합 생각카드 생성: card_trade_BTC_1750934599_1750935193
2025-06-26 19:53:13 - trading.thinking_cards.position_card_manager - INFO - 🔧 최근 카드 로딩 완료: 2개 로드, 10개 스킵 (1시간 이내)
2025-06-26 19:53:13 - trading.thinking_cards.position_card_manager - INFO - 🎯 포지션 카드 매니저 초기화 완료 (활성 카드: 2개, 완료 카드: 0개)
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 🎯 통합 생각카드 매니저 초기화 성공: G:\ai_bot_trading\data\thinking_cards
2025-06-26 19:53:13 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-26 19:53:13 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 환경변수 원본 값:
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_CONFIDENCE (str): '0.01'
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_IMPORTANCE (str): '0.001'
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_RISK_REWARD_RATIO (str): '0.001'
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 적응형 임계값 로드 완료:
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_CONFIDENCE: 0.01
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_IMPORTANCE: 0.001
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_RISK_REWARD_RATIO: 0.001
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - ChromaDB에 저장된 패턴이 없습니다. 새로 시작합니다.
2025-06-26 19:53:13 - trading.strategies.intelligent_position_sizing - INFO - 패턴 파일이 없습니다. 새로 시작합니다: G:\ai_bot_trading\data\trading_patterns\learned_patterns.json
2025-06-26 19:53:13 - hybrid_simulator - INFO - 고급 트레이딩 시스템 초기화 완료
2025-06-26 19:53:13 - models.vllm_request_queue - INFO - vLLM 요청 큐 초기화 완료
2025-06-26 19:53:13 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-26 19:53:13 - trading.hybrid_architecture.data_logger - INFO - 데이터 로거 초기화 완료: G:\ai_bot_trading\data
2025-06-26 19:53:13 - hybrid_simulator - WARNING - LLM 모델이 설정되지 않았습니다. 하이브리드 컨트롤러 초기화를 건너뜁니다.
2025-06-26 19:53:13 - hybrid_simulator - WARNING - 나중에 chatts_model을 설정한 후 initialize_controller() 메서드를 호출하세요.
2025-06-26 19:53:13 - models.vllm_request_queue - INFO - vLLM 클라이언트 설정 완료
2025-06-26 19:53:13 - hybrid_simulator - INFO - vLLM 요청 큐 초기화 완료
2025-06-26 19:53:13 - hybrid_simulator - INFO - 최소 신뢰도 임계값 설정 (환경변수): 0.01
2025-06-26 19:53:13 - hybrid_simulator - INFO - 최소 중요도 임계값 설정 (환경변수): 0.001
2025-06-26 19:53:13 - hybrid_simulator - INFO - 연속 모드 설정 완료
2025-06-26 19:53:13 - hybrid_simulator - INFO - 하이브리드 시스템에 vLLM 모델 설정 완료
2025-06-26 19:53:13 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-26 19:53:13 - trading.hybrid_architecture.data_logger - INFO - 데이터 로거 초기화 완료: G:\ai_bot_trading\data
2025-06-26 19:53:13 - hybrid_simulator - INFO - LLM 모델 사용: vllm - Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:15 - simulator.utils.vllm_client - INFO - vLLM API 연결 성공: 1개 모델 사용 가능
2025-06-26 19:53:15 - simulator.utils.vllm_client - INFO - 모델 'Qwen/Qwen3-14B-AWQ' 사용 가능
2025-06-26 19:53:15 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 llm_client 설정 완료: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:15 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-26 19:53:15 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-26 19:53:15 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 임베딩 함수 생성 완료: all-MiniLM-L6-v2
2025-06-26 19:53:16 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'inca_experiences_v3' 호환성 확인 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 벡터 DB 초기화 완료: 138개 경험 데이터
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 초기화 완료 (중요도 임계값: 0.3)
2025-06-26 19:53:16 - hybrid_simulator - INFO - InCA 에이전트에 시스템 설명 전달 완료 (학습 기능 활성화, LLM: 있음)
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.hiar_factory - INFO - FastHiAR 어댑터 생성
2025-06-26 19:53:16 - offline_pattern_generator - INFO - 5개 패턴 로드 완료
2025-06-26 19:53:16 - fast_hiar_agent - INFO - FastHiAR 에이전트 초기화 완료: 5개 패턴 로드
2025-06-26 19:53:16 - trading.reasoning.hiar_adapter - INFO - 기존 FastHiAR 에이전트 로드 완료
2025-06-26 19:53:16 - hybrid_simulator - INFO - HiAR 에이전트에 시스템 설명 전달 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: VLLMWrapper (VLLMWrapper 불필요)
2025-06-26 19:53:16 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-26 19:53:16 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: G:\ai_bot_trading\data\strategy_db\strategies.db
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-26 19:53:16 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 Tree Search 기반 SELA 에이전트 초기화 완료
2025-06-26 19:53:16 - hybrid_simulator - INFO - SELA 에이전트에 시스템 설명 전달 완료
2025-06-26 19:53:16 - HybridInterface - INFO - HybridInterface initialized
2025-06-26 19:53:16 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-26 19:53:16 - trading.hybrid_architecture.data.execution_log - INFO - 실행 로그 데이터베이스 초기화 완료: G:\ai_bot_trading\data\execution_logs.db
2025-06-26 19:53:16 - trading.hybrid_architecture.data.data_integration - INFO - 하이브리드 데이터 통합 초기화 완료
2025-06-26 19:53:16 - HybridInterface - INFO - Vector database initialized
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 인터페이스 초기화 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 계층적 합의 시스템 비활성화 완료 - SELA 직접 사용
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 신호 안정화 메커니즘 초기화 완료 (연속모드: True, 활성화: False)
2025-06-26 19:53:16 - trading.hybrid_architecture.lunar_data_collector - INFO - 데이터베이스 초기화 완료: data/lunar_data.db
2025-06-26 19:53:16 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: data/hybrid_store.db
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 🧹 시스템 시작 시 내부 포지션 강제 정리 실행
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🧹 내부 포지션 강제 정리 시작: ['SOL', 'BNB']
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🗑️ 제거할 포지션: BNB long (ID: sync_BNB_1750934424)
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - ✅ 포지션 제거 완료: BNB long
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - ✅ 자산 제거 완료: SOL 0.72
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🎯 내부 포지션 강제 정리 완료: 1개 포지션 제거
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ 내부 포지션 강제 정리 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 시스템 시작 시 바이낸스와 강제 동기화 실행
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔥 바이낸스와 강제 동기화 시작
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-26 19:53:16 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:16 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935195089}
2025-06-26 19:53:16 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935195089
2025-06-26 19:53:16 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935195089
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 522
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -0.68077584)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.0792)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -1.04684328)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BTCUSDT = 0.001 (PnL: 0.0479)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - positionAmt: 0.00 (float: 0.0)
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - entryPrice: 0.0
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - unrealizedProfit: 0.00000000
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:53:16 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - unrealizedProfit: -1.04684328
2025-06-26 19:53:16 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 바이낸스 실제 활성 포지션: 4개
2025-06-26 19:53:16 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:16 - binance.binance_utils - INFO - 변화율(%): 1.218
2025-06-26 19:53:16 - binance.binance_utils - INFO - 거래량: 5671989.365
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 [ETHUSDT] long 포지션:
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 수량: 0.023
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 진입가: $2486.1300
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 현재가: $2456.3800
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-0.6808
2025-06-26 19:53:16 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:16 - binance.binance_utils - INFO - 변화율(%): 0.192
2025-06-26 19:53:16 - binance.binance_utils - INFO - 거래량: 338898.19
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 [BNBUSDT] long 포지션:
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 수량: 0.09
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 진입가: $646.6100
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 현재가: $645.7300
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-0.0792
2025-06-26 19:53:16 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:16 - binance.binance_utils - INFO - 변화율(%): -1.917
2025-06-26 19:53:16 - binance.binance_utils - INFO - 거래량: 4968254122
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 [DOGEUSDT] long 포지션:
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 수량: 504.0
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 진입가: $0.1643
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 현재가: $0.1622
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-1.0468
2025-06-26 19:53:16 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:16 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:16 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:16 - binance.binance_utils - INFO - 거래량: 148167.266
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 [BTCUSDT] long 포지션:
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 수량: 0.001
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 진입가: $107311.4000
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 현재가: $107359.3000
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO -    - 미실현 손익: $0.0479
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 내부 포지션: 3개
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 내부 [ETH] long 포지션: 진입가 $2486.1300, PnL -1.18%
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 내부 [DOGE] long 포지션: 진입가 $0.1643, PnL -1.28%
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔍 내부 [BTC] long 포지션: 진입가 $107313.1000, PnL 0.00%
2025-06-26 19:53:16 - simulator.trading.portfolio - INFO - 🔥 바이낸스와 강제 동기화 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ 바이낸스 강제 동기화 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.data_store - INFO - 🔧 오래된 카드 정리 완료: 0개 삭제 (3일 이전)
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 정리할 오래된 카드가 없습니다
2025-06-26 19:53:16 - trading.hybrid_architecture.utils.virtual_position_tracker - INFO - 가상 포지션 추적기 초기화 완료 (중립 임계값: 0.5%)
2025-06-26 19:53:16 - trading.hybrid_architecture.utils.neutral_signal_evaluator - INFO - 중립 신호 평가기 초기화 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 🔮 가상 포지션 추적기 초기화 완료 (중립 임계값: 0.5%, 평가 주기: 3분)
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 컨트롤러 초기화 완료
2025-06-26 19:53:16 - models.vllm_request_queue - INFO - vLLM 클라이언트 설정 완료
2025-06-26 19:53:16 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (직접 접근 및 큐 모두)
2025-06-26 19:53:16 - hybrid_simulator - INFO - 하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (client 속성)
2025-06-26 19:53:16 - hybrid_simulator - INFO - 하이브리드 컨트롤러 초기화 완료
2025-06-26 19:53:16 - simulator.bridge - INFO - 데이터 수집 간격: 60초
2025-06-26 19:53:16 - simulator.bridge - INFO - 학습 간격(일반 모드): 60초
2025-06-26 19:53:16 - simulator.bridge - INFO - 연속 모드 대기 시간: 60초
2025-06-26 19:53:16 - simulator.bridge - INFO - 🔍 LLM 클라이언트 디버깅: config.get('llm_client') = True
2025-06-26 19:53:16 - simulator.bridge - INFO - ✅ 설정에서 LLM 클라이언트 참조 가져오기 성공: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:16 - simulator.bridge - INFO - LunarDataCollector 인스턴스 연결 완료
2025-06-26 19:53:16 - simulator.bridge - INFO - 크로마DB 컬렉션 초기화 완료: execution_results, market_data, reasoning_traces, trading_strategies
2025-06-26 19:53:16 - database.news_vectordb - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-26 19:53:16 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-26 19:53:16 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-26 19:53:16 - database.news_vectordb - INFO - Successfully loaded model: all-MiniLM-L6-v2
2025-06-26 19:53:16 - trading.hybrid_architecture.news_vector_bridge - INFO - NewsVectorBridge 초기화 완료
2025-06-26 19:53:16 - simulator.bridge - INFO - 뉴스 벡터 브릿지 초기화 완료
2025-06-26 19:53:16 - simulator.trading.prediction_history - INFO - 예측 히스토리 로드 완료: 16578개 예측
2025-06-26 19:53:16 - simulator.trading.prediction_history - INFO - 예측 히스토리 관리자 초기화 완료: 16578개 예측 로드됨
2025-06-26 19:53:16 - flow - INFO - 흐름 로거 초기화 완료 - 타임스탬프 테스트
2025-06-26 19:53:16 - flow - INFO - --------- 실행 플로우 시뮬레이터 시작 ---------
2025-06-26 19:53:16 - flow - INFO - 거래 모드: real
2025-06-26 19:53:16 - flow - INFO - 테스트넷: False
2025-06-26 19:53:16 - flow - INFO - 중요도 임계값: 0.3
2025-06-26 19:53:16 - flow - INFO - 처리 심볼: BTC, ETH, SOL, BNB, DOGE
2025-06-26 19:53:16 - flow - INFO - ----------------------------------------
2025-06-26 19:53:16 - simulator.execution_flow.simulator - INFO - ExecutionFlowSimulator 초기화: symbols=['BTC', 'ETH', 'SOL', 'BNB', 'DOGE'], testnet=False, trading_mode=real, importance_threshold=0.3
2025-06-26 19:53:16 - simulator.bridge - INFO - ExecutionFlowSimulator에 hybrid_controller 참조 추가 완료
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - LearningLoopAnalyzer 초기화: 심볼=['BTC', 'ETH', 'SOL', 'BNB', 'DOGE']
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - 전달받은 심볼: ['BTC', 'ETH', 'SOL', 'BNB', 'DOGE']
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - LearningLoopAnalyzer: vLLM 클라이언트 사용 설정됨
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - LLM 클라이언트 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - LLM 모델명: Qwen/Qwen3-14B-AWQ
2025-06-26 19:53:16 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-26 19:53:16 - simulator.learning_loop.analyzer - INFO - HNSW 컬렉션 최적화 시작...
2025-06-26 19:53:16 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' 데이터 백업 중...
2025-06-26 19:53:17 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' HNSW 테스트 통과 - 재생성 불필요
2025-06-26 19:53:17 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' 데이터 백업 중...
2025-06-26 19:53:17 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' HNSW 테스트 통과 - 재생성 불필요
2025-06-26 19:53:17 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' 데이터 백업 중...
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' HNSW 테스트 통과 - 재생성 불필요
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - HNSW 컬렉션 최적화 완료
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' 정상 작동
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' 정상 작동
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' 정상 작동
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 임베딩 함수 생성 완료: all-MiniLM-L6-v2
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_strategies' 호환성 확인 완료
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_reasonings' 호환성 확인 완료
2025-06-26 19:53:18 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_evaluations' 호환성 확인 완료
2025-06-26 19:53:18 - simulator.learning_loop.analyzer - INFO - ChromaDB 컬렉션 초기화 완료 (안전 모드)
2025-06-26 19:53:18 - simulator.learning_loop.analyzer - INFO - ChromaDB 클라이언트 초기화 완료
2025-06-26 19:53:18 - simulator.bridge - INFO - HybridSimulatorBridge 초기화 완료
2025-06-26 19:53:18 - hybrid_simulator - INFO - 하이브리드 시뮬레이터 브릿지 초기화 완료
2025-06-26 19:53:18 - hybrid_simulator - INFO - 하이브리드 컨트롤러 초기화 성공
2025-06-26 19:53:18 - hybrid_simulator - INFO - 연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)
2025-06-26 19:53:18 - hybrid_simulator - INFO - 하이브리드 거래 시스템 시작: 5 심볼, 모드: 실제 거래
2025-06-26 19:53:18 - hybrid_simulator - INFO - 연속 모드 활성화: 실행 → 학습 → 실행 → 학습 순서로 연속 실행
2025-06-26 19:53:18 - hybrid_simulator - INFO - 초기 시장 데이터 로드 중...
2025-06-26 19:53:18 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:18 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935197107}
2025-06-26 19:53:18 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935197107
2025-06-26 19:53:18 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935197107
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-26 19:53:18 - hybrid_simulator - INFO - USDT 잔액 정보 (API: 메인넷):
2025-06-26 19:53:18 - hybrid_simulator - INFO -   지갑 잔액: 610.68 USDT
2025-06-26 19:53:18 - hybrid_simulator - INFO -   사용 가능 잔액: 305.20 USDT
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포트폴리오 잔액 업데이트: $610.98 -> $610.68 USDT
2025-06-26 19:53:18 - hybrid_simulator - INFO - 포트폴리오 잔액 업데이트: $610.98 → $610.68 USDT
2025-06-26 19:53:18 - hybrid_simulator - INFO - 포트폴리오 총 가치: $610.68
2025-06-26 19:53:18 - hybrid_simulator - INFO - 초기 잔액: $10000.00
2025-06-26 19:53:18 - hybrid_simulator - INFO - 총 손익: $-9389.32 (-93.89%)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포지션 동기화 시작: 강제=False, 마지막 동기화 이후 1750935198초 경과
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 바이낸스 API를 통해 포지션 정보 동기화 시작
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-26 19:53:18 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:18 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935197274}
2025-06-26 19:53:18 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935197274
2025-06-26 19:53:18 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935197274
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 522
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -0.68084537)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.0783)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -1.04758416)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BTCUSDT = 0.001 (PnL: 0.0479)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - positionAmt: 0.00 (float: 0.0)
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - entryPrice: 0.0
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - unrealizedProfit: 0.00000000
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:53:18 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - unrealizedProfit: -1.04758416
2025-06-26 19:53:18 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 응답 타입: <class 'list'>
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 전체 포지션 수: 522
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 활성 포지션 개수: 4
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 수: 1
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 수: 1
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 상세: positionAmt=0.0, entryPrice=0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 상세: positionAmt=504.0, entryPrice=0.16426
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 전체 원본 데이터:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'SOLUSDT', 'initialMargin': '0', 'maintMargin': '0', 'unrealizedProfit': '0.00000000', 'positionInitialMargin': '0', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.0', 'breakEvenPrice': '0.0', 'maxNotional': '400000000', 'positionSide': 'BOTH', 'positionAmt': '0.00', 'notional': '0', 'isolatedWallet': '0', 'updateTime': 1750934575636, 'bidNotional': '0', 'askNotional': '0'}
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 주요 필드:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - symbol: SOLUSDT
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - positionAmt: 0.00 (타입: <class 'str'>)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: 0.00000000
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - notional: 0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 0.0 (타입: <class 'float'>)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [ETH] 포지션 방향 결정: positionAmt=0.023
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [ETH] 양수 포지션 → LONG 방향
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [ETH] 최종 결정된 방향: long
2025-06-26 19:53:18 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:53:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:18 - binance.binance_utils - INFO - 변화율(%): 1.218
2025-06-26 19:53:18 - binance.binance_utils - INFO - 거래량: 5672000.789
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [ETH] 🔧 강제 PnL 계산: -1.197% (진입: 2486.13, 현재: 2456.38, 방향: long)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [ETH] 바이낸스 포지션 상세:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 2486.13
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.68084537
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -1.20%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [ETH] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [ETH] 기존 포지션 ID: sync_ETH_1750928149, 동기화 포지션 ID: sync_ETH_1750935198
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BNB] 포지션 방향 결정: positionAmt=0.09
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BNB] 양수 포지션 → LONG 방향
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BNB] 최종 결정된 방향: long
2025-06-26 19:53:18 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:53:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:18 - binance.binance_utils - INFO - 변화율(%): 0.194
2025-06-26 19:53:18 - binance.binance_utils - INFO - 거래량: 338898.50
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [BNB] 🔧 강제 PnL 계산: -0.135% (진입: 646.61, 현재: 645.74, 방향: long)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [BNB] 바이낸스 포지션 상세:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 646.61
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.0783
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.13%
2025-06-26 19:53:18 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750935198_1750935198
2025-06-26 19:53:18 - trading.thinking_cards.position_card_manager - INFO - 🎯 [BNB] 새 포지션 카드 생성: sync_BNB_1750935198 -> card_sync_BNB_1750935198_1750935198
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🎯 [BNB] 동기화 포지션 통합 생각카드 생성: card_sync_BNB_1750935198_1750935198
2025-06-26 19:53:18 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 전체 원본 데이터:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'DOGEUSDT', 'initialMargin': '81.73945584', 'maintMargin': '0.53130646', 'unrealizedProfit': '-1.04758416', 'positionInitialMargin': '81.73945584', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.16426', 'breakEvenPrice': '0.16434213', 'maxNotional': '200000000', 'positionSide': 'BOTH', 'positionAmt': '504', 'notional': '81.73945584', 'isolatedWallet': '0', 'updateTime': 1750927964135, 'bidNotional': '0', 'askNotional': '0'}
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 주요 필드:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - symbol: DOGEUSDT
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - positionAmt: 504 (타입: <class 'str'>)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.04758416
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - notional: 81.73945584
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 504.0 (타입: <class 'float'>)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 포지션 방향 결정: positionAmt=504.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 양수 포지션 → LONG 방향
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 최종 결정된 방향: long
2025-06-26 19:53:18 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:53:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:18 - binance.binance_utils - INFO - 변화율(%): -1.917
2025-06-26 19:53:18 - binance.binance_utils - INFO - 거래량: 4968254122
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [DOGE] 🔧 강제 PnL 계산: -1.254% (진입: 0.16426, 현재: 0.1622, 방향: long)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 상세:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.04758416
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -1.25%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 기존 포지션 ID: sync_DOGE_1750927996, 동기화 포지션 ID: sync_DOGE_1750935198
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BTC] 포지션 방향 결정: positionAmt=0.001
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BTC] 양수 포지션 → LONG 방향
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔧 [BTC] 최종 결정된 방향: long
2025-06-26 19:53:18 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:18 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:18 - binance.binance_utils - INFO - 거래량: 148167.266
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [BTC] 🔧 강제 PnL 계산: 0.045% (진입: 107311.4, 현재: 107359.3, 방향: long)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - [BTC] 바이낸스 포지션 상세:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - entryPrice: 107311.4
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - unrealizedProfit: 0.0479
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO -   - 최종 PnL%: 0.04%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [BTC] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔒 [BTC] 기존 포지션 ID: trade_BTC_1750934599, 동기화 포지션 ID: sync_BTC_1750935198
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 시작: 현재 3개 포지션, 거래소에서 가져온 4개 포지션
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - exchange_positions 상세:
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[0]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[1]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[2]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[3]: BTC (positionAmt=0, size=0.001, quantity=0.001)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 현재 내부 포지션 키: ['ETH_long', 'DOGE_long', 'BTC_long']
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 거래소 포지션 키: ['ETH_long', 'BNB_long', 'DOGE_long', 'BTC_long']
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 바이낸스 API 포지션과 내부 포지션 스마트 매핑
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포지션 업데이트: ETH long, PnL: -1.20%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 새 포지션 발견: BNB long, 크기: 0.09
2025-06-26 19:53:18 - trading.thinking_cards.position_card_manager - WARNING - ⚠️ [BNB] 이미 존재하는 포지션 카드: sync_BNB_1750935198
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🎯 [BNB] 동기화 포지션 통합 생각카드 생성: card_sync_BNB_1750935198_1750935198
2025-06-26 19:53:18 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포지션 업데이트: DOGE long, PnL: -1.25%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포지션 업데이트: BTC long, PnL: 0.04%
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 완료: 최종 4개 포지션
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions 배열 크기: 4
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[0]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[1]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[2]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[3]: BTC (positionAmt=0, size=0.001, quantity=0.001)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 수: 4
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 1: ETH (크기: 0.023)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 2: BNB (크기: 0.09)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 3: DOGE (크기: 504.0)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 4: BTC (크기: 0.001)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 수: 4
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 1: ETH (크기: 0.023)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 2: BNB (크기: 0.09)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 3: DOGE (크기: 504.0)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 4: BTC (크기: 0.001)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 바이낸스 활성 포지션: ['ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)', 'BTC(0.001)']
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 내부 활성 포지션: ['ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)', 'BTC(0.001)']
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - ✅ 포지션 동기화 상태 양호: 불일치 없음
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔥 포지션 동기화 완료: 4개의 활성 포지션 (내부: 4개)
2025-06-26 19:53:18 - simulator.trading.portfolio - INFO - 🔥 동기화 변경사항: 5개
2025-06-26 19:53:18 - hybrid_simulator - INFO - 🔥 포지션 정보 동기화 완료
2025-06-26 19:53:18 - hybrid_simulator - INFO - 🔥 동기화 결과: 3 → 4 포지션
2025-06-26 19:53:18 - hybrid_simulator - INFO - 🔥 포지션 동기화 변경사항 (5개):
2025-06-26 19:53:18 - hybrid_simulator - INFO -    - 포지션 수 변경: 3 → 4
2025-06-26 19:53:18 - hybrid_simulator - INFO -    - 동기화: ETH long (PnL: -1.20%)
2025-06-26 19:53:18 - hybrid_simulator - INFO -    - 동기화: BNB long (PnL: -0.13%)
2025-06-26 19:53:18 - hybrid_simulator - INFO -    - 동기화: DOGE long (PnL: -1.25%)
2025-06-26 19:53:18 - hybrid_simulator - INFO -    - 동기화: BTC long (PnL: 0.04%)
2025-06-26 19:53:18 - hybrid_simulator - INFO - BTC 시장 데이터 로드 중...
2025-06-26 19:53:18 - simulator.utils.market_utils - INFO - BTC 실시간 시장 데이터 수집 중...
2025-06-26 19:53:18 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107359.3
2025-06-26 19:53:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:18 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:18 - binance.binance_utils - INFO - 거래량: 148167.115
2025-06-26 19:53:18 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:19 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:19 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107359.3
2025-06-26 19:53:19 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:19 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:19 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:19 - binance.binance_utils - INFO - 거래량: 148167.266
2025-06-26 19:53:19 - simulator.utils.market_utils - INFO - BTC 24h 변화율: 0.27%
2025-06-26 19:53:19 - simulator.utils.market_utils - INFO - BTC 현재 가격: 107359.3, 24h 변화율: 0.27%
2025-06-26 19:53:19 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107359.30, 변동률 0.27%
2025-06-26 19:53:19 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 중...
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC(ID:1) 코인 데이터 요청 중...
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/1/v1, 파라미터: {}
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/1/v1
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC 거래량 정보: volume_24h=51280500733.64
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:19 - data_collector.lunarcrush_collector - INFO - BTC 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/news/v1, 파라미터: {}
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/news/v1
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 99
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-26 19:53:20 - data_collector.lunarcrush_collector - INFO - BTC 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/posts/v1, 파라미터: {'start': 1750935020, 'end': 1750935200}
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/posts/v1
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 100개 수집 완료
2025-06-26 19:53:21 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=512805007, 뉴스=99개, 포스트=100개
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 결합 완료: 바이낸스 거래량=148167, 소셜볼륨=512805007
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:21 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:21 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 1m 첫 번째 캔들 샘플: [1750932240000, '107241.70', '107285.80', '107241.70', '107285.70', '64.176', 1750932299999, '6884006.50790', 1475, '44.340', '4756216.84560', '0']
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - ✅ BTC 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 단기 변동률: 0.047%
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107359.30, 24h변동률 0.27%, 단기변동률 0.047%, 캔들 20개
2025-06-26 19:53:21 - hybrid_simulator - INFO - BTC 시장 데이터 로드 완료: $107359.30
2025-06-26 19:53:21 - hybrid_simulator - INFO - ETH 시장 데이터 로드 중...
2025-06-26 19:53:21 - simulator.utils.market_utils - INFO - ETH 실시간 시장 데이터 수집 중...
2025-06-26 19:53:21 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:21 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:21 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2456.38
2025-06-26 19:53:21 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:21 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:21 - binance.binance_utils - INFO - 변화율(%): 1.214
2025-06-26 19:53:21 - binance.binance_utils - INFO - 거래량: 5672062.529
2025-06-26 19:53:21 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:21 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:21 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2456.38
2025-06-26 19:53:21 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:22 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:22 - binance.binance_utils - INFO - 변화율(%): 1.218
2025-06-26 19:53:22 - binance.binance_utils - INFO - 거래량: 5672000.789
2025-06-26 19:53:22 - simulator.utils.market_utils - INFO - ETH 24h 변화율: 1.22%
2025-06-26 19:53:22 - simulator.utils.market_utils - INFO - ETH 현재 가격: 2456.38, 24h 변화율: 1.22%
2025-06-26 19:53:22 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2456.38, 변동률 1.22%
2025-06-26 19:53:22 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 중...
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH(ID:2) 코인 데이터 요청 중...
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/2/v1, 파라미터: {}
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/2/v1
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH 거래량 정보: volume_24h=19309641058.6
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:22 - data_collector.lunarcrush_collector - INFO - ETH 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/news/v1, 파라미터: {}
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/news/v1
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 71
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 71개 수집 완료
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 71개 수집 완료
2025-06-26 19:53:23 - data_collector.lunarcrush_collector - INFO - ETH 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/posts/v1, 파라미터: {'start': 1750935023, 'end': 1750935203}
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/posts/v1
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 100개 수집 완료
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=193096410, 뉴스=71개, 포스트=100개
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 결합 완료: 바이낸스 거래량=5672062, 소셜볼륨=193096410
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:24 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:24 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 1m 첫 번째 캔들 샘플: [1750932240000, '2456.09', '2458.96', '2454.50', '2458.84', '7981.609', 1750932299999, '19610686.66390', 9315, '5460.325', '13416380.31063', '0']
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ✅ ETH 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 단기 변동률: 0.029%
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2456.38, 24h변동률 1.22%, 단기변동률 0.029%, 캔들 20개
2025-06-26 19:53:24 - hybrid_simulator - INFO - ETH 시장 데이터 로드 완료: $2456.38
2025-06-26 19:53:24 - hybrid_simulator - INFO - SOL 시장 데이터 로드 중...
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - SOL 실시간 시장 데이터 수집 중...
2025-06-26 19:53:24 - binance.binance_utils - INFO - 시장 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT)
2025-06-26 19:53:24 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-26 19:53:24 - binance.binance_utils - INFO - 최신 가격 조회 성공: SOLUSDT, 가격: 143.45
2025-06-26 19:53:24 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-26 19:53:24 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:24 - binance.binance_utils - INFO - 변화율(%): -1.612
2025-06-26 19:53:24 - binance.binance_utils - INFO - 거래량: 19461411.64
2025-06-26 19:53:24 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:24 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:24 - binance.binance_utils - INFO - 시장 데이터 조회 성공: SOLUSDT, 가격: 143.45
2025-06-26 19:53:24 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-26 19:53:24 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:24 - binance.binance_utils - INFO - 변화율(%): -1.612
2025-06-26 19:53:24 - binance.binance_utils - INFO - 거래량: 19461344.11
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - SOL 24h 변화율: -1.61%
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - SOL 현재 가격: 143.45, 24h 변화율: -1.61%
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - SOL 시장 데이터 수집 완료: 현재가 $143.45, 변동률 -1.61%
2025-06-26 19:53:24 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 수집 중...
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - SOL(ID:3079) 코인 데이터 요청 중...
2025-06-26 19:53:24 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/3079/v1, 파라미터: {}
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/3079/v1
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - SOL 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - SOL 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - SOL 거래량 정보: volume_24h=3215150706.611205
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - SOL 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - SOL 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:25 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/sol/news/v1, 파라미터: {}
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/sol/news/v1
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 22
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 22개 수집 완료
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 22개 수집 완료
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - SOL 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:26 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/sol/posts/v1, 파라미터: {'start': 1750935026, 'end': 1750935206}
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/sol/posts/v1
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 100개 수집 완료
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=32151507, 뉴스=22개, 포스트=100개
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 결합 완료: 바이낸스 거래량=19461411, 소셜볼륨=32151507
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:27 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:27 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 1m 첫 번째 캔들 샘플: [1750932240000, '143.5800', '143.7300', '143.5300', '143.7100', '12093.78', 1750932299999, '1737288.280200', 1471, '8365.04', '1201761.989700', '0']
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - ✅ SOL 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 단기 변동률: 0.098%
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - SOL 시장 데이터 수집 완료: 현재가 $143.45, 24h변동률 -1.61%, 단기변동률 0.098%, 캔들 20개
2025-06-26 19:53:27 - hybrid_simulator - INFO - SOL 시장 데이터 로드 완료: $143.45
2025-06-26 19:53:27 - hybrid_simulator - INFO - BNB 시장 데이터 로드 중...
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - BNB 실시간 시장 데이터 수집 중...
2025-06-26 19:53:27 - binance.binance_utils - INFO - 시장 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT)
2025-06-26 19:53:27 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-26 19:53:27 - binance.binance_utils - INFO - 최신 가격 조회 성공: BNBUSDT, 가격: 645.73
2025-06-26 19:53:27 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-26 19:53:27 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:27 - binance.binance_utils - INFO - 변화율(%): 0.192
2025-06-26 19:53:27 - binance.binance_utils - INFO - 거래량: 338899.52
2025-06-26 19:53:27 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:27 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:27 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BNBUSDT, 가격: 645.73
2025-06-26 19:53:27 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-26 19:53:27 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:27 - binance.binance_utils - INFO - 변화율(%): 0.194
2025-06-26 19:53:27 - binance.binance_utils - INFO - 거래량: 338899.29
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - BNB 24h 변화율: 0.19%
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - BNB 현재 가격: 645.73, 24h 변화율: 0.19%
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - BNB 시장 데이터 수집 완료: 현재가 $645.73, 변동률 0.19%
2025-06-26 19:53:27 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 수집 중...
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - BNB(ID:6) 코인 데이터 요청 중...
2025-06-26 19:53:27 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/6/v1, 파라미터: {}
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/6/v1
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - BNB 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - BNB 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - BNB 거래량 정보: volume_24h=1504921445.4502478
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - BNB 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - BNB 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:28 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/binance/news/v1, 파라미터: {}
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/binance/news/v1
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 15
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 15개 수집 완료
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 15개 수집 완료
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - BNB 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:29 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/binance/posts/v1, 파라미터: {'start': 1750935029, 'end': 1750935209}
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/binance/posts/v1
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 100개 수집 완료
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=15049214, 뉴스=15개, 포스트=100개
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 결합 완료: 바이낸스 거래량=338899, 소셜볼륨=15049214
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:30 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:30 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 1m 첫 번째 캔들 샘플: [1750932240000, '645.900', '646.100', '645.890', '646.090', '322.17', 1750932299999, '208139.85690', 224, '294.44', '190225.90220', '0']
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - ✅ BNB 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 단기 변동률: -0.002%
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - BNB 시장 데이터 수집 완료: 현재가 $645.73, 24h변동률 0.19%, 단기변동률 -0.002%, 캔들 20개
2025-06-26 19:53:30 - hybrid_simulator - INFO - BNB 시장 데이터 로드 완료: $645.73
2025-06-26 19:53:30 - hybrid_simulator - INFO - DOGE 시장 데이터 로드 중...
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - DOGE 실시간 시장 데이터 수집 중...
2025-06-26 19:53:30 - binance.binance_utils - INFO - 시장 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-26 19:53:30 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-26 19:53:30 - binance.binance_utils - INFO - 최신 가격 조회 성공: DOGEUSDT, 가격: 0.16221
2025-06-26 19:53:30 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-26 19:53:30 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:30 - binance.binance_utils - INFO - 변화율(%): -1.911
2025-06-26 19:53:30 - binance.binance_utils - INFO - 거래량: 4968714723
2025-06-26 19:53:30 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:30 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:30 - binance.binance_utils - INFO - 시장 데이터 조회 성공: DOGEUSDT, 가격: 0.16221
2025-06-26 19:53:30 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-26 19:53:30 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:30 - binance.binance_utils - INFO - 변화율(%): -1.911
2025-06-26 19:53:30 - binance.binance_utils - INFO - 거래량: 4968714723
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - DOGE 24h 변화율: -1.91%
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - DOGE 현재 가격: 0.16221, 24h 변화율: -1.91%
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - DOGE 시장 데이터 수집 완료: 현재가 $0.16, 변동률 -1.91%
2025-06-26 19:53:30 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 수집 중...
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - DOGE(ID:29) 코인 데이터 요청 중...
2025-06-26 19:53:30 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/29/v1, 파라미터: {}
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/29/v1
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - DOGE 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - DOGE 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - DOGE 거래량 정보: volume_24h=865888541.8953143
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - DOGE 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:31 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/doge/news/v1, 파라미터: {}
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/doge/news/v1
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 39
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 39개 수집 완료
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 39개 수집 완료
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - DOGE 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:32 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/doge/posts/v1, 파라미터: {'start': 1750935032, 'end': 1750935212}
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/doge/posts/v1
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 100개 수집 완료
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=8658885, 뉴스=39개, 포스트=100개
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 결합 완료: 바이낸스 거래량=4968714723, 소셜볼륨=8658885
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 1m 첫 번째 캔들 샘플: [1750932240000, '0.162330', '0.162530', '0.162260', '0.162530', '4285842', 1750932299999, '696070.304430', 1412, '2784994', '452306.620690', '0']
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - ✅ DOGE 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 단기 변동률: 0.056%
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - DOGE 시장 데이터 수집 완료: 현재가 $0.16, 24h변동률 -1.91%, 단기변동률 0.056%, 캔들 20개
2025-06-26 19:53:33 - hybrid_simulator - INFO - DOGE 시장 데이터 로드 완료: $0.16
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 기본 잔액: $610.68
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - BTC: 0.03800000 @ $107359.30 = $4079.65
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 총 가치: $4690.34
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - ===== 포트폴리오 상태 =====
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 거래 모드: 실제 거래
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - USDT 잔액: $610.68
2025-06-26 19:53:33 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:33 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:33 - binance.binance_utils - INFO - 거래량: 148169.307
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 포트폴리오 총 가치: $4,690.34 (USDT: $610.68 + 현물: $4,079.65)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 초기 잔액: $10,000.00
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 총 손익: $-5,309.66 (-53.10%)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - === 현물 자산 (Assets) ===
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   BTC: 0.03800000 개 × $107359.3000 = $4,079.65
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 현물 자산 총 가치: $4,079.65
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - === 선물 거래 상태 ===
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935212136}
2025-06-26 19:53:33 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935212136
2025-06-26 19:53:33 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935212136
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 미체결 주문: 없음
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935212271}
2025-06-26 19:53:33 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935212271
2025-06-26 19:53:33 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935212271
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - [SOL] 최근 주문 내역:
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750929621350
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750931066659
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750934575636
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:53:33 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935212402}
2025-06-26 19:53:33 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935212402
2025-06-26 19:53:33 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935212402
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - [DOGE] 최근 주문 내역:
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750927456355
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927959912
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927964135
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - === 내부 포지션 ===
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   ETH long: 0.02300000 @ $2486.13
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     현재가: $2456.38 | ROI: -1.20% | PnL: $-0.68 [손절조건 충족! 기준:-0.15%]
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   BNB long: 0.09000000 @ $646.61
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     현재가: $645.73 | ROI: -0.14% | PnL: $-0.08
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   DOGE long: 504.00000000 @ $0.16
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     현재가: $0.16 | ROI: -1.25% | PnL: $-1.05 [손절조건 충족! 기준:-0.15%]
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   BTC long: 0.00100000 @ $107313.10
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     현재가: $107359.30 | ROI: 0.04% | PnL: $0.05
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 총 거래 수: 997
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 승률: 19.08% (성공: 595, 실패: 2523)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 포트폴리오 총 손익: $-5309.66 (-53.10%)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 거부된 거래 수: 187
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 거부된 거래 검증 상태: 검증 완료 187건, 검증 대기 0건
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 거부 이유별 통계:
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - API 오류: 바이낸스 API 오류 -2019: Margin is insufficient.: 181건
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - 일반 오류: name 'action' is not defined: 6건
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - === 통합 생각카드 시스템 상태 ===
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 활성 카드 수: 3개
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 카드 완성도: 완료 0개, 진행중 3개, 생성됨 0개
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 심볼별 카드 현황:
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   BNB: 2개 카드
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750934424_1750934425: 완성도 50.0%
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750935198_1750935198: 완성도 25.0%
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   BTC: 1개 카드
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -     - card_trade_BTC_1750934599_1750934601: 완성도 25.0%
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 최근 업데이트된 카드: 1개
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   - BNB: 완성도 25.0%
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - 시간대별 성공률:
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   short_term: 0.0% (0/1건, 평균수익: -8.35%)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO -   전체: 0.0% (0/1건)
2025-06-26 19:53:33 - simulator.trading.portfolio - INFO - ===========================
2025-06-26 19:53:33 - hybrid_simulator - INFO - 🚀 연속 모드: 하이브리드 컨트롤러 직접 시작
2025-06-26 19:53:33 - hybrid_simulator - INFO - 하이브리드 컨트롤러 연속 모드 설정: True
2025-06-26 19:53:33 - hybrid_simulator - INFO - 하이브리드 컨트롤러에 포트폴리오 시뮬레이터 연결
2025-06-26 19:53:33 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 루프 시작
2025-06-26 19:53:33 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 스레드 시작
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - vLLM 요청 큐 워커 시작됨
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 데이터 수집 루프 시작
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 시작
2025-06-26 19:53:33 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 연속 실행-학습 루프 시작
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 시스템 가동 시작
2025-06-26 19:53:33 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 새로운 심볼 락 생성
2025-06-26 19:53:33 - hybrid_simulator - INFO - 🚀 하이브리드 컨트롤러 연속 모드 시작 완료
2025-06-26 19:53:33 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] 처리 시작
2025-06-26 19:53:33 - hybrid_simulator - INFO - 하이브리드 컨트롤러 연속 모드 설정: True
2025-06-26 19:53:33 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 순차 처리 시작 (락 획득)
2025-06-26 19:53:33 - hybrid_simulator - INFO - 거래 간격: 5초
2025-06-26 19:53:33 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 대기 중...
2025-06-26 19:53:33 - hybrid_simulator - INFO - ===== 반복 1/100000 =====
2025-06-26 19:53:33 - hybrid_simulator - INFO - ===== 거래 주기 시작: 2025-06-26 19:53:33 =====
2025-06-26 19:53:33 - hybrid_simulator - INFO - 🔄 BTC 완전 순차 처리 시작
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - BTC 실시간 시장 데이터 수집 중...
2025-06-26 19:53:33 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107359.2
2025-06-26 19:53:33 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107359.2
2025-06-26 19:53:33 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:33 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:33 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:33 - binance.binance_utils - INFO - 거래량: 148172.890
2025-06-26 19:53:33 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTC), 인터벌=1d, 개수=500
2025-06-26 19:53:33 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:33 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:33 - binance.binance_utils - INFO - 거래량: 148169.194
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:33 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107359.2
2025-06-26 19:53:33 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BTC/news/v1, 파라미터: None
2025-06-26 19:53:33 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:33 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107359.2
2025-06-26 19:53:33 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:53:33 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:33 - binance.binance_utils - INFO - 변화율(%): 0.269
2025-06-26 19:53:33 - binance.binance_utils - INFO - 거래량: 148169.417
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - BTC 24h 변화율: 0.27%
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - BTC 현재 가격: 107359.2, 24h 변화율: 0.27%
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107359.20, 변동률 0.27%
2025-06-26 19:53:33 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 중...
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - BTC(ID:1) 코인 데이터 요청 중...
2025-06-26 19:53:33 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/1/v1, 파라미터: {}
2025-06-26 19:53:34 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BTC/v1, 파라미터: None
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/1/v1
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - BTC 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - BTC 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - BTC 거래량 정보: volume_24h=51280500733.64
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - BTC 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - BTC 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:34 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/BTC/posts/v1, 파라미터: {'start': 1750848814, 'end': 1750935214}
2025-06-26 19:53:34 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/news/v1, 파라미터: {}
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: BTC)
2025-06-26 19:53:35 - trading.hybrid_architecture.data_store - INFO - BTC 시장 데이터 저장 완료
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_market_market_035581d5_1750935215.json
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - BTC 시장 데이터 로깅 완료: market_035581d5_1750935215
2025-06-26 19:53:35 - trading.hybrid_architecture.data_store - WARNING - BTC 뉴스 데이터가 문자열로 전달됨: config...
2025-06-26 19:53:35 - trading.hybrid_architecture.data_store - INFO - BTC 뉴스 데이터 저장 완료: config
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_news_news_fd3283be_1750935215.json
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - BTC 뉴스 데이터 로깅 완료: news_fd3283be_1750935215
2025-06-26 19:53:35 - trading.hybrid_architecture.data_store - WARNING - BTC 뉴스 데이터가 문자열로 전달됨: data...
2025-06-26 19:53:35 - trading.hybrid_architecture.data_store - INFO - BTC 뉴스 데이터 저장 완료: data
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_news_news_2e18b705_1750935215.json
2025-06-26 19:53:35 - trading.hybrid_architecture.data_logger - INFO - BTC 뉴스 데이터 로깅 완료: news_2e18b705_1750935215
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 완료 및 실행 큐 전달
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 플로우 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 데이터 수집 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 포지션 관리 시작
2025-06-26 19:53:35 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] 포지션 관리 v2 인라인 시작
2025-06-26 19:53:35 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETH)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 포트폴리오 연결됨, 포지션 확인 시작
2025-06-26 19:53:35 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] open_positions 속성으로 포지션 확인: 1개
2025-06-26 19:53:35 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 전체 포지션 수: 4개
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] 1개 포지션 관리 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 [BTC] InCA 포지션 평가 재활성화 - 정상 포지션 관리 실행
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 [BTC] InCA와 SELA를 통한 포지션 관리 재개
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 [BTC] InCA 포지션 평가 실행 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 [BTC] InCA 포지션 중요도 평가 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 [BTC] 포지션 평가용 캔들 기반 시장 분석
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 [BTC] InCA 포지션 평가 완료: market_neutral (중요도: 6, 신호: neutral)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 [BTC] InCA 포지션 평가 완료: {'hold_importance': 6, 'situation_type': 'market_neutral', 'should_close': False, 'signal_direction': 'neutral', 'reasoning': '포지션: long, 시장신호: neutral, PnL: 0.04%, 보유시간: 10.3분', 'confidence': 0.5, 'market_evaluation': {'importance_score': 0.5, 'situation_type': 'neutral', 'action_recommendation': 'hold', 'signal_direction': 'neutral', 'reasoning': '캔들 패턴 분석: 데이터 부족', 'conservative_analysis': True}, 'pnl_pct': 0.04305159388741644, 'hold_time_minutes': 10.268572044372558, 'timestamp': 1750935215}
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 바이낸스 실제 PnL 사용: 0.043% (기존 계산: 0.043%)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 바이낸스 원시 데이터: {'percentage': None, 'unrealized_pnl': 0.0479, 'current_pnl_pct': 0.04305159388741644, 'entry_price': 107313.1, 'current_price': 0.0}
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 5분 수익 실현 체크:
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 - 보유시간: 10.27분
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 - 현재 PnL: 0.043%
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 - 5분 조건: True
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 - 수익 조건: False (기준: 0.2%)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] LLM 클로즈 판단: should_close=False, situation=market_neutral, importance=6, confidence=0.50
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] LLM 판단 근거: 포지션: long, 시장신호: neutral, PnL: 0.04%, 보유시간: 10.3분
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] 포지션 정보: 보유시간 10.3분, PnL 0.04%
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 포지션 종료 판단: should_close=False, 이유=LLM 판단: 포지션: long, 시장신호: neutral, PnL: 0.04%, 보유시간: 10.3분
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] SELA 포지션 관리 전략 생성 (should_close=False)
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🎯 [BTC] SELA 포지션 관리 전략 생성 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🌳 [BTC] Tree Search 포지션 관리 옵션 평가 시작
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] 옵션 'hold': 점수 10.00
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] 옵션 'close': 점수 4.20
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] 옵션 'partial_close': 점수 5.00
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] 옵션 'adjust_stop_loss': 점수 5.00
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🏆 [BTC] 최적 포지션 관리: hold (점수: 10.00)
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - 🎯 [BTC] 판단 근거: 포지션=long, 신호=neutral, 상황=market_neutral
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [BTC] SELA 포지션 관리 전략: hold (점수: 10.00)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] SELA 포지션 유지 결정: trend_continuation
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 포지션 유지 상세: should_close=False, force_close_profit=False, sela_action=hold
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 📈 [BTC] 포지션 보유 계속: market_neutral
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] InCA 시장 중요도 평가 시작 (순차 처리)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] InCA 진짜 순차 실행 시작 (큐 우회)
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 순차 실행 변동률 추출: percent_change_24h=None, priceChangePercent=None, change_24h=0.269, 최종값=0.269
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 시장 데이터 키: ['symbol', 'price', 'percent_change_24h', 'volume_24h', 'recent_candles', 'news']
2025-06-26 19:53:35 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 추출된 값: 가격=$107359.2, 거래량=148172.89
2025-06-26 19:53:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA 프롬프트 크기: 2480자
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 🔧 kwargs에서 호환 파라미터 'stop' 전달됨: ['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The']
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 🔧 원본 kwargs: ['stop'], 필터링 후: ['stop']
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 2480자
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - Qwen3 모델 프롬프트 감지됨
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: Symbol:\s*([A-Z]+))
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 71자
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 1948자
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - Qwen3 모델에 강화된 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:35 - models.vllm_prompt_processor - INFO - Qwen3 모델용 stop sequences 제거됨 - 완전한 JSON 응답 보장
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Qwen3 모델에 inca_execution 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: inca_execution
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: inca_execution
2025-06-26 19:53:35 - models.vllm_session_manager - INFO - Created new session: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e for inca_execution agent (symbol: BTC)
2025-06-26 19:53:35 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_execution_execution_BTC → a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - inca_execution execution 세션 ID: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e (심볼: BTC)
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 2869
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Session ID: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.



market data, HiAR organizes reasoning, SELA executes strategies.



CRITICAL: V-shaped reversals, breakouts, volume ...
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\nIMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\nCRITICAL: V-shaped reversals, breakouts, volume spikes are STRONG signals\n2. 🎯 TRENDLINE ANALYSIS: Support/resistance breakouts indicate major direction changes\n3. 🎯 VOLUME CONFIRMATION: Price moves with high volume are more reliable than low-volume moves\n4. 🎯 PATTERN RECOGNITION: Look for double tops/bottoms, wedges, consecutive candles\n5. 🎯 SHORT-TERM TREND PRIORITY: Recent 5-minute patterns override 24h changes\n6. 🎯 BREAKOUT SIGNALS: When price breaks above resistance or below support with volume = STRONG signal\n7. 🎯 REVERSAL PATTERNS: V-shaped reversals from lows/highs with volume = immediate action needed\n8. News and social sentiment are secondary to clear technical patterns\n9. If technical analysis shows STRONG patterns (strength >50), prioritize over other factors\n\nPRIORITY ORDER:\n1. Short-term trend direction (from candle data) - HIGHEST PRIORITY\n2. Trend strength and consistency\n3. News sentiment\n4. Social sentiment\n5. 24h change (lowest priority)\n\n🚨 **CRITICAL TRADING SIGNAL RULES**:\n- If market_direction is "bearish" → action_recommendation MUST be "sell"\n- If market_direction is "bullish" → action_recommendation MUST be "buy"\n- If market_direction is "neutral" → action_recommendation MUST be "hold"\n- situation_type should match market_direction (bearish/bullish/neutral)\n\nRESPOND WITH ONLY A JSON OBJECT:\n{\n  "importance": <1-10 integer>,\n  "is_important": <true/false>,\n  "importance_score": <0.0-1.0 float>,\n  "reasoning": "<brief explanation prioritizing short-term trend>",\n  "market_direction": "bullish"/"bearish"/"neutral",\n  "situation_type": "bullish"/"bearish"/"neutral",\n  "action_recommendation": "buy"/"sell"/"hold",\n  "trading_direction": "long"/"short"/"neutral",\n  "confidence": <0.0-1.0 float>,\n  "keywords": ["<keyword1>", "<keyword2>", "<keyword3>"]\n}\n\nRate importance:\n1-3: Low activity, 4-6: Moderate activity, 7-10: High activity\n\nCRITICAL: Return ONLY valid JSON, no markdown, no explanations.\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT FOR INCA ANALYSIS (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "importance": 7,\n  "situation_type": "bullish/bearish/neutral",\n  "action_recommendation": "buy/sell/hold",\n  "trading_direction": "long/short/neutral",\n  "reasoning": "Based on market data and past experiences",\n  "news_impact": 0.7,\n  "market_impact": 0.8,\n  "confidence": 0.85\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 500, 'temperature': 0.0, 'top_p': 0.1, 'stop': []}
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 717
2025-06-26 19:53:35 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:35 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2456.22
2025-06-26 19:53:35 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:53:35 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-26 19:53:35 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:35 - binance.binance_utils - INFO - 변화율(%): 1.203
2025-06-26 19:53:35 - binance.binance_utils - INFO - 거래량: 5672492.504
2025-06-26 19:53:35 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:53:35 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETH), 인터벌=1d, 개수=500
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/news/v1
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 99
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - BTC 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:35 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:35 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2456.22
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/ETH/news/v1, 파라미터: None
2025-06-26 19:53:35 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/ETH/v1, 파라미터: None
2025-06-26 19:53:35 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/posts/v1, 파라미터: {'start': 1750935035, 'end': 1750935215}
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/ETH/posts/v1, 파라미터: {'start': 1750848816, 'end': 1750935216}
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/posts/v1
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 응답 키: ['config', 'data']
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 100개 수집 완료
2025-06-26 19:53:36 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 데이터베이스에 저장 완료
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=512805007, 뉴스=99개, 포스트=100개
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 결합 완료: 바이낸스 거래량=148169, 소셜볼륨=512805007
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 1m 캔들 데이터 수집 시도...
2025-06-26 19:53:36 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=1m, 개수=50
2025-06-26 19:53:36 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1m, 개수: 50
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 1m 첫 번째 캔들 샘플: [1750932240000, '107241.70', '107285.80', '107241.70', '107285.70', '64.176', 1750932299999, '6884006.50790', 1475, '44.340', '4756216.84560', '0']
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - ✅ BTC 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 단기 변동률: 0.047%
2025-06-26 19:53:36 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107359.20, 24h변동률 0.27%, 단기변동률 0.047%, 캔들 20개
2025-06-26 19:53:36 - hybrid_simulator - INFO - 📊 BTC 시장 데이터 처리 시작 (순차 모드)
2025-06-26 19:53:36 - simulator.bridge - INFO - BTC 시장 데이터 처리 중...
2025-06-26 19:53:36 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 플로우 시작 (InCA → HiAR → SELA)
2025-06-26 19:53:36 - trading.hybrid_architecture.hybrid_controller - INFO - BTC InCA 시장 중요도 평가 시작
2025-06-26 19:53:36 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 시장 데이터 키: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-26 19:53:36 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 추출된 값: 가격=$107359.2, 거래량=148169.194
2025-06-26 19:53:36 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 심볼 추출 (Symbol:): BTC
2025-06-26 19:53:36 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(BTC) vLLM 큐에 요청 추가
2025-06-26 19:53:36 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_execution(BTC) - 우선순위 1, 큐 크기: 1
2025-06-26 19:53:36 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_execution(BTC) - 큐 대기: 0.00초
2025-06-26 19:53:36 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_execution_BTC_1750935216393
2025-06-26 19:53:36 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
/no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.

Analyze ALL avail...
2025-06-26 19:53:36 - models.vllm_session_manager - INFO - 기존 inca_execution_execution_BTC 세션 재사용: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e
2025-06-26 19:53:36 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e (inca_execution_BTC)
2025-06-26 19:53:36 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_execution 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:36 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e
2025-06-26 19:53:36 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: a1a74560-b1b0-46c5-a8a8-a2d093ccfa1e)
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: ETH)
2025-06-26 19:53:36 - trading.hybrid_architecture.data_store - INFO - ETH 시장 데이터 저장 완료
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_market_market_79fe51ea_1750935216.json
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - ETH 시장 데이터 로깅 완료: market_79fe51ea_1750935216
2025-06-26 19:53:36 - trading.hybrid_architecture.data_store - WARNING - ETH 뉴스 데이터가 문자열로 전달됨: config...
2025-06-26 19:53:36 - trading.hybrid_architecture.data_store - INFO - ETH 뉴스 데이터 저장 완료: config
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_news_news_445b6443_1750935216.json
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - ETH 뉴스 데이터 로깅 완료: news_445b6443_1750935216
2025-06-26 19:53:36 - trading.hybrid_architecture.data_store - WARNING - ETH 뉴스 데이터가 문자열로 전달됨: data...
2025-06-26 19:53:36 - trading.hybrid_architecture.data_store - INFO - ETH 뉴스 데이터 저장 완료: data
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_news_news_ea125c6b_1750935216.json
2025-06-26 19:53:36 - trading.hybrid_architecture.data_logger - INFO - ETH 뉴스 데이터 로깅 완료: news_ea125c6b_1750935216
2025-06-26 19:53:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 데이터 수집 완료 및 실행 큐 전달
2025-06-26 19:53:36 - trading.hybrid_architecture.hybrid_controller - INFO - SOL 데이터 수집 시작
2025-06-26 19:53:36 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-26 19:53:36 - binance.binance_utils - INFO - 시장 데이터 요청: SOLUSDT (원본 심볼: SOL)
2025-06-26 19:53:36 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-26 19:53:36 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-26 19:53:36 - binance.binance_utils - INFO - 최신 가격 조회 성공: SOLUSDT, 가격: 143.48
2025-06-26 19:53:36 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-26 19:53:36 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-26 19:53:36 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:36 - binance.binance_utils - INFO - 변화율(%): -1.591
2025-06-26 19:53:36 - binance.binance_utils - INFO - 거래량: 19462095.51
2025-06-26 19:53:36 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-26 19:53:36 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOL), 인터벌=1d, 개수=500
2025-06-26 19:53:36 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:36 - binance.binance_utils - INFO - 시장 데이터 조회 성공: SOLUSDT, 가격: 143.48
2025-06-26 19:53:36 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/SOL/news/v1, 파라미터: None
2025-06-26 19:53:37 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/SOL/v1, 파라미터: None
2025-06-26 19:53:37 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/SOL/posts/v1, 파라미터: {'start': 1750848817, 'end': 1750935217}
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: SOL)
2025-06-26 19:53:38 - trading.hybrid_architecture.data_store - INFO - SOL 시장 데이터 저장 완료
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_market_market_ea9b7fdb_1750935218.json
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - SOL 시장 데이터 로깅 완료: market_ea9b7fdb_1750935218
2025-06-26 19:53:38 - trading.hybrid_architecture.data_store - WARNING - SOL 뉴스 데이터가 문자열로 전달됨: config...
2025-06-26 19:53:38 - trading.hybrid_architecture.data_store - INFO - SOL 뉴스 데이터 저장 완료: config
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_news_news_a773b292_1750935218.json
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - SOL 뉴스 데이터 로깅 완료: news_a773b292_1750935218
2025-06-26 19:53:38 - trading.hybrid_architecture.data_store - WARNING - SOL 뉴스 데이터가 문자열로 전달됨: data...
2025-06-26 19:53:38 - trading.hybrid_architecture.data_store - INFO - SOL 뉴스 데이터 저장 완료: data
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_news_news_116d011d_1750935218.json
2025-06-26 19:53:38 - trading.hybrid_architecture.data_logger - INFO - SOL 뉴스 데이터 로깅 완료: news_116d011d_1750935218
2025-06-26 19:53:38 - trading.hybrid_architecture.hybrid_controller - INFO - SOL 데이터 수집 완료 및 실행 큐 전달
2025-06-26 19:53:38 - trading.hybrid_architecture.hybrid_controller - INFO - BNB 데이터 수집 시작
2025-06-26 19:53:38 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:53:38 - binance.binance_utils - INFO - 시장 데이터 요청: BNBUSDT (원본 심볼: BNB)
2025-06-26 19:53:38 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:53:38 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-26 19:53:38 - binance.binance_utils - INFO - 최신 가격 조회 성공: BNBUSDT, 가격: 645.74
2025-06-26 19:53:38 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:53:38 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-26 19:53:38 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:38 - binance.binance_utils - INFO - 변화율(%): 0.194
2025-06-26 19:53:38 - binance.binance_utils - INFO - 거래량: 338904.30
2025-06-26 19:53:38 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:53:38 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNB), 인터벌=1d, 개수=500
2025-06-26 19:53:38 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:38 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BNBUSDT, 가격: 645.74
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BNB/news/v1, 파라미터: None
2025-06-26 19:53:38 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BNB/v1, 파라미터: None
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/BNB/posts/v1, 파라미터: {'start': 1750848819, 'end': 1750935219}
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - Text generation complete (time: 4.43s)
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - Generated text preview: {"importance": 8, "is_important": true, "importance_score": 0.85, "reasoning": "Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from re...
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 422자):
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {"importance": 8, "is_important": true, "importance_score": 0.85, "reasoning": "Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.", "market_direction": "bullish", "situation_type": "bullish", "action_recommendation": "buy", "trading_direction": "long", "confidence": 0.9, "keywords": ["breakout", "volume confirmation", "V-shaped reversal"]}
2025-06-26 19:53:39 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답에 완전한 JSON 구조 발견
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA vLLM 응답 수신: <class 'dict'>
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA vLLM 응답 구조: ['text', 'finish_reason', 'session_id', 'symbol']
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA 생성된 텍스트 길이: 422자
2025-06-26 19:53:39 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'importance': 8, 'is_important': True, 'importance_score': 0.85, 'reasoning': 'Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.', 'market_direction': 'bullish', 'situation_type': 'bullish', 'action_recommendation': 'buy', 'trading_direction': 'long', 'confidence': 0.9, 'keywords': ['breakout', 'volume confirmation', 'V-shaped reversal']}
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] InCA 직접 실행 성공: 중요도 8
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 99
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-26 19:53:39 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 99개 포스트 가공 완료 (토픽: BNB)
2025-06-26 19:53:39 - trading.hybrid_architecture.data_store - INFO - BNB 시장 데이터 저장 완료
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_market_market_730adb58_1750935219.json
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - BNB 시장 데이터 로깅 완료: market_730adb58_1750935219
2025-06-26 19:53:39 - trading.hybrid_architecture.data_store - WARNING - BNB 뉴스 데이터가 문자열로 전달됨: config...
2025-06-26 19:53:39 - trading.hybrid_architecture.data_store - INFO - BNB 뉴스 데이터 저장 완료: config
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_news_news_7c5f084a_1750935219.json
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - BNB 뉴스 데이터 로깅 완료: news_7c5f084a_1750935219
2025-06-26 19:53:39 - trading.hybrid_architecture.data_store - WARNING - BNB 뉴스 데이터가 문자열로 전달됨: data...
2025-06-26 19:53:39 - trading.hybrid_architecture.data_store - INFO - BNB 뉴스 데이터 저장 완료: data
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_news_news_66a3efa7_1750935219.json
2025-06-26 19:53:39 - trading.hybrid_architecture.data_logger - INFO - BNB 뉴스 데이터 로깅 완료: news_66a3efa7_1750935219
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - BNB 데이터 수집 완료 및 실행 큐 전달
2025-06-26 19:53:39 - trading.hybrid_architecture.hybrid_controller - INFO - DOGE 데이터 수집 시작
2025-06-26 19:53:39 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:53:39 - binance.binance_utils - INFO - 시장 데이터 요청: DOGEUSDT (원본 심볼: DOGE)
2025-06-26 19:53:39 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:53:39 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-26 19:53:39 - binance.binance_utils - INFO - 최신 가격 조회 성공: DOGEUSDT, 가격: 0.16216
2025-06-26 19:53:39 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:53:39 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-26 19:53:39 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:39 - binance.binance_utils - INFO - 변화율(%): -1.917
2025-06-26 19:53:39 - binance.binance_utils - INFO - 거래량: 4968783571
2025-06-26 19:53:39 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:53:39 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGE), 인터벌=1d, 개수=500
2025-06-26 19:53:40 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:40 - binance.binance_utils - INFO - 시장 데이터 조회 성공: DOGEUSDT, 가격: 0.16216
2025-06-26 19:53:40 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/DOGE/news/v1, 파라미터: None
2025-06-26 19:53:40 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/DOGE/v1, 파라미터: None
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] InCA 완료, 다음 단계 진행
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 중요도(0.80)가 임계값(0.30)을 초과하여 계속 진행
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] HiAR 사고 흐름 정리 시작 (순차 처리)
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] HiAR 진짜 순차 실행 시작 (큐 우회)
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] HiAR 프롬프트 데이터 추출: 가격=$107359.2, 변동률=0.269%, 거래량=148172.89
2025-06-26 19:53:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR 프롬프트 크기: 418자
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 🔧 kwargs에서 호환 파라미터 'stop' 전달됨: ['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The']
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 🔧 원본 kwargs: ['stop'], 필터링 후: ['stop']
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 418자
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-26 19:53:40 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-26 19:53:40 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 143자
2025-06-26 19:53:40 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-26 19:53:40 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 194자
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Qwen3 모델에 hiar_mcts 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: hiar_mcts
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: hiar_mcts
2025-06-26 19:53:40 - models.vllm_session_manager - INFO - Created new session: fe54b7ad-53cb-453b-9c8b-fe9652657dc6 for hiar_mcts agent (symbol: BTC)
2025-06-26 19:53:40 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: hiar_mcts_execution_BTC → fe54b7ad-53cb-453b-9c8b-fe9652657dc6
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - hiar_mcts execution 세션 ID: fe54b7ad-53cb-453b-9c8b-fe9652657dc6 (심볼: BTC)
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1160
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Session ID: fe54b7ad-53cb-453b-9c8b-fe9652657dc6
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 ===
Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.



market data, HiAR organizes reasoning, SELA ...
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 ===\nStrong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\nJSON 형식으로 사고 흐름을 정리하세요:\n{\n  "card_id": "reasoning_BTC_1750935220",\n  "symbol": "BTC",\n  "reasoning": "시장 상황 분석",\n  "market_analysis": "가격 및 변동성 분석",\n  "decision_factors": ["요인1", "요인2", "요인3"]\n}\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT FOR HIAR ANALYSIS (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "title": "Market Analysis Title",\n  "analysis": "Market analysis description",\n  "market_expectation": "Expected market movement",\n  "support_levels": [100.0, 95.0, 90.0],\n  "resistance_levels": [110.0, 115.0, 120.0],\n  "direction": "uptrend/downtrend/sideways",\n  "confidence": 7,\n  "key_factors": ["factor1", "factor2", "factor3"],\n  "quantity": 0.5,\n  "conclusion": "Trading recommendation"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 600, 'temperature': 0.0, 'top_p': 0.1, 'stop': []}
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 290
2025-06-26 19:53:40 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:40 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/DOGE/posts/v1, 파라미터: {'start': 1750848820, 'end': 1750935220}
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Fast generation complete (5.12s)
2025-06-26 19:53:41 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: inca_execution(BTC) - 처리: 5.12초, 총: 5.12초
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(BTC) vLLM 응답 수신: 704자
2025-06-26 19:53:41 - trading.hybrid_architecture.utils.json_extractor - INFO - </think> 태그 발견, 제거 중...
2025-06-26 19:53:41 - trading.hybrid_architecture.utils.json_extractor - INFO - 태그 제거 완료. 원본 길이: 704, 정리 후 길이: 669
2025-06-26 19:53:41 - trading.hybrid_architecture.utils.json_extractor - INFO - 정리 후 텍스트 미리보기: {
  "importance": 7,
  "is_important": true,
  "importance_score": 0.75,
  "reasoning": "The short-term trend shows a neutral direction with weak strength (0.4/100), but there is a mix of bullish and ...
2025-06-26 19:53:41 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'importance': 7, 'is_important': True, 'importance_score': 0.75, 'reasoning': 'The short-term trend shows a neutral direction with weak strength (0.4/100), but there is a mix of bullish and bearish candles within the 5-minute timeframe. The price has formed a slight upward movement with a V-shaped pattern in the last two candles, indicating potential for a reversal. However, the overall trend remains neutral due to mixed signals.', 'market_direction': 'neutral', 'situation_type': 'neutral', 'action_recommendation': 'hold', 'trading_direction': 'neutral', 'confidence': 0.7, 'keywords': ['neutral trend', 'V-shaped reversal', 'weak volume']}
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 🎯 [BTC] LLM 직접 실행 결과 저장: market_direction=neutral
2025-06-26 19:53:41 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 쿼리 완료: 10개 결과 반환
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.771, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.767, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.766, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.765, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.765, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.765, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.765, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.764, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.764, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.764, 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 벡터 DB에서 10개 유사 경험 발견
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 과거 경험 분석: 5개 경험, 성공률 0.0%, 평균 수익 0.00%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - WARNING - BTC 성공률 낮음 (0.0%), 신호 강도 조정 (방향 유지)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - WARNING - BTC 성공률 매우 낮음 (0.0%), 중립 신호 유지
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 과거 경험 기반 조정 완료: 중요도 7→5, 신뢰도 0.50→0.35
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 중요도(0.50)가 임계값(0.30)을 초과하여 계속 진행
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - BTC HiAR 사고 흐름 정리 시작
2025-06-26 19:53:41 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:53:41 - offline_pattern_generator - INFO - 선택된 패턴: ['Standard_CoT']
2025-06-26 19:53:41 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:53:41 - trading.hybrid_architecture.data_store - INFO - BTC 사고 카드 저장 완료: card_2a0f2eeb_1750935221
2025-06-26 19:53:41 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_card_card_2a0f2eeb_1750935221.json
2025-06-26 19:53:41 - trading.hybrid_architecture.data_logger - INFO - BTC 사고 카드 로깅 완료: card_2a0f2eeb_1750935221
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 전략 생성 시작
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR → SELA 데이터 전달 디버깅
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card direction: N/A
2025-06-26 19:53:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 내용: {'id': 'card_1', 'title': '패턴 분석 1', 'analysis': '현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.', 'reasoning': "사고 카드 'Standard_CoT' 실행 결과", 'confidence': 0.6, 'key_factors': ['패턴: S...
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search SELA 전략 생성 시작 (v2.0 - inca_result 지원)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 내용: {'importance': 5, 'is_important': True, 'importance_score': 0.5, 'situation_type': 'neutral', 'reasoning': 'The short-term trend shows a neutral direction with weak strength (0.4/100), but there is a mix of bullish and bearish candles within the 5-minute timeframe. The price has formed a slight upward movement with a V-shaped pattern in the last two candles, indicating potential for a reversal. However, the overall trend remains neutral due to mixed signals. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)', 'action_recommendation': 'hold', 'trading_direction': 'neutral', 'signal_direction': 'neutral', 'keywords': ['neutral trend', 'V-shaped reversal', 'weak volume'], 'raw_response': ' NO MARKDOWN. ONLY JSON.\n</think>\n\n{\n  "importance": 7,\n  "is_important": true,\n  "importance_score": 0.75,\n  "reasoning": "The short-term trend shows a neutral direction with weak strength (0.4/100),...', 'confidence': 0.35, 'historical_analysis': {'total_experiences': 5, 'success_rate': 0.0, 'avg_profit': 0.0, 'adjustment_applied': True}}
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 키들: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 시작
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - id: card_1
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: card_2a0f2eeb_1750935221
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: neutral (분석용)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: hold (분석용)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: 5 (분석용)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: The short-term trend shows a neutral direction wit... (분석용)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] InCA 결과 분석: hold, 중요도: 5 (SELA 독립 분석)
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 명확한 방향성 신호 없음
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107359.2
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.269%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148169.194
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: $2136.78B
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107359.2
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 4.706%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.27%
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 5179자
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935221572_7697', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-26 19:53:41 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935221572_7697', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-26 19:53:41 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935221572_7697'}
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:41 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935221572_7697'}
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5179자
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-26 19:53:41 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-26 19:53:41 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-26 19:53:41 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-26 19:53:41 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1564
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107359.2,\n  "stop_loss": [현재가 $107359.2의 ±1-3% 범위],\n  "take_profit": [현재가 $107359.2의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 391
2025-06-26 19:53:41 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:42 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-26 19:53:42 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-26 19:53:42 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 1139
2025-06-26 19:53:42 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-26 19:53:42 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 1139개 포스트 가공 완료 (토픽: DOGE)
2025-06-26 19:53:42 - trading.hybrid_architecture.data_store - INFO - DOGE 시장 데이터 저장 완료
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_market_market_55df866a_1750935222.json
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - DOGE 시장 데이터 로깅 완료: market_55df866a_1750935222
2025-06-26 19:53:42 - trading.hybrid_architecture.data_store - WARNING - DOGE 뉴스 데이터가 문자열로 전달됨: config...
2025-06-26 19:53:42 - trading.hybrid_architecture.data_store - INFO - DOGE 뉴스 데이터 저장 완료: config
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_news_news_207db7ff_1750935222.json
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - DOGE 뉴스 데이터 로깅 완료: news_207db7ff_1750935222
2025-06-26 19:53:42 - trading.hybrid_architecture.data_store - WARNING - DOGE 뉴스 데이터가 문자열로 전달됨: data...
2025-06-26 19:53:42 - trading.hybrid_architecture.data_store - INFO - DOGE 뉴스 데이터 저장 완료: data
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_news_news_2dd70c5b_1750935222.json
2025-06-26 19:53:42 - trading.hybrid_architecture.data_logger - INFO - DOGE 뉴스 데이터 로깅 완료: news_2dd70c5b_1750935222
2025-06-26 19:53:42 - trading.hybrid_architecture.hybrid_controller - INFO - DOGE 데이터 수집 완료 및 실행 큐 전달
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - Text generation complete (time: 3.69s)
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "card_id": "reasoning_BTC_1750935220",
  "symbol": "BTC",
  "reasoning": "시장 상황 분석",
  "market_analysis": "가격 및 변동성 분석",
  "decision_factors": ["강한 상승 돌파", "주요 저항선 상승", "고체적 거래량 확인", "V자형 반전 패턴"]
...
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 201자):
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {
  "card_id": "reasoning_BTC_1750935220",
  "symbol": "BTC",
  "reasoning": "시장 상황 분석",
  "market_analysis": "가격 및 변동성 분석",
  "decision_factors": ["강한 상승 돌파", "주요 저항선 상승", "고체적 거래량 확인", "V자형 반전 패턴"]
}
2025-06-26 19:53:44 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답에 완전한 JSON 구조 발견
2025-06-26 19:53:44 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR vLLM 응답 수신: <class 'dict'>
2025-06-26 19:53:44 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR vLLM 응답 구조: ['text', 'finish_reason', 'session_id', 'symbol']
2025-06-26 19:53:44 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR 생성된 텍스트 길이: 201자
2025-06-26 19:53:44 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'card_id': 'reasoning_BTC_1750935220', 'symbol': 'BTC', 'reasoning': '시장 상황 분석', 'market_analysis': '가격 및 변동성 분석', 'decision_factors': ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']}
2025-06-26 19:53:44 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 직접 실행 성공
2025-06-26 19:53:45 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 완료, 다음 단계 진행
2025-06-26 19:53:45 - trading.hybrid_architecture.data_store - INFO - BTC 사고 카드 저장 완료: reasoning_BTC_1750935220
2025-06-26 19:53:45 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_card_reasoning_BTC_1750935220.json
2025-06-26 19:53:45 - trading.hybrid_architecture.data_logger - INFO - BTC 사고 카드 로깅 완료: reasoning_BTC_1750935220
2025-06-26 19:53:45 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] SELA 전략 생성 시작 (순차 처리)
2025-06-26 19:53:45 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] SELA 진짜 순차 실행 시작 (정상 SELA 에이전트 사용)
2025-06-26 19:53:45 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 정상 SELA 에이전트 호출
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search SELA 전략 생성 시작 (v2.0 - inca_result 지원)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 타입: <class 'dict'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 타입: <class 'NoneType'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 내용: None
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 키들: ['symbol', 'formatted_symbol', 'price', 'lastPrice', 'change_24h', 'volume_24h', 'high_24h', 'low_24h', 'timestamp', 'prices', 'volumes', 'timestamps']
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 시작
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: reasoning_BTC_1750935220
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - symbol: BTC
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 시장 상황 분석
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - market_analysis: 가격 및 변동성 분석
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - decision_factors: ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - direct_execution: True
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: N/A (백업 추출)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: N/A (백업 추출)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: N/A (백업 추출)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: N/A... (백업 추출)
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: True
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - BULLISH 키워드 감지됨!
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107359.2
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.269%
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148172.89
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107359.2
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 0.000%
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.00%
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 3880자
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935225294_3173', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-26 19:53:45 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935225294_3173', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-26 19:53:45 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935225294_3173'}
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:45 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935225294_3173'}
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 3880자
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-26 19:53:45 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-26 19:53:45 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 85자
2025-06-26 19:53:45 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 8자
2025-06-26 19:53:45 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1526
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 참고 정보 (InCA 평가)
📊 시장 분석 정보:
- 시장 중요도: 5/10
- 시장 방향성: neutral
- InCA 추천 행동: hold



시장 데이터



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction": "[long|short|neutral]",
  "entry_pric...
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 참고 정보 (InCA 평가)\n📊 시장 분석 정보:\n- 시장 중요도: 5/10\n- 시장 방향성: neutral\n- InCA 추천 행동: hold\n\n\n\n시장 데이터\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107359.2,\n  "stop_loss": [현재가 $107359.2의 ±1-3% 범위],\n  "take_profit": [현재가 $107359.2의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 381
2025-06-26 19:53:45 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.73s)
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107359.2,
  "stop_loss": [105215.7, 109196.7],
  "take_profit": [105215.7, 111727.1],
  "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, R...
2025-06-26 19:53:47 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.7, 109196.7],\n  "take_profit": [105215.7, 111727.1],\n  "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 볼린저 밴드의 중간 선 근처에서 움직이고 있다. MACD는 0을 중심으로 상하 진동하며, 단기적인 방향성이 명확하지 않다. InCA의 HOLD 신호와 일치하여 현재는 거래를 자제하는 것이 적절하다.",\n  "confidence": 0.75,\n  "key_points": ["RSI는 중립 영역에서 진동", "MACD는 0을 중심으로 상하 진동", "볼린저 밴드 중간선 근처에서 가격 움직임"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.74초
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.7, 109196.7],\n  "take_profit": [105215.7, 111727.1],\n  "reasoning": "현재 시장 데이터에 따르면, 가격은...
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 439자
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 439자
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107359.2, 'stop_loss': [105215.7, 109196.7], 'take_profit': [105215.7, 111727.1], 'reasoning': '현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 볼린저 밴드의 중간 선 근처에서 움직이고 있다. MACD는 0을 중심으로 상하 진동하며, 단기적인 방향성이 명확하지 않다. InCA의 HOLD 신호와 일치하여 현재는 거래를 자제하는 것이 적절하다.', 'confidence': 0.75, 'key_points': ['RSI는 중립 영역에서 진동', 'MACD는 0을 중심으로 상하 진동', '볼린저 밴드 중간선 근처에서 가격 움직임']}
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: f956291c-07ca-48b2-bfe1-e0e491414eb3
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.31
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.463960173676051
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0640
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 볼린저 밴드의 중간 선 근처에서 움직이고 있다. MACD는 0을 중심으로 상하 진동하...
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.74초
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 완료
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과 타입: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과: {'strategy_id': 'f956291c-07ca-48b2-bfe1-e0e491414eb3', 'symbol': 'BTC', 'timestamp': 1750935227, 'type': 'none', 'entry_price': 107359.2, 'stop_loss': 105215.7, 'take_profit': 105215.7, 'reasoning': '현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 볼린저 밴드의 중간 선 근처에서 움직이고 있다. MACD는 0을 중심으로 상하 진동하며, 단기적인 방향성이 명확하지 않다. InCA의 HOLD 신호와 일치하여 현재는 거래를 자제하는 것이 적절하다.', 'confidence': 0.75, 'reasoning_card_id': 'card_2a0f2eeb_1750935221', 'risk_level': 'medium', 'key_points': ['RSI는 중립 영역에서 진동', 'MACD는 0을 중심으로 상하 진동', '볼린저 밴드 중간선 근처에서 가격 움직임'], 'market_context': {'price': 107359.2, 'percent_change_24h': 0.269, 'timestamp': 1750935213}, 'paper_based': False, 'risk_reward': 0.0, 'importance': 7.309238633018353}
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 클래스: <class 'super'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 메서드 목록: []
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 메서드 호출 시도
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: CachingLLMProxy (VLLMWrapper 불필요)
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: medium\strategies.db
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - id: card_1
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: card_2a0f2eeb_1750935221
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: neutral (분석용)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: hold (분석용)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: 5 (분석용)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: The short-term trend shows a neutral direction wit... (분석용)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] InCA 결과 분석: hold, 중요도: 5 (SELA 독립 분석)
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 명확한 방향성 신호 없음
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107359.2
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.269%
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148169.194
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: $2136.78B
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107359.2
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 4.706%
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.27%
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 5179자
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935227324_9619', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-26 19:53:47 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935227324_9619', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-26 19:53:47 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935227324_9619'}
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:47 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935227324_9619'}
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5179자
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-26 19:53:47 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-26 19:53:47 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-26 19:53:47 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-26 19:53:47 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1564
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107359.2,\n  "stop_loss": [현재가 $107359.2의 ±1-3% 범위],\n  "take_profit": [현재가 $107359.2의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 391
2025-06-26 19:53:47 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Text generation complete (time: 6.09s)
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107359.2,
  "stop_loss": [105215.264, 109503.136],
  "take_profit": [105215.264, 111929.16],
  "reasoning": "시장 중요도가 낮고 방향성이 중립인 상황에서, In...
2025-06-26 19:53:51 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.264, 109503.136],\n  "take_profit": [105215.264, 111929.16],\n  "reasoning": "시장 중요도가 낮고 방향성이 중립인 상황에서, InCA의 hold 신호와 SELA의 분석을 종합적으로 고려한 결과, 현재 시장 조건에서는 구체적인 매수 또는 매도 신호가 부족하다. 기술적 지표와 패턴 분석에서도 명확한 추세가 확인되지 않으며, 리스크/리워드 비율을 유지하기 위해 무위험 상태를 유지하는 것이 적절하다.",\n  "confidence": 0.75,\n  "key_points": ["시장 중요도가 낮아 단기적 변동성에 민감함", "InCA의 hold 신호와 SELA 분석이 일치", "리스크 관리 차원에서 무위험 전략 유지"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 6.09초
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.264, 109503.136],\n  "take_profit": [105215.264, 111929.16],\n  "reasoning": "시장 중요도가 낮고 방...
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 474자
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 474자
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107359.2, 'stop_loss': [105215.264, 109503.136], 'take_profit': [105215.264, 111929.16], 'reasoning': '시장 중요도가 낮고 방향성이 중립인 상황에서, InCA의 hold 신호와 SELA의 분석을 종합적으로 고려한 결과, 현재 시장 조건에서는 구체적인 매수 또는 매도 신호가 부족하다. 기술적 지표와 패턴 분석에서도 명확한 추세가 확인되지 않으며, 리스크/리워드 비율을 유지하기 위해 무위험 상태를 유지하는 것이 적절하다.', 'confidence': 0.75, 'key_points': ['시장 중요도가 낮아 단기적 변동성에 민감함', 'InCA의 hold 신호와 SELA 분석이 일치', '리스크 관리 차원에서 무위험 전략 유지']}
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: 503af175-761b-4157-8436-c11facf60dc6
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.33
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.4757236947377146
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0757
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 시장 중요도가 낮고 방향성이 중립인 상황에서, InCA의 hold 신호와 SELA의 분석을 종합적으로 고려한 결과, 현재 시장 조건에서는 구체적인 매수 또는 매도 신호가 부족하다....
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 6.09초
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 완료
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과 타입: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과: {'strategy_id': '503af175-761b-4157-8436-c11facf60dc6', 'symbol': 'BTC', 'timestamp': 1750935231, 'type': 'none', 'entry_price': 107359.2, 'stop_loss': 105215.264, 'take_profit': 105215.264, 'reasoning': '시장 중요도가 낮고 방향성이 중립인 상황에서, InCA의 hold 신호와 SELA의 분석을 종합적으로 고려한 결과, 현재 시장 조건에서는 구체적인 매수 또는 매도 신호가 부족하다. 기술적 지표와 패턴 분석에서도 명확한 추세가 확인되지 않으며, 리스크/리워드 비율을 유지하기 위해 무위험 상태를 유지하는 것이 적절하다.', 'confidence': 0.75, 'reasoning_card_id': 'reasoning_BTC_1750935220', 'risk_level': 'medium', 'key_points': ['시장 중요도가 낮아 단기적 변동성에 민감함', 'InCA의 hold 신호와 SELA 분석이 일치', '리스크 관리 차원에서 무위험 전략 유지'], 'market_context': {'price': 107359.2, 'percent_change_24h': 0.0, 'timestamp': 1750935213}, 'paper_based': False, 'risk_reward': 0.0, 'importance': 7.32533220896379}
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 클래스: <class 'super'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 메서드 목록: []
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 메서드 호출 시도
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: CachingLLMProxy (VLLMWrapper 불필요)
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: medium\strategies.db
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: reasoning_BTC_1750935220
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - symbol: BTC
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 시장 상황 분석
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - market_analysis: 가격 및 변동성 분석
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - decision_factors: ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - direct_execution: True
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: N/A (백업 추출)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: N/A (백업 추출)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: N/A (백업 추출)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: N/A... (백업 추출)
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: True
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - BULLISH 키워드 감지됨!
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107359.2
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.269%
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148172.89
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107359.2
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 0.000%
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.00%
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 3880자
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935231397_3586', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-26 19:53:51 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750935231397_3586', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-26 19:53:51 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935231397_3586'}
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 1820913621968
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-26 19:53:51 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750935231397_3586'}
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 3880자
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-26 19:53:51 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-26 19:53:51 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 85자
2025-06-26 19:53:51 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 8자
2025-06-26 19:53:51 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1526
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 참고 정보 (InCA 평가)
📊 시장 분석 정보:
- 시장 중요도: 5/10
- 시장 방향성: neutral
- InCA 추천 행동: hold



시장 데이터



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction": "[long|short|neutral]",
  "entry_pric...
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 참고 정보 (InCA 평가)\n📊 시장 분석 정보:\n- 시장 중요도: 5/10\n- 시장 방향성: neutral\n- InCA 추천 행동: hold\n\n\n\n시장 데이터\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107359.2,\n  "stop_loss": [현재가 $107359.2의 ±1-3% 범위],\n  "take_profit": [현재가 $107359.2의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 381
2025-06-26 19:53:51 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-26 19:53:53 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:53 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:53 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:53 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.81s)
2025-06-26 19:53:53 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107359.2,
  "stop_loss": [106215.6, 108502.8],
  "take_profit": [105220.3, 109500.1],
  "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, R...
2025-06-26 19:53:53 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-26 19:53:53 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [106215.6, 108502.8],\n  "take_profit": [105220.3, 109500.1],\n  "reasoning": "현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. MACD는 0선 근처에서 횡보하며 단기적인 방향성 부재를 보이고 있다. 볼린저 밴드는 가격이 중간 대에 위치해 있으며, 강한 추세 신호는 확인되지 않았다. InCA의 HOLD 신호와 일치하여 현재는 거래 기회가 제한적이다.",\n  "confidence": 0.72,\n  "key_points": ["RSI와 MACD의 중립적 지표", "볼린저 밴드의 중간 대 위치", "InCA의 HOLD 신호와 일치"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.82초
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [106215.6, 108502.8],\n  "take_profit": [105220.3, 109500.1],\n  "reasoning": "현재 시장 데이터에 따르면, 가격은...
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 468자
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 468자
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107359.2, 'stop_loss': [106215.6, 108502.8], 'take_profit': [105220.3, 109500.1], 'reasoning': '현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. MACD는 0선 근처에서 횡보하며 단기적인 방향성 부재를 보이고 있다. 볼린저 밴드는 가격이 중간 대에 위치해 있으며, 강한 추세 신호는 확인되지 않았다. InCA의 HOLD 신호와 일치하여 현재는 거래 기회가 제한적이다.', 'confidence': 0.72, 'key_points': ['RSI와 MACD의 중립적 지표', '볼린저 밴드의 중간 대 위치', 'InCA의 HOLD 신호와 일치']}
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: e496900a-cd90-4dc5-a3cf-5e129e7c1f18
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.32
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.470135148435751
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0701
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.72
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 현재 시장 데이터에 따르면, 가격은 중립적인 영역에 있으며, RSI는 50을 중심으로 진동하며 명확한 과열 또는 과냉 상태가 아니다. MACD는 0선 근처에서 횡보하며 단기적인 방...
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.82초
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-26 19:53:53 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 호출 결과: <class 'dict'>
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] SELA 직접 사용: none (신뢰도: 0.720)
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] 합의 데이터 수집 완료 (학습용)
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 실제 학습 데이터 추출 완료: InCA=hold, SELA=none, 장기=neutral
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 거래 결정 메서드 호출 시작
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 메서드 존재 확인: True
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM이 none 신호로 판단하여 거래하지 않음
2025-06-26 19:53:53 - trading.hybrid_architecture.utils.virtual_position_tracker - INFO - 🔮 [BTC] 가상 포지션 생성: neutral 예측 (가격: $107359.2000, 신뢰도: 0.50, 평가 기간: 180초 = 3분)
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - 🔮 [BTC] 중립 신호 가상 포지션 생성 완료: 가격=$107359.2000, 신뢰도=0.50, 중요도=5, 3분 후 평가
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 거래 결정 완료: False
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - ERROR - BTC 실행 플로우 중 오류 발생: local variable 'inca_action' referenced before assignment
2025-06-26 19:53:53 - trading.hybrid_architecture.hybrid_controller - ERROR - Traceback (most recent call last):
  File "G:\ai_bot_trading\trading\hybrid_architecture\hybrid_controller.py", line 3392, in execute_flow
    if inca_action in ['buy', 'sell'] and importance_score > 8.0:
UnboundLocalError: local variable 'inca_action' referenced before assignment

2025-06-26 19:53:53 - hybrid_simulator - INFO - ⏳ BTC 처리 완료 대기 시작 (최대 300초)
2025-06-26 19:53:53 - hybrid_simulator - INFO - ✅ BTC 처리 완료 확인 (0.00초)
2025-06-26 19:53:53 - hybrid_simulator - INFO - ✅ BTC 시장 데이터 처리 완료 (순차 모드)
2025-06-26 19:53:53 - hybrid_simulator - INFO - 🔄 ETH 완전 순차 처리 시작
2025-06-26 19:53:53 - simulator.utils.market_utils - INFO - ETH 실시간 시장 데이터 수집 중...
2025-06-26 19:53:53 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:53 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:53 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2454.48
2025-06-26 19:53:53 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:53 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:53 - binance.binance_utils - INFO - 변화율(%): 1.156
2025-06-26 19:53:53 - binance.binance_utils - INFO - 거래량: 5672948.796
2025-06-26 19:53:53 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=3m, 개수=500
2025-06-26 19:53:53 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 3m, 개수: 480
2025-06-26 19:53:53 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2454.48
2025-06-26 19:53:53 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-26 19:53:53 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:53 - binance.binance_utils - INFO - 변화율(%): 1.140
2025-06-26 19:53:53 - binance.binance_utils - INFO - 거래량: 5673113.121
2025-06-26 19:53:53 - simulator.utils.market_utils - INFO - ETH 24h 변화율: 1.14%
2025-06-26 19:53:53 - simulator.utils.market_utils - INFO - ETH 현재 가격: 2454.48, 24h 변화율: 1.14%
2025-06-26 19:53:53 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2454.48, 변동률 1.14%
2025-06-26 19:53:53 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 중...
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH(ID:2) 코인 데이터 요청 중...
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/2/v1, 파라미터: {}
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/2/v1
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH 코인 데이터 응답 키: ['config', 'data']
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH 거래량 정보: volume_24h=19346372232.32
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-26 19:53:53 - data_collector.lunarcrush_collector - INFO - ETH 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/news/v1, 파라미터: {}
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/news/v1
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 응답 키: ['config', 'data']
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 71
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 71개 수집 완료
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 데이터베이스에 저장 완료
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 71개 수집 완료
2025-06-26 19:53:54 - data_collector.lunarcrush_collector - INFO - ETH 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-26 19:53:55 - hybrid_simulator - INFO - 사용자에 의해 중단됨
2025-06-26 19:53:55 - hybrid_simulator - INFO - 하이브리드 거래 시스템 종료 중...
2025-06-26 19:53:56 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-26 19:53:56 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-26 19:53:56 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-26 19:53:56 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.00s)
2025-06-26 19:53:56 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107359.2,
  "stop_loss": [105215.264, 109512.136],
  "take_profit": [105215.264, 111927.14],
  "reasoning": "시장 중요도가 낮고 방향성이 중립적임. InCA 추...
2025-06-26 19:53:56 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-26 19:53:56 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.264, 109512.136],\n  "take_profit": [105215.264, 111927.14],\n  "reasoning": "시장 중요도가 낮고 방향성이 중립적임. InCA 추천은 hold로, SELA 분석에서도 단기/중기 트렌드가 명확하지 않으며 기술적 지표도 혼합된 신호를 보임. 리스크/리워드 비율을 유지하기 위해 무위험 상태 유지.",\n  "confidence": 0.75,\n  "key_points": ["시장 중요도 낮음", "InCA 추천 hold", "리스크/리워드 비율 1.2 이상 유지"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.03초
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107359.2,\n  "stop_loss": [105215.264, 109512.136],\n  "take_profit": [105215.264, 111927.14],\n  "reasoning": "시장 중요도가 낮고 방...
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 387자
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 387자
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107359.2, 'stop_loss': [105215.264, 109512.136], 'take_profit': [105215.264, 111927.14], 'reasoning': '시장 중요도가 낮고 방향성이 중립적임. InCA 추천은 hold로, SELA 분석에서도 단기/중기 트렌드가 명확하지 않으며 기술적 지표도 혼합된 신호를 보임. 리스크/리워드 비율을 유지하기 위해 무위험 상태 유지.', 'confidence': 0.75, 'key_points': ['시장 중요도 낮음', 'InCA 추천 hold', '리스크/리워드 비율 1.2 이상 유지']}
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: a6a51e62-fe45-44a6-a529-90ced26b5739
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.34
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.4843198863287834
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0843
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 시장 중요도가 낮고 방향성이 중립적임. InCA 추천은 hold로, SELA 분석에서도 단기/중기 트렌드가 명확하지 않으며 기술적 지표도 혼합된 신호를 보임. 리스크/리워드 비율을...
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.03초
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-26 19:53:56 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 호출 결과: <class 'dict'>
2025-06-26 19:53:56 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] 정상 SELA 에이전트 전략 생성 성공: none
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] SELA 완료, 다음 단계 진행
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] sela_result 타입: <class 'dict'>
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] sela_result 값: {'strategies': [{'strategy_id': 'a6a51e62-fe45-44a6-a529-90ced26b5739', 'symbol': 'BTC', 'timestamp': 1750935236, 'type': 'none', 'entry_price': 107359.2, 'stop_loss': 105215.264, 'take_profit': 105215.264, 'reasoning': '시장 중요도가 낮고 방향성이 중립적임. InCA 추천은 hold로, SELA 분석에서도 단기/중기 트렌드가 명확하지 않으며 기술적 지표도 혼합된 신호를 보임. 리스크/리워드 비율을 유지하기 위해 무위험 상태 유지.', 'confidence': 0.75, 'reasoning_card_id': 'reasoning_BTC_1750935220', 'risk_level': 'medium', 'key_points': ['시장 중요도 낮음', 'InCA 추천 hold', '리스크/리워드 비율 1.2 이상 유지'], 'market_context': {'price': 107359.2, 'percent_change_24h': 0.0, 'timestamp': 1750935213}, 'paper_based': False, 'risk_reward': 0.0, 'importance': 7.337017687674382}], 'direct_execution': False}
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - WARNING - ⚠️ [BTC] InCA 신호(buy)와 일치하는 SELA 전략 없음, 최고 신뢰도 전략 선택: none (신뢰도: 0.750)
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] SELA 논문 방법론 적용: type=none, confidence=0.750
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] SELA 직접 사용: none (신뢰도: 0.750)
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] 합의 데이터 수집 완료 (학습용)
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 실제 학습 데이터 추출 완료: InCA=buy, SELA=none, 장기=neutral
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM이 none 신호로 판단하여 거래하지 않음
2025-06-26 19:53:57 - trading.hybrid_architecture.utils.virtual_position_tracker - INFO - 🔮 [BTC] 가상 포지션 생성: neutral 예측 (가격: $107359.2000, 신뢰도: 0.50, 평가 기간: 180초 = 3분)
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🔮 [BTC] 중립 신호 가상 포지션 생성 완료: 가격=$107359.2000, 신뢰도=0.50, 중요도=5, 3분 후 평가
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 신호가 중립이므로 거래하지 않음
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 학습 루프 시작
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 학습 데이터 저장 완료 (큐 크기: 1)
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 로그 50개 검색됨
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] SELA 학습 에이전트 실행 시작
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] 과거 전략 50개, 성과 데이터 50개 추출
2025-06-26 19:53:57 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:57 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:57 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:57 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:57 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107342.2
2025-06-26 19:53:57 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:57 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:53:57 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:53:57 - binance.binance_utils - INFO - 변화율(%): 0.218
2025-06-26 19:53:57 - binance.binance_utils - INFO - 거래량: 148199.125
2025-06-26 19:53:57 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:53:57 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTC), 인터벌=1d, 개수=500
2025-06-26 19:53:57 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1d, 개수: 1
2025-06-26 19:53:57 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107342.2
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 학습용 실제 시장 데이터: 가격=$107342.2
2025-06-26 19:53:57 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] SELA 학습 에이전트 호출 - 실험적 전략 생성
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search 기반 SELA 학습 전략 5개 생성 시작
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 Tree Search SELA 다양한 전략 5개 생성 시작
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - BTC 다양한 전략 5개 생성 시작
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] 외부 시장 데이터 사용: 가격=$107342.2
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] 다양한 전략 생성용 시장 데이터:
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107342.2
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.218%
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148199.125
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성용 시장 데이터 확인:
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107342.2
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.218%
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 148199.125
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성 프롬프트 (처음 200자):
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 Generate 5 trading strategies for BTC.

Market: BTC $107342.2 (0.22%)

IMPORTANT: Use current price $107342.2 as base for all entry_price calculations.

Required JSON format:
{"strategies": [
  {
    ...
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성 프롬프트 (마지막 200자):
2025-06-26 19:53:57 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 ..., "point2"]
  }
]}

Create 5 different strategies with varied types (buy/sell/none), risk levels (low/medium/high), and strategy types.
All entry_price values must be based on current price $107342.2.
2025-06-26 19:53:57 - models.vllm_request_queue - INFO - vLLM 요청 추가: sela_diverse(BTC) - 우선순위 2, 큐 크기: 1
2025-06-26 19:53:57 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: sela_diverse(BTC) - 큐 대기: 0.00초
2025-06-26 19:53:57 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_sela_diverse_BTC_1750935237679
2025-06-26 19:53:57 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): Generate 5 trading strategies for BTC.

Market: BTC $107342.2 (0.22%)

IMPORTANT: Use current price ...
2025-06-26 19:53:57 - models.vllm_session_manager - INFO - Created new session: 32a7846b-6df2-4551-ad37-1bb8616d9595 for sela_diverse agent (symbol: BTC)
2025-06-26 19:53:57 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: sela_diverse_execution_BTC → 32a7846b-6df2-4551-ad37-1bb8616d9595
2025-06-26 19:53:57 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: 32a7846b-6df2-4551-ad37-1bb8616d9595 (sela_diverse_BTC)
2025-06-26 19:53:57 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:53:57 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: 32a7846b-6df2-4551-ad37-1bb8616d9595
2025-06-26 19:53:57 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 180s, session: 32a7846b-6df2-4551-ad37-1bb8616d9595)
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - ============================================================
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 📊 vLLM 요청 큐 통계 요약
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 총 요청: 2, 완료: 1, 처리중: 1
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 대기중: 0, 오류: 0
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 평균 총 시간: 5.12초
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 평균 처리 시간: 5.12초
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - 평균 큐 대기 시간: 0.0초
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - [INCA_EXECUTION] 요청: 1, 성공률: 100.0%, 평균 처리: 5.12초
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - [SELA_DIVERSE] 요청: 1, 성공률: 0.0%, 평균 처리: 0초
2025-06-26 19:54:05 - models.vllm_request_queue - INFO - ============================================================
2025-06-26 19:54:06 - models.vllm_client_enhanced - INFO - Fast generation complete (9.31s)
2025-06-26 19:54:06 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: sela_diverse(BTC) - 처리: 9.32초, 총: 9.32초
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 루프 종료
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 다양한 전략 생성 - vLLM 큐 호출 성공 (소요 시간: 9.42초)
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 스레드 중지
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 LLM 응답 타입: <class 'dict'>
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - vLLM 요청 큐 워커 중지됨
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 LLM 응답 전체: {'text': ' NO EXPLANATIONS. NO MARKDOWN. ONLY VALID JSON.\n</think>\n\n{"strategies": [{"type": "buy", "entry_price": 107342.2, "stop_loss": 105195.36, "take_profit": 111635.89, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109524.83, "take_profit": 104865.12, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "reversal", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107342.2, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with tight range and low volatility indicators", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volatility"]}, {"type": "buy", "entry_price": 107342.2, "stop_loss": 104865.12, "take_profit": 113000.00, "reasoning": "Breakout above key resistance level with increasing volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109000.00, "take_profit": 103000.00, "reasoning": "Fibonacci retracement level with bearish divergence and weak support holding", "confidence": 0.7, "risk_level": "medium", "strategy_type": "fibonacci", "key_points": ["Fibonacci level", "divergence"]}]}', 'finish_reason': 'stop', 'elapsed_time': 9.312599897384644, 'fast_mode': True, 'symbol': 'BTC', 'agent_type': 'sela_diverse', 'session_id': '32a7846b-6df2-4551-ad37-1bb8616d9595'}
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 시스템 종료 완료
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 응답 텍스트 길이: 1624자
2025-06-26 19:54:07 - hybrid_simulator - INFO - 🚀 하이브리드 컨트롤러 연속 모드 종료 완료
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 응답 텍스트 전체:  NO EXPLANATIONS. NO MARKDOWN. ONLY VALID JSON.
</think>

{"strategies": [{"type": "buy", "entry_price": 107342.2, "stop_loss": 105195.36, "take_profit": 111635.89, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109524.83, "take_profit": 104865.12, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "reversal", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107342.2, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with tight range and low volatility indicators", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volatility"]}, {"type": "buy", "entry_price": 107342.2, "stop_loss": 104865.12, "take_profit": 113000.00, "reasoning": "Breakout above key resistance level with increasing volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109000.00, "take_profit": 103000.00, "reasoning": "Fibonacci retracement level with bearish divergence and weak support holding", "confidence": 0.7, "risk_level": "medium", "strategy_type": "fibonacci", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 동기화 시작: 강제=True, 마지막 동기화 이후 49초 경과
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 SELA 다양한 전략 생성에서 </think> 태그 감지됨, 제거 중...
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 바이낸스 API를 통해 포지션 정보 동기화 시작
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 SELA 다양한 전략 생성 </think> 태그 제거 완료
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 정리된 응답 텍스트: {"strategies": [{"type": "buy", "entry_price": 107342.2, "stop_loss": 105195.36, "take_profit": 111635.89, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109524.83, "take_profit": 104865.12, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "reversal", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107342.2, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with tight range and low volatility indicators", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volatility"]}, {"type": "buy", "entry_price": 107342.2, "stop_loss": 104865.12, "take_profit": 113000.00, "reasoning": "Breakout above key resistance level with increasing volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107342.2, "stop_loss": 109000.00, "take_profit": 103000.00, "reasoning": "Fibonacci retracement level with bearish divergence and weak support holding", "confidence": 0.7, "risk_level": "medium", "strategy_type": "fibonacci", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 길이: 1566자
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 첫 100자: {"strategies": [{"type": "buy", "entry_price": 107342.2, "stop_loss": 105195.36, "take_profit": 1116
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 마지막 100자: k_level": "medium", "strategy_type": "fibonacci", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 char 413 위치 문자: '1'
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 char 410-420 범위: '": 104865.'
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 1566자
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 성공!
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - BTC 다양한 전략 생성 완료 (소요 시간: 9.42초, 생성된 전략: 5개)
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] SELA 학습 에이전트 성공: 5개 실험적 전략 생성됨
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 실행 시작
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 1/5 분석 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107359.2, 변동률=0.269%
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:54:07 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 1 사고 카드 1개 생성
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 2/5 분석 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107359.2, 변동률=0.269%
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:54:07 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 2 사고 카드 1개 생성
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 3/5 분석 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107359.2, 변동률=0.269%
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:54:07 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 3 사고 카드 1개 생성
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 4/5 분석 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107359.2, 변동률=0.269%
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:54:07 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 4 사고 카드 1개 생성
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 5/5 분석 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107359.2, 변동률=0.269%
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-26 19:54:07 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-26 19:54:07 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 5 사고 카드 1개 생성
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 성공: 5개 사고 카드 생성됨
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 실행 시작
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 5개 전략-사고카드 쌍 준비됨
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 전략 1/5 평가 중
2025-06-26 19:54:07 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 과거 성과 데이터 50개 사용
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 0
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING - 캔들 데이터가 없어서 직접 바이낸스 API 호출 시도...
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935246044}
2025-06-26 19:54:07 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935246044
2025-06-26 19:54:07 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935246044
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 직접 API 호출로 캔들 데이터 5개 수집 성공!
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 캔들 패턴 분석 시작: 5개 캔들
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 캔들 패턴 분석 완료: bearish
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING - 🔍 [BTC] 전략 평가 프롬프트 캔들 데이터 상태:
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - market_data 키들: ['symbol', 'formatted_symbol', 'price', 'lastPrice', 'change_24h', 'volume_24h', 'high_24h', 'low_24h', 'timestamp', 'prices', 'volumes', 'timestamps']
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 5
2025-06-26 19:54:07 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 첫 캔들 샘플: [1750935000000, '107308.80', '107358.20', '107308.60', '107358.10', '45.819', 1750935059999, '4917286.26590', 905, '41.235', '4425328.08160', '0']
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_learning(BTC) - 우선순위 4, 큐 크기: 1
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 루프 시작
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 스레드 시작
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_learning(BTC) - 큐 대기: 0.00초
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_learning_BTC_1750935247204
2025-06-26 19:54:07 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
BTC 전략 평가 요청

=== 시장 데이터 ===
현재가: $107359.2
캔들 분석: 캔들 패턴: bearish - 3연속 하락 캔들 (강한), 추세: -0.08%
거래량:...
2025-06-26 19:54:07 - models.vllm_session_manager - INFO - Created new session: 51ca132b-4142-4a53-a2f3-d3d6f3a1319a for inca_learning agent (symbol: BTC)
2025-06-26 19:54:07 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_learning_execution_BTC → 51ca132b-4142-4a53-a2f3-d3d6f3a1319a
2025-06-26 19:54:07 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: 51ca132b-4142-4a53-a2f3-d3d6f3a1319a (inca_learning_BTC)
2025-06-26 19:54:07 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 /no_think 태그 추가됨
2025-06-26 19:54:07 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_learning 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-26 19:54:07 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: 51ca132b-4142-4a53-a2f3-d3d6f3a1319a
2025-06-26 19:54:07 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: 51ca132b-4142-4a53-a2f3-d3d6f3a1319a)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 522
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -0.83996)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.1143)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -1.22976)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BTCUSDT = 0.001 (PnL: -0.0274)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - positionAmt: 0.00 (float: 0.0)
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - entryPrice: 0.0
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - unrealizedProfit: 0.00000000
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:54:07 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - markPrice: None
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - unrealizedProfit: -1.22976000
2025-06-26 19:54:07 - binance.binance_utils - INFO -   - percentage: None
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 응답 타입: <class 'list'>
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 전체 포지션 수: 522
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 활성 포지션 개수: 4
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 수: 1
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 수: 1
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 상세: positionAmt=0.0, entryPrice=0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 상세: positionAmt=504.0, entryPrice=0.16426
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 전체 원본 데이터:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'SOLUSDT', 'initialMargin': '0', 'maintMargin': '0', 'unrealizedProfit': '0.00000000', 'positionInitialMargin': '0', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.0', 'breakEvenPrice': '0.0', 'maxNotional': '400000000', 'positionSide': 'BOTH', 'positionAmt': '0.00', 'notional': '0', 'isolatedWallet': '0', 'updateTime': 1750934575636, 'bidNotional': '0', 'askNotional': '0'}
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 주요 필드:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - symbol: SOLUSDT
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - positionAmt: 0.00 (타입: <class 'str'>)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: 0.00000000
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - notional: 0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 0.0 (타입: <class 'float'>)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [ETH] 포지션 방향 결정: positionAmt=0.023
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [ETH] 양수 포지션 → LONG 방향
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [ETH] 최종 결정된 방향: long
2025-06-26 19:54:07 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-26 19:54:07 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:54:07 - binance.binance_utils - INFO - 변화율(%): 0.868
2025-06-26 19:54:07 - binance.binance_utils - INFO - 거래량: 5694563.501
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [ETH] 🔧 강제 PnL 계산: -1.538% (진입: 2486.13, 현재: 2447.89, 방향: long)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [ETH] 바이낸스 포지션 상세:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 2486.13
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.83996
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -1.54%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [ETH] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [ETH] 기존 포지션 ID: sync_ETH_1750928149, 동기화 포지션 ID: sync_ETH_1750935247
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BNB] 포지션 방향 결정: positionAmt=0.09
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BNB] 양수 포지션 → LONG 방향
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BNB] 최종 결정된 방향: long
2025-06-26 19:54:07 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-26 19:54:07 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:54:07 - binance.binance_utils - INFO - 변화율(%): 0.144
2025-06-26 19:54:07 - binance.binance_utils - INFO - 거래량: 339261.09
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [BNB] 🔧 강제 PnL 계산: -0.184% (진입: 646.61, 현재: 645.42, 방향: long)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [BNB] 바이낸스 포지션 상세:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 646.61
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.1143
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.18%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [BNB] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [BNB] 기존 포지션 ID: sync_BNB_1750935198, 동기화 포지션 ID: sync_BNB_1750935247
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 전체 원본 데이터:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'DOGEUSDT', 'initialMargin': '81.55728000', 'maintMargin': '0.53012232', 'unrealizedProfit': '-1.22976000', 'positionInitialMargin': '81.55728000', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.16426', 'breakEvenPrice': '0.16434213', 'maxNotional': '200000000', 'positionSide': 'BOTH', 'positionAmt': '504', 'notional': '81.55728000', 'isolatedWallet': '0', 'updateTime': 1750927964135, 'bidNotional': '0', 'askNotional': '0'}
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 주요 필드:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - symbol: DOGEUSDT
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - positionAmt: 504 (타입: <class 'str'>)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.22976000
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - notional: 81.55728000
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 504.0 (타입: <class 'float'>)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 포지션 방향 결정: positionAmt=504.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 양수 포지션 → LONG 방향
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 최종 결정된 방향: long
2025-06-26 19:54:07 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-26 19:54:07 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:54:07 - binance.binance_utils - INFO - 변화율(%): -2.243
2025-06-26 19:54:07 - binance.binance_utils - INFO - 거래량: 4987341160
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [DOGE] 🔧 강제 PnL 계산: -1.540% (진입: 0.16426, 현재: 0.16173, 방향: long)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 상세:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.22976
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -1.54%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 기존 포지션 ID: sync_DOGE_1750927996, 동기화 포지션 ID: sync_DOGE_1750935247
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BTC] 포지션 방향 결정: positionAmt=0.001
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BTC] 양수 포지션 → LONG 방향
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔧 [BTC] 최종 결정된 방향: long
2025-06-26 19:54:07 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-26 19:54:07 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:54:07 - binance.binance_utils - INFO - 변화율(%): 0.185
2025-06-26 19:54:07 - binance.binance_utils - INFO - 거래량: 148221.535
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [BTC] 🔧 강제 PnL 계산: -0.034% (진입: 107311.4, 현재: 107274.4, 방향: long)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [BTC] 바이낸스 포지션 상세:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - entryPrice: 107311.4
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.0274
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.03%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [BTC] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔒 [BTC] 기존 포지션 ID: trade_BTC_1750934599, 동기화 포지션 ID: sync_BTC_1750935247
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 시작: 현재 4개 포지션, 거래소에서 가져온 4개 포지션
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - exchange_positions 상세:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[0]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[1]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[2]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[3]: BTC (positionAmt=0, size=0.001, quantity=0.001)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 현재 내부 포지션 키: ['ETH_long', 'BNB_long', 'DOGE_long', 'BTC_long']
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 거래소 포지션 키: ['ETH_long', 'BNB_long', 'DOGE_long', 'BTC_long']
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 바이낸스 API 포지션과 내부 포지션 스마트 매핑
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 업데이트: ETH long, PnL: -1.54%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 업데이트: BNB long, PnL: -0.18%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 업데이트: DOGE long, PnL: -1.54%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 업데이트: BTC long, PnL: -0.03%
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 완료: 최종 4개 포지션
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions 배열 크기: 4
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[0]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[1]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[2]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[3]: BTC (positionAmt=0, size=0.001, quantity=0.001)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 수: 4
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 1: ETH (크기: 0.023)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 2: BNB (크기: 0.09)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 3: DOGE (크기: 504.0)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 4: BTC (크기: 0.001)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 수: 4
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 1: ETH (크기: 0.023)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 2: BNB (크기: 0.09)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 3: DOGE (크기: 504.0)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 4: BTC (크기: 0.001)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 바이낸스 활성 포지션: ['ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)', 'BTC(0.001)']
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 내부 활성 포지션: ['ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)', 'BTC(0.001)']
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - ✅ 포지션 동기화 상태 양호: 불일치 없음
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔥 포지션 동기화 완료: 4개의 활성 포지션 (내부: 4개)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 🔥 동기화 변경사항: 4개
2025-06-26 19:54:07 - hybrid_simulator - INFO - 🔥 종료 전 포지션 정보 동기화 완료
2025-06-26 19:54:07 - hybrid_simulator - INFO - 🔥 최종 동기화: 4 → 4 포지션
2025-06-26 19:54:07 - hybrid_simulator - INFO - 🔥 최종 동기화 변경사항:
2025-06-26 19:54:07 - hybrid_simulator - INFO -    - 동기화: ETH long (PnL: -1.54%)
2025-06-26 19:54:07 - hybrid_simulator - INFO -    - 동기화: BNB long (PnL: -0.18%)
2025-06-26 19:54:07 - hybrid_simulator - INFO -    - 동기화: DOGE long (PnL: -1.54%)
2025-06-26 19:54:07 - hybrid_simulator - INFO -    - 동기화: BTC long (PnL: -0.03%)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 기본 잔액: $610.68
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - BTC: 0.03800000 @ $107359.20 = $4079.65
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 총 가치: $4690.33
2025-06-26 19:54:07 - hybrid_simulator - INFO - 최종 포트폴리오 총 가치: $4690.33
2025-06-26 19:54:07 - hybrid_simulator - INFO - 초기 잔액: $10000.00
2025-06-26 19:54:07 - hybrid_simulator - INFO - 총 손익: $-5309.67 (-53.10%)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 기본 잔액: $610.68
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - BTC: 0.03800000 @ $107359.20 = $4079.65
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 총 가치: $4690.33
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - ===== 포트폴리오 상태 =====
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 거래 모드: 실제 거래
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - USDT 잔액: $610.68
2025-06-26 19:54:07 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-26 19:54:07 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-26 19:54:07 - binance.binance_utils - INFO - 변화율(%): 0.185
2025-06-26 19:54:07 - binance.binance_utils - INFO - 거래량: 148221.535
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 포트폴리오 총 가치: $4,687.11 (USDT: $610.68 + 현물: $4,076.43)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 초기 잔액: $10,000.00
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 총 손익: $-5,312.89 (-53.13%)
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - === 현물 자산 (Assets) ===
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   BTC: 0.03800000 개 × $107359.2000 = $4,079.65
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 현물 자산 총 가치: $4,079.65
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - === 선물 거래 상태 ===
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935246594}
2025-06-26 19:54:07 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935246594
2025-06-26 19:54:07 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935246594
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - 미체결 주문: 없음
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935246715}
2025-06-26 19:54:07 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935246715
2025-06-26 19:54:07 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935246715
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO - [SOL] 최근 주문 내역:
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750929621350
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750931066659
2025-06-26 19:54:07 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750934575636
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-26 19:54:07 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750935246846}
2025-06-26 19:54:07 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750935246846
2025-06-26 19:54:07 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750935246846
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - [DOGE] 최근 주문 내역:
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750927456355
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927959912
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927964135
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - === 내부 포지션 ===
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   ETH long: 0.02300000 @ $2486.13
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     현재가: $2456.38 | ROI: -1.20% | PnL: $-0.84 [손절조건 충족! 기준:-0.15%]
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   BNB long: 0.09000000 @ $646.61
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     현재가: $645.73 | ROI: -0.14% | PnL: $-0.11
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   DOGE long: 504.00000000 @ $0.16
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     현재가: $0.16 | ROI: -1.25% | PnL: $-1.23 [손절조건 충족! 기준:-0.15%]
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   BTC long: 0.00100000 @ $107313.10
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     현재가: $107359.20 | ROI: 0.04% | PnL: $-0.03
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 총 거래 수: 997
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 승률: 19.08% (성공: 595, 실패: 2523)
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 포트폴리오 총 손익: $-5309.67 (-53.10%)
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 거부된 거래 수: 187
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 거부된 거래 검증 상태: 검증 완료 187건, 검증 대기 0건
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 거부 이유별 통계:
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - API 오류: 바이낸스 API 오류 -2019: Margin is insufficient.: 181건
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - 일반 오류: name 'action' is not defined: 6건
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - === 통합 생각카드 시스템 상태 ===
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 활성 카드 수: 3개
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 카드 완성도: 완료 0개, 진행중 3개, 생성됨 0개
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 심볼별 카드 현황:
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   BNB: 2개 카드
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750934424_1750934425: 완성도 50.0%
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750935198_1750935198: 완성도 25.0%
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   BTC: 1개 카드
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -     - card_trade_BTC_1750934599_1750934601: 완성도 25.0%
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 최근 업데이트된 카드: 1개
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   - BNB: 완성도 25.0%
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - 시간대별 성공률:
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   short_term: 0.0% (0/1건, 평균수익: -8.35%)
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO -   전체: 0.0% (0/1건)
2025-06-26 19:54:08 - simulator.trading.portfolio - INFO - ===========================
2025-06-26 19:54:08 - hybrid_simulator - INFO - 하이브리드 거래 시스템 종료 완료
2025-06-26 19:54:08 - hybrid_simulator - INFO - 프로그램 종료
