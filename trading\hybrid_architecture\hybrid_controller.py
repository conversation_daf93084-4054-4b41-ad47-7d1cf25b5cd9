"""
하이브리드 아키텍처 컨트롤러

실행 플로우(InCA → HiAR → SELA → 실시간 실행)와
학습 루프(SELA → HiAR → InCA → 학습 강화)를 관리하고
두 플로우 간의 데이터 공유를 조정하는 컨트롤러입니다.
"""
import os
import logging
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import threading
import queue

# 데이터 스토어 및 로거 임포트
from trading.hybrid_architecture.data_store import DataStore
from trading.hybrid_architecture.data_logger import DataLogger

# 에이전트 임포트
from trading.hybrid_architecture.agents.inca_agent import InCAAgent
from trading.hybrid_architecture.agents.hiar_factory import HiARFactory, create_default_hiar
from trading.hybrid_architecture.agents.sela_agent import SELAAgent
from trading.hybrid_architecture.agents.hierarchical_consensus import HierarchicalConsensus

# 바이낸스 API 유틸
from binance.binance_utils import BinanceUtils

# 루나크러시 데이터 수집기 임포트
from trading.hybrid_architecture.lunar_data_collector import LunarDataCollector

# vLLM 요청 큐 임포트
from models.vllm_request_queue import vllm_queue

# 가상 포지션 추적기 임포트
from trading.hybrid_architecture.utils.virtual_position_tracker import VirtualPositionTracker
from trading.hybrid_architecture.utils.neutral_signal_evaluator import NeutralSignalEvaluator

logger = logging.getLogger(__name__)

class HybridController:
    """
    하이브리드 아키텍처 컨트롤러

    실행 플로우와 학습 루프를 관리하고, 데이터 공유를 조정하는 컨트롤러
    """

    def _get_rich_market_data(self, symbol: str, basic_market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        기본 시장 데이터에 캔들/뉴스 데이터를 추가하여 풍부한 데이터 생성

        Args:
            symbol: 심볼
            basic_market_data: 기본 시장 데이터

        Returns:
            Dict[str, Any]: 캔들/뉴스 데이터가 포함된 풍부한 시장 데이터
        """
        try:
            # 기본 데이터 복사
            rich_data = basic_market_data.copy()

            # 캔들 데이터 추가
            if hasattr(self, 'binance_utils') and self.binance_utils:
                try:
                    # 최근 20개 캔들 데이터 가져오기 (3분봉) - 올바른 메서드명 사용
                    candles = self.binance_utils.get_klines(symbol, "3m", 20)
                    if candles:
                        rich_data['recent_candles'] = candles
                        rich_data['candles_count'] = len(candles)

                        # 단기 변동률 계산
                        if len(candles) >= 2:
                            latest_close = float(candles[-1][4])  # 최신 종가
                            prev_close = float(candles[-2][4])    # 이전 종가
                            short_term_change = ((latest_close - prev_close) / prev_close) * 100
                            rich_data['short_term_change_pct'] = short_term_change

                        # 최근 고가/저가
                        recent_highs = [float(candle[2]) for candle in candles[-5:]]  # 최근 5개 고가
                        recent_lows = [float(candle[3]) for candle in candles[-5:]]   # 최근 5개 저가
                        rich_data['recent_high'] = max(recent_highs) if recent_highs else 0
                        rich_data['recent_low'] = min(recent_lows) if recent_lows else 0

                        logger.debug(f"🔧 [{symbol}] 캔들 데이터 추가: {len(candles)}개")
                except Exception as e:
                    logger.warning(f"🔧 [{symbol}] 캔들 데이터 추가 실패: {e}")

            # 뉴스 데이터 추가 (데이터 스토어에서 가져오기)
            try:
                if hasattr(self, 'data_store') and self.data_store:
                    # 최근 뉴스 데이터 가져오기 - 올바른 메서드명 사용
                    recent_news = self.data_store.get_latest_news(symbol, limit=10)
                    if recent_news:
                        rich_data['news'] = recent_news
                        rich_data['news_count'] = len(recent_news)
                        rich_data['has_news'] = True

                        # 뉴스 제목만 추출
                        news_titles = [news.get('title', '') for news in recent_news[:5]]  # 최근 5개
                        rich_data['recent_news_titles'] = news_titles

                        logger.debug(f"🔧 [{symbol}] 뉴스 데이터 추가: {len(recent_news)}개")
                    else:
                        rich_data['has_news'] = False
                        rich_data['news_count'] = 0
            except Exception as e:
                logger.warning(f"🔧 [{symbol}] 뉴스 데이터 추가 실패: {e}")
                rich_data['has_news'] = False
                rich_data['news_count'] = 0

            # 소셜 데이터 추가 (현재 DataStore에 해당 메서드가 없으므로 스킵)
            try:
                # TODO: DataStore에 get_recent_social_posts 메서드 구현 후 활성화
                # if hasattr(self, 'data_store') and self.data_store:
                #     recent_posts = self.data_store.get_recent_social_posts(symbol, hours=3)
                #     if recent_posts:
                #         rich_data['top_social_posts'] = recent_posts[:10]
                #         rich_data['post_count'] = len(recent_posts)
                #         logger.debug(f"🔧 [{symbol}] 소셜 데이터 추가: {len(recent_posts)}개")

                # 현재는 빈 데이터로 설정
                rich_data['top_social_posts'] = []
                rich_data['post_count'] = 0
                logger.debug(f"🔧 [{symbol}] 소셜 데이터: 메서드 미구현으로 스킵")
            except Exception as e:
                logger.warning(f"🔧 [{symbol}] 소셜 데이터 추가 실패: {e}")

            # 타임시리즈 데이터 플래그
            rich_data['has_timeseries_data'] = 'recent_candles' in rich_data
            rich_data['data_timestamp'] = int(time.time())

            logger.info(f"🔧 [{symbol}] 풍부한 시장 데이터 생성 완료: 캔들={rich_data.get('candles_count', 0)}개, 뉴스={rich_data.get('news_count', 0)}개")
            return rich_data

        except Exception as e:
            logger.error(f"🔧 [{symbol}] 풍부한 시장 데이터 생성 실패: {e}")
            # 실패 시 기본 데이터 반환
            return basic_market_data

    def __init__(self,
                 inca_agent: InCAAgent = None,
                 hiar_agent = None,  # HiAR 에이전트 (팩토리에서 생성)
                 sela_agent: SELAAgent = None,
                 data_store: DataStore = None,
                 execution_logger: DataLogger = None,
                 config: Dict[str, Any] = None,
                 binance_utils: BinanceUtils = None,
                 portfolio=None,
                 vllm_queue=None,
                 continuous_mode=False):
        """
        초기화

        Args:
            inca_agent: InCA 에이전트
            hiar_agent: HiAR 에이전트
            sela_agent: SELA 에이전트
            data_store: 데이터 스토어
            execution_logger: 실행 로그 기록기
            config: 설정 정보
            binance_utils: 바이낸스 API 유틸
            portfolio: 포트폴리오 객체
        """
        self.config = config or {}

        # logger 인스턴스 속성으로 설정
        self.logger = logger

        # 하이브리드 인터페이스 먼저 초기화 (SELA 에이전트 생성 전에)
        try:
            from trading.hybrid_architecture.hybrid_interface import HybridInterface
            self.hybrid_interface = HybridInterface(
                data_dir=self.config.get('data_dir', './data'),
                enable_persistence=True,
                enable_vector_db=True
            )
            logger.info("하이브리드 인터페이스 초기화 완료")
        except Exception as e:
            logger.error(f"하이브리드 인터페이스 초기화 실패: {e}")
            self.hybrid_interface = None

        # 에이전트 설정
        self.inca_agent = inca_agent
        self.hiar_agent = hiar_agent
        self.sela_agent = sela_agent
        self.data_store = data_store
        self.execution_logger = execution_logger
        self.binance_utils = binance_utils
        self.portfolio = portfolio

        # 🚀 계층적 합의 시스템 비활성화 (SELA 직접 사용)
        self.hierarchical_consensus = None
        logger.info("계층적 합의 시스템 비활성화 완료 - SELA 직접 사용")

        # 🔧 신호 안정화 메커니즘 초기화 (연속 모드에서는 비활성화)
        self.signal_stability = {}  # {symbol: {'last_signal': str, 'last_time': float, 'signal_count': int}}
        self.min_signal_interval = 0 if continuous_mode else 300  # 연속 모드에서는 0초 (비활성화)
        self.signal_change_threshold = 1 if continuous_mode else 3  # 연속 모드에서는 1번 (즉시 변경)
        self.signal_stability_enabled = not continuous_mode  # 연속 모드에서는 완전 비활성화
        logger.info(f"신호 안정화 메커니즘 초기화 완료 (연속모드: {continuous_mode}, 활성화: {self.signal_stability_enabled})")

        # 외부에서 전달된 SELA 에이전트의 hybrid_interface 업데이트
        if self.sela_agent is not None and self.hybrid_interface is not None:
            if hasattr(self.sela_agent, 'hybrid_interface'):
                self.sela_agent.hybrid_interface = self.hybrid_interface
                logger.info("SELA 에이전트의 hybrid_interface 업데이트 완료")

        # 기본 설정
        self.data_dir = self.config.get("data_dir", "data")
        self.log_dir = self.config.get("log_dir", "logs")
        self.db_path = os.path.join(self.data_dir, self.config.get("db_name", "hybrid_system.db"))

        # 데이터 수집 설정
        self.collection_interval = self.config.get("collection_interval", 180)  # 기본 3분
        self.symbols = self.config.get("symbols", ["BTC", "ETH"])

        # 학습 루프 설정
        self.learning_interval = self.config.get("learning_interval", 3600)  # 기본 1시간
        self.diverse_strategies_count = self.config.get("diverse_strategies_count", 5)
        self.importance_threshold = self.config.get("importance_threshold", 0.3)  # 🎯 임계값 0.5 → 0.3으로 더 완화

        # 연속 모드 설정
        self.continuous_mode = continuous_mode

        # 디렉토리 생성
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)

        # 데이터 수집기 초기화
        api_key = os.environ.get("LUNARCRUSH_API_KEY")
        if not api_key:
            error_msg = "LunarCrush API 키가 설정되지 않았습니다. 환경 변수 LUNARCRUSH_API_KEY를 설정하세요."
            logger.error(error_msg)
            raise ValueError(error_msg)

        # lunar_data_collector 이름으로 초기화 (브릿지와 일치시키기 위함)
        self.lunar_data_collector = LunarDataCollector(api_key)
        self.data_collector = self.lunar_data_collector  # 기존 코드 호환성 유지

        # 바이낸스 API 유틸 초기화
        self.binance_utils = binance_utils

        # 🔧 바이낸스 연결 설정 (기본 HybridController에도 추가)
        self.exchange = binance_utils  # binance_utils를 exchange로 사용

        # 스레드 및 큐 설정
        self.execution_queue = queue.Queue()  # 브릿지와 통합을 위한 실행 큐
        self.learning_queue = queue.Queue()
        self.execution_thread = None
        self.learning_thread = None
        self.collection_thread = None
        self.is_running = False

        # 🔒 심볼별 순차 처리를 위한 락 메커니즘
        self.symbol_locks = {}  # 심볼별 락 딕셔너리
        self.symbol_processing = {}  # 심볼별 처리 상태 추적
        self.symbol_last_completion = {}  # 심볼별 마지막 완료 시간 (참고용)
        self.global_lock = threading.RLock()  # 전역 락 (락 딕셔너리 접근용)

        # vLLM 요청 큐 설정 (완전 순차 처리로 변경)
        self.vllm_queue = vllm_queue
        if self.vllm_queue:
            self.vllm_queue.request_delay = 0.1     # 요청 간 0.1초 지연 (빠른 순차 처리)
            self.vllm_queue.max_concurrent = 1      # 최대 1개 처리 (완전 순차 처리)

        # 🧹 내부 포지션 강제 정리 (바이낸스와 동기화 문제 해결)
        if self.portfolio and hasattr(self.portfolio, 'force_clear_internal_positions'):
            try:
                logger.info("🧹 시스템 시작 시 내부 포지션 강제 정리 실행")
                self.portfolio.force_clear_internal_positions(['SOL', 'BNB'])
                logger.info("✅ 내부 포지션 강제 정리 완료")
            except Exception as e:
                logger.error(f"❌ 내부 포지션 강제 정리 실패: {e}")

        # 🔥 바이낸스와 강제 동기화 (실제 포지션 상태 확인)
        if self.portfolio and hasattr(self.portfolio, 'force_sync_with_binance') and self.exchange:
            try:
                logger.info("🔥 시스템 시작 시 바이낸스와 강제 동기화 실행")
                self.portfolio.force_sync_with_binance(self.exchange)
                logger.info("✅ 바이낸스 강제 동기화 완료")
            except Exception as e:
                logger.error(f"❌ 바이낸스 강제 동기화 실패: {e}")

        # 🔧 시스템 시작 시 오래된 카드 정리 (3일 이전)
        if self.data_store and hasattr(self.data_store, 'cleanup_old_cards'):
            try:
                deleted_count = self.data_store.cleanup_old_cards(days_old=3)
                if deleted_count > 0:
                    logger.info(f"🔧 시스템 시작 시 오래된 카드 정리: {deleted_count}개 삭제")
                else:
                    logger.info("🔧 정리할 오래된 카드가 없습니다")
            except Exception as e:
                logger.warning(f"⚠️ 카드 정리 실패: {e}")

        # 🔮 가상 포지션 추적기 초기화 (중립 신호 학습용)
        try:
            self.virtual_position_tracker = VirtualPositionTracker(neutral_threshold=0.5)
            self.neutral_signal_evaluator = NeutralSignalEvaluator()
            logger.info("🔮 가상 포지션 추적기 초기화 완료 (중립 임계값: 0.5%, 평가 주기: 3분)")
        except Exception as e:
            logger.error(f"🔮 가상 포지션 추적기 초기화 실패: {e}")
            self.virtual_position_tracker = None
            self.neutral_signal_evaluator = None

        logger.info("하이브리드 컨트롤러 초기화 완료")

    def _should_allow_signal_change(self, symbol: str, new_signal: str) -> bool:
        """신호 변경이 허용되는지 확인 (안정화 메커니즘)"""
        try:
            # 연속 모드에서는 신호 안정화 메커니즘 완전 비활성화
            if not self.signal_stability_enabled:
                logger.debug(f"🔧 [{symbol}] 신호 안정화 비활성화됨: {new_signal} 즉시 허용")
                return True

            current_time = time.time()

            # 심볼별 신호 기록 초기화
            if symbol not in self.signal_stability:
                self.signal_stability[symbol] = {
                    'last_signal': new_signal,
                    'last_time': current_time,
                    'signal_count': 1,
                    'consecutive_count': 1
                }
                logger.info(f"🔧 [{symbol}] 첫 신호 기록: {new_signal}")
                return True

            stability_info = self.signal_stability[symbol]
            last_signal = stability_info['last_signal']
            last_time = stability_info['last_time']
            time_since_last = current_time - last_time

            # 같은 신호인 경우
            if new_signal == last_signal:
                stability_info['consecutive_count'] += 1
                stability_info['last_time'] = current_time
                logger.debug(f"🔧 [{symbol}] 같은 신호 반복: {new_signal} (연속 {stability_info['consecutive_count']}회)")
                return True

            # 다른 신호인 경우 - 안정화 조건 확인

            # 조건 1: 최소 시간 간격 확인
            if time_since_last < self.min_signal_interval:
                logger.info(f"🚫 [{symbol}] 신호 변경 거부 (시간 간격): {last_signal} → {new_signal} "
                          f"(경과: {time_since_last:.0f}초 < 최소: {self.min_signal_interval}초)")
                return False

            # 조건 2: 연속 신호 횟수 확인 (선택적)
            if stability_info['consecutive_count'] < 2:
                logger.info(f"🚫 [{symbol}] 신호 변경 거부 (연속 횟수): {last_signal} → {new_signal} "
                          f"(연속: {stability_info['consecutive_count']}회 < 최소: 2회)")
                return False

            # 조건 3: neutral 신호는 더 관대하게 처리
            if new_signal.lower() == 'neutral':
                logger.info(f"✅ [{symbol}] neutral 신호 허용: {last_signal} → {new_signal}")
                stability_info['last_signal'] = new_signal
                stability_info['last_time'] = current_time
                stability_info['consecutive_count'] = 1
                return True

            # 모든 조건 통과 - 신호 변경 허용
            logger.info(f"✅ [{symbol}] 신호 변경 허용: {last_signal} → {new_signal} "
                      f"(경과: {time_since_last:.0f}초, 연속: {stability_info['consecutive_count']}회)")

            stability_info['last_signal'] = new_signal
            stability_info['last_time'] = current_time
            stability_info['consecutive_count'] = 1

            return True

        except Exception as e:
            logger.error(f"❌ [{symbol}] 신호 안정화 확인 실패: {e}")
            return True  # 오류 시 허용

    def set_vllm_client(self, vllm_client):
        """vLLM 클라이언트 설정"""
        self.vllm_client = vllm_client  # 🔥 직접 접근용 vLLM 클라이언트 설정
        if self.vllm_queue:
            self.vllm_queue.set_vllm_client(vllm_client)
        logger.info("하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (직접 접근 및 큐 모두)")

    def _get_symbol_lock(self, symbol: str) -> threading.RLock:
        """심볼별 락 가져오기 (없으면 생성)"""
        with self.global_lock:
            if symbol not in self.symbol_locks:
                self.symbol_locks[symbol] = threading.RLock()
                self.symbol_processing[symbol] = False
                self.symbol_last_completion[symbol] = 0
                logger.info(f"🔒 [{symbol}] 새로운 심볼 락 생성")
            return self.symbol_locks[symbol]

    def _is_symbol_processing(self, symbol: str) -> bool:
        """심볼 처리 중인지 확인"""
        with self.global_lock:
            return self.symbol_processing.get(symbol, False)

    def _set_symbol_processing(self, symbol: str, processing: bool):
        """심볼 처리 상태 설정"""
        with self.global_lock:
            self.symbol_processing[symbol] = processing
            if not processing:
                # 처리 완료 시 완료 시간 기록
                self.symbol_last_completion[symbol] = time.time()
                completion_time = datetime.now().strftime('%H:%M:%S')
                logger.info(f"🔄 [{symbol}] 처리 완료 ({completion_time})")
            else:
                logger.info(f"🔄 [{symbol}] 처리 시작")

    def _wait_for_symbol_completion(self, symbol: str, timeout: int = 60):
        """심볼 처리 완료까지 대기 (타임아웃 포함)"""
        start_time = time.time()
        while self._is_symbol_processing(symbol):
            if time.time() - start_time > timeout:
                logger.warning(f"⏰ [{symbol}] 처리 완료 대기 타임아웃 ({timeout}초)")
                break
            time.sleep(0.5)  # 0.5초마다 체크

    def _get_time_since_last_completion(self, symbol: str) -> float:
        """마지막 완료 이후 경과 시간 (참고용)"""
        with self.global_lock:
            last_completion = self.symbol_last_completion.get(symbol, 0)
            if last_completion == 0:
                return float('inf')  # 처음 실행
            return time.time() - last_completion

    def _log_session_stats_if_needed(self):
        """필요한 경우 세션 통계를 출력합니다 (10분마다)"""
        current_time = time.time()

        # 마지막 세션 통계 출력 시간 체크
        if not hasattr(self, '_last_session_stats_time'):
            self._last_session_stats_time = current_time
            return

        # 10분마다 출력
        if current_time - self._last_session_stats_time >= 600:  # 10분
            try:
                # vLLM 클라이언트의 세션 매니저에서 통계 출력
                if hasattr(self, 'vllm_client') and self.vllm_client:
                    if hasattr(self.vllm_client, 'session_manager') and self.vllm_client.session_manager:
                        self.vllm_client.session_manager.log_session_stats()
                    else:
                        logger.info("📊 vLLM 세션 매니저가 비활성화되어 있습니다")
                else:
                    logger.info("📊 vLLM 클라이언트가 없습니다")

                # vLLM 요청 큐 통계도 출력
                try:
                    from models.vllm_request_queue import vllm_queue
                    vllm_queue.print_stats_summary()
                except Exception as e:
                    logger.warning(f"vLLM 큐 통계 출력 중 오류: {e}")

            except Exception as e:
                logger.warning(f"세션 통계 출력 중 오류: {e}")
            finally:
                self._last_session_stats_time = current_time

    def start(self, continuous_mode=False):
        """
        모든 스레드를 시작하고 시스템 가동

        Args:
            continuous_mode: 연속 모드 활성화 여부 (실행 → 학습 → 실행 → 학습 순서로 연속 실행)
        """
        if self.is_running:
            logger.warning("시스템이 이미 실행 중입니다.")
            return

        self.is_running = True

        # vLLM 큐 워커 시작
        if self.vllm_queue:
            self.vllm_queue.start_worker()
            logger.info("vLLM 요청 큐 워커 시작됨")
        else:
            logger.warning("vLLM 큐가 없어 워커를 시작할 수 없습니다")

        # 연속 모드 설정
        self.continuous_mode = continuous_mode

        if continuous_mode:
            logger.info("연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)")

            # 데이터 수집 스레드 시작
            self.collection_thread = threading.Thread(target=self._data_collection_loop)
            self.collection_thread.daemon = True
            self.collection_thread.start()

            # 연속 실행-학습 스레드 시작
            self.execution_thread = threading.Thread(target=self._continuous_execution_learning_loop)
            self.execution_thread.daemon = True
            self.execution_thread.start()
        else:
            # 기존 방식: 독립적인 스레드로 실행
            logger.info("독립 모드로 시스템 시작 (실행 및 학습 독립적 실행)")

            # 데이터 수집 스레드 시작
            self.collection_thread = threading.Thread(target=self._data_collection_loop)
            self.collection_thread.daemon = True
            self.collection_thread.start()

            # 실행 플로우 스레드 시작
            self.execution_thread = threading.Thread(target=self._execution_flow_loop)
            self.execution_thread.daemon = True
            self.execution_thread.start()

            # 학습 루프 스레드 시작
            self.learning_thread = threading.Thread(target=self._learning_loop)
            self.learning_thread.daemon = True
            self.learning_thread.start()

        logger.info("하이브리드 시스템 가동 시작")

    def stop(self):
        """
        모든 스레드를 중지하고 시스템 종료
        """
        if not self.is_running:
            logger.warning("시스템이 이미 중지되었습니다.")
            return

        self.is_running = False

        # 모든 큐에 종료 신호 추가
        self.execution_queue.put(None)

        # 스레드 조인
        if self.collection_thread:
            self.collection_thread.join(timeout=5)

        if self.execution_thread:
            self.execution_thread.join(timeout=5)

        if self.learning_thread:
            self.learning_thread.join(timeout=5)

        # vLLM 큐 워커 중지 및 통계 출력
        if self.vllm_queue:
            self.vllm_queue.print_stats_summary()
            self.vllm_queue.stop_worker()
            logger.info("vLLM 요청 큐 워커 중지됨")
        else:
            logger.warning("vLLM 큐가 없어 워커를 중지할 수 없습니다")

        logger.info("하이브리드 시스템 종료 완료")

    def _execute_inca_sequential(self, symbol: str, market_data: dict, news_data: dict) -> dict:
        """
        InCA 에이전트 진짜 순차 실행 (vLLM 큐 완전 우회)

        Args:
            symbol: 심볼
            market_data: 시장 데이터
            news_data: 뉴스 데이터

        Returns:
            InCA 평가 결과
        """
        try:
            logger.info(f"🔄 [{symbol}] InCA 진짜 순차 실행 시작 (큐 우회)")

            # 🔥 vLLM 클라이언트 직접 사용 (큐 완전 우회)
            if hasattr(self, 'vllm_client') and self.vllm_client:
                # InCA 프롬프트 직접 생성 (올바른 메서드명 사용)
                news_list = news_data.get('news', []) if news_data else []

                # 🔥 변동률 추출 개선 (여러 필드에서 시도)
                price_change = market_data.get('percent_change_24h')
                if price_change is None or price_change == 0:
                    price_change = market_data.get('priceChangePercent')
                if price_change is None or price_change == 0:
                    price_change = market_data.get('change_24h')
                if price_change is None or price_change == 0:
                    price_change = market_data.get('price_change_percent')
                if price_change is None:
                    price_change = 0

                # 🔍 디버깅: 순차 실행 변동률 추출 로깅
                logger.info(f"🔧 [{symbol}] 순차 실행 변동률 추출: percent_change_24h={market_data.get('percent_change_24h')}, priceChangePercent={market_data.get('priceChangePercent')}, change_24h={market_data.get('change_24h')}, 최종값={price_change}")

                volume = market_data.get('volume_24h', 0)
                current_price = market_data.get('price', 0)
                candles = market_data.get('candles', [])

                # 시장 데이터 구성
                market_data_for_prompt = {
                    "symbol": symbol,
                    "price": current_price,
                    "percent_change_24h": price_change,
                    "volume_24h": volume,
                    "recent_candles": candles,
                    "news": news_list
                }

                prompt = self.inca_agent._create_importance_prompt(market_data_for_prompt)

                logger.info(f"🔍 [{symbol}] InCA 프롬프트 크기: {len(prompt)}자")

                # vLLM 클라이언트 직접 호출 (큐 우회) - 더 안전한 방식
                try:
                    response = self.vllm_client.generate(
                        prompt=prompt,
                        max_tokens=500,  # 200 → 500으로 증가 (JSON 완성도 향상)
                        temperature=0.0,
                        top_p=0.1,
                        stop=['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The'],
                        agent_type="inca_execution",
                        symbol=symbol
                    )
                    logger.info(f"🔍 [{symbol}] InCA vLLM 응답 수신: {type(response)}")
                except Exception as vllm_error:
                    logger.error(f"❌ [{symbol}] InCA vLLM 호출 실패: {vllm_error}")
                    raise Exception(f"vLLM 호출 실패: {vllm_error}")

                # vLLM 응답 구조 확인 및 처리
                logger.info(f"🔍 [{symbol}] InCA vLLM 응답 구조: {list(response.keys()) if isinstance(response, dict) else 'Not dict'}")

                # 다양한 응답 구조 처리
                generated_text = None
                if response:
                    if isinstance(response, dict):
                        # 가능한 키들 확인
                        generated_text = (response.get('generated_text') or
                                        response.get('text') or
                                        response.get('content') or
                                        response.get('output'))
                    elif isinstance(response, str):
                        generated_text = response

                if generated_text:
                    logger.info(f"🔍 [{symbol}] InCA 생성된 텍스트 길이: {len(generated_text)}자")

                    # JSON 파싱 (InCA 에이전트와 동일한 방식)
                    from trading.hybrid_architecture.utils.json_extractor import extract_json_from_text

                    try:
                        result = extract_json_from_text(generated_text)
                        if result:
                            logger.info(f"✅ [{symbol}] InCA 직접 실행 성공: 중요도 {result.get('importance', 0)}")

                            # 표준 형식으로 변환
                            return {
                                "importance_score": result.get('importance', 5) / 10.0,  # 1-10 → 0.1-1.0
                                "situation_type": result.get('situation_type', 'neutral'),
                                "action_recommendation": result.get('action_recommendation', 'hold'),
                                "trading_direction": result.get('trading_direction', 'neutral'),
                                "reasoning": result.get('reasoning', ''),
                                "confidence": result.get('confidence', 0.5),
                                "news_impact": result.get('news_impact', 0.5),
                                "market_impact": result.get('market_impact', 0.5),
                                "direct_execution": True,
                                "llm_analysis": True
                            }
                        else:
                            raise Exception("JSON 추출 실패")
                    except Exception as e:
                        logger.error(f"❌ [{symbol}] InCA JSON 파싱 실패: {e}")
                        logger.error(f"❌ [{symbol}] 원본 텍스트: {generated_text[:200]}...")
                        raise Exception(f"JSON 파싱 실패: {e}")
                else:
                    logger.error(f"❌ [{symbol}] vLLM 응답에서 텍스트를 찾을 수 없음: {response}")
                    raise Exception("vLLM 응답에서 생성된 텍스트 없음")
            else:
                raise Exception("vLLM 클라이언트 없음")

        except Exception as e:
            logger.error(f"❌ [{symbol}] InCA 직접 실행 실패: {e}")
            # 폴백 로직 사용하지 않음 - LLM 기반 분석만 사용
            raise Exception(f"InCA LLM 분석 실패: {e}")

    def _execute_hiar_sequential(self, symbol: str, market_data: dict, news_data: dict, topics_data: dict, importance_data: dict = None) -> dict:
        """
        HiAR 에이전트 진짜 순차 실행 (vLLM 큐 완전 우회)

        Args:
            symbol: 심볼
            market_data: 시장 데이터
            news_data: 뉴스 데이터
            topics_data: 토픽 데이터
            importance_data: InCA 중요도 데이터

        Returns:
            HiAR 사고 카드
        """
        try:
            logger.info(f"🔄 [{symbol}] HiAR 진짜 순차 실행 시작 (큐 우회)")

            # 🔥 vLLM 클라이언트 직접 사용 (큐 완전 우회)
            if hasattr(self, 'vllm_client') and self.vllm_client:
                # HiAR 프롬프트 직접 생성
                if hasattr(self.hiar_agent, '_create_reasoning_prompt'):
                    prompt = self.hiar_agent._create_reasoning_prompt(
                        symbol=symbol,
                        market_data=market_data,
                        news_data=news_data,
                        social_data=topics_data,
                        importance_data=importance_data or {}
                    )
                else:
                    # 🔧 시장 데이터에서 안전하게 추출
                    current_price = (
                        market_data.get('price') or
                        market_data.get('current_price') or
                        market_data.get('lastPrice') or
                        0
                    )

                    price_change = (
                        market_data.get('percent_change_24h') or
                        market_data.get('priceChangePercent') or
                        market_data.get('change_24h') or
                        0
                    )

                    volume = (
                        market_data.get('volume_24h') or
                        market_data.get('volume') or
                        market_data.get('quoteVolume') or
                        0
                    )

                    # 🔧 디버깅: HiAR 프롬프트 데이터 추출 로깅
                    logger.info(f"🔧 [{symbol}] HiAR 프롬프트 데이터 추출: 가격=${current_price}, 변동률={price_change}%, 거래량={volume}")

                    # 간단한 프롬프트 생성
                    prompt = f"""분석 대상: {symbol}

=== 시장 데이터 ===
현재가: ${current_price:,.2f}
24시간 변동률: {price_change:.2f}%
거래량: {volume:,.0f}

=== 중요도 분석 ===
{importance_data.get('reasoning', '없음') if importance_data else '없음'}

다음 JSON 형식으로 사고 흐름을 정리하세요:
{{
  "card_id": "reasoning_{symbol}_{int(time.time())}",
  "symbol": "{symbol}",
  "reasoning": "시장 상황 분석",
  "market_analysis": "가격 및 변동성 분석",
  "decision_factors": ["요인1", "요인2", "요인3"]
}}"""

                logger.info(f"🔍 [{symbol}] HiAR 프롬프트 크기: {len(prompt)}자")

                # vLLM 클라이언트 직접 호출 (큐 우회) - 더 안전한 방식
                try:
                    response = self.vllm_client.generate(
                        prompt=prompt,
                        max_tokens=600,  # 200 → 600으로 증가 (HiAR 분석 완성도 향상)
                        temperature=0.0,
                        top_p=0.1,
                        stop=['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The'],
                        agent_type="hiar_mcts",
                        symbol=symbol
                    )
                    logger.info(f"🔍 [{symbol}] HiAR vLLM 응답 수신: {type(response)}")
                except Exception as vllm_error:
                    logger.error(f"❌ [{symbol}] HiAR vLLM 호출 실패: {vllm_error}")
                    raise Exception(f"vLLM 호출 실패: {vllm_error}")

                # vLLM 응답 구조 확인 및 처리
                logger.info(f"🔍 [{symbol}] HiAR vLLM 응답 구조: {list(response.keys()) if isinstance(response, dict) else 'Not dict'}")

                # 다양한 응답 구조 처리
                generated_text = None
                if response:
                    if isinstance(response, dict):
                        generated_text = (response.get('generated_text') or
                                        response.get('text') or
                                        response.get('content') or
                                        response.get('output'))
                    elif isinstance(response, str):
                        generated_text = response

                if generated_text:
                    logger.info(f"🔍 [{symbol}] HiAR 생성된 텍스트 길이: {len(generated_text)}자")

                    # JSON 파싱 (InCA와 동일한 방식)
                    from trading.hybrid_architecture.utils.json_extractor import extract_json_from_text

                    try:
                        result = extract_json_from_text(generated_text)
                        if result:
                            logger.info(f"✅ [{symbol}] HiAR 직접 실행 성공")
                            result["direct_execution"] = True
                            return result
                        else:
                            raise Exception("JSON 추출 실패")
                    except Exception as e:
                        logger.error(f"❌ [{symbol}] HiAR JSON 파싱 실패: {e}")
                        logger.error(f"❌ [{symbol}] 원본 텍스트: {generated_text[:200]}...")
                        raise Exception(f"JSON 파싱 실패: {e}")
                else:
                    logger.error(f"❌ [{symbol}] vLLM 응답에서 텍스트를 찾을 수 없음: {response}")
                    raise Exception("vLLM 응답에서 생성된 텍스트 없음")
            else:
                raise Exception("vLLM 클라이언트 없음")

        except Exception as e:
            logger.error(f"❌ [{symbol}] HiAR 직접 실행 실패: {e}")
            # 폴백 로직 사용하지 않음 - LLM 기반 분석만 사용
            raise Exception(f"HiAR LLM 분석 실패: {e}")

    def _execute_sela_sequential(self, symbol: str, market_data: dict, reasoning_card: dict) -> dict:
        """
        SELA 에이전트 진짜 순차 실행 (vLLM 큐 완전 우회)

        Args:
            symbol: 심볼
            market_data: 시장 데이터
            reasoning_card: HiAR 사고 카드

        Returns:
            SELA 전략 결과
        """
        try:
            logger.info(f"🔄 [{symbol}] SELA 진짜 순차 실행 시작 (정상 SELA 에이전트 사용)")

            # 🔥 정상 SELA 에이전트 우선 사용
            if hasattr(self, 'sela_agent') and self.sela_agent:
                logger.info(f"🔍 [{symbol}] 정상 SELA 에이전트 호출")

                # SELA 에이전트의 generate_strategy 메서드 호출
                sela_result = self.sela_agent.generate_strategy(
                    symbol=symbol,
                    market_data=market_data,
                    reasoning_card=reasoning_card,
                    inca_result=None  # InCA 결과는 별도로 전달되지 않음
                )

                if sela_result:
                    logger.info(f"✅ [{symbol}] 정상 SELA 에이전트 전략 생성 성공: {sela_result.get('type', 'unknown')}")

                    # 기존 구조와 호환되도록 변환
                    if isinstance(sela_result, dict) and 'type' in sela_result:
                        return {
                            "strategies": [sela_result],
                            "direct_execution": False  # 정상 SELA 에이전트 사용
                        }
                    else:
                        return sela_result
                else:
                    logger.warning(f"⚠️ [{symbol}] SELA 에이전트에서 결과 없음, vLLM 직접 호출로 폴백")

            # 🔄 폴백: vLLM 클라이언트 직접 사용 (큐 완전 우회)
            if hasattr(self, 'vllm_client') and self.vllm_client:
                # SELA 프롬프트 직접 생성
                if hasattr(self.sela_agent, '_create_strategy_prompt'):
                    prompt = self.sela_agent._create_strategy_prompt(
                        symbol=symbol,
                        market_data=market_data,
                        reasoning_card=reasoning_card
                    )
                else:
                    # 🔧 시장 데이터에서 현재가 안전하게 추출
                    current_price = (
                        market_data.get('price') or
                        market_data.get('current_price') or
                        market_data.get('lastPrice') or
                        0
                    )

                    # 🔧 24시간 변동률 안전하게 추출
                    price_change = (
                        market_data.get('percent_change_24h') or
                        market_data.get('priceChangePercent') or
                        market_data.get('change_24h') or
                        0
                    )

                    # 🔧 거래량 안전하게 추출
                    volume = (
                        market_data.get('volume_24h') or
                        market_data.get('volume') or
                        market_data.get('quoteVolume') or
                        0
                    )

                    reasoning = reasoning_card.get('reasoning', '분석 없음')

                    # 🔧 디버깅: 추출된 값들 로깅
                    logger.info(f"🔧 [{symbol}] SELA 프롬프트 데이터 추출: 가격=${current_price}, 변동률={price_change}%, 거래량={volume}")

                    prompt = f"""분석 대상: {symbol}

=== 시장 데이터 ===
현재가: ${current_price:,.2f}
24시간 변동률: {price_change:.2f}%
거래량: {volume:,.0f}

=== 분석 결과 ===
{reasoning}

다음 JSON 형식으로 거래 전략을 생성하세요:
{{
  "type": "buy/sell/none",
  "direction": "long/short/neutral",
  "entry_price": {current_price},
  "stop_loss": {current_price * 0.98},
  "take_profit": {current_price * 1.04},
  "reasoning": "전략 근거",
  "confidence": 0.75,
  "key_points": ["포인트1", "포인트2", "포인트3"]
}}"""

                logger.info(f"🔍 [{symbol}] SELA 프롬프트 크기: {len(prompt)}자")

                # vLLM 클라이언트 직접 호출 (큐 우회) - 더 안전한 방식
                try:
                    response = self.vllm_client.generate(
                        prompt=prompt,
                        max_tokens=800,  # 200 → 800으로 증가 (SELA 전략 완성도 향상)
                        temperature=0.0,
                        top_p=0.1,
                        stop=['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The'],
                        agent_type="sela_diverse",
                        symbol=symbol
                    )
                    logger.info(f"🔍 [{symbol}] SELA vLLM 응답 수신: {type(response)}")
                except Exception as vllm_error:
                    logger.error(f"❌ [{symbol}] SELA vLLM 호출 실패: {vllm_error}")
                    raise Exception(f"vLLM 호출 실패: {vllm_error}")

                # vLLM 응답 구조 확인 및 처리
                logger.info(f"🔍 [{symbol}] SELA vLLM 응답 구조: {list(response.keys()) if isinstance(response, dict) else 'Not dict'}")

                # 다양한 응답 구조 처리
                generated_text = None
                if response:
                    if isinstance(response, dict):
                        generated_text = (response.get('generated_text') or
                                        response.get('text') or
                                        response.get('content') or
                                        response.get('output'))
                    elif isinstance(response, str):
                        generated_text = response

                if generated_text:
                    logger.info(f"🔍 [{symbol}] SELA 생성된 텍스트 길이: {len(generated_text)}자")

                    # JSON 파싱 (InCA와 동일한 방식)
                    from trading.hybrid_architecture.utils.json_extractor import extract_json_from_text

                    try:
                        strategy = extract_json_from_text(generated_text)
                        if strategy:
                            logger.info(f"✅ [{symbol}] SELA 직접 실행 성공: {strategy.get('type', 'unknown')}")

                            # 표준 형식으로 변환
                            strategy["strategy_id"] = f"direct_{symbol}_{int(time.time())}"
                            strategy["symbol"] = symbol
                            strategy["direct_execution"] = True

                            return {
                                "strategies": [strategy],
                                "direct_execution": True
                            }
                        else:
                            raise Exception("JSON 추출 실패")
                    except Exception as e:
                        logger.error(f"❌ [{symbol}] SELA JSON 파싱 실패: {e}")
                        logger.error(f"❌ [{symbol}] 원본 텍스트: {generated_text[:200]}...")
                        raise Exception(f"JSON 파싱 실패: {e}")
                else:
                    logger.error(f"❌ [{symbol}] vLLM 응답에서 텍스트를 찾을 수 없음: {response}")
                    raise Exception("vLLM 응답에서 생성된 텍스트 없음")
            else:
                raise Exception("vLLM 클라이언트 없음")

        except Exception as e:
            logger.error(f"❌ [{symbol}] SELA 직접 실행 실패: {e}")
            # 폴백 로직 사용하지 않음 - LLM 기반 분석만 사용
            raise Exception(f"SELA LLM 분석 실패: {e}")



    def _continuous_execution_learning_loop(self):
        """
        연속 실행-학습 루프

        실행 → 학습 → 실행 → 학습 순서로 연속적으로 실행합니다.
        각 심볼에 대해 실행 후 즉시 학습을 수행합니다.
        """
        import traceback  # 오류 추적용

        logger.info("연속 실행-학습 루프 시작")

        while self.is_running:
            try:
                # 각 심볼에 대해 실행 및 학습 수행
                for symbol in self.symbols:
                    # 🔒 심볼별 락 획득 및 처리 가능 여부 확인
                    symbol_lock = self._get_symbol_lock(symbol)

                    # 논블로킹 락 시도
                    if not symbol_lock.acquire(blocking=False):
                        logger.warning(f"🔒 [{symbol}] 다른 스레드에서 처리 중, 건너뛰기")
                        continue

                    try:
                        # 처리 중 상태 확인
                        if self._is_symbol_processing(symbol):
                            logger.warning(f"🔄 [{symbol}] 이미 처리 중, 건너뛰기")
                            continue

                        # 처리 시작 표시
                        self._set_symbol_processing(symbol, True)

                        # 마지막 완료 이후 경과 시간 로깅 (참고용)
                        time_since_last = self._get_time_since_last_completion(symbol)
                        if time_since_last != float('inf'):
                            logger.info(f"🕐 [{symbol}] 마지막 완료 후 {time_since_last:.1f}초 경과")

                        logger.info(f"🔒 [{symbol}] 순차 처리 시작 (락 획득)")

                        # 1. 데이터 수집 (실행 큐에서 가져오기)
                        logger.info(f"{symbol} 데이터 수집 대기 중...")

                        # 실행 큐에서 데이터 가져오기
                        data_package = None
                        try:
                            # 최대 60초 대기 (타임아웃 설정 - vLLM 안정화를 위해 증가)
                            data_package = self.execution_queue.get(timeout=60)

                            # 종료 신호 확인
                            if data_package is None:
                                logger.info("종료 신호 수신. 연속 루프 종료.")
                                return

                            # 심볼 확인 (현재 처리 중인 심볼과 일치하는지)
                            if data_package.get("symbol") != symbol:
                                logger.warning(f"큐에서 가져온 데이터의 심볼({data_package.get('symbol')})이 현재 처리 중인 심볼({symbol})과 일치하지 않습니다.")
                                # 다시 큐에 넣고 다음 심볼로 넘어감
                                self.execution_queue.put(data_package)
                                continue

                        except queue.Empty:
                            # 타임아웃 발생 시 직접 데이터 수집
                            logger.warning(f"⚠️ {symbol} 데이터 큐 타임아웃 (60초). 직접 데이터 수집 시작.")
                            logger.warning(f"⚠️ 큐 크기: {self.execution_queue.qsize()}, 데이터 수집 스레드 상태 확인 필요")

                            # 시장 데이터 수집
                            market_data = self.binance_utils.get_market_data(symbol)
                            if "error" in market_data:
                                raise Exception(f"시장 데이터 수집 실패: {market_data['error']}")

                            # 뉴스 데이터 수집
                            news_data = self.data_collector.get_news(symbol, limit=10)
                            if "error" in news_data:
                                raise Exception(f"뉴스 데이터 수집 실패: {news_data['error']}")

                            # 토픽 데이터 수집
                            topics_data = self.data_collector.get_topics(symbol, limit=5)
                            if "error" in topics_data:
                                raise Exception(f"토픽 데이터 수집 실패: {topics_data['error']}")

                            # 소셜 포스트 수집
                            posts_data = self.data_collector.get_posts(symbol, limit=10)
                            if "error" in posts_data:
                                raise Exception(f"소셜 포스트 수집 실패: {posts_data['error']}")

                            # 데이터 패키지 생성
                            data_package = {
                                "symbol": symbol,
                                "timestamp": int(time.time()),
                                "market_data": market_data,
                                "news_data": news_data,
                                "topics_data": topics_data,
                                "posts_data": posts_data
                            }

                            # 데이터 로깅
                            self.execution_logger.log_market_data(symbol, market_data)
                            self.execution_logger.log_news(symbol, news_data)

                        # 2. 실행 플로우 (InCA → HiAR → SELA → 실행)
                        logger.info(f"{symbol} 실행 플로우 시작")

                        # 데이터 추출
                        market_data = data_package["market_data"]
                        news_data = data_package["news_data"]
                        topics_data = data_package.get("topics_data", {})
                        posts_data = data_package.get("posts_data", {})

                        # 🔧 2.0 포지션 관리 (새로 추가) - v2.0 인라인
                        logger.info(f"{symbol} 포지션 관리 시작")
                        # 포지션 관리 인라인 구현 (메서드 인식 문제 해결)
                        try:
                            logger.info(f"🎯 [{symbol}] 포지션 관리 v2 인라인 시작")

                            # 포트폴리오가 없으면 건너뛰기
                            if not hasattr(self, 'portfolio') or not self.portfolio:
                                logger.warning(f"🔧 [{symbol}] 포트폴리오 없음, 포지션 관리 건너뛰기")
                                logger.warning(f"🔧 [{symbol}] hasattr(self, 'portfolio'): {hasattr(self, 'portfolio')}")
                                logger.warning(f"🔧 [{symbol}] self.portfolio: {getattr(self, 'portfolio', None)}")
                            else:
                                logger.info(f"🔧 [{symbol}] 포트폴리오 연결됨, 포지션 확인 시작")

                                # 현재 포지션 확인 (강화된 로직)
                                existing_positions = []

                                # 🔧 1. get_positions 메서드 시도
                                if hasattr(self.portfolio, 'get_positions'):
                                    try:
                                        existing_positions = self.portfolio.get_positions(symbol)
                                        logger.info(f"🔧 [{symbol}] get_positions 메서드로 포지션 확인: {len(existing_positions)}개")
                                    except Exception as e:
                                        logger.warning(f"🔧 [{symbol}] get_positions 메서드 오류: {e}")

                                # 🔧 2. open_positions 속성 시도 (백업)
                                if not existing_positions and hasattr(self.portfolio, 'open_positions'):
                                    try:
                                        all_positions = getattr(self.portfolio, 'open_positions', [])
                                        existing_positions = [pos for pos in all_positions
                                                            if pos.get('symbol') == symbol]
                                        logger.info(f"🔧 [{symbol}] open_positions 속성으로 포지션 확인: {len(existing_positions)}개")
                                        logger.info(f"🔧 [{symbol}] 전체 포지션 수: {len(all_positions)}개")
                                    except Exception as e:
                                        logger.warning(f"🔧 [{symbol}] open_positions 속성 오류: {e}")

                                # 🔧 3. 포트폴리오 강제 동기화 시도 (최후 수단)
                                if not existing_positions:
                                    try:
                                        if hasattr(self.portfolio, 'sync_positions'):
                                            logger.info(f"🔧 [{symbol}] 포트폴리오 강제 동기화 시도")
                                            self.portfolio.sync_positions()
                                            # 동기화 후 다시 확인
                                            if hasattr(self.portfolio, 'open_positions'):
                                                all_positions = getattr(self.portfolio, 'open_positions', [])
                                                existing_positions = [pos for pos in all_positions
                                                                    if pos.get('symbol') == symbol]
                                                logger.info(f"🔧 [{symbol}] 동기화 후 포지션 확인: {len(existing_positions)}개")
                                    except Exception as e:
                                        logger.warning(f"🔧 [{symbol}] 포트폴리오 동기화 오류: {e}")

                                if not existing_positions:
                                    logger.warning(f"🔧 [{symbol}] 포트폴리오에서 포지션을 찾을 수 없음")

                                if not existing_positions:
                                    logger.info(f"🔧 [{symbol}] 관리할 포지션 없음 - 새 포지션 생성 검토")

                                    # 🔥 포지션이 없을 때 새 포지션 생성 검토
                                    self._check_new_position_creation(symbol, market_data, news_data)
                                else:
                                    logger.info(f"🎯 [{symbol}] {len(existing_positions)}개 포지션 관리 시작")

                                    for position in existing_positions:
                                        try:
                                            # 🔥 InCA 포지션 평가 재활성화 - 포지션 변화를 위해 필요
                                            logger.info(f"🔥 [{symbol}] InCA 포지션 평가 재활성화 - 정상 포지션 관리 실행")
                                            logger.info(f"🔥 [{symbol}] InCA와 SELA를 통한 포지션 관리 재개")

                                            # LLM 포지션 관리 활성화
                                            skip_llm_position_management = False

                                            # 🔥 1. InCA 포지션 평가 실행
                                            logger.info(f"🔥 [{symbol}] InCA 포지션 평가 실행 시작")
                                            try:
                                                # InCA 포지션 평가 호출
                                                if hasattr(self.inca_agent, 'evaluate_position_importance'):
                                                    position_importance = self.inca_agent.evaluate_position_importance(
                                                        symbol, position, market_data
                                                    )
                                                else:
                                                    # Fallback 포지션 평가 사용
                                                    position_importance = self._fallback_position_evaluation_v2(position, market_data)

                                                logger.info(f"🔥 [{symbol}] InCA 포지션 평가 완료: {position_importance}")
                                            except Exception as e:
                                                logger.error(f"❌ [{symbol}] InCA 포지션 평가 실패: {e}")
                                                # Fallback 사용
                                                position_importance = self._fallback_position_evaluation_v2(position, market_data)

                                            # 🔥 2. LLM 기반 포지션 종료 판단 + 5분 수익 실현 로직
                                            if not locals().get('skip_llm_position_management', False):
                                                should_close = position_importance.get("should_close", False)
                                                situation_type = position_importance.get("situation_type", "neutral")
                                                hold_importance = position_importance.get("hold_importance", 5)
                                                confidence = position_importance.get("confidence", 0.5)
                                                reasoning = position_importance.get("reasoning", "LLM 판단")
                                            else:
                                                # InCA 포지션 평가 제거된 경우 기본값 설정
                                                should_close = False
                                                situation_type = "neutral"
                                                hold_importance = 5
                                                confidence = 0.5
                                                reasoning = "InCA 포지션 평가 제거됨"
                                                # position_importance 딕셔너리 생성 (5분 수익 실현 로직에서 사용)
                                                position_importance = {
                                                    "should_close": should_close,
                                                    "situation_type": situation_type,
                                                    "hold_importance": hold_importance,
                                                    "confidence": confidence,
                                                    "reasoning": reasoning
                                                }

                                            # 🎯 5분마다 0.2% 이상 수익 시 포지션 클로즈 체크 (바이낸스 실제 PnL 사용)
                                            # 올바른 보유시간 계산
                                            hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60
                                            pnl_pct = position_importance.get("pnl_pct", 0.0)
                                            force_close_profit = False

                                            # 🔧 바이낸스 실제 PnL 가져오기 (포트폴리오에서 직접)
                                            try:
                                                real_pnl_pct = None
                                                binance_raw_data = None

                                                if hasattr(self, 'portfolio') and self.portfolio:
                                                    # open_positions에서 직접 찾기
                                                    open_positions = getattr(self.portfolio, 'open_positions', [])
                                                    for pos in open_positions:
                                                        if isinstance(pos, dict) and pos.get('symbol') == symbol:
                                                            real_pnl = pos.get('current_pnl_pct') or pos.get('current_pnl')
                                                            if real_pnl is not None:
                                                                real_pnl_pct = float(real_pnl)
                                                                # 바이낸스 원시 데이터도 저장
                                                                binance_raw_data = {
                                                                    'percentage': pos.get('percentage'),
                                                                    'unrealized_pnl': pos.get('unrealized_pnl'),
                                                                    'current_pnl_pct': pos.get('current_pnl_pct'),
                                                                    'entry_price': pos.get('entry_price'),
                                                                    'current_price': pos.get('current_price', pos.get('mark_price'))
                                                                }
                                                                break

                                                if real_pnl_pct is not None:
                                                    logger.info(f"🔧 [{symbol}] 바이낸스 실제 PnL 사용: {real_pnl_pct:.3f}% (기존 계산: {pnl_pct:.3f}%)")
                                                    logger.info(f"🔍 [{symbol}] 바이낸스 원시 데이터: {binance_raw_data}")
                                                    pnl_pct = real_pnl_pct
                                                else:
                                                    logger.warning(f"⚠️ [{symbol}] 바이낸스 실제 PnL 찾을 수 없음, 기존 계산 사용: {pnl_pct:.3f}%")

                                                    # 바이낸스 API에서 직접 조회 시도
                                                    if hasattr(self, 'binance_utils') and self.binance_utils:
                                                        try:
                                                            positions = self.binance_utils.get_positions()
                                                            for pos in positions:
                                                                if pos.get('symbol') == f"{symbol}USDT":
                                                                    position_amt = float(pos.get('positionAmt', 0))
                                                                    if abs(position_amt) > 0.000001:
                                                                        percentage = pos.get('percentage')
                                                                        if percentage is not None and percentage != '':
                                                                            direct_pnl = float(percentage)
                                                                            logger.warning(f"🔧 [{symbol}] 바이낸스 API 직접 조회 PnL: {direct_pnl:.3f}%")
                                                                            pnl_pct = direct_pnl
                                                                        break
                                                        except Exception as direct_e:
                                                            logger.error(f"❌ [{symbol}] 바이낸스 API 직접 조회 실패: {direct_e}")
                                            except Exception as e:
                                                logger.error(f"❌ [{symbol}] 바이낸스 실제 PnL 조회 오류: {e}, 기존 계산 사용: {pnl_pct:.3f}%")

                                            # 🔧 환경변수에서 익절 임계값 가져오기 (먼저 정의)
                                            take_profit_threshold = float(os.getenv('TAKE_PROFIT_THRESHOLD', '0.2'))

                                            # 🔍 디버깅: 5분 수익 실현 체크 로깅 (보유시간 재계산 보장)
                                            # hold_time_minutes가 정의되지 않은 경우를 대비해 재계산
                                            if 'hold_time_minutes' not in locals():
                                                hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60

                                            logger.info(f"🔍 [{symbol}] 5분 수익 실현 체크:")
                                            logger.info(f"🔍 - 보유시간: {hold_time_minutes:.2f}분")
                                            logger.info(f"🔍 - 현재 PnL: {pnl_pct:.3f}%")
                                            logger.info(f"🔍 - 5분 조건: {hold_time_minutes >= 5.0}")
                                            logger.info(f"🔍 - 수익 조건: {pnl_pct >= take_profit_threshold} (기준: {take_profit_threshold}%)")

                                            # 수익 조건만으로도 익절 가능하도록 수정 (5분 조건 완화)
                                            if pnl_pct >= take_profit_threshold:
                                                should_close = True
                                                force_close_profit = True
                                                reasoning = f"💰 익절 실현: 보유시간 {hold_time_minutes:.1f}분, PnL {pnl_pct:.3f}% (기준: {take_profit_threshold}%)"
                                                logger.warning(f"💰 [{symbol}] 익절 조건 충족: 보유시간 {hold_time_minutes:.1f}분, PnL {pnl_pct:.3f}% >= {take_profit_threshold}%")

                                            # LLM 판단 결과 로깅 (InCA 포지션 평가 제거된 경우 건너뛰기)
                                            if not locals().get('skip_llm_position_management', False):
                                                close_reason = f"LLM 판단: {reasoning}"

                                                logger.info(f"🔍 [{symbol}] LLM 클로즈 판단: should_close={should_close}, situation={situation_type}, importance={hold_importance}, confidence={confidence:.2f}")
                                                logger.info(f"🔍 [{symbol}] LLM 판단 근거: {reasoning}")

                                                # LLM 기반 포지션 관리: 하드코딩된 조건 제거
                                                # LLM이 이미 시간, PnL, 시장 상황을 모두 고려하여 결정했으므로
                                                # 추가적인 하드코딩된 조건은 LLM 결정을 방해함

                                                # 참고용 정보만 로깅 (LLM 결정에 영향 주지 않음)
                                                hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60
                                                current_pnl = position.get("current_pnl_pct", 0)
                                                logger.info(f"📊 [{symbol}] 포지션 정보: 보유시간 {hold_time_minutes:.1f}분, PnL {current_pnl:.2f}%")

                                                # LLM 결정만 사용 (하드코딩된 오버라이드 제거)

                                                logger.info(f"🔍 [{symbol}] 포지션 종료 판단: should_close={should_close}, 이유={close_reason}")

                                                # 3. SELA: 포지션 관리 전략 생성 (종료/유지 모든 경우)
                                                logger.info(f"🎯 [{symbol}] SELA 포지션 관리 전략 생성 (should_close={should_close})")

                                                # 간단한 추론 카드 생성 (포지션 관리용)
                                                recommendation = "포지션 종료 검토" if should_close else "포지션 유지 검토"
                                                reasoning_card = {
                                                    "title": f"{symbol} 포지션 관리 분석",
                                                    "position_analysis": position_importance.get("reasoning", ""),
                                                    "recommendation": recommendation
                                                }

                                                # SELA 에이전트에 포지션 관리 메서드가 있는지 확인
                                                if hasattr(self.sela_agent, 'generate_position_management_strategy'):
                                                    position_strategy = self.sela_agent.generate_position_management_strategy(
                                                        symbol, position, position_importance, reasoning_card
                                                    )
                                                else:
                                                    logger.warning(f"[{symbol}] SELA 에이전트에 generate_position_management_strategy 메서드 없음")
                                                    if should_close:
                                                        position_strategy = {"action": "close", "reason": "fallback_close"}
                                                    else:
                                                        position_strategy = {"action": "hold", "reason": "fallback_hold"}
                                            else:
                                                # InCA 포지션 평가 제거된 경우 기본 hold 전략
                                                position_strategy = {"action": "hold", "reason": "inca_disabled_hold"}
                                                close_reason = "InCA 포지션 평가 제거됨"
                                                logger.info(f"🔒 [{symbol}] InCA 포지션 평가 제거됨 - 기본 hold 전략 적용")

                                                # 5분 수익 실현을 위한 기본 정보 설정
                                                hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60
                                                current_pnl = position.get("current_pnl_pct", 0)
                                                logger.info(f"📊 [{symbol}] 포지션 정보: 보유시간 {hold_time_minutes:.1f}분, PnL {current_pnl:.2f}%")

                                            # ✅ 5분 수익 실현 조건 확실히 적용 (환경변수 기준)
                                            if force_close_profit and pnl_pct < take_profit_threshold:
                                                logger.info(f"🛡️ [{symbol}] 수익률 부족으로 5분 수익 실현 취소: {pnl_pct:.3f}% < {take_profit_threshold}%")
                                                force_close_profit = False

                                            # 💰 5분 수익 실현은 SELA hold 추천보다 우선 (수익 확정 우선)
                                            if force_close_profit:
                                                logger.info(f"💰 [{symbol}] 5분 수익 실현 우선 실행 (SELA hold 무시): {pnl_pct:.3f}% >= {take_profit_threshold}%")

                                            # 4. 전략에 따른 포지션 관리 (5분 수익 실현 우선, SELA 판단 차순위)
                                            # 🔧 SELA close 전략 무시 (반대 신호가 아닌 경우)
                                            sela_action = position_strategy.get("action", "hold")
                                            sela_reason = position_strategy.get("reason", "unknown")

                                            # 🔧 SELA가 close를 추천해도 반대 신호가 없으면 무시
                                            ignore_sela_close = (
                                                sela_action == "close" and
                                                not force_close_profit and
                                                not should_close and
                                                "opposite" not in sela_reason.lower() and
                                                "reverse" not in sela_reason.lower()
                                            )

                                            if ignore_sela_close:
                                                logger.info(f"🛡️ [{symbol}] SELA close 전략 무시 (반대 신호 없음): {sela_reason}")
                                                sela_action = "hold"

                                            if sela_action == "close" or force_close_profit:
                                                if force_close_profit:
                                                    logger.info(f"🔄 [{symbol}] 5분 수익 실현 강제 포지션 종료 (수익률: {pnl_pct:.3f}%): {reasoning}")
                                                    close_reason = "5min_profit_realization"
                                                else:
                                                    logger.info(f"🔄 [{symbol}] SELA 포지션 종료 실행: {sela_reason}")
                                                    close_reason = f"llm_strategy_{sela_reason}"

                                                # 🔧 실제 바이낸스 포지션 종료 먼저 실행
                                                self._close_real_binance_position(symbol, close_reason)
                                                logger.info(f"🔄 [{symbol}] 실제 바이낸스 포지션 종료 완료: {close_reason}")

                                                # 포트폴리오의 포지션 종료 메서드 호출
                                                if hasattr(self.portfolio, 'close_position'):
                                                    result = self.portfolio.close_position(
                                                        symbol,
                                                        reason=close_reason
                                                    )

                                                    if result.get("success", False):
                                                        logger.info(f"✅ [{symbol}] 포지션 종료 성공: {result}")

                                                        # 🔧 반대 포지션 진입 비활성화 (불필요한 포지션 변경 방지)
                                                        if not force_close_profit:
                                                            # self._check_opposite_position_entry(symbol, position_importance, market_data)
                                                            logger.info(f"🛡️ [{symbol}] InCA 기반 반대 포지션 진입 비활성화됨")

                                                        # 🔧 즉시 반대 포지션 실행 (사이클 완료 대기하지 않음)
                                                        if hasattr(self, 'portfolio') and self.portfolio:
                                                            try:
                                                                if symbol in self.portfolio.pending_position_changes:
                                                                    logger.info(f"🚀 [{symbol}] 저장된 반대 포지션 요청 즉시 실행")
                                                                    self.portfolio.process_pending_position_changes(symbol)
                                                            except Exception as e:
                                                                logger.error(f"❌ [{symbol}] 즉시 반대 포지션 실행 실패: {e}")
                                                    else:
                                                        logger.error(f"❌ [{symbol}] 포지션 종료 실패: {result}")
                                                else:
                                                    logger.warning(f"⚠️ [{symbol}] 포트폴리오에 close_position 메서드 없음")
                                            else:
                                                # SELA가 포지션 유지 결정
                                                logger.info(f"📊 [{symbol}] SELA 포지션 유지 결정: {sela_reason}")
                                                logger.info(f"🔍 [{symbol}] 포지션 유지 상세: should_close={should_close}, force_close_profit={force_close_profit}, sela_action={sela_action}")

                                                # 포지션 유지 시에도 추가 관리 작업 수행 가능
                                                if position_strategy.get("adjustments"):
                                                    logger.info(f"🔧 [{symbol}] SELA 포지션 조정 권장: {position_strategy.get('adjustments')}")

                                            # 포지션 보유 계속 (should_close=False일 때)
                                            if not should_close:
                                                logger.info(f"📈 [{symbol}] 포지션 보유 계속: {position_importance.get('situation_type', 'unknown')}")

                                        except Exception as e:
                                            logger.error(f"❌ [{symbol}] 개별 포지션 관리 실패: {e}")
                        except Exception as e:
                            logger.error(f"❌ [{symbol}] 포지션 관리 인라인 전체 실패: {e}")

                        # 🔒 저장된 포지션 변화 요청 확인 (새로운 InCA 실행 전)
                        if hasattr(self, 'portfolio') and self.portfolio and hasattr(self.portfolio, 'pending_position_changes'):
                            if symbol in self.portfolio.pending_position_changes:
                                logger.info(f"🔒 [{symbol}] 저장된 포지션 변화 요청 발견 - 새로운 InCA 실행 건너뜀")
                                logger.info(f"🔒 [{symbol}] 사이클 완료 시 저장된 요청이 처리됩니다")
                                continue

                        # 🔄 2.1 InCA: 시장 상황 중요도 평가 (진짜 순차 실행)
                        logger.info(f"🔄 [{symbol}] InCA 시장 중요도 평가 시작 (순차 처리)")
                        inca_result = self._execute_inca_sequential(symbol, market_data, news_data)

                        # 🕐 InCA 완료 후 대기 (순차 처리 보장)
                        time.sleep(1)  # 1초 대기
                        logger.info(f"✅ [{symbol}] InCA 완료, 다음 단계 진행")

                        importance_score = inca_result.get("importance_score", 0.0)

                        # 실행 결과 및 로그 초기화
                        execution_result = None
                        execution_log = None
                        strategy = None

                        # 중요도가 임계값 이상인 경우에만 계속 진행
                        if importance_score >= self.importance_threshold:
                            logger.info(f"{symbol} 중요도({importance_score:.2f})가 임계값({self.importance_threshold:.2f})을 초과하여 계속 진행")

                            # 🔄 2.2 HiAR: 사고 흐름 정리 (진짜 순차 실행)
                            logger.info(f"🔄 [{symbol}] HiAR 사고 흐름 정리 시작 (순차 처리)")
                            reasoning_card = self._execute_hiar_sequential(symbol, market_data, news_data, topics_data, inca_result)

                            # 🕐 HiAR 완료 후 대기 (순차 처리 보장)
                            time.sleep(1)  # 1초 대기
                            logger.info(f"✅ [{symbol}] HiAR 완료, 다음 단계 진행")

                            # 사고 흐름 로깅
                            if reasoning_card:
                                self.execution_logger.log_reasoning_card(symbol, reasoning_card)

                            # 🔄 2.3 SELA: 전략 생성 (진짜 순차 실행)
                            logger.info(f"🔄 [{symbol}] SELA 전략 생성 시작 (순차 처리)")
                            sela_result = self._execute_sela_sequential(symbol, market_data, reasoning_card)

                            # 🕐 SELA 완료 후 대기 (순차 처리 보장)
                            time.sleep(1)  # 1초 대기
                            logger.info(f"✅ [{symbol}] SELA 완료, 다음 단계 진행")

                            # 🚀 2.3.5 계층적 합의 시스템 비활성화 - SELA 직접 사용
                            logger.info(f"{symbol} 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용")
                            # consensus_result = None  # 🔧 None 설정 제거

                            # 🔧 기본 consensus_result 구조 생성 (에러 방지용)
                            consensus_result = {
                                'breakdown': {},
                                'reasoning': f'SELA 직접 사용 모드 - {symbol}',
                                'final_signal': 'sela_direct',
                                'consensus_confidence': 0.8
                            }

                            # 🔍 디버깅: SELA 결과 확인
                            logger.info(f"🔍 [{symbol}] sela_result 타입: {type(sela_result)}")
                            logger.info(f"🔍 [{symbol}] sela_result 값: {sela_result}")

                            # 🎯 SELA 결과를 직접 사용 (새로운 구조 지원)
                            sela_action = 'none'
                            sela_confidence = 0.5

                            if sela_result and isinstance(sela_result, dict):
                                # SELA 논문 방법론: InCA 신호와 일치하는 전략 중 최고 점수 선택
                                if 'strategies' in sela_result and sela_result['strategies']:
                                    # 1. InCA 신호와 일치하는 전략들 필터링
                                    inca_action = inca_result.get('action_recommendation', 'hold')
                                    matching_strategies = [s for s in sela_result['strategies']
                                                          if s.get('type') == inca_action]

                                    # 2. 일치하는 전략 중 최고 신뢰도 선택
                                    if matching_strategies:
                                        best_strategy = max(matching_strategies,
                                                           key=lambda x: x.get('confidence', 0))
                                        logger.info(f"🎯 [{symbol}] InCA 신호({inca_action})와 일치하는 SELA 전략 선택: 신뢰도 {best_strategy.get('confidence', 0):.3f}")
                                    else:
                                        # 일치하는 전략이 없으면 전체 중 최고 신뢰도 선택
                                        best_strategy = max(sela_result['strategies'],
                                                           key=lambda x: x.get('confidence', 0))
                                        logger.warning(f"⚠️ [{symbol}] InCA 신호({inca_action})와 일치하는 SELA 전략 없음, 최고 신뢰도 전략 선택: {best_strategy.get('type')} (신뢰도: {best_strategy.get('confidence', 0):.3f})")

                                    sela_action = best_strategy.get('type', 'none')
                                    sela_confidence = best_strategy.get('confidence', 0.5)
                                    logger.info(f"🔍 [{symbol}] SELA 논문 방법론 적용: type={sela_action}, confidence={sela_confidence:.3f}")
                                # 기존 구조: {'type': '...', 'confidence': ...}
                                elif 'type' in sela_result:
                                    sela_action = sela_result.get('type', 'none')
                                    sela_confidence = sela_result.get('confidence', 0.5)
                                    logger.info(f"🔍 [{symbol}] 기존 구조에서 추출: type={sela_action}, confidence={sela_confidence:.3f}")
                                else:
                                    logger.warning(f"⚠️ [{symbol}] SELA 결과 구조를 인식할 수 없음: {list(sela_result.keys())}")
                            else:
                                logger.warning(f"⚠️ [{symbol}] SELA 결과가 None이거나 dict가 아님")

                            logger.info(f"🎯 [{symbol}] SELA 직접 사용: {sela_action} (신뢰도: {sela_confidence:.3f})")
                            logger.info(f"📊 [{symbol}] 합의 데이터 수집 완료 (학습용)")

                            # SELA 결과를 최종 신호로 사용
                            if sela_result and isinstance(sela_result, dict):
                                sela_result['consensus_signal'] = sela_action  # SELA 결과 그대로
                                sela_result['consensus_confidence'] = sela_confidence  # SELA 신뢰도 그대로
                                # 학습용 데이터 수집 (실제 InCA, SELA, 장기 분석 데이터 추출)
                                try:
                                    sela_result['consensus_breakdown'] = self._extract_learning_data(
                                        inca_result, sela_result, market_data, symbol
                                    )
                                except Exception as e:
                                    logger.error(f"❌ [{symbol}] 학습 데이터 추출 실패: {e}")
                                    # 기본 구조로 대체
                                    sela_result['consensus_breakdown'] = {
                                        'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                                        'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                                        'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
                                    }

                            # 🚀 2.4 SELA 신호 기반 거래 결정
                            should_execute = self._should_execute_based_on_consensus(sela_action, sela_confidence, symbol, market_data, sela_result)

                            if should_execute:
                                logger.info(f"{symbol} SELA 신호({sela_action}, 신뢰도: {sela_confidence:.3f})에 따라 거래 실행")

                                # 2.5 전략 실행 (시뮬레이터 연결이 있는 경우)
                                if hasattr(self, 'simulator') and self.simulator:
                                    if sela_result and isinstance(sela_result, dict):
                                        strategy = sela_result  # SELA 결과는 이미 단일 전략 객체

                                        # SELA 신호는 이미 전략에 반영되어 있으므로 오버라이드 불필요
                                        logger.info(f"{symbol} 전략 실행 준비: {strategy.get('type', 'unknown')} @ {strategy.get('entry_price', 'N/A')} (SELA: {sela_action})")

                                        # 🔧 전략 실행 요청 (올바른 market_data 파라미터 전달)
                                        execution_result = self.execute_strategy(symbol, strategy, market_data)

                                        # 🔧 strategy_log 초기화 (오류 방지)
                                        strategy_log = {
                                            "strategy_id": strategy.get("strategy_id", f"strat_{int(time.time())}"),
                                            "symbol": symbol,
                                            "timestamp": int(time.time()),
                                            "market_situation": inca_result,
                                            "reasoning_card": reasoning_card,
                                            "strategy": strategy,
                                            "execution_result": execution_result if execution_result else {"status": "no_result"}
                                        }

                                        if execution_result:
                                            # 2.5 실행 결과 로깅 (성공 케이스)
                                            logger.info(f"{symbol} 실행 결과 있음 - 로깅 진행")
                                        else:
                                            # 실행 결과 없음 (실패 케이스)
                                            logger.warning(f"{symbol} 실행 결과 없음 - 기본 로깅 진행")

                                        # 기존 실행 기록 저장 메서드 호출 (항상 실행)
                                        try:
                                            self.execution_logger.log_execution_record(symbol, strategy_log)
                                        except Exception as log_error:
                                            logger.error(f"{symbol} 실행 기록 저장 실패: {log_error}")

                                        # 새로운 실행 로그 저장 (통합 로그) - None은 중립 의미로 유지
                                        execution_log = {
                                            "symbol": symbol,
                                            "timestamp": int(time.time()),
                                            "market_situation": inca_result,
                                            "reasoning_card": reasoning_card,
                                            "strategy": strategy,
                                            "execution_result": execution_result,
                                            "market_data": market_data,
                                            "news_data": news_data,
                                            "topics_data": topics_data,
                                            "posts_data": posts_data
                                        }

                                        # 새로운 DataStore 메서드를 통해 실행 로그 저장
                                        log_id = self.data_store.save_execution_log(execution_log)

                                        if log_id:
                                            logger.info(f"{symbol} 실행 로그 저장 완료 (ID: {log_id})")
                                            # 실행 로그 ID를 전략에 연결 (추후 업데이트를 위해)
                                            strategy["execution_log_id"] = log_id

                                        logger.info(f"{symbol} 전략 실행 완료: 전략 ID {strategy.get('strategy_id', 'Unknown')}")
                                    else:
                                        logger.error(f"{symbol} 전략 실행 실패 - 시뮬레이터 응답 없음")
                                        # 실패 기록도 로깅 (실패 케이스 학습을 위해) - None은 중립 의미로 유지
                                        execution_log = {
                                            "symbol": symbol,
                                            "timestamp": int(time.time()),
                                            "market_situation": inca_result,
                                            "reasoning_card": reasoning_card,
                                            "strategy": strategy,
                                            "execution_result": {"status": "failed", "reason": "simulator_error"},
                                            "market_data": market_data,
                                            "news_data": news_data
                                        }
                                        self.data_store.save_execution_log(execution_log)
                                else:
                                    logger.error(f"{symbol} 생성된 전략이 없습니다.")
                            else:
                                if not hasattr(self, 'simulator') or not self.simulator:
                                    logger.error(f"{symbol} 시뮬레이터가 연결되지 않아 전략을 실행할 수 없습니다.")
                                else:
                                    # 🎯 중립 신호도 시장 조건에 따라 거래 허용
                                    if importance_score > 7.0:  # 높은 중요도일 때는 중립 신호도 거래
                                        logger.info(f"{symbol} 중립 신호이지만 높은 중요도({importance_score:.1f})로 거래 진행")
                                        # 중립 신호를 약한 매수 신호로 변환
                                        sela_result['strategies'][0]['type'] = 'buy'
                                        sela_result['strategies'][0]['confidence'] = 0.6
                                    else:
                                        logger.info(f"{symbol} SELA 신호가 중립이므로 거래하지 않음")
                        else:
                            logger.info(f"{symbol} 충분히 중요하지 않은 시장 상황: {importance_score:.2f}")

                            # 중요하지 않은 상황도 로깅 (학습용) - None은 중립 의미로 유지
                            execution_log = {
                                "symbol": symbol,
                                "timestamp": int(time.time()),
                                "market_situation": inca_result,
                                "reasoning_card": None,  # 중립 상태 (중요도 낮음)
                                "strategy": None,        # 중립 상태 (전략 없음)
                                "execution_result": {"status": "skipped", "reason": "low_importance"},
                                "market_data": market_data,
                                "news_data": news_data
                            }
                            self.data_store.save_execution_log(execution_log)

                        # 🚀 주기적 카드 인덱스 재구성 (200회마다)
                        if hasattr(self, 'iteration_count') and self.iteration_count % 200 == 0:
                            try:
                                if hasattr(self, 'hiar_agent') and self.hiar_agent and hasattr(self.hiar_agent, 'card_manager'):
                                    if self.hiar_agent.card_manager:
                                        self.hiar_agent.card_manager.rebuild_indexes()
                                        logger.info(f"✅ [{symbol}] 생각카드 인덱스 재구성 완료 ({self.iteration_count}회)")
                            except Exception as e:
                                logger.warning(f"⚠️ [{symbol}] 카드 인덱스 재구성 실패: {e}")

                        # 실행 큐 처리 완료 표시 (연속 모드에서는 큐를 사용하지 않음)
                        # if data_package:
                        #     self.execution_queue.task_done()  # 연속 모드에서는 큐 관리 불필요

                        # 3. 학습 루프 (SELA → HiAR → InCA → 학습)
                        logger.info(f"{symbol} 학습 루프 시작")

                        # 3.0 학습 데이터 저장 (실행 결과를 학습 큐에 추가)
                        if sela_result and isinstance(sela_result, dict):
                            learning_record = {
                                'symbol': symbol,
                                'timestamp': time.time(),
                                'market_data': market_data,
                                'sela_result': sela_result,
                                'execution_result': execution_result,
                                'consensus_breakdown': sela_result.get('consensus_breakdown', {}),
                                'action': sela_result.get('action', 'none'),
                                'confidence': sela_result.get('confidence', 0.5)
                            }

                            # 학습 큐에 추가 (Queue 객체와 list 객체 모두 지원)
                            if not hasattr(self, 'learning_queue'):
                                self.learning_queue = []

                            # Queue 객체인지 list 객체인지 확인
                            if hasattr(self.learning_queue, 'put'):
                                # Queue 객체인 경우
                                try:
                                    self.learning_queue.put_nowait(learning_record)
                                    queue_size = self.learning_queue.qsize()
                                    logger.info(f"[{symbol}] 학습 데이터 저장 완료 (큐 크기: {queue_size})")
                                except Exception as e:
                                    logger.warning(f"[{symbol}] Queue에 학습 데이터 저장 실패: {e}")
                            else:
                                # list 객체인 경우
                                self.learning_queue.append(learning_record)

                                # 큐 크기 제한 (최대 1000개)
                                if len(self.learning_queue) > 1000:
                                    self.learning_queue = self.learning_queue[-1000:]

                                logger.info(f"[{symbol}] 학습 데이터 저장 완료 (큐 크기: {len(self.learning_queue)})")

                        # 3.1 최신 실행 로그 가져오기
                        execution_logs = self.data_store.get_execution_logs(symbol, limit=50)

                        if not execution_logs:
                            logger.warning(f"{symbol} 학습을 위한 충분한 실행 로그가 없습니다.")
                            # 대체: 기존 메서드로 실행 기록 가져오기 (하위 호환성)
                            execution_records = self.data_store.get_execution_records(symbol, limit=50)

                            if not execution_records:
                                # 학습 큐에서 데이터 가져오기 (Queue 객체와 list 객체 모두 지원)
                                if hasattr(self, 'learning_queue') and self.learning_queue:
                                    symbol_learning_data = []

                                    # Queue 객체인지 list 객체인지 확인
                                    if hasattr(self.learning_queue, 'qsize'):
                                        # Queue 객체인 경우 - 모든 데이터를 임시로 가져와서 필터링
                                        temp_data = []
                                        try:
                                            while not self.learning_queue.empty():
                                                temp_data.append(self.learning_queue.get_nowait())
                                        except:
                                            pass

                                        # 심볼별 데이터 필터링
                                        symbol_learning_data = [r for r in temp_data if r.get('symbol') == symbol]

                                        # 나머지 데이터는 다시 큐에 넣기
                                        for data in temp_data:
                                            try:
                                                self.learning_queue.put_nowait(data)
                                            except:
                                                pass
                                    else:
                                        # list 객체인 경우
                                        symbol_learning_data = [r for r in self.learning_queue if r.get('symbol') == symbol]

                                    if symbol_learning_data:
                                        logger.info(f"[{symbol}] 학습 큐에서 {len(symbol_learning_data)}개 데이터 발견")
                                        # 학습 큐 데이터를 실행 로그 형식으로 변환
                                        execution_logs = []
                                        for data in symbol_learning_data[-10:]:  # 최근 10개만 사용
                                            execution_logs.append({
                                                "log_id": f"queue_{int(data.get('timestamp', time.time()))}",
                                                "symbol": symbol,
                                                "timestamp": int(data.get('timestamp', time.time())),
                                                "market_situation": data.get('market_data', {}),
                                                "reasoning_card": data.get('sela_result', {}),
                                                "strategy": data.get('sela_result', {}),
                                                "execution_result": data.get('execution_result', {}),
                                                "market_data": data.get('market_data', {}),
                                                "news_data": {}
                                            })
                                        logger.info(f"[{symbol}] 학습 큐 데이터를 실행 로그로 변환 완료: {len(execution_logs)}개")
                                    else:
                                        logger.warning(f"{symbol} 기존 실행 기록도 없습니다. 학습 건너뜁니다.")
                                        continue
                                else:
                                    logger.warning(f"{symbol} 기존 실행 기록도 없습니다. 학습 건너뜁니다.")
                                    continue

                            logger.info(f"{symbol} 기존 실행 기록 {len(execution_records)}개 사용")

                            # 기존 형식의 실행 기록을 새 형식으로 변환
                            execution_logs = []
                            for record in execution_records:
                                execution_logs.append({
                                    "log_id": record.get("strategy_id", f"legacy_{int(time.time())}"),
                                    "symbol": symbol,
                                    "timestamp": record.get("timestamp", int(time.time())),
                                    "market_situation": record.get("market_situation", {}),
                                    "reasoning_card": record.get("reasoning_card", {}),
                                    "strategy": record.get("strategy", {}),
                                    "execution_result": record.get("execution_result", {}),
                                    "market_data": {},  # 레거시 데이터에는 없음
                                    "news_data": {}     # 레거시 데이터에는 없음
                                })
                        else:
                            logger.info(f"{symbol} 실행 로그 {len(execution_logs)}개 검색됨")

                        # 3.2 SELA: 다양한 실험적 전략 생성
                        logger.info(f"🎓 [{symbol}] SELA 학습 에이전트 실행 시작")

                        # 과거 전략 데이터 추출 (실행 로그에서)
                        past_strategies = []
                        strategy_performances = []

                        for log in execution_logs:
                            strategy = log.get("strategy", {})
                            result = log.get("execution_result", {})

                            if strategy and result:
                                past_strategies.append(strategy)

                                # 실행 결과에서 성과 데이터 추출
                                performance = {
                                    "profit_loss": result.get("profit_loss", 0.0),
                                    "win": result.get("win", False),
                                    "pnl_percentage": result.get("pnl_percentage", 0.0),
                                    "execution_time": result.get("execution_time", 0),
                                    "market_condition": log.get("market_situation", {}).get("condition", "unknown")
                                }
                                strategy_performances.append(performance)

                        logger.info(f"🎓 [{symbol}] 과거 전략 {len(past_strategies)}개, 성과 데이터 {len(strategy_performances)}개 추출")

                        # 🔥 최신 시장 데이터 가져오기 (실제 데이터 사용)
                        current_market_data = self.binance_utils.get_market_data(symbol)
                        if "error" in current_market_data:
                            logger.warning(f"{symbol} 실제 시장 데이터 가져오기 실패, 기본 데이터 사용")
                            current_market_data = market_data  # 기존 데이터 사용
                        else:
                            logger.info(f"🔍 [{symbol}] 학습용 실제 시장 데이터: 가격=${current_market_data.get('price', 'N/A')}")

                        # 다양한 실험적 전략 생성 (실제 시장 데이터 전달)
                        logger.info(f"🎓 [{symbol}] SELA 학습 에이전트 호출 - 실험적 전략 생성")
                        experimental_strategies = self.sela_agent.generate_learning_strategies(
                            symbol,
                            count=5,  # 실험적 전략 5개 생성
                            external_market_data=current_market_data  # 🔥 실제 시장 데이터 전달
                        )

                        if not experimental_strategies:
                            logger.warning(f"❌ [{symbol}] SELA 학습 에이전트 실험적 전략 생성 실패")
                            continue

                        logger.info(f"✅ [{symbol}] SELA 학습 에이전트 성공: {len(experimental_strategies)}개 실험적 전략 생성됨")

                        # 3.3 HiAR: 각 전략에 대한 사고 흐름 정리
                        logger.info(f"🎓 [{symbol}] HiAR 학습 에이전트 실행 시작")

                        # 모든 실험적 전략에 대한 사고 흐름 분석
                        reasoning_cards = []

                        for i, strategy in enumerate(experimental_strategies):
                            logger.info(f"🎓 [{symbol}] HiAR 학습 에이전트 - 전략 {i+1}/{len(experimental_strategies)} 분석 중")

                            # 각 전략을 SELA 결과 형태로 포맷팅
                            sela_result = {
                                "strategy_id": strategy.get("strategy_id", ""),
                                "strategies": [strategy]
                            }

                            # 🔒 HiAR에 실행 단계와 동일한 데이터 스냅샷 전달 (데이터 일관성 보장)
                            logger.info(f"🔒 [{symbol}] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달")
                            logger.info(f"🔒 [{symbol}] 실행 단계 데이터: 가격=${market_data.get('price', 'N/A')}, 변동률={market_data.get('change_24h', 'N/A')}%")
                            hiar_analysis = self.hiar_agent.analyze(symbol, sela_result, market_data)

                            # 생성된 사고 카드 추출 및 전략과 연결
                            if hiar_analysis and "reasoning_cards" in hiar_analysis:
                                for card in hiar_analysis.get("reasoning_cards", []):
                                    # 카드에 전략 ID 추가
                                    card["strategy_id"] = strategy.get("strategy_id", "")
                                    reasoning_cards.append(card)
                                logger.info(f"✅ [{symbol}] HiAR 학습 에이전트 - 전략 {i+1} 사고 카드 {len(hiar_analysis.get('reasoning_cards', []))}개 생성")
                            else:
                                logger.warning(f"❌ [{symbol}] HiAR 학습 에이전트 - 전략 {i+1} 사고 카드 생성 실패")

                        if not reasoning_cards:
                            logger.warning(f"❌ [{symbol}] HiAR 학습 에이전트 전체 사고 카드 생성 실패")
                            continue

                        logger.info(f"✅ [{symbol}] HiAR 학습 에이전트 성공: {len(reasoning_cards)}개 사고 카드 생성됨")

                        # 3.4 InCA: 각 전략을 과거 데이터와 비교하여 평가
                        logger.info(f"🎓 [{symbol}] InCA 학습 에이전트 실행 시작")

                        # 각 전략과 사고 카드 연결
                        strategy_reasoning_pairs = []

                        for strategy in experimental_strategies:
                            strategy_id = strategy.get("strategy_id", "")
                            matching_cards = [card for card in reasoning_cards if card.get("strategy_id") == strategy_id]

                            if matching_cards:
                                strategy_reasoning_pairs.append({
                                    "strategy": strategy,
                                    "reasoning_card": matching_cards[0]
                                })

                        logger.info(f"🎓 [{symbol}] InCA 학습 에이전트 - {len(strategy_reasoning_pairs)}개 전략-사고카드 쌍 준비됨")

                        # InCA를 통해 전략 평가
                        strategy_evaluations = []

                        for i, pair in enumerate(strategy_reasoning_pairs):
                            logger.info(f"🎓 [{symbol}] InCA 학습 에이전트 - 전략 {i+1}/{len(strategy_reasoning_pairs)} 평가 중")

                            # 실행 로그에서 과거 성과 데이터 추출
                            past_performances = []
                            for log in execution_logs:
                                result = log.get("execution_result", {})
                                if result:
                                    performance = {
                                        "profit_loss": result.get("profit_loss", 0.0),
                                        "win": result.get("win", False),
                                        "pnl_percentage": result.get("pnl_percentage", 0.0),
                                        "execution_time": result.get("execution_time", 0),
                                        "market_condition": log.get("market_situation", {}).get("condition", "unknown")
                                    }
                                    past_performances.append(performance)

                            logger.info(f"🎓 [{symbol}] InCA 학습 에이전트 - 과거 성과 데이터 {len(past_performances)}개 사용")

                            evaluation = self.inca_agent.evaluate_strategy(
                                symbol=symbol,
                                strategy=pair["strategy"],
                                reasoning_card=pair["reasoning_card"],
                                market_data=market_data,
                                past_performances=past_performances
                            )

                            if evaluation:
                                strategy_evaluations.append({
                                    "strategy_id": pair["strategy"].get("strategy_id", ""),
                                    "strategy": pair["strategy"],
                                    "evaluation": evaluation
                                })
                                logger.info(f"✅ [{symbol}] InCA 학습 에이전트 - 전략 {i+1} 평가 완료")
                            else:
                                logger.warning(f"❌ [{symbol}] InCA 학습 에이전트 - 전략 {i+1} 평가 실패")

                        logger.info(f"✅ [{symbol}] InCA 학습 에이전트 성공: {len(strategy_evaluations)}개 전략 평가 완료")

                        # 3.5 SELA: 평가 결과를 기반으로 학습
                        logger.info(f"🎓 [{symbol}] SELA 최종 학습 단계 시작")

                        learning_result = self.sela_agent.learn_from_evaluations(
                            symbol=symbol,
                            strategy_evaluations=strategy_evaluations,
                            execution_logs=execution_logs
                        )

                        if learning_result:
                            logger.info(f"✅ [{symbol}] SELA 최종 학습 완료: {learning_result.get('status', 'unknown')}")
                            logger.info(f"🎓 [{symbol}] 학습 결과 요약: {learning_result.get('summary', 'N/A')}")
                        else:
                            logger.warning(f"❌ [{symbol}] SELA 최종 학습 실패")

                        # 🔥 가상거래 결과 로깅 추가
                        virtual_success_rate = learning_result.get('virtual_success_rate', 0.0)
                        avg_virtual_pnl = learning_result.get('avg_virtual_pnl', 0.0)
                        virtual_successes = learning_result.get('virtual_trade_successes', 0)

                        logger.info(f"🔥 [{symbol}] 가상거래 성과: {virtual_successes}/{len(strategy_evaluations)} 성공 ({virtual_success_rate*100:.1f}%), 평균 PnL: {avg_virtual_pnl:.2f}%")

                        # 학습 결과 로깅 (가상거래 통계 포함)
                        learning_log = {
                            "symbol": symbol,
                            "timestamp": int(time.time()),
                            "learning_result": learning_result,
                            "strategies_count": len(experimental_strategies),
                            "evaluations_count": len(strategy_evaluations),
                            "virtual_trade_stats": {  # 🔥 가상거래 통계 추가
                                "success_count": virtual_successes,
                                "success_rate": virtual_success_rate,
                                "avg_pnl": avg_virtual_pnl,
                                "total_strategies": len(strategy_evaluations)
                            }
                        }

                        self.data_store.save_learning_log(learning_log)

                        # 🔥 벡터 데이터베이스에 학습 패턴 저장
                        try:
                            if hasattr(self, 'vector_db') and self.vector_db:
                                # 학습 패턴을 벡터 데이터베이스에 저장
                                pattern_data = {
                                    'symbol': symbol,
                                    'timestamp': int(time.time()),
                                    'market_data': market_data,
                                    'strategies': experimental_strategies,
                                    'evaluations': strategy_evaluations,
                                    'learning_result': learning_result,
                                    'performance_stats': {
                                        'success_rate': virtual_success_rate,
                                        'avg_pnl': avg_virtual_pnl,
                                        'total_strategies': len(strategy_evaluations)
                                    }
                                }

                                # 벡터 임베딩 생성 및 저장
                                pattern_text = f"Symbol: {symbol}, Market: {market_data.get('price', 'N/A')}, Strategies: {len(experimental_strategies)}, Success Rate: {virtual_success_rate:.2f}"

                                self.vector_db.add_pattern(
                                    pattern_id=f"{symbol}_{int(time.time())}",
                                    pattern_text=pattern_text,
                                    metadata=pattern_data
                                )

                                logger.info(f"🔥 [{symbol}] 학습 패턴이 벡터 데이터베이스에 저장됨")
                            else:
                                logger.warning(f"⚠️ [{symbol}] 벡터 데이터베이스가 초기화되지 않음")
                        except Exception as e:
                            logger.error(f"❌ [{symbol}] 벡터 데이터베이스 저장 실패: {e}")

                        # 학습 완료 후 잠시 대기 (다음 심볼로 넘어가기 전)
                        logger.info(f"🎉 [{symbol}] 실행-학습 사이클 완료. 다음 심볼로 넘어갑니다.")
                        time.sleep(1)  # 1초 대기

                    except Exception as e:
                        logger.error(f"{symbol} 처리 중 오류 발생: {str(e)}")
                        logger.error(traceback.format_exc())

                    finally:
                        # 🔧 사이클 완료 시 포지션 변화 처리
                        if hasattr(self, 'portfolio') and self.portfolio:
                            try:
                                self.portfolio.mark_cycle_completion(symbol)
                                logger.info(f"🏁 [{symbol}] 사이클 완료 - 포지션 변화 처리 완료")
                            except Exception as e:
                                logger.error(f"❌ [{symbol}] 사이클 완료 처리 중 오류: {e}")

                        # 🔒 심볼 처리 완료 및 락 해제
                        self._set_symbol_processing(symbol, False)
                        logger.info(f"🔒 [{symbol}] 순차 처리 완료 (락 해제)")

                        # 🔒 심볼 락 해제
                        symbol_lock.release()

                # 🔮 가상 포지션 평가 (모든 심볼 처리 후)
                try:
                    self._evaluate_virtual_positions()
                except Exception as e:
                    logger.error(f"🔮 가상 포지션 평가 중 오류: {e}")

                # 모든 심볼 처리 완료 후 잠시 대기
                logger.info("모든 심볼에 대한 실행-학습 사이클 완료. 다음 사이클을 위해 대기합니다.")

                # 세션 통계 출력 (10분마다)
                self._log_session_stats_if_needed()

                time.sleep(10)  # 10초 대기

            except Exception as e:
                logger.error(f"연속 실행-학습 루프 오류: {str(e)}")
                logger.error(traceback.format_exc())
                time.sleep(30)  # 오류 발생 시 30초 대기 후 재시도

    def _data_collection_loop(self):
        """
        데이터 수집 루프 - 3분마다 바이낸스에서 최신 데이터 수집
        """
        logger.info("데이터 수집 루프 시작")

        while self.is_running:
            try:
                # 각 심볼에 대한 데이터 수집
                for symbol in self.symbols:
                    logger.info(f"{symbol} 데이터 수집 시작")

                    try:
                        # 시장 데이터 수집 (가격, Galaxy Score, Alt Rank 포함)
                        market_data = self.binance_utils.get_market_data(symbol)
                        if "error" in market_data:
                            raise Exception(f"시장 데이터 수집 실패: {market_data['error']}")

                        # 뉴스 데이터 수집 (최신 10개)
                        news_data = self.data_collector.get_news(symbol, limit=10)
                        if "error" in news_data:
                            raise Exception(f"뉴스 데이터 수집 실패: {news_data['error']}")

                        # 토픽 데이터 수집 (소셜 감성 분석 포함)
                        topics_data = self.data_collector.get_topics(symbol, limit=5)
                        if "error" in topics_data:
                            raise Exception(f"토픽 데이터 수집 실패: {topics_data['error']}")

                        # 소셜 포스트 수집 (상호작용 많은 순으로 정렬)
                        posts_data = self.data_collector.get_posts(symbol, limit=10)
                        if "error" in posts_data:
                            raise Exception(f"소셜 포스트 수집 실패: {posts_data['error']}")

                        # 데이터 로깅
                        self.execution_logger.log_market_data(symbol, market_data)
                        self.execution_logger.log_news(symbol, news_data)

                        # 실행 큐에 데이터 전달
                        data_package = {
                            "symbol": symbol,
                            "timestamp": int(time.time()),
                            "market_data": market_data,
                            "news_data": news_data,
                            "topics_data": topics_data,
                            "posts_data": posts_data
                        }

                        # 실행 큐에 전달
                        self.execution_queue.put(data_package)

                        logger.info(f"{symbol} 데이터 수집 완료 및 실행 큐 전달")

                    except Exception as e:
                        logger.error(f"{symbol} 데이터 수집 오류: {str(e)}")
                        # 모의나 fallback 없이 오류 그대로 전파

                # 수집 간격만큼 대기 (기본 3분)
                time.sleep(self.collection_interval)

            except Exception as e:
                logger.error(f"데이터 수집 루프 오류: {str(e)}")
                time.sleep(10)  # 오류 발생 시 10초 대기 후 재시도

    def _execution_flow_loop(self):
        """
        실행 플로우 루프 (InCA → HiAR → SELA → 실시간 실행)
        - InCA: 시장 상황 중요도 평가
        - HiAR: 사고 흐름 정리
        - SELA: 전략 생성
        - 실행: 실시간 거래 실행
        """
        logger.info("실행 플로우 루프 시작")

        while self.is_running:
            try:
                # 실행 큐에서 데이터 가져오기
                data_package = self.execution_queue.get()

                # 종료 신호 확인
                if data_package is None:
                    break

                symbol = data_package["symbol"]
                market_data = data_package["market_data"]
                news_data = data_package["news_data"]
                topics_data = data_package["topics_data"]
                posts_data = data_package["posts_data"]

                logger.info(f"{symbol} 실행 플로우 시작")

                # 1. InCA: 시장 상황 중요도 평가
                logger.info(f"{symbol} InCA 시장 중요도 평가 시작")
                inca_result = self.inca_agent.evaluate_market_importance(
                    symbol=symbol,
                    market_data=market_data,
                    news_data=news_data
                )

                importance_score = inca_result.get("importance_score", 0.0)

                # 중요도가 임계값 이상인 경우에만 계속 진행
                if importance_score >= self.importance_threshold:
                    logger.info(f"{symbol} 중요도({importance_score:.2f})가 임계값({self.importance_threshold:.2f})을 초과하여 계속 진행")

                    # 2. HiAR: 사고 흐름 정리
                    logger.info(f"{symbol} HiAR 사고 흐름 정리 시작")
                    reasoning_card = self.hiar_agent.create_reasoning_card(
                        symbol=symbol,
                        market_data=market_data,
                        news_data=news_data,
                        inca_result=inca_result or {}
                    )

                    # 사고 흐름 로깅
                    self.execution_logger.log_reasoning_card(symbol, reasoning_card)

                    # 3. 저장된 포지션 변화 요청 우선 확인
                    if hasattr(self, 'portfolio') and self.portfolio and hasattr(self.portfolio, 'pending_position_changes'):
                        if symbol in self.portfolio.pending_position_changes:
                            logger.info(f"🔒 [{symbol}] 저장된 포지션 변화 요청 발견 - 새로운 전략 생성 건너뜀")
                            logger.info(f"🔒 [{symbol}] 사이클 완료 시 저장된 요청이 처리됩니다")
                            continue

                    # 4. SELA: 전략 생성 (실행 모드)
                    logger.info(f"{symbol} SELA 전략 생성 시작")
                    sela_result = self.sela_agent.generate_strategy(
                        symbol=symbol,
                        market_data=market_data,
                        reasoning_card=reasoning_card,
                        inca_result=inca_result  # 🔧 InCA 결과 전달
                    )

                    # 🚀 3.5 계층적 합의 시스템 비활성화 - SELA 직접 사용
                    logger.info(f"{symbol} 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용")
                    # consensus_result = None  # 🔧 None 설정 제거

                    # 🔧 기본 consensus_result 구조 생성 (에러 방지용)
                    consensus_result = {
                        'breakdown': {},
                        'reasoning': f'SELA 직접 사용 모드 - {symbol}',
                        'final_signal': 'sela_direct',
                        'consensus_confidence': 0.8
                    }

                    # 🎯 SELA 결과를 직접 사용 (새로운 구조 지원)
                    sela_action = 'none'
                    sela_confidence = 0.5

                    if sela_result and isinstance(sela_result, dict):
                        # 🚀 SELA 우선 방법론: 최고 신뢰도 전략 직접 선택 (InCA 매칭 제거)
                        if 'strategies' in sela_result and sela_result['strategies']:
                            # SELA 전략 중 최고 신뢰도 선택 (InCA 신호 무관)
                            best_strategy = max(sela_result['strategies'],
                                               key=lambda x: x.get('confidence', 0))

                            inca_action = inca_result.get('action_recommendation', 'hold')
                            logger.info(f"🎯 [{symbol}] SELA 최고 신뢰도 전략 선택: {best_strategy.get('type')} (신뢰도: {best_strategy.get('confidence', 0):.3f}) | InCA: {inca_action}")

                            sela_action = best_strategy.get('type', 'none')
                            sela_confidence = best_strategy.get('confidence', 0.5)
                        # 기존 구조: {'type': '...', 'confidence': ...}
                        elif 'type' in sela_result:
                            sela_action = sela_result.get('type', 'none')
                            sela_confidence = sela_result.get('confidence', 0.5)

                    logger.info(f"🎯 [{symbol}] SELA 직접 사용: {sela_action} (신뢰도: {sela_confidence:.3f})")
                    logger.info(f"📊 [{symbol}] 합의 데이터 수집 완료 (학습용)")

                    # SELA 결과를 최종 신호로 사용
                    if sela_result and isinstance(sela_result, dict):
                        sela_result['consensus_signal'] = sela_action  # SELA 결과 그대로
                        sela_result['consensus_confidence'] = sela_confidence  # SELA 신뢰도 그대로
                        # 학습용 데이터 수집 (실제 InCA, SELA, 장기 분석 데이터 추출)
                        try:
                            sela_result['consensus_breakdown'] = self._extract_learning_data(
                                inca_result, sela_result, market_data, symbol
                            )
                        except Exception as e:
                            logger.error(f"❌ [{symbol}] 학습 데이터 추출 실패: {e}")
                            # 기본 구조로 대체
                            sela_result['consensus_breakdown'] = {
                                'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                                'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                                'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
                            }

                    # 🚀 4. SELA 신호 기반 거래 결정
                    should_execute = self._should_execute_based_on_consensus(sela_action, sela_confidence, symbol, market_data, sela_result)

                    # 4. 전략 실행 (시뮬레이터 연결이 있는 경우)
                    if hasattr(self, 'simulator') and self.simulator:
                        if should_execute and sela_result:
                            strategy = sela_result  # generate_strategy는 단일 전략 반환

                            # SELA 신호는 이미 전략에 반영되어 있으므로 오버라이드 불필요
                            # 🚀 SELA 신호가 거래 가능한 신호인 경우 강제로 거래 실행 (학습 데이터 축적용)
                            force_execute = False
                            if sela_action in ['buy', 'sell', 'long', 'short']:
                                force_execute = True
                                should_execute = True  # 기존 should_execute 오버라이드
                                logger.info(f"{symbol} SELA 신호({sela_action})가 거래 가능한 신호이므로 강제 실행")

                            logger.info(f"{symbol} SELA 기반 전략 실행 준비: {strategy['type']} @ {strategy.get('entry_price', 'N/A')} (SELA: {sela_action}, 신뢰도: {sela_confidence:.3f}, 강제실행: {force_execute})")

                            # 🚀 실제 거래 실행 (강제 실행 또는 기존 조건)
                            execution_result = None  # 실행 결과 초기화

                            if force_execute or should_execute:
                                logger.info(f"{symbol} 실제 거래 실행 시작: {strategy.get('action', strategy.get('type', 'unknown'))}")

                                # 실제 거래 실행 모드 확인
                                execution_mode = self.config.get('trading_mode', 'real')
                                logger.info(f"🔧 [{symbol}] 실행 모드 확인: {execution_mode}")

                                if execution_mode == 'real':
                                    logger.info(f"{symbol} 실제 거래 모드로 실행")

                                    # 포트폴리오 매니저를 통한 실제 거래 실행
                                    if hasattr(self, 'portfolio') and self.portfolio:
                                        try:
                                            # 🔧 strategy 타입 검증 및 수정
                                            if not isinstance(strategy, dict):
                                                logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
                                                logger.error(f"❌ [{symbol}] strategy 내용: {strategy}")
                                                real_execution_result = {
                                                    'success': False,
                                                    'error': f"Invalid strategy type: {type(strategy)}, expected dict"
                                                }
                                            else:
                                                # 🔧 SELA 결과를 포트폴리오 호환 형식으로 변환
                                                portfolio_strategy = strategy.copy()
                                                if 'action' not in portfolio_strategy and 'type' in portfolio_strategy:
                                                    portfolio_strategy['action'] = portfolio_strategy['type']

                                                action = portfolio_strategy.get('action') or portfolio_strategy.get('type', 'unknown')
                                                logger.info(f"🔧 [{symbol}] 포트폴리오 실제 거래 실행 시작: {action}")
                                                real_execution_result = self.portfolio.execute_strategy(
                                                    symbol=symbol,
                                                    strategy=portfolio_strategy,
                                                    market_data=market_data,
                                                    binance_utils=getattr(self, 'exchange', None)
                                                )

                                            if real_execution_result and real_execution_result.get('success', False):
                                                logger.info(f"{symbol} 실제 거래 실행 성공: {real_execution_result.get('order_id', 'unknown')}")
                                                # 🔥 실제 거래 성공 시 execution_result 설정하여 중복 실행 방지
                                                execution_result = {
                                                    'mode': 'real',
                                                    'status': 'executed',
                                                    'timestamp': int(time.time()),
                                                    'price': market_data.get('price', 0) if market_data else 0,
                                                    'details': real_execution_result
                                                }
                                            else:
                                                # 🚀 지능형 포지션 유지 결정 특별 처리
                                                if real_execution_result and real_execution_result.get('reason') == 'intelligent_hold':
                                                    logger.info(f"🎯 [{symbol}] 지능형 포지션 유지 결정: {real_execution_result.get('error', '단기 중립으로 판단')}")
                                                    # 🔥 execution_result 설정하여 "시뮬레이터 응답 없음" 오류 방지
                                                    execution_result = {
                                                        'mode': 'real',
                                                        'status': 'intelligent_hold',
                                                        'timestamp': int(time.time()),
                                                        'price': market_data.get('price', 0) if market_data else 0,
                                                        'details': real_execution_result
                                                    }
                                                elif real_execution_result and real_execution_result.get('reason') == 'position_maintained':
                                                    logger.info(f"🎯 [{symbol}] 같은 방향 포지션 유지: {real_execution_result.get('message', '불필요한 거래 방지')}")
                                                    # 🔥 execution_result 설정하여 "시뮬레이터 응답 없음" 오류 방지
                                                    execution_result = {
                                                        'mode': 'real',
                                                        'status': 'position_maintained',
                                                        'timestamp': int(time.time()),
                                                        'price': market_data.get('price', 0) if market_data else 0,
                                                        'details': real_execution_result
                                                    }
                                                elif real_execution_result and real_execution_result.get('reason') == 'position_updated':
                                                    logger.info(f"🎯 [{symbol}] 포지션 정보 업데이트: {real_execution_result.get('message', '신뢰도 향상')}")
                                                    # 🔥 execution_result 설정하여 "시뮬레이터 응답 없음" 오류 방지
                                                    execution_result = {
                                                        'mode': 'real',
                                                        'status': 'position_updated',
                                                        'timestamp': int(time.time()),
                                                        'price': market_data.get('price', 0) if market_data else 0,
                                                        'details': real_execution_result
                                                    }
                                                elif real_execution_result and real_execution_result.get('reason') == 'intelligent_hold_at_switch':
                                                    logger.info(f"🛡️ [{symbol}] switch_position 레벨 지능형 포지션 유지: {real_execution_result.get('error', 'switch 레벨 차단')}")
                                                    # 🔥 execution_result 설정하여 "시뮬레이터 응답 없음" 오류 방지
                                                    execution_result = {
                                                        'mode': 'real',
                                                        'status': 'intelligent_hold_at_switch',
                                                        'timestamp': int(time.time()),
                                                        'price': market_data.get('price', 0) if market_data else 0,
                                                        'details': real_execution_result
                                                    }
                                                else:
                                                    # None 체크를 더 안전하게 처리
                                                    if real_execution_result is None:
                                                        error_msg = "No result returned"
                                                    elif isinstance(real_execution_result, dict):
                                                        error_msg = real_execution_result.get('error', real_execution_result.get('message', 'Unknown error'))
                                                    else:
                                                        error_msg = str(real_execution_result)
                                                    logger.error(f"{symbol} 실제 거래 실행 실패: {error_msg}")

                                                    # 🔥 실제 거래 실패 시에도 execution_result 설정하여 "시뮬레이터 응답 없음" 오류 방지
                                                    execution_result = {
                                                        'mode': 'real',
                                                        'status': 'failed',
                                                        'timestamp': int(time.time()),
                                                        'price': market_data.get('price', 0) if market_data else 0,
                                                        'error': error_msg,
                                                        'details': real_execution_result
                                                    }
                                        except Exception as e:
                                            logger.error(f"{symbol} 실제 거래 실행 중 오류: {str(e)}")
                                            # 🔥 예외 발생 시에도 execution_result 설정
                                            execution_result = {
                                                'mode': 'real',
                                                'status': 'error',
                                                'timestamp': int(time.time()),
                                                'price': market_data.get('price', 0) if market_data else 0,
                                                'error': str(e)
                                            }
                                    else:
                                        logger.warning(f"{symbol} 포트폴리오 매니저가 없어 실제 거래 실행 불가")
                                        # 🔥 포트폴리오 매니저 없을 시에도 execution_result 설정
                                        execution_result = {
                                            'mode': 'real',
                                            'status': 'no_portfolio',
                                            'timestamp': int(time.time()),
                                            'price': market_data.get('price', 0) if market_data else 0,
                                            'error': 'Portfolio manager not available'
                                        }
                                else:
                                    logger.info(f"{symbol} 시뮬레이션 모드로 실행")
                                    # 🔥 시뮬레이션 모드에서도 execution_result 설정
                                    execution_result = {
                                        'mode': 'simulation',
                                        'status': 'simulated',
                                        'timestamp': int(time.time()),
                                        'price': market_data.get('price', 0) if market_data else 0,
                                        'details': {
                                            'order_id': f"sim_{symbol}_{int(time.time())}",
                                            'entry_price': market_data.get('price', 0) if market_data else 0,
                                            'direction': strategy.get('action', strategy.get('type', 'unknown')),
                                            'position_size': strategy.get('position_size', 0),
                                            'stop_loss': strategy.get('stop_loss', 0),
                                            'take_profit': strategy.get('take_profit', 0)
                                        }
                                    }
                            else:
                                # 🔥 거래 조건을 만족하지 않는 경우에도 execution_result 설정
                                logger.info(f"{symbol} 거래 조건 미충족 - 실행하지 않음 (force_execute: {force_execute}, should_execute: {should_execute})")
                                execution_result = {
                                    'mode': 'skipped',
                                    'status': 'conditions_not_met',
                                    'timestamp': int(time.time()),
                                    'price': market_data.get('price', 0) if market_data else 0,
                                    'reason': f'force_execute={force_execute}, should_execute={should_execute}'
                                }

                            # 🔥 execution_result가 항상 설정되므로 중복 실행 방지 로직 제거
                            logger.info(f"{symbol} 전략 실행 완료: {execution_result.get('status', 'unknown')} 모드")

                            # 🔥 execution_result는 항상 설정되므로 if 체크 제거
                            # 5. 실행 결과 로깅 - 기존 로깅 유지
                            strategy_log = {
                                "strategy_id": strategy.get("strategy_id", f"strat_{int(time.time())}"),
                                "symbol": symbol,
                                "timestamp": int(time.time()),
                                "market_situation": inca_result,
                                "reasoning_card": reasoning_card,
                                "strategy": strategy,
                                "execution_result": execution_result
                            }

                            # 기존 실행 기록 저장 메서드 호출
                            self.execution_logger.log_execution_record(symbol, strategy_log)

                            # 새로운 실행 로그 저장 (통합 로그) - None 값 방지
                            # 학습 루프에서 활용하기 위한 포괄적 데이터 저장
                            execution_log = {
                                "symbol": symbol,
                                "timestamp": int(time.time()),
                                "market_situation": inca_result if inca_result else {},
                                "reasoning_card": reasoning_card if reasoning_card else {},
                                "strategy": strategy if strategy else {},
                                "execution_result": execution_result if execution_result else {},
                                "market_data": market_data if market_data else {},
                                "news_data": news_data if news_data else {},
                                # 추가 데이터
                                "topics_data": topics_data if topics_data else {},
                                "posts_data": posts_data if posts_data else {}
                            }

                            # 새로운 DataStore 메서드를 통해 실행 로그 저장
                            log_id = self.data_store.save_execution_log(execution_log)

                            if log_id:
                                logger.info(f"{symbol} 실행 로그 저장 완료 (ID: {log_id})")
                                # 실행 로그 ID를 전략에 연결 (추후 업데이트를 위해)
                                strategy["execution_log_id"] = log_id

                            # 🎯 통합 생각카드에 추세 예측 정보 저장
                            self._update_thinking_cards_with_predictions(symbol, inca_result, strategy, market_data)

                            logger.info(f"{symbol} 전략 실행 완료: 전략 ID {strategy.get('strategy_id', 'Unknown')}")
                        else:
                            if not should_execute:
                                logger.info(f"{symbol} SELA 신호({sela_action}, 신뢰도: {sela_confidence:.3f})가 거래 조건을 만족하지 않아 거래하지 않음")
                            elif not sela_result:
                                logger.error(f"{symbol} SELA 전략 생성 실패")
                            else:
                                logger.error(f"{symbol} 알 수 없는 이유로 전략 실행 조건 미충족")
                    else:
                        logger.error(f"{symbol} 시뮬레이터가 연결되지 않아 전략을 실행할 수 없습니다.")
                else:
                    logger.info(f"{symbol} 충분히 중요하지 않은 시장 상황: {importance_score:.2f}")

                # 실행 큐 처리 완료 표시 (연속 모드에서는 연속 루프에서 처리)
                # self.execution_queue.task_done()  # 연속 모드에서는 중복 호출 방지

            except Exception as e:
                logger.error(f"실행 플로우 오류: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                # if 'data_package' in locals() and data_package:
                #     self.execution_queue.task_done()  # 연속 모드에서는 중복 호출 방지

    def _learning_loop(self):
        """
        학습 루프 (SELA → HiAR → InCA → 학습 루프)
        - SELA: 다양한 전략 생성 (실험용)
        - HiAR: 각 전략에 대한 사고 흐름 정리
        - InCA: 과거 실행 결과를 기반으로 전략 평가

        실행 플로우에서 축적된 실행 로그를 활용하여 전략을 지속적으로 개선합니다.
        """
        logger.info("학습 루프 시작")

        while self.is_running:
            try:
                logger.info("학습 루프 사이클 시작")

                # 각 심볼에 대한 학습 루프 실행
                for symbol in self.symbols:
                    try:
                        logger.info(f"{symbol} 학습 루프 시작")

                        # 1. 최신 실행 로그 가져오기 (개선된 메서드 사용)
                        execution_logs = self.data_store.get_execution_logs(symbol, limit=50)

                        if not execution_logs:
                            logger.warning(f"{symbol} 학습을 위한 충분한 실행 로그가 없습니다.")
                            # 대체: 기존 메서드로 실행 기록 가져오기 (하위 호환성)
                            execution_records = self.data_store.get_execution_records(symbol, limit=50)

                            if not execution_records:
                                logger.warning(f"{symbol} 기존 실행 기록도 없습니다. 학습 건너뜁니다.")
                                continue

                            logger.info(f"{symbol} 기존 실행 기록 {len(execution_records)}개 사용")

                            # 기존 형식의 실행 기록을 새 형식으로 변환
                            execution_logs = []
                            for record in execution_records:
                                execution_logs.append({
                                    "log_id": record.get("strategy_id", f"legacy_{int(time.time())}"),
                                    "symbol": symbol,
                                    "timestamp": record.get("timestamp", int(time.time())),
                                    "market_situation": record.get("market_situation", {}),
                                    "reasoning_card": record.get("reasoning_card", {}),
                                    "strategy": record.get("strategy", {}),
                                    "execution_result": record.get("execution_result", {}),
                                    "market_data": {},  # 레거시 데이터에는 없음
                                    "news_data": {}     # 레거시 데이터에는 없음
                                })
                        else:
                            logger.info(f"{symbol} 실행 로그 {len(execution_logs)}개 검색됨")

                        # 2. 최신 시장 데이터 가져오기
                        market_data = self.binance_utils.get_market_data(symbol)

                        if "error" in market_data:
                            logger.error(f"{symbol} 최신 시장 데이터 조회 실패: {market_data.get('error')}")
                            raise Exception(f"시장 데이터 조회 실패: {market_data.get('error')}")

                        # 최신 뉴스 데이터 가져오기
                        news_data = self.data_collector.get_news_data(symbol)

                        # 3. SELA: 다양한 실험적 전략 생성
                        logger.info(f"{symbol} SELA 다양한 전략 생성 시작")

                        # 과거 전략 데이터 추출 (실행 로그에서)
                        past_strategies = []
                        strategy_performances = []

                        for log in execution_logs:
                            strategy = log.get("strategy", {})
                            result = log.get("execution_result", {})

                            if strategy and result:
                                past_strategies.append(strategy)

                                # 실행 결과에서 성과 데이터 추출
                                performance = {
                                    "profit_loss": result.get("profit_loss", 0.0),
                                    "win": result.get("win", False),
                                    "pnl_percentage": result.get("pnl_percentage", 0.0),
                                    "execution_time": result.get("execution_time", 0),
                                    "market_condition": log.get("market_situation", {}).get("condition", "unknown")
                                }
                                strategy_performances.append(performance)

                        # 다양한 실험적 전략 생성
                        experimental_strategies_result = self.sela_agent.create_experimental_strategies(
                            symbol,
                            market_data,
                            past_strategies,
                            strategy_performances,
                            count=5  # 실험적 전략 5개 생성
                        )

                        experimental_strategies = experimental_strategies_result.get("strategies", [])

                        if not experimental_strategies:
                            logger.warning(f"{symbol} 실험적 전략 생성 실패")
                            continue

                        logger.info(f"{symbol} {len(experimental_strategies)}개 실험적 전략 생성됨")

                        # 4. HiAR: 각 전략에 대한 사고 흐름 정리
                        logger.info(f"{symbol} HiAR 사고 흐름 분석 시작")

                        # 모든 실험적 전략에 대한 사고 흐름 분석
                        reasoning_cards = []

                        for strategy in experimental_strategies:
                            # 각 전략을 SELA 결과 형태로 포맷팅
                            sela_result = {
                                "strategy_id": strategy.get("strategy_id", ""),
                                "strategies": [strategy]
                            }

                            # 🔒 HiAR에 최신 시장 데이터 전달 (학습 루프용 최신 데이터)
                            logger.info(f"🔒 [{symbol}] 학습 루프 HiAR에 최신 시장 데이터 전달")
                            # 최신 시장 데이터 수집
                            current_market_data = self.binance_utils.get_market_data(symbol)
                            if current_market_data and "error" not in current_market_data:
                                logger.info(f"🔒 [{symbol}] 학습 루프 최신 데이터: 가격=${current_market_data.get('price', 'N/A')}, 변동률={current_market_data.get('change_24h', 'N/A')}%")
                                hiar_analysis = self.hiar_agent.analyze(symbol, sela_result, current_market_data)
                            else:
                                # 폴백: 기존 시장 데이터 사용
                                logger.warning(f"⚠️ [{symbol}] 최신 데이터 수집 실패, 기존 데이터 사용")
                                logger.info(f"🔒 [{symbol}] 폴백 데이터: 가격=${market_data.get('price', 'N/A')}, 변동률={market_data.get('change_24h', 'N/A')}%")
                                hiar_analysis = self.hiar_agent.analyze(symbol, sela_result, market_data)

                            # 생성된 사고 카드 추출 및 전략과 연결
                            if hiar_analysis and "reasoning_cards" in hiar_analysis:
                                for card in hiar_analysis.get("reasoning_cards", []):
                                    # 카드에 전략 ID 추가
                                    card["strategy_id"] = strategy.get("strategy_id", "")
                                    reasoning_cards.append(card)

                        if not reasoning_cards:
                            logger.warning(f"{symbol} 사고 카드 생성 실패")
                            continue

                        logger.info(f"{symbol} {len(reasoning_cards)}개 사고 카드 생성됨")

                        # 5. InCA: 과거 실행 결과를 기반으로 전략 평가
                        logger.info(f"{symbol} InCA 전략 평가 시작")

                        learning_records = []

                        for strategy, card in zip(experimental_strategies, reasoning_cards):
                            if not strategy or not card:
                                continue

                            # InCA의 전략 평가 호출
                            evaluation = self.inca_agent.evaluate_strategy(
                                symbol=symbol,
                                strategy=strategy,
                                reasoning_card=card,
                                market_data=market_data,
                                news_data=news_data,
                                execution_logs=execution_logs  # 과거 실행 로그 전달
                            )

                            if evaluation:
                                # 학습 기록 생성
                                learning_record = {
                                    "symbol": symbol,
                                    "timestamp": int(time.time()),
                                    "strategy": strategy,
                                    "reasoning_card": card,
                                    "evaluation": evaluation,
                                    "market_data": market_data
                                }

                                learning_records.append(learning_record)

                                # 학습 기록 저장
                                self.execution_logger.log_learning_record(symbol, learning_record)

                                # 6. SELA에 피드백 전달하여 학습
                                self.sela_agent.learn_from_feedback(symbol, learning_record)

                                # 7. InCA에도 피드백 전달하여 학습 (개선된 데이터 구조)
                                # 학습 루프에서는 가상 성과 평가를 통해 피드백 생성
                                virtual_profit = self._calculate_virtual_performance(strategy, evaluation, market_data)

                                inca_feedback = {
                                    'symbol': symbol,
                                    'prediction': {
                                        'action_recommendation': strategy.get('type', 'hold'),
                                        'trading_direction': self._normalize_direction(strategy.get('type', 'neutral')),
                                        'confidence': strategy.get('confidence', 0.5),
                                        'importance': strategy.get('importance', 5),
                                        'strategy_type': 'learning_loop_evaluation',
                                        'reasoning': strategy.get('reasoning', 'SELA 학습 루프 전략')
                                    },
                                    'trade_result': {
                                        'profit': virtual_profit,
                                        'profit_percent': virtual_profit,
                                        'success': virtual_profit > 0.1,  # 0.1% 이상 수익 시 성공
                                        'status': 'learning_evaluation',
                                        'evaluation_details': evaluation
                                    },
                                    'market_data': market_data
                                }
                                self.inca_agent.learn_from_feedback(inca_feedback)

                        logger.info(f"{symbol} {len(learning_records)}개 학습 기록 생성 및 피드백 전달 완료")

                    except Exception as e:
                        logger.error(f"{symbol} 학습 루프 오류: {str(e)}")
                        import traceback
                        logger.error(traceback.format_exc())

                # 학습 주기 대기 (기본 1시간)
                logger.info(f"학습 루프 완료. {self.learning_interval}초 대기 후 다음 사이클 시작")
                time.sleep(self.learning_interval)

            except Exception as e:
                logger.error(f"학습 루프 전체 오류: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(60)  # 오류 발생 시 1분 대기 후 재시도

    def initialize_agents(self):
        """
        에이전트 초기화

        Returns:
            bool: 초기화 성공 여부
        """
        try:
            logger.info("InCA 에이전트 초기화 중...")
            self.inca_agent = InCAAgent(
                llm_model=self.exaone_model,
                vector_db_path=os.path.join(self.data_dir, "vector_db"),
                importance_threshold=self.importance_threshold,
                cache_enabled=True
            )

            # 디버그: 필수 메서드 확인
            required_methods = ['initialize', 'process_data', 'learn_from_feedback',
                              'save_state', 'load_state', 'find_similar_patterns']
            for method in required_methods:
                if hasattr(self.inca_agent, method):
                    logger.debug(f"InCA 에이전트의 {method} 메서드 존재: ")
                else:
                    logger.error(f"InCA 에이전트의 {method} 메서드 누락: ")
                    raise AttributeError(f"InCA 에이전트에 필수 메서드 {method}가 구현되지 않았습니다.")

            # InCA 에이전트 초기화
            if not self.inca_agent.initialize():
                logger.error("InCA 에이전트 초기화 실패")
                return False
            logger.info("InCA 에이전트 초기화 완료")

            logger.info("HiAR 에이전트 초기화 중...")
            # FastHiAR 우선 사용
            try:
                # FastHiAR 어댑터 직접 사용
                from trading.reasoning.hiar_adapter import HiARAdapter
                self.hiar_agent = HiARAdapter(
                    storage_path=os.path.join(self.data_dir, "thought_cards"),
                    auto_migrate=True
                )
                logger.info("✅ FastHiAR 어댑터 사용 (논문 기반 빠른 분석)")
            except Exception as e:
                logger.warning(f"FastHiAR 어댑터 초기화 실패: {e}, 팩토리 패턴으로 폴백")
                # 폴백: 팩토리를 통한 HiAR 생성
                self.hiar_agent = create_default_hiar(self.exaone_model)
                logger.info("팩토리 기반 HiAR 에이전트 사용")

            # HiAR 에이전트 메서드 확인 (FastHiAR 어댑터용)
            required_methods = ['initialize', 'analyze', 'create_reasoning_card', 'generate_reasoning']
            for method in required_methods:
                if hasattr(self.hiar_agent, method):
                    logger.debug(f"HiAR 에이전트의 {method} 메서드 존재")
                else:
                    logger.warning(f"HiAR 에이전트의 {method} 메서드 누락")

            # HiAR 에이전트 초기화
            try:
                if hasattr(self.hiar_agent, 'initialize') and callable(getattr(self.hiar_agent, 'initialize')):
                    if not self.hiar_agent.initialize():
                        logger.error("HiAR 에이전트 초기화 실패")
                        return False
                else:
                    logger.info("HiAR 어댑터는 별도 초기화가 필요하지 않습니다")
                logger.info("HiAR 에이전트 초기화 완료")
            except Exception as e:
                logger.error(f"HiAR 에이전트 초기화 중 오류: {e}")
                return False

            # SELA 에이전트가 외부에서 전달되지 않은 경우에만 새로 생성
            if self.sela_agent is None:
                logger.info("SELA 에이전트 초기화 중...")
                self.sela_agent = SELAAgent(
                    data_store=self.data_store,
                    execution_logger=self.execution_logger,
                    hybrid_interface=self.hybrid_interface,
                    config={
                        'exaone_model': self.exaone_model,
                        'data_dir': self.data_dir,
                        'diverse_strategies_count': self.diverse_strategies_count
                    }
                )
            else:
                logger.info("외부에서 전달된 SELA 에이전트 사용")
                # 전달받은 SELA 에이전트의 hybrid_interface 업데이트
                if hasattr(self.sela_agent, 'hybrid_interface'):
                    self.sela_agent.hybrid_interface = self.hybrid_interface
                    logger.info("SELA 에이전트의 hybrid_interface 업데이트 완료")

            # 디버그: 필수 메서드 확인
            required_methods = ['initialize', 'process_data', 'learn_from_feedback',
                              'save_state', 'load_state', 'create_execution_strategy',
                              'create_experimental_strategies']
            for method in required_methods:
                if hasattr(self.sela_agent, method):
                    logger.debug(f"SELA 에이전트의 {method} 메서드 존재: ")
                else:
                    logger.error(f"SELA 에이전트의 {method} 메서드 누락: ")
                    raise AttributeError(f"SELA 에이전트에 필수 메서드 {method}가 구현되지 않았습니다.")

            # SELA 에이전트 초기화
            if not self.sela_agent.initialize():
                logger.error("SELA 에이전트 초기화 실패")
                return False
            logger.info("SELA 에이전트 초기화 완료")

            return True

        except Exception as e:
            logger.error(f"에이전트 초기화 중 오류 발생: {e}")
            logger.error(traceback.format_exc())
            return False

    def _update_thinking_cards_with_predictions(self, symbol: str, inca_result: Dict, strategy: Dict, market_data: Dict):
        """🎯 통합 생각카드에 추세 예측 및 SELA 매칭 정보 업데이트"""
        try:
            # 포트폴리오에서 카드 매니저 가져오기 (디버그 로그 추가)
            logger.debug(f"🔍 [{symbol}] 통합 생각카드 업데이트 시작 - 포트폴리오 접근 확인")

            if not hasattr(self, 'portfolio'):
                logger.warning(f"❌ [{symbol}] 하이브리드 컨트롤러에 portfolio 속성이 없습니다")
                return

            if not self.portfolio:
                logger.warning(f"❌ [{symbol}] 포트폴리오 객체가 None입니다")
                return

            if not hasattr(self.portfolio, 'card_manager'):
                logger.warning(f"❌ [{symbol}] 포트폴리오에 card_manager 속성이 없습니다")
                return

            card_manager = self.portfolio.card_manager
            if not card_manager:
                logger.warning(f"❌ [{symbol}] 카드 매니저 객체가 None입니다")
                return

            logger.debug(f"✅ [{symbol}] 카드 매니저 접근 성공")

            # 해당 심볼의 활성 포지션 카드들 찾기
            symbol_position_ids = card_manager.symbol_cards.get(symbol, [])
            logger.debug(f"🔍 [{symbol}] 활성 포지션 카드 수: {len(symbol_position_ids)}개")

            # 포지션 카드가 없으면 포트폴리오에서 활성 포지션 확인하여 카드 생성
            if not symbol_position_ids and hasattr(self, 'portfolio') and self.portfolio:
                active_positions = self.portfolio.get_positions_by_symbol(symbol)
                for pos in active_positions:
                    position_id = pos.get('trade_id')
                    if position_id and position_id not in card_manager.position_cards:
                        try:
                            card = card_manager.create_card_for_position(
                                position_id=position_id,
                                symbol=symbol,
                                initial_data=pos
                            )
                            logger.info(f"🎯 [{symbol}] 누락된 포지션 카드 자동 생성: {position_id} -> {card.card_id}")
                            symbol_position_ids.append(position_id)
                        except Exception as e:
                            logger.warning(f"❌ [{symbol}] 포지션 카드 자동 생성 실패 ({position_id}): {e}")

            for position_id in symbol_position_ids:
                card = card_manager.position_cards.get(position_id)
                if not card or not card.is_active:
                    continue

                # 🔮 1. 추세 예측 정보 업데이트
                trend_prediction_data = {
                    'short_term': strategy.get('type', 'neutral'),  # buy/sell/neutral
                    'medium_term': inca_result.get('trend_direction', 'neutral'),
                    'long_term': inca_result.get('long_term_trend', 'neutral'),
                    'confidence': inca_result.get('confidence', 0.5),
                    'reasoning': inca_result.get('reasoning', f"InCA 분석 기반 {symbol} 추세 예측")
                }

                success = card_manager.update_card(position_id, 'trend_prediction', trend_prediction_data)
                if success:
                    logger.info(f"🔮 [{symbol}] 통합 생각카드 추세 예측 업데이트: {trend_prediction_data['short_term']} (신뢰도: {trend_prediction_data['confidence']:.2f})")

                # 📊 2. SELA 매칭 정보 업데이트
                sela_matching_data = {
                    'sela_prediction': strategy.get('type', 'neutral'),
                    'position_prediction': card.position_info.get('direction', 'unknown'),
                    'match_score': self._calculate_prediction_match_score(strategy.get('type'), card.position_info.get('direction')),
                    'result': {
                        'timestamp': time.time(),
                        'market_price': market_data.get('price', 0),
                        'strategy_confidence': strategy.get('confidence', 0.5),
                        'inca_confidence': inca_result.get('confidence', 0.5)
                    }
                }

                success = card_manager.update_card(position_id, 'sela_matching', sela_matching_data)
                if success:
                    logger.info(f"📊 [{symbol}] 통합 생각카드 SELA 매칭 업데이트: 매칭점수 {sela_matching_data['match_score']:.2f}")

                # ⏰ 3. 시간대별 학습 정보 업데이트 (단기 결과)
                timeframe_learning_data = {
                    'timeframe': 'short',
                    'result_data': {
                        'timestamp': time.time(),
                        'prediction': strategy.get('type', 'neutral'),
                        'confidence': strategy.get('confidence', 0.5),
                        'market_condition': {
                            'price': market_data.get('price', 0),
                            'volume': market_data.get('volume_24h', 0),
                            'change_24h': market_data.get('change_24h', 0)
                        },
                        'inca_analysis': inca_result
                    },
                    'insights': {
                        'prediction_strength': 'strong' if strategy.get('confidence', 0) > 0.7 else 'weak',
                        'market_volatility': 'high' if abs(market_data.get('change_24h', 0)) > 5 else 'low'
                    },
                    'metrics': {
                        'prediction_count': 1,
                        'avg_confidence': strategy.get('confidence', 0.5)
                    }
                }

                success = card_manager.update_card(position_id, 'timeframe_learning', timeframe_learning_data)
                if success:
                    logger.info(f"⏰ [{symbol}] 통합 생각카드 시간대별 학습 업데이트: 단기 결과 추가")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 통합 생각카드 예측 정보 업데이트 실패: {e}")

    def _calculate_virtual_performance(self, strategy: Dict[str, Any], evaluation: Dict[str, Any], market_data: Dict[str, Any]) -> float:
        """학습 루프에서 가상 성과 계산"""
        try:
            # 전략 신뢰도와 평가 점수를 기반으로 가상 수익률 계산
            confidence = strategy.get('confidence', 0.5)
            overall_score = evaluation.get('overall_score', 0.5)

            # 시장 변동성 고려
            price_change = market_data.get('percent_change_24h', 0)
            volatility_factor = min(abs(price_change) / 10.0, 1.0)  # 최대 1.0

            # 전략 타입에 따른 방향성 고려
            strategy_type = strategy.get('type', 'neutral').lower()
            if strategy_type in ['buy', 'long'] and price_change > 0:
                direction_bonus = 0.1  # 상승장에서 long 전략
            elif strategy_type in ['sell', 'short'] and price_change < 0:
                direction_bonus = 0.1  # 하락장에서 short 전략
            else:
                direction_bonus = 0.0

            # 가상 수익률 계산 (범위: -5% ~ +5%) - 더 현실적인 범위
            base_performance = (overall_score - 0.5) * 4  # -2% ~ +2%
            confidence_bonus = (confidence - 0.5) * 1.0  # -0.5% ~ +0.5%
            volatility_bonus = volatility_factor * 0.8  # 0% ~ +0.8%

            virtual_profit = base_performance + confidence_bonus + volatility_bonus + direction_bonus

            # 범위 제한 (더 현실적인 범위)
            virtual_profit = max(-5.0, min(5.0, virtual_profit))

            logger.info(f"🎯 가상 성과 계산: 기본 {base_performance:.3f}% + 신뢰도 {confidence_bonus:.3f}% + 변동성 {volatility_bonus:.3f}% + 방향성 {direction_bonus:.3f}% = {virtual_profit:.3f}%")

            return virtual_profit

        except Exception as e:
            logger.warning(f"가상 성과 계산 실패: {e}")
            return 0.0

    def _calculate_prediction_match_score(self, sela_prediction: str, position_direction: str) -> float:
        """SELA 예측과 포지션 방향 간의 매칭 점수 계산"""
        try:
            # 방향 정규화
            sela_norm = self._normalize_direction(sela_prediction)
            pos_norm = self._normalize_direction(position_direction)

            if sela_norm == pos_norm:
                return 1.0  # 완전 일치
            elif sela_norm == 'neutral' or pos_norm == 'neutral':
                return 0.5  # 중립
            else:
                return 0.0  # 반대 방향

        except Exception:
            return 0.5  # 오류 시 중립 점수

    def _normalize_direction(self, direction: str) -> str:
        """방향 문자열 정규화"""
        if not direction:
            return 'neutral'

        direction = direction.lower()
        if direction in ['buy', 'long', 'bullish', 'strong_buy']:
            return 'long'
        elif direction in ['sell', 'short', 'bearish', 'strong_sell']:
            return 'short'
        else:
            return 'neutral'

    def process_trade_feedback(self, symbol: str, strategy_id: str,
                             exit_price: float, status: str = "completed"):
        """
        거래 피드백 처리

        Args:
            symbol: 코인 심볼
            strategy_id: 전략 ID
            exit_price: 종료 가격
            status: 상태 (completed, cancelled)
        """
        try:
            # 전략 정보 가져오기
            strategy = self.data_store.get_strategy_by_id(strategy_id)

            if not strategy:
                logger.warning(f"{symbol} 전략 ID를 찾을 수 없습니다: {strategy_id}")
                return

            # 실행 결과 계산
            entry_price = strategy["entry_price"]

            if strategy["type"] == "LONG":
                profit_loss_percent = (exit_price - entry_price) / entry_price * 100
            else:  # SHORT
                profit_loss_percent = (entry_price - exit_price) / entry_price * 100

            # 절대 손익 계산 (가상으로 1000 달러 거래 가정)
            position_size = strategy["position_size_percent"] / 100 * 1000
            profit_loss = position_size * profit_loss_percent / 100

            # 실행 결과 업데이트
            execution_result = {
                "strategy_id": strategy_id,
                "symbol": symbol,
                "timestamp": int(time.time()),
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "type": strategy["type"],
                "entry_price": entry_price,
                "exit_price": exit_price,
                "profit_loss": profit_loss,
                "profit_loss_percent": profit_loss_percent,
                "status": status
            }

            # 실행 결과 로깅
            self.execution_logger.log_execution_result(symbol, execution_result)

            logger.info(f"{symbol} 거래 피드백 처리 완료: {strategy_id}, PL: {profit_loss_percent:.2f}%")

            # 학습 큐에 피드백 전달
            feedback = {
                "symbol": symbol,
                "strategy_id": strategy_id,
                "profit_loss": profit_loss,
                "profit_loss_percent": profit_loss_percent,
                "timestamp": int(time.time())
            }

            self.learning_queue.put(feedback)

            # InCA에 거래 피드백 전달
            inca_trade_feedback = {
                'symbol': symbol,
                'prediction': {
                    'strategy': strategy,
                    'action': strategy.get('type', 'HOLD'),
                    'entry_price': entry_price
                },
                'trade_result': {
                    'profit': profit_loss,
                    'profit_percent': profit_loss_percent,
                    'exit_price': exit_price,
                    'success': profit_loss > 0
                },
                'market_data': {}
            }
            self.inca_agent.learn_from_feedback(inca_trade_feedback)

            return execution_result

        except Exception as e:
            logger.error(f"{symbol} 거래 피드백 처리 오류: {str(e)}")
            return None

    def get_current_status(self) -> Dict[str, Any]:
        """
        현재 시스템 상태 가져오기

        Returns:
            시스템 상태 정보
        """
        status = {
            "is_running": self.is_running,
            "symbols": self.symbols,
            "collection_interval": self.collection_interval,
            "learning_interval": self.learning_interval,
            "execution_queue_size": self.execution_queue.qsize(),
            "learning_queue_size": self.learning_queue.qsize(),
            "timestamp": int(time.time()),
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 최근 실행 결과 (각 심볼당 최대 3개)
        recent_executions = {}
        for symbol in self.symbols:
            recent_executions[symbol] = self.data_store.get_latest_execution_results(symbol, 3)

        status["recent_executions"] = recent_executions

        return status

    def start_execution_flow_thread(self):
        """
        실행 플로우 스레드 시작 (InCA → HiAR → SELA → 실시간 실행)

        메인 시뮬레이터에서 호출되어 실행 플로우를 독립된 스레드로 시작
        """
        if self.execution_thread and self.execution_thread.is_alive():
            logger.warning("실행 플로우 스레드가 이미 실행 중입니다.")
            return

        logger.info("실행 플로우 스레드 시작")
        self.is_running = True

        # 실행 플로우 스레드 시작
        self.execution_thread = threading.Thread(target=self._execution_flow_loop)
        self.execution_thread.daemon = True
        self.execution_thread.start()

        logger.info("실행 플로우 스레드 시작 완료")

    def start_learning_loop_thread(self):
        """
        학습 루프 스레드 시작 (SELA → HiAR → InCA → 학습 루프)

        메인 시뮬레이터에서 호출되어 학습 루프를 독립된 스레드로 시작
        """
        if self.learning_thread and self.learning_thread.is_alive():
            logger.warning("학습 루프 스레드가 이미 실행 중입니다.")
            return

        logger.info("학습 루프 스레드 시작")
        self.is_running = True

        # 학습 루프 스레드 시작
        self.learning_thread = threading.Thread(target=self._learning_loop)
        self.learning_thread.daemon = True
        self.learning_thread.start()

        logger.info("학습 루프 스레드 시작 완료")

    def execute_strategy(self, symbol: str, strategy: Dict[str, Any], simulator=None):
        """
        생성된 전략을 실행

        Args:
            symbol: 코인 심볼
            strategy: 실행할 전략
            simulator: 연결된 시뮬레이터 (실제 거래 실행용)

        Returns:
            실행 결과
        """
        try:
            logger.info(f"{symbol} 전략 실행: {strategy['type']} @ {strategy.get('entry_price', 'Unknown')}")

            if simulator:
                # 시뮬레이터를 통해 실제 거래 실행
                quantity = strategy.get('quantity', 0)

                # 전략 타입 정규화 (대소문자 구분 없이 처리)
                strategy_type = strategy['type'].lower()
                if strategy_type in ['long', 'buy']:
                    position_type = "LONG"
                elif strategy_type in ['short', 'sell']:
                    position_type = "SHORT"
                else:
                    logger.warning(f"{symbol} 알 수 없는 전략 타입: {strategy['type']}, 기본값 LONG 사용")
                    position_type = "LONG"

                logger.info(f"{symbol} 전략 타입 변환: {strategy['type']} -> {position_type}")

                # 시뮬레이터에 전략 실행 요청
                execution_result = simulator.execute_strategy(
                    symbol=symbol,
                    position_type=position_type,
                    quantity=quantity,
                    entry_price=strategy.get('entry_price', 0),
                    stop_loss=strategy.get('stop_loss', 0),
                    take_profit=strategy.get('take_profit', 0),
                    strategy_id=strategy.get('strategy_id', ''),
                    reason=f"HybridController: {strategy.get('reason', 'N/A')}"
                )

                # 실행 결과 로깅
                if execution_result:
                    self.execution_logger.log_execution_result(symbol, execution_result)
                    logger.info(f"{symbol} 전략 실행 완료: {strategy.get('strategy_id', 'Unknown')}")
                    return execution_result
                else:
                    logger.warning(f"{symbol} 전략 실행 실패")
                    return None
            else:
                # 시뮬레이터가 없는 경우 로깅만 수행
                logger.info(f"{symbol} 시뮬레이터 없음. 로깅만 수행")

                # 실행 결과 생성 (더미 데이터)
                execution_result = {
                    "strategy_id": strategy.get("strategy_id", f"strat_{int(time.time())}"),
                    "symbol": symbol,
                    "timestamp": int(time.time()),
                    "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "type": strategy["type"],
                    "entry_price": strategy.get("entry_price", 0),
                    "exit_price": 0,
                    "profit_loss": 0,
                    "profit_loss_percent": 0,
                    "status": "pending"
                }

                self.execution_logger.log_execution_result(symbol, execution_result)
                return execution_result

        except Exception as e:
            logger.error(f"{symbol} 전략 실행 오류: {str(e)}")
            return None

    def connect_simulator(self, simulator):
        """
        시뮬레이터 연결

        Args:
            simulator: 연결할 시뮬레이터
        """
        self.simulator = simulator
        logger.info("시뮬레이터 연결 완료")

    def execute_flow(self, symbol: str, market_data: Dict[str, Any], news_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        실행 플로우(InCA → HiAR → SELA) 실행

        Args:
            symbol: 코인 심볼
            market_data: 실시간 시장 데이터
            news_data: 뉴스 데이터 (선택적)

        Returns:
            Dict[str, Any]: 실행 결과
        """
        logger.info(f"{symbol} 실행 플로우 시작 (InCA → HiAR → SELA)")

        try:
            # 에이전트 확인
            if not self.inca_agent or not self.hiar_agent or not self.sela_agent:
                logger.error("필요한 에이전트가 초기화되지 않았습니다.")
                raise RuntimeError("에이전트가 초기화되지 않았습니다.")

            # 기본 응답 구조
            result = {
                "symbol": symbol,
                "timestamp": int(time.time()),
                "strategy_executed": False,
                "importance_level": 0.0,
                "strategy": None
            }

            # 1. InCA: 시장 상황 중요도 평가
            logger.info(f"{symbol} InCA 시장 중요도 평가 시작")

            # 시장 데이터와 뉴스 데이터 결합
            combined_market_data = market_data.copy()
            combined_market_data["symbol"] = symbol
            combined_market_data["news"] = news_data

            inca_result = self.inca_agent.evaluate_importance(
                market_data=combined_market_data
            )

            importance_score = inca_result.get("importance_score", 0.0)
            result["importance_level"] = importance_score

            # 중요도가 임계값을 넘는 경우에만 계속 진행
            if importance_score >= self.importance_threshold:
                logger.info(f"{symbol} 중요도({importance_score:.2f})가 임계값({self.importance_threshold:.2f})을 초과하여 계속 진행")

                # 2. HiAR: 사고 흐름 정리
                logger.info(f"{symbol} HiAR 사고 흐름 정리 시작")
                reasoning_card = self.hiar_agent.create_reasoning_card(
                    symbol=symbol,
                    market_data=market_data,
                    news_data=news_data,
                    inca_result=inca_result
                )

                # 사고 흐름 로깅
                self.execution_logger.log_reasoning_card(symbol, reasoning_card)

                # 3. 저장된 포지션 변화 요청 우선 확인 (순차 처리 모드)
                if hasattr(self, 'portfolio') and self.portfolio and hasattr(self.portfolio, 'pending_position_changes'):
                    if symbol in self.portfolio.pending_position_changes:
                        logger.info(f"🔒 [{symbol}] 저장된 포지션 변화 요청 발견 - 새로운 전략 생성 건너뜀 (순차 모드)")
                        logger.info(f"🔒 [{symbol}] 사이클 완료 시 저장된 요청이 처리됩니다")
                        return result

                # 4. SELA: 전략 생성
                logger.info(f"{symbol} SELA 전략 생성 시작")

                # SELAStrategyGenerator.generate_strategy 메서드 시그니처 확인
                # 매개변수: symbol, market_data, reasoning_card
                # SELAAgent.generate_strategy 메서드 시그니처 확인
                # 매개변수: symbol, market_data, reasoning_trace

                # 🔍 HiAR → SELA 데이터 전달 디버깅
                logger.info(f"🔍 [{symbol}] HiAR → SELA 데이터 전달 디버깅")
                logger.info(f"🔍 [{symbol}] reasoning_card 타입: {type(reasoning_card)}")
                logger.info(f"🔍 [{symbol}] reasoning_card 키들: {list(reasoning_card.keys()) if isinstance(reasoning_card, dict) else 'N/A'}")
                if isinstance(reasoning_card, dict):
                    logger.info(f"🔍 [{symbol}] reasoning_card analysis: {reasoning_card.get('analysis', 'N/A')[:100]}...")
                    logger.info(f"🔍 [{symbol}] reasoning_card direction: {reasoning_card.get('direction', 'N/A')}")
                logger.info(f"🔍 [{symbol}] reasoning_card 내용: {str(reasoning_card)[:200]}...")

                sela_result = self.sela_agent.generate_strategy(
                    symbol=symbol,
                    market_data=market_data,
                    reasoning_card=reasoning_card,
                    inca_result=inca_result  # 🔧 InCA 결과 전달
                )

                # 🚀 3.5 계층적 합의 시스템 비활성화 - SELA 직접 사용
                logger.info(f"{symbol} 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용")
                # consensus_result = None  # 🔧 None 설정 제거

                # 🔧 기본 consensus_result 구조 생성 (에러 방지용)
                consensus_result = {
                    'breakdown': {},
                    'reasoning': f'SELA 직접 사용 모드 - {symbol}',
                    'final_signal': 'sela_direct',
                    'consensus_confidence': 0.8
                }

                # 🎯 SELA 결과를 직접 사용 (새로운 구조 지원)
                sela_action = 'none'
                sela_confidence = 0.5
                inca_action = inca_result.get('action_recommendation', 'hold') if inca_result else 'hold'

                if sela_result and isinstance(sela_result, dict):
                    # 🚀 SELA 우선 방법론: 최고 신뢰도 전략 직접 선택 (InCA 매칭 제거)
                    if 'strategies' in sela_result and sela_result['strategies']:
                        # SELA 전략 중 최고 신뢰도 선택 (InCA 신호 무관)
                        best_strategy = max(sela_result['strategies'],
                                           key=lambda x: x.get('confidence', 0))

                        logger.info(f"🎯 [{symbol}] SELA 최고 신뢰도 전략 선택: {best_strategy.get('type')} (신뢰도: {best_strategy.get('confidence', 0):.3f}) | InCA: {inca_action}")

                        sela_action = best_strategy.get('type', 'none')
                        sela_confidence = best_strategy.get('confidence', 0.5)
                    # 기존 구조: {'type': '...', 'confidence': ...}
                    elif 'type' in sela_result:
                        sela_action = sela_result.get('type', 'none')
                        sela_confidence = sela_result.get('confidence', 0.5)

                logger.info(f"🎯 [{symbol}] SELA 직접 사용: {sela_action} (신뢰도: {sela_confidence:.3f})")
                logger.info(f"📊 [{symbol}] 합의 데이터 수집 완료 (학습용)")

                # SELA 결과를 최종 신호로 사용
                if sela_result and isinstance(sela_result, dict):
                    sela_result['consensus_signal'] = sela_action  # SELA 결과 그대로
                    sela_result['consensus_confidence'] = sela_confidence  # SELA 신뢰도 그대로
                    # 학습용 데이터 수집 (실제 InCA, SELA, 장기 분석 데이터 추출)
                    try:
                        sela_result['consensus_breakdown'] = self._extract_learning_data(
                            inca_result, sela_result, market_data, symbol
                        )
                    except Exception as e:
                        logger.error(f"❌ [{symbol}] 학습 데이터 추출 실패: {e}")
                        # 기본 구조로 대체
                        sela_result['consensus_breakdown'] = {
                            'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                            'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                            'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
                        }

                # 🚀 3.6 합의 신호 기반 거래 결정
                logger.info(f"{symbol} SELA 기반 거래 결정 메서드 호출 시작")
                logger.info(f"{symbol} 메서드 존재 확인: {hasattr(self, '_should_execute_based_on_consensus')}")
                should_execute = self._should_execute_based_on_consensus(sela_action, sela_confidence, symbol, market_data, sela_result)
                logger.info(f"{symbol} SELA 기반 거래 결정 완료: {should_execute}")

                # SELA 신호는 이미 전략에 반영되어 있으므로 오버라이드 불필요

                # 🚀 SELA 신호가 거래 가능한 신호인 경우 강제로 거래 실행 (학습 데이터 축적용)
                # 🔧 NONE 신호는 포지션 유지를 위해 강제 실행에서 제외
                force_execute = False
                if sela_action in ['buy', 'sell', 'long', 'short']:
                    # 🛡️ 추가 검증: 실제 전략 타입도 확인
                    actual_strategy_type = 'none'
                    if sela_result and isinstance(sela_result, dict):
                        if 'strategies' in sela_result and sela_result['strategies']:
                            actual_strategy_type = sela_result['strategies'][0].get('type', 'none')
                        else:
                            actual_strategy_type = sela_result.get('type', 'none')

                    # 실제 전략 타입이 none이면 강제 실행하지 않음
                    if actual_strategy_type in ['none', 'neutral', 'hold']:
                        force_execute = False
                        logger.info(f"{symbol} SELA 신호({sela_action})이지만 실제 전략({actual_strategy_type})이 중립이므로 강제 실행하지 않음")
                    else:
                        force_execute = True
                        should_execute = True  # 기존 should_execute 오버라이드
                        logger.info(f"{symbol} SELA 신호({sela_action})가 거래 가능한 신호이므로 강제 실행")
                elif sela_action in ['none', 'neutral', 'hold']:
                    # 🎯 종합 판단: InCA와 SELA 신호 불일치 시 추가 검증
                    if inca_action in ['buy', 'sell'] and importance_score > 8.0:
                        # 매우 높은 중요도일 때만 InCA 신호 우선 (종합 판단)
                        force_execute = True
                        should_execute = True
                        logger.info(f"{symbol} InCA-SELA 불일치이지만 매우 높은 중요도({importance_score:.1f})로 InCA 신호({inca_action}) 채택")
                    else:
                        # SELA의 보수적 판단 존중
                        force_execute = False
                        logger.info(f"{symbol} SELA 보수적 판단({sela_action}) 존중 - 거래하지 않음")

                # 🚀 실제 거래 실행 (강제 실행 또는 기존 조건)
                if force_execute or should_execute:
                    # 거래 실행 액션 추출 (새로운 구조 지원)
                    trade_action = 'unknown'
                    if sela_result and isinstance(sela_result, dict):
                        if 'strategies' in sela_result and sela_result['strategies']:
                            # SELA 논문 방법론: InCA 신호와 일치하는 전략 중 최고 점수 선택
                            inca_action = inca_result.get('action_recommendation', 'hold')
                            matching_strategies = [s for s in sela_result['strategies']
                                                  if s.get('type') == inca_action]

                            if matching_strategies:
                                best_strategy = max(matching_strategies,
                                                   key=lambda x: x.get('confidence', 0))
                            else:
                                best_strategy = max(sela_result['strategies'],
                                                   key=lambda x: x.get('confidence', 0))

                            trade_action = best_strategy.get('action', best_strategy.get('type', 'unknown'))
                        else:
                            trade_action = sela_result.get('action', sela_result.get('type', 'unknown'))

                    logger.info(f"{symbol} 실제 거래 실행 시작: {trade_action}")

                    # 실제 거래 실행 모드 확인
                    execution_mode = self.config.get('trading_mode', 'real')
                    logger.info(f"🔧 [{symbol}] 실행 모드 확인: {execution_mode}")

                    if execution_mode == 'real':
                        logger.info(f"{symbol} 실제 거래 모드로 실행")

                        # 포트폴리오 매니저를 통한 실제 거래 실행
                        if hasattr(self, 'portfolio') and self.portfolio:
                            try:
                                # 포트폴리오 매니저의 execute_trade 메서드 호출 (기존 메서드 사용)
                                real_execution_result = self.portfolio.execute_trade(
                                    symbol=symbol,
                                    strategy=sela_result,
                                    market_data=market_data,
                                    binance_utils=getattr(self, 'binance_utils', None)
                                )

                                # 🚀 지능형 포지션 유지 결정 특별 처리
                                # 🛡️ 성공한 경우에도 skipped_trade 확인
                                if real_execution_result and real_execution_result.get('skipped_trade', False):
                                    logger.info(f"🎯 [{symbol}] 같은 방향 포지션으로 거래 스킵: {real_execution_result.get('message', '거래 스킵')}")
                                    return  # 함수 종료

                                if real_execution_result and not real_execution_result.get('success', False):
                                    if real_execution_result.get('reason') == 'intelligent_hold':
                                        logger.info(f"🎯 [{symbol}] 지능형 포지션 유지 결정: {real_execution_result.get('error', '단기 중립으로 판단')}")
                                        return  # 함수 종료
                                    elif real_execution_result.get('reason') == 'position_maintained':
                                        logger.info(f"🎯 [{symbol}] 같은 방향 포지션 유지: {real_execution_result.get('message', '불필요한 거래 방지')}")
                                        return  # 함수 종료
                                    elif real_execution_result.get('reason') == 'position_updated':
                                        logger.info(f"🎯 [{symbol}] 포지션 정보 업데이트: {real_execution_result.get('message', '신뢰도 향상')}")
                                        return  # 함수 종료
                                    elif real_execution_result.get('reason') == 'intelligent_hold_at_switch':
                                        logger.info(f"🛡️ [{symbol}] switch_position 레벨 지능형 포지션 유지: {real_execution_result.get('error', 'switch 레벨 차단')}")
                                        return  # 함수 종료
                                    elif real_execution_result.get('reason') == 'same_direction_hold':
                                        logger.info(f"🎯 [{symbol}] 같은 방향 포지션 유지: {real_execution_result.get('message', '같은 방향 포지션 감지')}")
                                        return  # 함수 종료

                                if real_execution_result and isinstance(real_execution_result, dict) and real_execution_result.get('success', False):
                                    order_id = real_execution_result.get('order_id', real_execution_result.get('position_id', 'unknown'))
                                    logger.info(f"{symbol} 실제 거래 실행 성공: {order_id}")
                                else:
                                    # None 체크를 더 안전하게 처리
                                    if real_execution_result is None:
                                        error_msg = "No result returned"
                                    elif isinstance(real_execution_result, dict):
                                        error_msg = real_execution_result.get('error', real_execution_result.get('message', 'Unknown error'))
                                    else:
                                        error_msg = str(real_execution_result)
                                    logger.error(f"{symbol} 실제 거래 실행 실패: {error_msg}")
                            except Exception as e:
                                logger.error(f"{symbol} 실제 거래 실행 중 오류: {str(e)}")
                        else:
                            logger.warning(f"{symbol} 포트폴리오 매니저가 없어 실제 거래 실행 불가")
                    else:
                        logger.info(f"{symbol} 시뮬레이션 모드로 실행")

                # 결과를 strategies 리스트로 변환
                if sela_result:
                    sela_result = {"strategies": [sela_result]}

                # 전략 처리 방식 통일
                strategies = []

                # 1. strategies 리스트 형태로 제공된 경우
                if sela_result and "strategies" in sela_result and isinstance(sela_result["strategies"], list):
                    strategies = sela_result["strategies"]
                # 2. 단일 전략 객체로 제공된 경우
                elif sela_result and isinstance(sela_result, dict) and "strategy_id" in sela_result:
                    strategies = [sela_result]
                # 3. 단일 전략 객체로 제공된 경우 (id 필드 사용)
                elif sela_result and isinstance(sela_result, dict) and "id" in sela_result:
                    # strategy_id 필드 추가
                    if "strategy_id" not in sela_result:
                        sela_result["strategy_id"] = sela_result["id"]
                    strategies = [sela_result]

                if strategies and len(strategies) > 0:
                    # 최적 전략 선택
                    best_strategy = strategies[0]  # 첫 번째 전략 사용

                    # 전략 정보 로깅 및 포맷팅
                    if best_strategy:
                        # 필드 이름 통일
                        strategy_id = best_strategy.get("strategy_id", best_strategy.get("id", f"strat_{int(time.time())}"))
                        entry_price = best_strategy.get("entry_price", market_data.get("price", 0))

                        # direction 필드 처리 (여러 가능한 필드 이름 처리)
                        direction = best_strategy.get("direction",
                                   best_strategy.get("position",
                                   best_strategy.get("action",
                                   best_strategy.get("type", "none"))))  # type 필드도 확인

                        # strategy_executed 필드 처리
                        strategy_executed = best_strategy.get("strategy_executed", True)

                        # 🚀 합의 기반 전략 실행 결정
                        if should_execute:
                            # 중립/거래없음 조건 확인 (short/long은 유효한 전략)
                            neutral_conditions = ["neutral", "no_trade", "none", "hold"]
                            if direction.lower() in neutral_conditions:
                                strategy_executed = False
                                logger.info(f"{symbol} SELA 신호({sela_action}, 신뢰도: {sela_confidence:.3f})에도 불구하고 중립 전략으로 판단: {direction}")
                            else:
                                strategy_executed = True
                                logger.info(f"{symbol} SELA 기반 유효한 전략으로 판단: {direction} (SELA: {sela_action}, 신뢰도: {sela_confidence:.3f})")
                        else:
                            strategy_executed = False
                            logger.info(f"{symbol} SELA 신호({sela_action}, 신뢰도: {sela_confidence:.3f})가 거래 조건을 만족하지 않아 전략 실행하지 않음")

                        # 결과 업데이트
                        result.update({
                            "strategy_executed": strategy_executed,
                            "strategy_id": strategy_id,
                            "strategy_type": best_strategy.get("strategy_type", "unknown"),
                            "direction": direction,
                            "entry_price": entry_price,
                            "entry_time": int(time.time()),
                            "stop_loss": best_strategy.get("stop_loss", 0),
                            "take_profit": best_strategy.get("take_profit", 0),
                            "timeframe": best_strategy.get("timeframe", "short_term"),
                            "reasoning": reasoning_card.get("summary", ""),
                            "strategy": best_strategy  # 전체 전략 객체 포함
                        })

                        # 전략 로그 저장 (합의 정보 포함)
                        strategy_log = {
                            "symbol": symbol,
                            "strategy_id": strategy_id,
                            "timestamp": int(time.time()),
                            "market_data": market_data,
                            "importance": inca_result,
                            "reasoning_card": reasoning_card,
                            "strategy": best_strategy,
                            "execution_status": "created",
                            # 🚀 계층적 합의 시스템 결과 추가
                            "consensus_result": {
                                "final_signal": sela_action,
                                "consensus_confidence": sela_confidence,
                                "should_execute": should_execute,
                                "breakdown": consensus_result.get('breakdown', {}),
                                "reasoning": consensus_result.get('reasoning', '')
                            }
                        }

                        # 기존 실행 기록 저장 메서드 호출
                        self.execution_logger.log_execution_record(symbol, strategy_log)

                        logger.info(f"{symbol} SELA 기반 전략 생성 완료: {strategy_id}, {direction} @ ${entry_price:.2f} [SELA: {sela_action}, 신뢰도: {sela_confidence:.3f}, 실행: {should_execute}]")
                    else:
                        logger.info(f"{symbol} 적합한 전략이 생성되지 않았습니다.")
                else:
                    logger.info(f"{symbol} 전략 생성 실패 또는 적합한 전략 없음")
            else:
                logger.info(f"{symbol} 중요도({importance_score:.2f})가 임계값({self.importance_threshold:.2f})보다 낮아 진행하지 않음")

            return result

        except Exception as e:
            logger.error(f"{symbol} 실행 플로우 중 오류 발생: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

            # 오류 정보를 포함한 결과 반환
            return {
                "symbol": symbol,
                "timestamp": int(time.time()),
                "strategy_executed": False,
                "error": str(e)
            }

    def collect_market_data(self) -> Dict[str, Any]:
        """
        최신 시장 데이터 수집 (캔들 데이터 포함)

        Returns:
            Dict[str, Any]: 수집된 시장 데이터
        """
        market_data = {}
        try:
            for symbol in self.symbols:
                if self.binance_utils:
                    # 🎯 기본 시장 데이터 수집
                    data = self.binance_utils.get_market_data(symbol)
                    if data and "error" not in data:
                        # 🎯 캔들 데이터 추가 (InCA 단기 추세 분석용)
                        try:
                            # 1분봉 5개 (InCA용)
                            recent_candles = self.binance_utils.get_klines(f"{symbol}USDT", "1m", 5)
                            if recent_candles:
                                data['recent_candles'] = recent_candles
                                data['candles'] = recent_candles  # 호환성
                                logger.debug(f"{symbol} 캔들 데이터 추가: {len(recent_candles)}개")
                        except Exception as e:
                            logger.warning(f"{symbol} 캔들 데이터 수집 실패: {e}")
                            data['recent_candles'] = []
                            data['candles'] = []

                        market_data[symbol] = data
                        continue

                # 바이낸스 데이터 수집 실패 시 데이터 스토어에서 가져오기 시도
                if self.data_store:
                    stored_data = self.data_store.get_latest_market_data(symbol)
                    if stored_data:
                        market_data[symbol] = stored_data

            return market_data
        except Exception as e:
            logger.error(f"시장 데이터 수집 중 오류 발생: {str(e)}")
            return {}

    def collect_news_data(self) -> Dict[str, Any]:
        """
        최신 뉴스 데이터 수집

        Returns:
            Dict[str, Any]: 수집된 뉴스 데이터
        """
        news_data = {}
        try:
            for symbol in self.symbols:
                # LunarCrush API를 통한 뉴스 수집
                if hasattr(self, 'data_collector') and self.data_collector:
                    data = self.data_collector.get_news(symbol, limit=10)
                    if data and "error" not in data:
                        news_data[symbol] = data
                        continue

                # 수집 실패 시 데이터 스토어에서 가져오기 시도
                if self.data_store:
                    stored_data = self.data_store.get_latest_news_data(symbol)
                    if stored_data:
                        news_data[symbol] = stored_data

            return news_data
        except Exception as e:
            logger.error(f"뉴스 데이터 수집 중 오류 발생: {str(e)}")
            return {}

    def run_inca_analysis(self, market_data: Dict[str, Any], news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        InCA 분석 실행 (뉴스 중요도 평가)

        Args:
            market_data: 시장 데이터
            news_data: 뉴스 데이터

        Returns:
            Dict[str, Any]: InCA 분석 결과
        """
        if not self.inca_agent:
            logger.error("InCA 에이전트가 초기화되지 않았습니다.")
            return {"importance": 0, "error": "InCA 에이전트 없음"}

        try:
            results = {}
            for symbol in self.symbols:
                if symbol not in market_data or symbol not in news_data:
                    continue

                # 시장 데이터와 뉴스 데이터 결합
                combined_market_data = market_data[symbol].copy()
                combined_market_data["symbol"] = symbol
                combined_market_data["news"] = news_data[symbol]

                # InCA 중요도 평가 실행
                importance_result = self.inca_agent.evaluate_importance(
                    market_data=combined_market_data
                )

                results[symbol] = importance_result

            # 가장 중요도가 높은 심볼 결과 반환
            if results:
                max_importance_symbol = max(results.keys(), key=lambda s: results[s].get("importance_score", 0))
                return {
                    "symbol": max_importance_symbol,
                    "importance": results[max_importance_symbol].get("importance_score", 0),
                    "analysis": results[max_importance_symbol],
                    "all_results": results
                }
            else:
                return {"importance": 0, "error": "분석 결과 없음"}

        except Exception as e:
            logger.error(f"InCA 분석 중 오류 발생: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {"importance": 0, "error": str(e)}

    def generate_reasoning_trace(self, market_data: Dict[str, Any], inca_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        HiAR 사고 흐름 생성

        Args:
            market_data: 시장 데이터
            inca_result: InCA 분석 결과

        Returns:
            Dict[str, Any]: 사고 흐름 결과
        """
        if not self.hiar_agent:
            logger.error("HiAR 에이전트가 초기화되지 않았습니다.")
            return {"error": "HiAR 에이전트 없음"}

        try:
            symbol = inca_result.get("symbol")
            if not symbol or symbol not in market_data:
                return {"error": "유효한 심볼 또는 시장 데이터 없음"}

            # HiAR 사고 흐름 카드 생성
            reasoning_card = self.hiar_agent.create_reasoning_card(
                symbol=symbol,
                market_data=market_data[symbol],
                news_data=None,
                inca_result=inca_result.get("analysis", {})
            )

            return reasoning_card

        except Exception as e:
            logger.error(f"HiAR 사고 흐름 생성 중 오류 발생: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {"error": str(e)}

    def generate_and_execute_strategy(self, symbol: str, market_data: Dict[str, Any],
                               hiar_result: Dict[str, Any], inca_result: Dict[str, Any],
                               learning_insights=None, learned_strategies=None) -> Dict[str, Any]:
        """
        HiAR 사고 흐름을 기반으로 SELA가 전략을 생성하고 실행

        Args:
            symbol: 대상 심볼
            market_data: 시장 데이터
            hiar_result: HiAR 추론 결과
            inca_result: InCA 분석 결과
            learning_insights: 선택적 학습 인사이트
            learned_strategies: 선택적 학습된 전략들

        Returns:
            전략 생성 및 실행 결과 딕셔너리
        """
        try:
            if not self.sela_agent:
                self.logger.error("SELA 에이전트가 초기화되지 않았습니다.")
                return None

            # 시작 로그
            self.logger.info(f"{symbol} SELA 전략 생성 및 실행 시작")

            # 전략 생성 입력 데이터 구성
            strategy_input = {
                'symbol': symbol,
                'market_data': market_data,
                'reasoning_card': hiar_result,
                'market_analysis': inca_result,
                'timestamp': int(time.time())
            }

            # 학습된 인사이트가 있으면 추가
            if learning_insights:
                strategy_input['learning_insights'] = learning_insights

            # 학습된 전략이 있으면 추가
            if learned_strategies:
                strategy_input['reference_strategies'] = learned_strategies

            # 1. 전략 생성
            strategy = self.sela_agent.generate_strategy(**strategy_input)

            if not strategy:
                self.logger.warning(f"{symbol} 전략 생성에 실패했습니다.")
                return {
                    'success': False,
                    'error': 'Failed to generate strategy',
                    'timestamp': int(time.time())
                }

            # 2. 전략 유효성 검증
            is_valid, validation_result = self.sela_agent.validate_strategy(strategy)

            if not is_valid:
                self.logger.warning(f"{symbol} 전략 유효성 검증 실패: {validation_result.get('reason', 'Unknown reason')}")
                return {
                    'success': False,
                    'error': f"Strategy validation failed: {validation_result.get('reason', 'Unknown reason')}",
                    'strategy': strategy,
                    'timestamp': int(time.time())
                }

            # 3. 전략 실행
            self.logger.info(f"{symbol} 전략 실행 시작: {strategy.get('id', 'unknown-strategy')}")
            execution_result = self.execute_strategy(symbol, strategy, market_data)

            # 4. 결과 구성
            result = {
                'success': True,
                'strategy': strategy,
                'validation_result': validation_result,
                'execution_result': execution_result,
                'timestamp': int(time.time())
            }

            # 5. 전략 실행 결과 로깅
            self.log_strategy_execution(
                symbol=symbol,
                strategy=strategy,
                execution_result=execution_result,
                market_data=market_data,
                reasoning_card=hiar_result
            )

            self.logger.info(f"{symbol} SELA 전략 생성 및 실행 완료")
            return result

        except Exception as e:
            self.logger.error(f"{symbol} SELA 전략 생성 및 실행 중 오류 발생: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

            return {
                'success': False,
                'error': str(e),
                'timestamp': int(time.time())
            }

    def execute_strategy(self, symbol: str, strategy: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        전략 실행

        Args:
            symbol: 대상 심볼
            strategy: 전략 정보
            market_data: 시장 데이터

        Returns:
            실행 결과 딕셔너리
        """
        try:
            # 실행 모드 확인 (trading_mode로 통일)
            execution_mode = self.config.get('trading_mode', 'real')
            logger.info(f"🔧 [{symbol}] 실행 모드 확인: {execution_mode}")

            if execution_mode == 'real':
                # 실제 거래 실행
                self.logger.info(f"{symbol} 실제 거래 실행: {strategy.get('id', 'unknown')}")

                # 🚀 실제 거래 API 호출 구현
                try:
                    # 포트폴리오 매니저를 통한 실제 거래 실행
                    if hasattr(self, 'portfolio') and self.portfolio:
                        # 🔧 strategy 타입 검증 및 수정
                        if not isinstance(strategy, dict):
                            logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
                            logger.error(f"❌ [{symbol}] strategy 내용: {strategy}")
                            return {
                                'mode': 'real',
                                'status': 'error',
                                'timestamp': int(time.time()),
                                'error': f"Invalid strategy type: {type(strategy)}, expected dict"
                            }

                        # 🔧 SELA 결과를 포트폴리오 호환 형식으로 변환
                        portfolio_strategy = strategy.copy()
                        if 'action' not in portfolio_strategy and 'type' in portfolio_strategy:
                            portfolio_strategy['action'] = portfolio_strategy['type']

                        action = portfolio_strategy.get('action') or portfolio_strategy.get('type', 'unknown')
                        logger.info(f"🔧 [{symbol}] 포트폴리오 실제 거래 실행 시작: {action}")
                        execution_result = self.portfolio.execute_strategy(
                            symbol=symbol,
                            strategy=portfolio_strategy,
                            market_data=market_data,
                            binance_utils=getattr(self, 'exchange', None)
                        )

                        if execution_result:
                            self.logger.info(f"{symbol} 실제 거래 실행 성공: {execution_result}")
                            return {
                                'mode': 'real',
                                'status': 'executed',
                                'timestamp': int(time.time()),
                                'price': market_data.get('price', 0),
                                'execution_result': execution_result,
                                'details': {
                                    'order_id': execution_result.get('position_id', f"order_{symbol}_{int(time.time())}"),
                                    'entry_price': market_data.get('price', 0),
                                    'direction': strategy.get('type', strategy.get('direction', 'unknown')),
                                    'position_size': strategy.get('position_size', strategy.get('size', 0)),
                                    'stop_loss': strategy.get('stop_loss', 0),
                                    'take_profit': strategy.get('take_profit', 0)
                                }
                            }
                        else:
                            self.logger.error(f"{symbol} 실제 거래 실행 실패")
                            return {
                                'mode': 'real',
                                'status': 'failed',
                                'timestamp': int(time.time()),
                                'price': market_data.get('price', 0) if market_data else 0,
                                'error': 'Portfolio execution failed'
                            }
                    else:
                        self.logger.warning(f"{symbol} 포트폴리오 매니저가 없어 실제 거래 실행 불가")
                        return {
                            'mode': 'real',
                            'status': 'failed',
                            'timestamp': int(time.time()),
                            'price': market_data.get('price', 0) if market_data else 0,
                            'error': 'No portfolio manager available'
                        }

                except Exception as e:
                    self.logger.error(f"{symbol} 실제 거래 실행 중 오류: {str(e)}")
                    return {
                        'mode': 'real',
                        'status': 'error',
                        'timestamp': int(time.time()),
                        'price': market_data.get('price', 0) if market_data else 0,
                        'error': str(e)
                    }

            else:
                # 🔧 strategy 타입 검증 및 수정 (시뮬레이션 모드)
                if not isinstance(strategy, dict):
                    logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
                    logger.error(f"❌ [{symbol}] strategy 내용: {strategy}")
                    return {
                        'mode': 'simulation',
                        'status': 'error',
                        'timestamp': int(time.time()),
                        'error': f"Invalid strategy type: {type(strategy)}, expected dict"
                    }

                # 거래 실행 모드 확인
                trading_mode = self.config.get('trading_mode', 'real')
                # 🔧 SELA 결과에서 필드 추출 (type → action, strategy_id → id 변환)
                strategy_id = strategy.get('id') or strategy.get('strategy_id', 'unknown')
                action = strategy.get('action') or strategy.get('type', 'unknown')

                if trading_mode == 'real':
                    logger.info(f"{symbol} 실제 거래 실행: {strategy_id}")
                else:
                    logger.info(f"{symbol} 시뮬레이션 거래 실행: {strategy_id}")

                # 🚀 실제 시뮬레이션 거래 실행 구현
                try:
                    # 포트폴리오 매니저를 통한 시뮬레이션 거래 실행
                    if hasattr(self, 'portfolio') and self.portfolio:
                        # 🔧 SELA 결과를 포트폴리오 호환 형식으로 변환
                        portfolio_strategy = strategy.copy()
                        if 'action' not in portfolio_strategy and 'type' in portfolio_strategy:
                            portfolio_strategy['action'] = portfolio_strategy['type']

                        # 🔧 direction 키 명시적 추가 (portfolio.py 호환성)
                        if 'direction' not in portfolio_strategy:
                            action = portfolio_strategy.get('action') or portfolio_strategy.get('type', 'unknown')
                            portfolio_strategy['direction'] = action
                            logger.info(f"🔧 [{symbol}] direction 키 추가: {action}")

                        # 실제 거래 vs 시뮬레이션 모드 분기
                        logger.info(f"🔧 [{symbol}] 포트폴리오 전략 실행 시작: {action} (모드: {trading_mode})")
                        binance_utils_obj = self.exchange if trading_mode == 'real' else None
                        execution_result = self.portfolio.execute_strategy(
                            symbol=symbol,
                            strategy=portfolio_strategy,
                            market_data=market_data,
                            binance_utils=binance_utils_obj
                        )

                        if execution_result:
                            mode_text = "실제" if trading_mode == 'real' else "시뮬레이션"
                            logger.info(f"{symbol} {mode_text} 거래 실행 성공: {execution_result}")
                            # 🔧 market_data 타입 검증 후 안전한 접근
                            market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                            return {
                                'mode': trading_mode,
                                'status': 'executed' if trading_mode == 'real' else 'simulated',
                                'timestamp': int(time.time()),
                                'price': market_price,
                                'execution_result': execution_result,
                                'details': {
                                    'entry_price': market_price,
                                    'direction': strategy.get('type', strategy.get('direction', 'unknown')),
                                    'position_size': strategy.get('position_size', strategy.get('size', 0)),
                                    'stop_loss': strategy.get('stop_loss', 0),
                                    'take_profit': strategy.get('take_profit', 0),
                                    'simulation_id': execution_result.get('position_id', f"sim_{symbol}_{int(time.time())}")
                                }
                            }
                        else:
                            mode_text = "실제" if trading_mode == 'real' else "시뮬레이션"
                            logger.error(f"{symbol} {mode_text} 거래 실행 실패")
                            # 🔧 market_data 타입 검증 후 안전한 접근
                            market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                            return {
                                'mode': trading_mode,
                                'status': 'failed',
                                'timestamp': int(time.time()),
                                'price': market_price,
                                'error': 'Portfolio simulation failed'
                            }
                    else:
                        # 포트폴리오 매니저가 없으면 기본 시뮬레이션 결과 생성
                        logger.warning(f"{symbol} 포트폴리오 매니저가 없어 기본 시뮬레이션 결과 생성")
                        # 🔧 market_data 타입 검증 후 안전한 접근
                        market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                        return {
                            'mode': 'simulation',
                            'status': 'simulated',
                            'timestamp': int(time.time()),
                            'price': market_price,
                            'details': {
                                'entry_price': market_price,
                                'direction': strategy.get('type', strategy.get('direction', 'unknown')),
                                'position_size': strategy.get('position_size', strategy.get('size', 0)),
                                'stop_loss': strategy.get('stop_loss', 0),
                                'take_profit': strategy.get('take_profit', 0),
                                'simulation_id': f"sim_{symbol}_{int(time.time())}"
                            }
                        }

                except Exception as e:
                    mode_text = "실제" if trading_mode == 'real' else "시뮬레이션"
                    logger.error(f"{symbol} {mode_text} 거래 실행 중 오류: {str(e)}")
                    # 🔧 market_data 타입 검증 후 안전한 접근
                    market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                    return {
                        'mode': trading_mode,
                        'status': 'error',
                        'timestamp': int(time.time()),
                        'price': market_price,
                        'error': str(e)
                    }

        except Exception as e:
            logger.error(f"{symbol} 전략 실행 오류: {str(e)}")
            return None

    def _should_execute_based_on_consensus(self, final_signal: str, consensus_confidence: float, symbol: str = None, market_data: Dict[str, Any] = None, consensus_result: Dict[str, Any] = None) -> bool:
        """
        계층적 합의 신호를 기반으로 거래 실행 여부 결정

        🚀 Updated: 2025-06-03 00:13 - 계층적 합의 시스템 통합

        Args:
            final_signal: 합의된 최종 신호 (strong_buy, buy, neutral, sell, strong_sell)
            consensus_confidence: 합의 신뢰도 (0.0 ~ 1.0)
            symbol: 심볼 (가상 포지션 생성용)
            market_data: 시장 데이터 (가상 포지션 생성용)
            consensus_result: 합의 결과 (가상 포지션 생성용)

        Returns:
            bool: 거래 실행 여부
        """
        try:
            # 🚀 LLM 완전 신뢰 모드: 임계값 제거, LLM 판단 100% 존중
            logger.info(f"🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중")

            # 🎯 강화된 신호 처리: 약한 신호도 고려
            weak_signals = ['weak_bullish', 'weak_bearish']
            if final_signal in weak_signals:
                logger.info(f"🤖 약한 신호 감지: {final_signal} - 낮은 투자 비중으로 거래 진행")
                return True  # 약한 신호도 거래 진행
            elif final_signal in ['neutral', 'none', 'hold']:
                logger.info(f"🤖 LLM이 {final_signal} 신호로 판단하여 거래하지 않음")

                # 🔮 중립 신호 시 가상 포지션 생성 (학습용)
                if symbol and market_data and consensus_result:
                    self._create_virtual_position_for_neutral_signal(symbol, market_data, consensus_result)
                else:
                    logger.debug("🔮 가상 포지션 생성 스킵: 필요한 매개변수 누락")

                return False

            # 🚀 실제 거래 신호인 경우에만 로그 출력
            logger.info(f"🤖 LLM 거래 신호({final_signal}) 감지 - 신뢰도({consensus_confidence:.3f})와 관계없이 실행")

            # 🚀 LLM이 거래 신호를 보냈다면 무조건 실행 (임계값 완전 제거)
            if final_signal in ['strong_buy', 'strong_sell', 'buy', 'sell']:
                logger.info(f"🤖 LLM 거래 신호({final_signal}) 실행 - 신뢰도: {consensus_confidence:.3f}")
                return True

            logger.info(f"합의 신호({final_signal}, 신뢰도: {consensus_confidence:.3f})가 거래 조건을 만족하지 않음")
            return False

        except Exception as e:
            logger.error(f"합의 기반 거래 결정 중 오류: {e}")
            return False  # 오류 시 안전하게 거래하지 않음

    def _create_virtual_position_for_neutral_signal(self, symbol: str, market_data: Dict[str, Any], consensus_result: Dict[str, Any]):
        """
        중립 신호 시 가상 포지션 생성 (학습용)

        Args:
            symbol: 심볼
            market_data: 시장 데이터
            consensus_result: 합의 결과
        """
        try:
            if not self.virtual_position_tracker:
                logger.debug(f"🔮 [{symbol}] 가상 포지션 추적기가 비활성화되어 있음")
                return

            # 현재 가격 가져오기
            current_price = market_data.get('price', 0)
            if current_price <= 0:
                logger.warning(f"🔮 [{symbol}] 유효하지 않은 가격으로 가상 포지션 생성 불가: {current_price}")
                return

            # InCA 결과에서 정보 추출
            inca_result = consensus_result.get('inca_result', {})
            inca_evaluation = inca_result.get('inca_evaluation', {})

            confidence = inca_evaluation.get('confidence', 0.5)
            importance = inca_evaluation.get('importance', 5)
            reasoning = inca_evaluation.get('reasoning', 'InCA 중립 신호')

            # 가상 포지션 생성
            virtual_position = self.virtual_position_tracker.create_virtual_position(
                symbol=symbol,
                current_price=current_price,
                prediction_type='neutral',  # 중립 예측
                confidence=confidence,
                importance=importance,
                market_data=market_data.copy(),
                inca_reasoning=reasoning,
                evaluation_duration=180  # 3분 후 평가 (다음 사이클)
            )

            logger.info(f"🔮 [{symbol}] 중립 신호 가상 포지션 생성 완료: "
                       f"가격=${current_price:.4f}, 신뢰도={confidence:.2f}, 중요도={importance}, 3분 후 평가")

        except Exception as e:
            logger.error(f"🔮 [{symbol}] 가상 포지션 생성 실패: {e}")

    def _evaluate_virtual_positions(self):
        """만료된 가상 포지션들을 평가하고 학습 데이터로 활용"""
        try:
            if not self.virtual_position_tracker:
                return

            # 현재 가격 정보 수집
            current_prices = {}
            for symbol in self.symbols:
                try:
                    if self.binance_utils:
                        price_data = self.binance_utils.get_symbol_price(symbol)
                        if price_data and 'price' in price_data:
                            current_prices[symbol] = float(price_data['price'])
                except Exception as e:
                    logger.warning(f"🔮 [{symbol}] 현재 가격 조회 실패: {e}")

            if not current_prices:
                logger.debug("🔮 현재 가격 정보가 없어 가상 포지션 평가 스킵")
                return

            # 만료된 포지션들 평가
            evaluated_results = self.virtual_position_tracker.evaluate_positions(current_prices)

            if not evaluated_results:
                return

            logger.info(f"🔮 가상 포지션 평가 완료: {len(evaluated_results)}개 포지션")

            # 평가 결과를 InCA 에이전트에 학습 데이터로 전달
            for result in evaluated_results:
                try:
                    if self.inca_agent:
                        learning_result = self.inca_agent.learn_from_virtual_position(result.to_dict())
                        if learning_result.get('success'):
                            logger.info(f"🔮 [{result.virtual_position.symbol}] 가상 포지션 학습 완료: "
                                       f"{result.prediction_accuracy} (가중치: {result.learning_weight:.3f})")
                        else:
                            logger.warning(f"🔮 [{result.virtual_position.symbol}] 가상 포지션 학습 실패: "
                                          f"{learning_result.get('message', 'Unknown error')}")
                except Exception as e:
                    logger.error(f"🔮 [{result.virtual_position.symbol}] 가상 포지션 학습 중 오류: {e}")

            # 통계 출력
            stats = self.virtual_position_tracker.get_statistics()
            if stats['total_positions'] > 0:
                logger.info(f"🔮 가상 포지션 통계: 총 {stats['total_positions']}개, "
                           f"전체 정확도 {stats['overall_accuracy']:.1%}, "
                           f"중립 정확도 {stats['neutral_predictions']['accuracy']:.1%}")

        except Exception as e:
            logger.error(f"🔮 가상 포지션 평가 중 오류: {e}")

    def _execute_strategy(self, symbol: str, strategy: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        전략 실행 (실제 거래 또는 시뮬레이션)

        Args:
            symbol: 대상 심볼
            strategy: 전략 정보
            market_data: 시장 데이터

        Returns:
            실행 결과 딕셔너리
        """
        try:
            # 실행 모드 확인
            # 🔧 trading_mode 기본값을 'real'로 변경 (바이낸스 연결 활성화)
            execution_mode = self.config.get('trading_mode', 'real')
            logger.info(f"🔧 [{symbol}] 실행 모드 확인: {execution_mode}")

            if execution_mode == 'real':
                # 실제 거래 실행
                logger.info(f"[{symbol}] 실제 거래 실행: {strategy.get('action', 'unknown')}")

                # 🚀 실제 거래 API 호출 구현
                try:
                    # 포트폴리오 매니저를 통한 실제 거래 실행
                    if hasattr(self, 'portfolio') and self.portfolio:
                        # 🔧 strategy 타입 검증 및 수정
                        if not isinstance(strategy, dict):
                            logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
                            logger.error(f"❌ [{symbol}] strategy 내용: {strategy}")
                            return {
                                'mode': 'real',
                                'status': 'error',
                                'timestamp': int(time.time()),
                                'error': f"Invalid strategy type: {type(strategy)}, expected dict"
                            }

                        # 🔥 전략에 심볼 정보 추가 (ExtendedPortfolio.execute_strategy 호환성)
                        strategy_with_symbol = strategy.copy()
                        strategy_with_symbol['symbol'] = symbol

                        # 🔧 SELA 결과를 포트폴리오 호환 형식으로 변환
                        if 'action' not in strategy_with_symbol and 'type' in strategy_with_symbol:
                            strategy_with_symbol['action'] = strategy_with_symbol['type']

                        # 🔧 direction 키 명시적 추가 (portfolio.py 호환성)
                        if 'direction' not in strategy_with_symbol:
                            action = strategy_with_symbol.get('action') or strategy_with_symbol.get('type', 'unknown')
                            strategy_with_symbol['direction'] = action
                            logger.info(f"🔧 [{symbol}] direction 키 추가: {action}")

                        action = strategy_with_symbol.get('action') or strategy_with_symbol.get('type', 'unknown')
                        logger.info(f"🔧 [{symbol}] 포트폴리오 실제 거래 실행 시작: {action}")
                        # 🔧 바이낸스 연결 확인 및 전달
                        exchange_obj = getattr(self, 'exchange', None)
                        logger.info(f"🔧 [{symbol}] 바이낸스 연결 상태 확인: {exchange_obj is not None}")
                        if exchange_obj:
                            logger.info(f"🔧 [{symbol}] 바이낸스 연결 타입: {type(exchange_obj)}")

                        execution_result = self.portfolio.execute_strategy(
                            symbol=symbol,
                            strategy=strategy_with_symbol,
                            market_data=market_data,
                            binance_utils=exchange_obj
                        )

                        if execution_result and execution_result.get('success', False):
                            # 🔥 같은 방향 포지션으로 인한 거래 스킵 처리
                            if execution_result.get('skipped_trade', False):
                                logger.info(f"[{symbol}] 같은 방향 포지션으로 거래 스킵: {execution_result.get('message', 'Unknown reason')}")
                                # 🔧 market_data 타입 검증 후 안전한 접근
                                market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                                return {
                                    'mode': 'real',
                                    'status': 'skipped',
                                    'timestamp': int(time.time()),
                                    'price': market_price,
                                    'details': execution_result,
                                    'reason': 'same_direction_position'
                                }
                            else:
                                logger.info(f"[{symbol}] 실제 거래 실행 성공: {execution_result.get('order_id', 'unknown')}")
                                # 🔧 market_data 타입 검증 후 안전한 접근
                                market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                                return {
                                    'mode': 'real',
                                    'status': 'executed',
                                    'timestamp': int(time.time()),
                                    'price': market_price,
                                    'details': execution_result
                                }
                        else:
                            logger.error(f"[{symbol}] 실제 거래 실행 실패: {execution_result.get('error', 'Unknown error')}")
                            return {
                                'mode': 'real',
                                'status': 'failed',
                                'timestamp': int(time.time()),
                                'error': execution_result.get('error', 'Unknown error')
                            }
                    else:
                        logger.warning(f"[{symbol}] 포트폴리오 매니저가 없어 실제 거래 실행 불가")
                        return {
                            'mode': 'real',
                            'status': 'failed',
                            'timestamp': int(time.time()),
                            'error': 'No portfolio manager'
                        }

                except Exception as e:
                    logger.error(f"[{symbol}] 실제 거래 실행 중 오류: {str(e)}")
                    return {
                        'mode': 'real',
                        'status': 'failed',
                        'timestamp': int(time.time()),
                        'error': str(e)
                    }
            else:
                # 🔧 strategy 타입 검증 및 수정 (시뮬레이션 모드)
                if not isinstance(strategy, dict):
                    logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
                    logger.error(f"❌ [{symbol}] strategy 내용: {strategy}")
                    return {
                        'mode': 'simulation',
                        'status': 'error',
                        'timestamp': int(time.time()),
                        'error': f"Invalid strategy type: {type(strategy)}, expected dict"
                    }

                # 폴백 모드 (실제 거래 실행 실패 시)
                # 🔧 SELA 결과에서 action 필드 추출 (type → action 변환)
                action = strategy.get('action') or strategy.get('type', 'unknown')
                trading_mode = self.config.get('trading_mode', 'real')
                mode_text = "실제" if trading_mode == 'real' else "시뮬레이션"
                logger.info(f"[{symbol}] {mode_text} 거래 실행 (폴백): {action}")

                # 거래 실행 (폴백)
                # 🔧 market_data 타입 검증 후 안전한 접근
                market_price = market_data.get('price', 0) if isinstance(market_data, dict) else 0
                return {
                    'mode': trading_mode,
                    'status': 'executed' if trading_mode == 'real' else 'simulated',
                    'timestamp': int(time.time()),
                    'price': market_price,
                    'details': {
                        'order_id': f"sim_{symbol}_{int(time.time())}",
                        'entry_price': market_price,
                        'direction': action,
                        'position_size': strategy.get('position_size', 0),
                        'stop_loss': strategy.get('stop_loss', 0),
                        'take_profit': strategy.get('take_profit', 0)
                    }
                }

        except Exception as e:
            logger.error(f"[{symbol}] 전략 실행 중 오류: {str(e)}")
            return {
                'mode': execution_mode,
                'status': 'failed',
                'timestamp': int(time.time()),
                'error': str(e)
            }

    def log_strategy_execution(self, symbol: str, strategy: Dict[str, Any],
                              execution_result: Dict[str, Any], market_data: Dict[str, Any],
                              reasoning_card: Dict[str, Any]) -> None:
        """
        전략 실행 결과 로깅

        Args:
            symbol: 대상 심볼
            strategy: 전략 정보
            execution_result: 실행 결과
            market_data: 시장 데이터
            reasoning_card: 추론 카드
        """
        try:
            # 로그 데이터 구성
            log_data = {
                'timestamp': int(time.time()),
                'symbol': symbol,
                'strategy': strategy,
                'execution_result': execution_result,
                'market_data': market_data,
                'reasoning_card': reasoning_card
            }

            # 로그 디렉토리 확인 및 생성
            log_dir = os.path.join(self.data_dir, 'execution_logs')
            os.makedirs(log_dir, exist_ok=True)

            # 로그 파일 경로
            log_file = os.path.join(log_dir, f"{symbol}_execution_log_{int(time.time())}.json")

            # 로그 파일 저장
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            logger.info(f"{symbol} 전략 실행 로그 저장 완료: {log_file}")

        except Exception as e:
            logger.error(f"{symbol} 전략 실행 로그 저장 중 오류 발생: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _close_real_binance_position(self, symbol: str, reason: str) -> None:
        """실제 바이낸스 포지션 종료"""
        try:
            logger.info(f"🔄 [{symbol}] 실제 바이낸스 포지션 종료 시작: {reason}")

            # 바이낸스 유틸리티가 있는지 확인
            if not hasattr(self, 'binance_utils') or self.binance_utils is None:
                if hasattr(self.portfolio, 'binance_utils') and self.portfolio.binance_utils:
                    self.binance_utils = self.portfolio.binance_utils
                else:
                    logger.warning(f"⚠️ [{symbol}] 바이낸스 유틸리티 없음 - 실제 포지션 종료 불가")
                    return

            # 현재 바이낸스 포지션 조회 (올바른 메서드 사용)
            binance_symbol = f"{symbol}USDT"
            positions = self.binance_utils.get_positions()

            if not positions:
                logger.warning(f"⚠️ [{symbol}] 바이낸스 포지션 조회 실패")
                return

            # 해당 심볼의 열린 포지션 찾기
            target_positions = []
            for position in positions:
                # 포지션 데이터 구조 확인 (position_amount 또는 positionAmt)
                position_amt = float(position.get('position_amount', position.get('positionAmt', 0)))
                if position.get('symbol') == binance_symbol and position_amt != 0:
                    target_positions.append(position)

            if not target_positions:
                logger.info(f"📊 [{symbol}] 바이낸스에 열린 포지션 없음")
                return

            # 각 포지션 종료
            for position in target_positions:
                # 포지션 데이터 구조 확인
                position_amt = float(position.get('position_amount', position.get('positionAmt', 0)))
                entry_price = float(position.get('entry_price', position.get('entryPrice', 0)))
                unrealized_pnl = float(position.get('unrealized_pnl', position.get('unRealizedPnl', 0)))
                quantity = abs(position_amt)

                logger.info(f"🎯 [{symbol}] 바이낸스 포지션 종료: {quantity} (진입가: ${entry_price}, PnL: ${unrealized_pnl})")

                # 포지션 방향에 따른 전용 종료 메서드 사용
                if position_amt > 0:  # Long 포지션
                    logger.info(f"📊 [{symbol}] 롱 포지션 종료 중...")
                    order_result = self.binance_utils.close_long_position(symbol, quantity)
                elif position_amt < 0:  # Short 포지션
                    logger.info(f"📊 [{symbol}] 숏 포지션 종료 중...")
                    order_result = self.binance_utils.close_short_position(symbol, quantity)
                else:
                    continue

                if order_result and 'error' not in order_result:
                    order_id = order_result.get('orderId', order_result.get('order_id', 'N/A'))
                    position_type = "롱" if position_amt > 0 else "숏"
                    logger.info(f"✅ [{symbol}] 바이낸스 {position_type} 포지션 종료 성공: 주문ID {order_id}")
                    logger.info(f"📊 [{symbol}] 종료된 포지션: {quantity} {symbol} ({position_type})")
                else:
                    error_msg = order_result.get('error', str(order_result)) if order_result else "주문 결과 없음"
                    logger.error(f"❌ [{symbol}] 바이낸스 포지션 종료 실패: {error_msg}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 실제 바이낸스 포지션 종료 실패: {e}")

    def _check_opposite_position_entry(self, symbol: str, position_importance: Dict[str, Any], market_data: Dict[str, Any]) -> None:
        """
        포지션 클로즈 후 반대 방향 포지션 진입 체크 및 실행

        Args:
            symbol: 대상 심볼
            position_importance: InCA 포지션 중요도 평가 결과
            market_data: 시장 데이터
        """
        try:
            logger.info(f"🔄 [{symbol}] 반대 방향 포지션 진입 체크 시작")

            # InCA 신호 확인
            situation_type = position_importance.get("situation_type", "neutral")
            signal_direction = position_importance.get("signal_direction", "neutral")
            confidence = position_importance.get("confidence", 0.5)

            # 🔧 중립 신호에서는 반대 포지션 진입하지 않음
            if signal_direction == "neutral" or situation_type == "neutral":
                logger.info(f"🛡️ [{symbol}] 중립 신호로 반대 포지션 진입 건너뜀: signal={signal_direction}, situation={situation_type}")
                return

            logger.info(f"🔍 [{symbol}] InCA 신호: {situation_type}, 방향: {signal_direction}, 신뢰도: {confidence:.3f}")

            # bearish/bullish 신호에 따른 반대 방향 포지션 진입 요청 저장 (사이클 마지막에 처리)
            opposite_action = None
            if situation_type in ["should_close", "should_close_loss"] and signal_direction == "bearish":
                opposite_action = "sell"  # SHORT 포지션
                logger.info(f"🎯 [{symbol}] bearish 신호 감지 → SHORT 포지션 진입 요청 저장 (사이클 마지막에 처리)")
            elif situation_type in ["should_close", "should_close_loss"] and signal_direction == "bullish":
                opposite_action = "buy"   # LONG 포지션
                logger.info(f"🎯 [{symbol}] bullish 신호 감지 → LONG 포지션 진입 요청 저장 (사이클 마지막에 처리)")
            else:
                logger.info(f"📊 [{symbol}] 반대 방향 진입 조건 미충족: {situation_type}, {signal_direction}")
                return

            # 신뢰도 임계값 체크 (0.6 이상에서만 반대 포지션 진입)
            if confidence < 0.6:
                logger.warning(f"⚠️ [{symbol}] 신뢰도 부족으로 반대 포지션 진입 건너뜀: {confidence:.3f} < 0.6")
                return

            # 반대 방향 포지션 진입 요청을 포트폴리오에 저장 (사이클 마지막에 처리)
            logger.info(f"🚀 [{symbol}] 반대 방향 포지션 진입 요청 저장: {opposite_action}")

            # 🔧 최소 주문 금액 계산 (바이낸스 요구사항)
            current_price = market_data.get("price", 0)
            min_notional = 5.0  # 최소 $5 주문
            min_quantity = min_notional / current_price if current_price > 0 else 0.01

            # 심볼별 최소 수량 조정
            if symbol == "SOL":
                min_quantity = max(min_quantity, 0.04)  # SOL 최소 0.04개
            elif symbol == "DOGE":
                min_quantity = max(min_quantity, 30.0)  # DOGE 최소 30개
            elif symbol == "ETH":
                min_quantity = max(min_quantity, 0.003)  # ETH 최소 0.003개
            elif symbol == "BNB":
                min_quantity = max(min_quantity, 0.008)  # BNB 최소 0.008개

            logger.info(f"🔧 [{symbol}] 최소 주문 수량 계산: {min_quantity:.6f} (가격: ${current_price:.2f})")

            # 반대 방향 전략 데이터 구성
            opposite_strategy = {
                "symbol": symbol,
                "type": opposite_action,
                "action": opposite_action,
                "direction": "short" if opposite_action == "sell" else "long",
                "entry_price": current_price,
                "quantity": min_quantity,  # 최소 주문 수량 추가
                "confidence": confidence,
                "reasoning": f"포지션 클로즈 후 {signal_direction} 신호에 따른 반대 방향 진입",
                "strategy_id": f"opposite_{symbol}_{int(time.time())}",
                "timestamp": int(time.time())
            }

            logger.info(f"🎯 [{symbol}] 반대 방향 전략 생성 완료: {opposite_strategy['direction']} @ ${opposite_strategy['entry_price']}")

            # 포트폴리오에 반대 방향 포지션 요청 저장 (사이클 마지막에 처리)
            if hasattr(self, 'portfolio') and self.portfolio:
                logger.info(f"🔧 [{symbol}] 반대 방향 포지션 진입 요청 저장 (사이클 마지막에 처리)")

                # 바이낸스 연결 확인
                exchange_obj = getattr(self, 'exchange', None)

                # 포지션 변화 요청 저장
                self.portfolio._store_pending_position_change(
                    symbol,
                    opposite_strategy['direction'],
                    market_data,
                    exchange_obj,
                    {'direction': 'opposite', 'strategy': opposite_strategy}
                )
                logger.info(f"📝 [{symbol}] 반대 방향 포지션 진입 요청 저장 완료: {opposite_strategy['direction']}")
            else:
                logger.warning(f"⚠️ [{symbol}] 포트폴리오 매니저 없음 - 반대 방향 진입 요청 저장 불가")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 반대 방향 포지션 진입 체크 중 오류: {e}")

    def _extract_learning_data(self, inca_result: Dict[str, Any], sela_result: Dict[str, Any],
                              market_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """
        실제 InCA, SELA, 장기 분석 데이터를 추출하여 학습용 데이터 구조 생성

        Args:
            inca_result: InCA 분석 결과
            sela_result: SELA 전략 결과
            market_data: 시장 데이터
            symbol: 심볼

        Returns:
            Dict[str, Any]: 학습용 합의 breakdown 데이터
        """
        try:
            # 1. InCA 단기 데이터 추출
            short_term_data = self._extract_inca_learning_data(inca_result)

            # 2. SELA 중기 데이터 추출
            medium_term_data = self._extract_sela_learning_data(sela_result)

            # 3. 장기 추세 데이터 추출
            long_term_data = self._extract_long_term_learning_data(market_data)

            learning_breakdown = {
                'short_term': short_term_data,
                'medium_term': medium_term_data,
                'long_term': long_term_data
            }

            self.logger.info(f"[{symbol}] 실제 학습 데이터 추출 완료: "
                           f"InCA={short_term_data['action']}, "
                           f"SELA={medium_term_data['action']}, "
                           f"장기={long_term_data['action']}")

            return learning_breakdown

        except Exception as e:
            self.logger.error(f"[{symbol}] 학습 데이터 추출 중 오류: {e}")
            # 오류 시 기본 구조 반환
            return {
                'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
            }

    def _extract_inca_learning_data(self, inca_result: Dict[str, Any]) -> Dict[str, Any]:
        """InCA 결과에서 학습용 데이터 추출"""
        try:
            # InCA 결과에서 실제 데이터 추출
            inca_evaluation = inca_result.get('inca_evaluation', {})

            action = inca_evaluation.get('action_recommendation', inca_result.get('action_recommendation', 'hold'))
            situation = inca_evaluation.get('situation_type', inca_result.get('situation_type', 'neutral'))
            importance_raw = inca_evaluation.get('importance', inca_result.get('importance', 5))
            confidence = inca_evaluation.get('confidence', inca_result.get('confidence', 0.5))

            # importance 정규화 (1-10 → 0-1)
            importance = importance_raw / 10.0 if isinstance(importance_raw, (int, float)) else 0.5

            return {
                'action': action,
                'situation': situation,
                'importance': importance,
                'confidence': confidence,
                'source': 'InCA',
                'timeframe': '1분봉'
            }

        except Exception as e:
            self.logger.warning(f"InCA 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'hold',
                'situation': 'neutral',
                'importance': 0.5,
                'confidence': 0.5,
                'source': 'InCA',
                'timeframe': '1분봉'
            }

    def _extract_sela_learning_data(self, sela_result: Dict[str, Any]) -> Dict[str, Any]:
        """SELA 결과에서 학습용 데이터 추출"""
        try:
            action = sela_result.get('action', 'none')
            strategy_type = sela_result.get('type', 'none')
            confidence = sela_result.get('confidence', 0.5)

            # SELA의 tree_search_score를 importance로 사용 (정규화)
            tree_search_score = sela_result.get('tree_search_score', 5.0)
            importance = tree_search_score / 10.0 if isinstance(tree_search_score, (int, float)) else 0.5

            return {
                'action': action,
                'type': strategy_type,
                'importance': importance,
                'confidence': confidence,
                'source': 'SELA',
                'timeframe': '1시간봉'
            }

        except Exception as e:
            self.logger.warning(f"SELA 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'none',
                'type': 'none',
                'importance': 0.5,
                'confidence': 0.5,
                'source': 'SELA',
                'timeframe': '1시간봉'
            }

    def _extract_long_term_learning_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """시장 데이터에서 장기 추세 학습용 데이터 추출"""
        try:
            # 일봉 데이터가 있으면 추세 분석
            daily_candles = market_data.get('daily_candles', [])

            if not daily_candles or len(daily_candles) < 7:
                return {
                    'action': 'neutral',
                    'trend': 'sideways',
                    'trend_change_pct': 0.0,
                    'importance': 0.5,
                    'confidence': 0.3,
                    'source': 'LongTerm',
                    'timeframe': '일봉',
                    'note': '일봉 데이터 부족'
                }

            # 7일 추세 변화율 계산
            first_candle = daily_candles[0]
            last_candle = daily_candles[-1]

            if isinstance(first_candle, list) and isinstance(last_candle, list):
                first_close = float(first_candle[4])
                last_close = float(last_candle[4])
            else:
                first_close = float(first_candle.get('close', 0))
                last_close = float(last_candle.get('close', 0))

            if first_close > 0:
                trend_change_pct = ((last_close - first_close) / first_close) * 100
            else:
                trend_change_pct = 0.0

            # 추세에 따른 액션 및 중요도 결정
            if trend_change_pct > 5:
                action = 'strong_buy'
                trend = 'strong_uptrend'
                importance = 0.8
                confidence = 0.8
            elif trend_change_pct > 2:
                action = 'buy'
                trend = 'uptrend'
                importance = 0.65
                confidence = 0.7
            elif trend_change_pct < -5:
                action = 'strong_sell'
                trend = 'strong_downtrend'
                importance = 0.8
                confidence = 0.8
            elif trend_change_pct < -2:
                action = 'sell'
                trend = 'downtrend'
                importance = 0.65
                confidence = 0.7
            else:
                action = 'neutral'
                trend = 'sideways'
                importance = 0.5
                confidence = 0.6

            return {
                'action': action,
                'trend': trend,
                'trend_change_pct': trend_change_pct,
                'importance': importance,
                'confidence': confidence,
                'source': 'LongTerm',
                'timeframe': '일봉'
            }

        except Exception as e:
            self.logger.warning(f"장기 추세 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'neutral',
                'trend': 'sideways',
                'trend_change_pct': 0.0,
                'importance': 0.5,
                'confidence': 0.3,
                'source': 'LongTerm',
                'timeframe': '일봉',
                'error': str(e)
            }

    def _add_to_learning_queue(self, record: Dict[str, Any]) -> None:
        """
        학습 큐에 레코드 추가

        Args:
            record: 학습 큐에 추가할 레코드
        """
        try:
            # learning_queue가 없으면 초기화
            if not hasattr(self, 'learning_queue'):
                self.learning_queue = []

            # 중복 체크 (timestamp와 symbol로 구분)
            timestamp = record.get('timestamp', 0)
            symbol = record.get('symbol', '')

            # 이미 동일한 레코드가 있는지 확인
            if any(r.get('timestamp') == timestamp and r.get('symbol') == symbol for r in self.learning_queue):
                return

            # 최대 큐 크기 제한
            max_queue_size = self.config.get('max_learning_queue_size', 100)

            if len(self.learning_queue) >= max_queue_size:
                self.learning_queue.pop(0)  # 가장 오래된 항목 제거

            self.learning_queue.append(record)
            self.logger.debug(f"[{symbol}] 학습 큐에 추가됨 (큐 크기: {len(self.learning_queue)})")

        except Exception as e:
            self.logger.error(f"학습 큐 추가 중 오류: {str(e)}")

    def _check_new_position_creation(self, symbol: str, market_data: Dict[str, Any], news_data: Dict[str, Any]) -> None:
        """포지션이 없을 때 새 포지션 생성 검토"""
        try:
            logger.info(f"🔍 [{symbol}] 새 포지션 생성 검토 시작")

            # 1. InCA 시장 중요도 평가
            inca_result = self._execute_inca_sequential(symbol, market_data, news_data)

            if not inca_result:
                logger.warning(f"⚠️ [{symbol}] InCA 결과 없음 - 새 포지션 생성 건너뜀")
                return

            inca_action = inca_result.get('action_recommendation', 'hold')
            importance_score = inca_result.get('importance', 5)

            logger.info(f"🔍 [{symbol}] InCA 결과: {inca_action} (중요도: {importance_score})")

            # 2. 명확한 buy/sell 신호이고 중요도가 높을 때만 새 포지션 생성
            if inca_action in ['buy', 'sell'] and importance_score > 8.0:
                logger.info(f"🚀 [{symbol}] 새 포지션 생성 조건 충족: {inca_action} (중요도: {importance_score})")

                # 3. SELA 전략 생성 및 실행
                sela_result = self._execute_sela_sequential(symbol, market_data, news_data, inca_result)

                if sela_result and sela_result.get('type') == inca_action:
                    logger.info(f"✅ [{symbol}] SELA 전략과 InCA 신호 일치: {inca_action}")

                    # 4. 실제 포지션 생성 실행
                    strategy = {
                        'type': inca_action,
                        'confidence': sela_result.get('confidence', 0.8),
                        'entry_price': market_data.get('price'),
                        'stop_loss': sela_result.get('stop_loss'),
                        'take_profit': sela_result.get('take_profit')
                    }

                    execution_result = self.execute_strategy(symbol, strategy, market_data)

                    if execution_result and execution_result.get('success'):
                        logger.info(f"🎯 [{symbol}] 새 포지션 생성 성공: {inca_action}")
                    else:
                        logger.error(f"❌ [{symbol}] 새 포지션 생성 실패: {execution_result}")
                else:
                    logger.warning(f"⚠️ [{symbol}] SELA 전략과 InCA 신호 불일치 - 포지션 생성 건너뜀")
            else:
                logger.info(f"📊 [{symbol}] 새 포지션 생성 조건 미충족: {inca_action} (중요도: {importance_score})")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 새 포지션 생성 검토 실패: {e}")


class HybridControllerExtended(HybridController):
    def __init__(self,
                 inca_agent: InCAAgent = None,
                 hiar_agent = None,  # HiAR 에이전트 (팩토리에서 생성)
                 sela_agent: SELAAgent = None,
                 data_store: DataStore = None,
                 execution_logger: DataLogger = None,
                 config: Dict[str, Any] = None,
                 binance_utils: BinanceUtils = None):
        super().__init__(inca_agent, hiar_agent, sela_agent, data_store, execution_logger, config, binance_utils)

        # logger 인스턴스 속성으로 설정 (부모 클래스에서 설정되지만 명시적으로 재설정)
        self.logger = logger

        # LLM 클라이언트 초기화
        openai_api_key = os.environ.get("OPENAI_API_KEY")
        if not openai_api_key:
            self.logger.error("OPENAI_API_KEY 환경 변수가 설정되지 않았습니다.")
            raise ValueError("OPENAI_API_KEY 환경 변수가 필요합니다.")

        # 시뮬레이터 초기화
        self.simulator = ExecutionFlowSimulator(
            llm_client=None,  # LLM 클라이언트 대신 내부 로직 사용
            importance_threshold=self.config.get('importance_threshold', 0.3),  # 🎯 시뮬레이터 임계값도 완화
            sela_agent=self.sela_agent  # SELA 에이전트 전달
        )

        # LunarCrush 데이터 수집기 초기화
        self.lunar_collector = LunarDataCollector()

        # Exchange API 초기화 (Binance)
        self.exchange = None
        # 🔧 trading_mode 기본값을 'real'로 변경 (바이낸스 연결 활성화)
        trading_mode = self.config.get('trading_mode', 'real')
        logger.info(f"🔧 거래 모드 확인: {trading_mode}")
        if trading_mode == 'real':
            try:
                from simulator.exchange.binance_client import BinanceClient

                # 바이낸스 API 키
                binance_api_key = os.environ.get("BINANCE_API_KEY")
                binance_api_secret = os.environ.get("BINANCE_API_SECRET")

                if not binance_api_key or not binance_api_secret:
                    self.logger.error("Binance API 키가 설정되지 않았습니다.")
                    raise ValueError("Binance API 키가 필요합니다.")

                # 테스트넷/메인넷 설정
                testnet = self.config.get('testnet', True)  # 기본값은 테스트넷

                self.exchange = BinanceClient(
                    api_key=binance_api_key,
                    api_secret=binance_api_secret,
                    testnet=testnet
                )

                self.logger.info(f"바이낸스 클라이언트 초기화 완료 (테스트넷: {testnet})")
            except Exception as e:
                self.logger.error(f"바이낸스 클라이언트 초기화 실패: {str(e)}")
                raise

        # 포트폴리오 관리자 초기화
        from simulator.trading.portfolio import ExtendedPortfolio

        portfolio_path = self.config.get('portfolio_path', './data/portfolio_data.json')
        # 🔧 바이낸스 연결을 포트폴리오에 전달
        self.portfolio = ExtendedPortfolio(
            base_currency=self.config.get('base_currency', 'USDT'),
            initial_balance=self.config.get('initial_balance', 10000),
            save_path=portfolio_path,
            max_positions=self.config.get('max_positions', 5),
            mode=trading_mode,  # 거래 모드 전달
            testnet=self.config.get('testnet', True),  # 테스트넷 설정 전달
            binance_utils=self.exchange  # 바이낸스 연결 전달
        )

        # 벡터 데이터베이스 초기화 (ChromaDB)
        import chromadb
        from simulator.db.vector_store import VectorStore

        db_path = self.config.get('db_path', './chroma_db')  # 학습 루프와 같은 경로 사용
        try:
            self.vector_db = VectorStore(db_path)
            self.logger.info(f"벡터 데이터베이스 초기화 완료 (경로: {db_path})")
        except Exception as e:
            self.logger.error(f"벡터 데이터베이스 초기화 실패: {str(e)}")
            raise

        # 학습 큐 초기화
        self.learning_queue = self.config.get('learning_queue', [])

        # 시작 시간과 최근 실행 시간 초기화
        self.start_time = time.time()
        self.last_execution_time = self.start_time

    def run(self):
        """메인 실행 루프"""
        try:
            self.logger.info("하이브리드 아키텍처 컨트롤러 시작")

            self._initialize_resources()

            # 메인 루프
            while True:
                # 1. 연속 모드에서는 interval 체크 제거
                current_time = time.time()

                # 연속 모드가 아닌 경우에만 실행 주기 대기
                if not self.continuous_mode:
                    time_since_last = current_time - self.last_execution_time
                    if time_since_last < self.config.get('execution_interval', 180):  # 기본 3분
                        sleep_time = max(1, self.config.get('execution_interval', 180) - time_since_last)
                        time.sleep(sleep_time)
                        continue

                self.last_execution_time = time.time()

                # 2. 감시 대상 코인 가져오기
                watch_symbols = self.config.get('watch_symbols', ['BTC', 'ETH'])

                # 🔧 3.0 전체 심볼에 대해 한 번만 포지션 동기화 실행
                if self.portfolio:
                    # 전체 시장 데이터 수집
                    all_market_data = {}
                    for symbol in watch_symbols:
                        all_market_data[symbol] = self._fetch_market_data(symbol)

                    # 🔧 바이낸스 연결을 항상 전달 (trading_mode가 real인 경우)
                    trading_mode = self.config.get('trading_mode', 'real')
                    closed_positions = self.portfolio.update_positions(
                        market_data=all_market_data,  # 전체 심볼 데이터 전달
                        binance_utils=self.exchange if trading_mode == 'real' else None
                    )

                    # 종료된 포지션이 있으면 학습 큐에 추가
                    for position in closed_positions:
                        self._add_to_learning_queue(position)

                # 3. 각 코인에 대해 실행 흐름 처리
                for symbol in watch_symbols:
                    try:
                        # 3.1 시장 데이터는 이미 수집됨 (위에서)
                        market_data = all_market_data.get(symbol, self._fetch_market_data(symbol))

                        # 3.2 시장 데이터 가져오기
                        market_data = self._fetch_market_data(symbol)

                        # 3.3 뉴스 데이터 가져오기
                        news_data = self._fetch_news_data(symbol)

                        # 3.4 소셜 데이터 가져오기
                        social_data = self._fetch_social_data(symbol)

                        # 3.5 실행 플로우 처리
                        execution_result = self._process_execution_flow(symbol, market_data, news_data, social_data)

                        # 3.6 플로우가 완료되면 바로 학습 루프 실행 (각 코인 처리 후)
                        if execution_result and execution_result.get('completed', False):
                            try:
                                self._process_learning_loop(symbol_filter=symbol)  # 현재 처리한 심볼에 대해서만 학습
                            except Exception as e:
                                self.logger.error(f"[{symbol}] 학습 루프 처리 중 오류: {str(e)}")

                    except Exception as e:
                        self.logger.error(f"[{symbol}] 처리 중 오류: {str(e)}")

                # 4. 추가로 주기적인 전체 학습 루프도 유지 (모든 데이터에 대한 전체적인 학습용)
                learning_interval = self.config.get('learning_interval', 3600)  # 기본 1시간
                if current_time - self.start_time > learning_interval:
                    try:
                        self._process_learning_loop()  # 전체 학습
                        self.start_time = current_time  # 학습 타이머 리셋
                    except Exception as e:
                        self.logger.error(f"정기 학습 루프 처리 중 오류: {str(e)}")

                # 5. 메모리 정리 및 로그 출력
                self._cleanup_resources()

                # 종료 조건 체크 (테스트용)
                if self.config.get('test_mode', False) and current_time - self.start_time > self.config.get('test_duration', 3600):
                    self.logger.info("테스트 모드 종료 시간 도달")
                    break

        except KeyboardInterrupt:
            self.logger.info("사용자에 의해 종료됨")
        except Exception as e:
            self.logger.error(f"실행 중 오류: {str(e)}")
            raise
        finally:
            self._cleanup_resources()

    def _process_execution_flow(self, symbol: str, market_data: Dict[str, Any], news_data: List[Dict[str, Any]], social_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        실행 플로우 처리 (InCA -> HiAR -> SELA)

        Args:
            symbol: 코인 심볼
            market_data: 시장 데이터
            news_data: 뉴스 데이터
            social_data: 소셜 데이터

        Returns:
            실행 결과 딕셔너리 (completed, execution_record 등 포함)
        """
        self.logger.info(f"[{symbol}] 실행 플로우 처리 시작")

        timestamp = int(time.time())
        execution_result = {"completed": False, "execution_record": None}

        try:
            # 1. InCA로 중요도 평가
            inca_result = self.simulator._process_with_inca(symbol, market_data, news_data, social_data, timestamp)

            if not inca_result or not inca_result.get("is_significant", False):
                self.logger.info(f"[{symbol}] InCA 평가 결과: 중요도 낮음 ({inca_result.get('importance', 0):.2f}), 처리 중단")
                return execution_result

            self.logger.info(f"[{symbol}] InCA 평가 결과: 중요도 높음 ({inca_result.get('importance', 0):.2f}), 계속 진행")

            # 2. HiAR로 사고 흐름 생성
            hiar_result = self.simulator._process_with_hiar(symbol, market_data, news_data, social_data, inca_result, timestamp)

            if not hiar_result:
                self.logger.warning(f"[{symbol}] HiAR 처리 실패, 플로우 중단")
                return execution_result

            self.logger.info(f"[{symbol}] HiAR 사고 흐름: {hiar_result.get('title', '')}")

            # 3. SELA로 전략 생성
            sela_result = self.simulator._process_with_sela(
                symbol, market_data, news_data, social_data, inca_result, hiar_result, timestamp
            )

            if not sela_result:
                self.logger.warning(f"[{symbol}] SELA 전략 생성 실패, 플로우 중단")
                return execution_result

            # 🚀 3.5 계층적 합의 시스템 비활성화 - SELA 직접 사용
            self.logger.info(f"[{symbol}] 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용")
            # consensus_result = None  # 🔧 None 설정 제거

            # 🔧 기본 consensus_result 구조 생성 (에러 방지용)
            consensus_result = {
                'breakdown': {},
                'reasoning': f'SELA 직접 사용 모드 - {symbol}',
                'final_signal': 'sela_direct',
                'consensus_confidence': 0.8
            }

            # 🎯 SELA 결과를 직접 사용 (새로운 구조 지원)
            sela_action = 'none'
            sela_confidence = 0.5

            if sela_result and isinstance(sela_result, dict):
                # 🚀 SELA 우선 방법론: 최고 신뢰도 전략 직접 선택 (InCA 매칭 제거)
                if 'strategies' in sela_result and sela_result['strategies']:
                    # SELA 전략 중 최고 신뢰도 선택 (InCA 신호 무관)
                    best_strategy = max(sela_result['strategies'],
                                       key=lambda x: x.get('confidence', 0))

                    inca_action = inca_result.get('action_recommendation', 'hold')
                    logger.info(f"🎯 [{symbol}] SELA 최고 신뢰도 전략 선택: {best_strategy.get('type')} (신뢰도: {best_strategy.get('confidence', 0):.3f}) | InCA: {inca_action}")

                    sela_action = best_strategy.get('type', 'none')
                    sela_confidence = best_strategy.get('confidence', 0.5)
                # 기존 구조: {'type': '...', 'confidence': ...}
                elif 'type' in sela_result:
                    sela_action = sela_result.get('type', 'none')
                    sela_confidence = sela_result.get('confidence', 0.5)

            self.logger.info(f"🎯 [{symbol}] SELA 직접 사용: {sela_action} (신뢰도: {sela_confidence:.3f})")
            self.logger.info(f"📊 [{symbol}] 합의 데이터 수집 완료 (학습용)")

            # SELA 결과를 최종 신호로 사용 (합의 시스템 우회)
            self.logger.info(f"[{symbol}] SELA 직접 사용 완료: {sela_action} (신뢰도: {sela_confidence:.3f})")

            # SELA 결과를 전략에 반영 (호환성을 위해 consensus 필드도 설정)
            if sela_result and isinstance(sela_result, dict):
                sela_result['consensus_signal'] = sela_action  # SELA 결과 그대로
                sela_result['consensus_confidence'] = sela_confidence  # SELA 신뢰도 그대로
                # 학습용 데이터 수집 (실제 InCA, SELA, 장기 분석 데이터 추출)
                try:
                    sela_result['consensus_breakdown'] = self._extract_learning_data(
                        inca_result, sela_result, market_data, symbol
                    )
                except Exception as e:
                    logger.error(f"❌ [{symbol}] 학습 데이터 추출 실패: {e}")
                    # 기본 구조로 대체
                    sela_result['consensus_breakdown'] = {
                        'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                        'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                        'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
                    }

            # SELA 신호는 이미 전략에 반영되어 있으므로 오버라이드 불필요

            # 🛡️ SELA 신호 우선 검증: type이 none이면 action도 none으로 강제 설정
            sela_type = sela_result.get('type', '').lower()
            if sela_type in ['none', 'neutral', 'hold']:
                action = 'none'
                self.logger.info(f"[{symbol}] SELA type({sela_type})이 중립이므로 action을 none으로 강제 설정")
            else:
                action = sela_result.get('action', '')

            entry_price = sela_result.get('entry_price', 0)
            take_profit = sela_result.get('take_profit', 0)
            stop_loss = sela_result.get('stop_loss', 0)

            # 🚀 디버깅: 강제 실행 코드 진입 확인
            self.logger.info(f"[{symbol}] 🔍 강제 실행 코드 진입 - sela_action: {sela_action}, type: {type(sela_action)}")

            # 🚀 SELA 신호가 거래 가능한 신호인 경우 강제로 거래 실행 (학습 데이터 축적용)
            should_execute = False
            if sela_action in ['buy', 'sell', 'long', 'short']:
                should_execute = True
                self.logger.info(f"[{symbol}] SELA 신호({sela_action})가 거래 가능한 신호이므로 강제 실행")
            else:
                # 🚀 3.6 SELA 신호 기반 거래 결정 (거래 가능한 신호가 아닌 경우에만)
                should_execute = self._should_execute_based_on_consensus(sela_action, sela_confidence, symbol, market_data, sela_result)
                self.logger.info(f"[{symbol}] SELA 기반 거래 결정 완료: {should_execute}")

            # 🚀 디버깅: should_execute 값 확인
            self.logger.info(f"[{symbol}] 🔍 should_execute 값: {should_execute}, type: {type(should_execute)}")

            # SELA 기반 유효한 전략인지 확인 - SELA 신호가 우선
            # 🛡️ SELA 신호가 none/neutral/hold인 경우 실행하지 않음
            if sela_action in ['none', 'neutral', 'hold']:
                self.logger.info(f"[{symbol}] SELA 신호({sela_action})가 중립이므로 전략 실행하지 않음 (action: {action})")
                should_execute = False
            elif action in ['buy', 'sell'] or sela_action in ['buy', 'sell', 'long', 'short']:
                self.logger.info(f"[{symbol}] SELA 기반 유효한 전략으로 판단: {action} (SELA: {sela_action}, 신뢰도: {sela_confidence:.3f})")

                # 전략 ID 생성 및 저장
                strategy_id = f"{uuid.uuid4().hex[:8]}"
                sela_result['strategy_id'] = strategy_id
                sela_result['consensus_signal'] = sela_action
                sela_result['consensus_confidence'] = sela_confidence

                # 실행 결과 저장
                execution_id = f"exec_{strategy_id}_{timestamp}"
                self.data_store.save_execution_result(symbol, {
                    'execution_id': execution_id,
                    'strategy_id': strategy_id,
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'consensus_signal': sela_action,
                    'consensus_confidence': sela_confidence,
                    'action': action,
                    'entry_price': entry_price,
                    'should_execute': should_execute,
                    'strategy': sela_result
                })
                self.execution_logger.log_execution_result(symbol, execution_id, {
                    'execution_id': execution_id,
                    'strategy_id': strategy_id,
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'consensus_signal': sela_action,
                    'consensus_confidence': sela_confidence,
                    'action': action,
                    'entry_price': entry_price,
                    'should_execute': should_execute,
                    'strategy': sela_result
                })

                self.logger.info(f"[{symbol}] SELA 기반 전략 생성 완료: {strategy_id}, {action} @ ${entry_price:.2f} [SELA: {sela_action}, 신뢰도: {sela_confidence:.3f}, 실행: {should_execute}]")

            # 4. 합의 기반 전략 실행 (실제 거래 또는 시뮬레이션)
            # 🛡️ 최종 실행 전 SELA 신호 재검증
            if should_execute and sela_action not in ['none', 'neutral', 'hold']:
                self.logger.info(f"[{symbol}] 실제 거래 실행 시작: {action}")
            elif should_execute and sela_action in ['none', 'neutral', 'hold']:
                self.logger.info(f"[{symbol}] SELA 신호({sela_action})가 중립이므로 실행 취소")
                should_execute = False

            if should_execute:

                # 🚀 수정된 _execute_strategy 메서드 호출
                trade_execution_result = self._execute_strategy(
                    symbol=symbol,
                    strategy=sela_result,
                    market_data=market_data
                )

                if trade_execution_result:
                    if trade_execution_result.get('status') == 'executed':
                        self.logger.info(f"[{symbol}] SELA 기반 전략 실행 성공: {trade_execution_result.get('mode', 'unknown')} 모드 [SELA: {sela_action}]")
                    elif trade_execution_result.get('status') == 'simulated':
                        self.logger.info(f"[{symbol}] SELA 기반 시뮬레이션 실행 성공: {trade_execution_result.get('mode', 'unknown')} 모드 [SELA: {sela_action}]")
                    elif trade_execution_result.get('status') == 'skipped':
                        self.logger.info(f"[{symbol}] 🔄 같은 방향 포지션으로 거래 스킵: {trade_execution_result.get('reason', 'Unknown reason')} [SELA: {sela_action}]")
                        # 🔥 거래 스킵 시 추가 거래 시도 방지
                        should_execute = False
                    else:
                        self.logger.warning(f"[{symbol}] SELA 기반 전략 실행 실패: {trade_execution_result.get('error', 'Unknown error')} [SELA: {sela_action}]")
                else:
                    self.logger.error(f"[{symbol}] SELA 기반 전략 실행 결과 없음 [SELA: {sela_action}]")
            else:
                self.logger.info(f"[{symbol}] SELA 신호({sela_action}, 신뢰도: {sela_confidence:.3f})가 거래 조건을 만족하지 않아 전략 실행하지 않음")

            # 🚀 InCA 경험 축적을 위한 강제 가상거래 (실제 거래 여부와 관계없이 항상 실행)
            force_virtual_trading = os.getenv('FORCE_VIRTUAL_TRADING_FOR_LEARNING', 'false').lower() == 'true'

            if force_virtual_trading:
                self.logger.info(f"[{symbol}] InCA 학습을 위한 강제 가상거래 실행 시작")

                try:
                    # 가상거래용 전략 생성 (원본 전략 복사)
                    virtual_strategy = sela_result.copy() if sela_result else {}
                    virtual_strategy['virtual_mode'] = True
                    virtual_strategy['learning_purpose'] = True
                    virtual_strategy['consensus_signal'] = sela_action
                    virtual_strategy['consensus_confidence'] = sela_confidence

                    # none 신호인 경우 임의 방향 설정 (학습용)
                    if sela_action in ['none', 'neutral', 'hold']:
                        import random
                        virtual_direction = random.choice(['buy', 'sell'])
                        virtual_strategy['action'] = virtual_direction
                        virtual_strategy['type'] = virtual_direction
                        virtual_strategy['original_signal'] = sela_action
                        self.logger.info(f"[{symbol}] {sela_action} 신호를 {virtual_direction} 가상거래로 변환 (학습용)")

                    # 포트폴리오 매니저를 통한 가상거래 실행
                    if hasattr(self, 'portfolio') and self.portfolio:
                        # 🔧 virtual_strategy 타입 검증
                        if not isinstance(virtual_strategy, dict):
                            self.logger.error(f"❌ [{symbol}] virtual_strategy 파라미터가 딕셔너리가 아님: {type(virtual_strategy)}")
                            virtual_strategy = {}  # 기본 딕셔너리로 설정

                        virtual_result = self.portfolio._execute_virtual_trade(
                            symbol=symbol,
                            direction=virtual_strategy.get('action', 'buy'),
                            strategy=virtual_strategy,
                            market_data=market_data,
                            position_size_usd=float(os.getenv('VIRTUAL_BALANCE', '1000.0')) * 0.1,  # 가상 잔액의 10%
                            current_price=market_data.get('price', 0)
                        )

                        if virtual_result and virtual_result.get('success', False):
                            trade_id = virtual_result.get('trade_id', 'unknown')
                            self.logger.info(f"[{symbol}] 강제 가상거래 실행 성공: {trade_id}")
                        else:
                            error_msg = virtual_result.get('error', '알 수 없는 오류') if virtual_result else '결과 없음'
                            self.logger.warning(f"[{symbol}] 강제 가상거래 실행 실패: {error_msg}")
                    else:
                        self.logger.warning(f"[{symbol}] 포트폴리오 매니저가 없어 강제 가상거래 실행 불가")

                except Exception as e:
                    self.logger.error(f"[{symbol}] 강제 가상거래 실행 중 오류: {str(e)}")
            else:
                self.logger.debug(f"[{symbol}] 강제 가상거래 비활성화됨 (FORCE_VIRTUAL_TRADING_FOR_LEARNING=false)")

            # 5. 실행 결과 저장 (벡터 DB)
            execution_record = {
                "timestamp": timestamp,
                "symbol": symbol,
                "market_data": {
                    "price": market_data.get('price', 0),
                    "change_24h": market_data.get('change_24h', 0),
                    "volume": market_data.get('volume', 0)
                },
                "news_count": len(news_data),
                "social_data": {
                    "volume": social_data.get('volume', 0),
                    "sentiment_score": social_data.get('sentiment_score', 50)
                },
                "inca_result": {
                    "importance": inca_result.get('importance', 0),
                    "situation_type": inca_result.get('situation_type', 'unknown')
                },
                "hiar_result": {
                    "title": hiar_result.get('title', ''),
                    "keywords": hiar_result.get('keywords', [])
                },
                "sela_result": {
                    "action": action,
                    "entry_price": entry_price,
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "confidence": sela_result.get('confidence', 0),
                    "timeframe": sela_result.get('timeframe', 'short_term')
                },
                # 🚀 계층적 합의 시스템 결과 추가 (SELA 직접 사용)
                "consensus_result": {
                    "final_signal": sela_action,
                    "consensus_confidence": sela_confidence,
                    "should_execute": should_execute,
                    "breakdown": consensus_result.get('breakdown', {}) if consensus_result else {},
                    "reasoning": consensus_result.get('reasoning', '') if consensus_result else ''
                }
            }

            # 실행 결과를 벡터 DB에 저장
            try:
                self.vector_db.add_execution_record(execution_record)
                self.logger.info(f"[{symbol}] 실행 결과 저장 완료")
            except Exception as e:
                self.logger.error(f"[{symbol}] 실행 결과 저장 오류: {str(e)}")

            # 학습 큐에 추가
            self._add_to_learning_queue(execution_record)

            # 실행 플로우 완료 표시
            execution_result = {
                "completed": True,
                "execution_record": execution_record,
                "strategy_executed": True if self.config.get('trading_mode', 'simulation') == 'real' else False
            }

            return execution_result

        except Exception as e:
            self.logger.error(f"[{symbol}] 실행 플로우 처리 중 오류: {str(e)}")
            return execution_result

    def _process_learning_loop(self, symbol_filter: str = None) -> None:
        """
        학습 루프 처리 (SELA -> HiAR -> InCA)
        실행 결과를 기반으로 전략 평가 및 개선

        Args:
            symbol_filter: 특정 심볼에 대해서만 학습 (None이면 전체 데이터 학습)
        """
        if symbol_filter:
            self.logger.info(f"[{symbol_filter}] 학습 루프 처리 시작")
        else:
            self.logger.info("전체 학습 루프 처리 시작")

        if not self.learning_queue:
            self.logger.info("학습 큐가 비어 있습니다.")
            return

        try:
            # 1. 학습 데이터 준비
            if symbol_filter:
                # 특정 심볼에 대한 학습 데이터만 필터링
                learning_data = [r for r in self.learning_queue if r.get('symbol') == symbol_filter][:5]
            else:
                # 전체 학습: 최대 10개 항목 처리
                learning_data = self.learning_queue[:10]

            # 학습할 데이터가 없으면 종료
            if not learning_data:
                self.logger.info(f"학습할 데이터가 없습니다.")
                return

            # 2. 학습 데이터를 사용하여 전략 강화
            for record in learning_data:
                try:
                    symbol = record.get('symbol', '')

                    # 벡터 DB에서 유사한 과거 실행 결과 조회
                    situation_type = record.get('inca_result', {}).get('situation_type', '')

                    similar_records = self.vector_db.find_similar_records(
                        query_text=f"{symbol} {situation_type}",
                        limit=5
                    )

                    # 학습 데이터와 유사 사례를 벡터 DB에 반영
                    self.vector_db.update_learning_from_execution(record, similar_records)

                    # 학습 큐에서 처리된 항목 제거
                    self.learning_queue.remove(record)

                    self.logger.info(f"[{symbol}] 학습 데이터 처리 완료")

                except Exception as e:
                    self.logger.error(f"학습 데이터 처리 중 오류: {str(e)}")

        except Exception as e:
            self.logger.error(f"학습 루프 처리 중 오류: {str(e)}")

    def _fetch_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        시장 데이터 가져오기

        Args:
            symbol: 코인 심볼

        Returns:
            시장 데이터 딕셔너리
        """
        try:
            # 바이낸스에서 최신 가격 정보 가져오기
            if self.exchange:
                try:
                    ticker = self.exchange.get_ticker(f"{symbol}USDT")

                    price = float(ticker.get('price', 0))
                    change_24h = float(ticker.get('priceChangePercent', 0))
                    volume = float(ticker.get('volume', 0))

                    # 🚀 계층적 시간 분석: 각 에이전트별 시간 프레임 데이터 수집
                    # InCA: 1분봉 5개 (즉시 반응성)
                    recent_candles = self.exchange.get_klines(f"{symbol}USDT", interval='1m', limit=5)

                    # SELA: 1시간봉 24개 (추세 확인)
                    hourly_candles = self.exchange.get_klines(f"{symbol}USDT", interval='1h', limit=24)

                    # 추가: 일봉 7개 (전체 방향)
                    daily_candles = self.exchange.get_klines(f"{symbol}USDT", interval='1d', limit=7)

                    market_data = {
                        'symbol': symbol,
                        'price': price,
                        'percent_change_24h': change_24h,  # 표준화된 필드명
                        'change_24h': change_24h,  # 기존 호환성
                        'volume': volume,
                        'timestamp': int(time.time()),
                        # 🚀 계층적 시간 프레임 데이터
                        'recent_candles': recent_candles,      # InCA용 (1분봉 5개)
                        'hourly_candles': hourly_candles,      # SELA용 (1시간봉 24개)
                        'daily_candles': daily_candles,        # 전체 방향용 (일봉 7개)
                        'candles': recent_candles              # 기존 호환성
                    }

                    return market_data

                except Exception as e:
                    self.logger.error(f"[{symbol}] 바이낸스 API에서 시장 데이터 가져오기 실패: {str(e)}")

            # 기본 시뮬레이션 데이터 생성
            return {
                'symbol': symbol,
                'price': 30000 if symbol == 'BTC' else 2000,  # 기본값
                'change_24h': random.uniform(-5, 5),
                'volume': random.uniform(1000000, 5000000),
                'timestamp': int(time.time()),
                'candles': self._generate_default_candles(symbol)
            }

        except Exception as e:
            self.logger.error(f"[{symbol}] 시장 데이터 가져오기 오류: {str(e)}")
            return {
                'symbol': symbol,
                'price': 30000 if symbol == 'BTC' else 2000,  # 기본값
                'timestamp': int(time.time())
            }

    def _generate_default_candles(self, symbol: str, count: int = 96) -> List[Dict[str, Any]]:
        """
        기본 캔들 데이터 생성 (시뮬레이션용)

        Args:
            symbol: 코인 심볼
            count: 캔들 수

        Returns:
            캔들 데이터 리스트
        """
        base_price = 30000 if symbol == 'BTC' else 2000
        candles = []

        for i in range(count):
            timestamp = int(time.time()) - (count - i) * 900  # 15분 간격

            # 랜덤 가격 변동 (+-1% 이내)
            price_change = random.uniform(-0.01, 0.01)
            price = base_price * (1 + price_change)

            # 랜덤 OHLCV 생성
            open_price = price * (1 + random.uniform(-0.005, 0.005))
            high_price = max(open_price, price) * (1 + random.uniform(0, 0.005))
            low_price = min(open_price, price) * (1 - random.uniform(0, 0.005))

            candle = {
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': price,
                'volume': random.uniform(100, 1000)
            }

            candles.append(candle)

        return candles

    def _fetch_news_data(self, symbol: str) -> List[Dict[str, Any]]:
        """
        뉴스 데이터 가져오기

        Args:
            symbol: 코인 심볼

        Returns:
            뉴스 데이터 리스트
        """
        try:
            if self.lunar_collector:
                news_data = self.lunar_collector.get_news(symbol)

                # API 응답 구조에 따라 데이터 추출
                if "data" in news_data and isinstance(news_data["data"], list):
                    return news_data["data"]

            # 기본 데이터 (API 없거나 오류 시)
            return []

        except Exception as e:
            self.logger.error(f"[{symbol}] 뉴스 데이터 가져오기 오류: {str(e)}")
            return []

    def _fetch_social_data(self, symbol: str) -> Dict[str, Any]:
        """
        소셜 데이터 가져오기

        Args:
            symbol: 코인 심볼

        Returns:
            소셜 데이터 딕셔너리
        """
        try:
            if self.lunar_collector:
                # 소셜 인플루언스 데이터 가져오기
                influence_data = self.lunar_collector.get_coin_info(symbol)

                # 소셜 포스트 가져오기
                posts_data = self.lunar_collector.get_posts(symbol)

                # 데이터 추출 및 통합
                posts = []
                if "data" in posts_data and isinstance(posts_data["data"], list):
                    posts = posts_data["data"]

                # LunarCrush API 응답 구조에서 데이터 추출
                social_data = {
                    "volume": influence_data.get("social_volume", 0),
                    "sentiment_score": influence_data.get("average_sentiment", 50),
                    "posts_count": len(posts),
                    "posts": posts[:10]  # 최근 10개 포스트만
                }

                return social_data

            # 기본 데이터 (API 없거나 오류 시)
            return {
                "volume": random.randint(1000, 5000),
                "sentiment_score": random.uniform(40, 60),
                "posts_count": 0,
                "posts": []
            }

        except Exception as e:
            self.logger.error(f"[{symbol}] 소셜 데이터 가져오기 오류: {str(e)}")
            return {
                "volume": 0,
                "sentiment_score": 50,
                "posts_count": 0,
                "posts": []
            }

    def _extract_learning_data(self, inca_result: Dict[str, Any], sela_result: Dict[str, Any],
                              market_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """
        실제 InCA, SELA, 장기 분석 데이터를 추출하여 학습용 데이터 구조 생성

        Args:
            inca_result: InCA 분석 결과
            sela_result: SELA 전략 결과
            market_data: 시장 데이터
            symbol: 심볼

        Returns:
            Dict[str, Any]: 학습용 합의 breakdown 데이터
        """
        try:
            # 1. InCA 단기 데이터 추출
            short_term_data = self._extract_inca_learning_data(inca_result)

            # 2. SELA 중기 데이터 추출
            medium_term_data = self._extract_sela_learning_data(sela_result)

            # 3. 장기 추세 데이터 추출
            long_term_data = self._extract_long_term_learning_data(market_data)

            learning_breakdown = {
                'short_term': short_term_data,
                'medium_term': medium_term_data,
                'long_term': long_term_data
            }

            self.logger.info(f"[{symbol}] 실제 학습 데이터 추출 완료: "
                           f"InCA={short_term_data['action']}, "
                           f"SELA={medium_term_data['action']}, "
                           f"장기={long_term_data['action']}")

            return learning_breakdown

        except Exception as e:
            self.logger.error(f"[{symbol}] 학습 데이터 추출 중 오류: {e}")
            # 오류 시 기본 구조 반환
            return {
                'short_term': {'action': 'hold', 'confidence': 0.5, 'importance': 0.5, 'source': 'InCA'},
                'medium_term': {'action': 'none', 'confidence': 0.5, 'importance': 0.5, 'source': 'SELA'},
                'long_term': {'action': 'neutral', 'confidence': 0.5, 'importance': 0.5, 'source': 'LongTerm'}
            }

    def _extract_inca_learning_data(self, inca_result: Dict[str, Any]) -> Dict[str, Any]:
        """InCA 결과에서 학습용 데이터 추출"""
        try:
            # InCA 결과에서 실제 데이터 추출
            inca_evaluation = inca_result.get('inca_evaluation', {})

            action = inca_evaluation.get('action_recommendation', inca_result.get('action_recommendation', 'hold'))
            situation = inca_evaluation.get('situation_type', inca_result.get('situation_type', 'neutral'))
            importance_raw = inca_evaluation.get('importance', inca_result.get('importance', 5))
            confidence = inca_evaluation.get('confidence', inca_result.get('confidence', 0.5))

            # importance 정규화 (1-10 → 0-1)
            importance = importance_raw / 10.0 if isinstance(importance_raw, (int, float)) else 0.5

            return {
                'action': action,
                'situation': situation,
                'importance': importance,
                'confidence': confidence,
                'source': 'InCA',
                'timeframe': '1분봉'
            }

        except Exception as e:
            self.logger.warning(f"InCA 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'hold',
                'situation': 'neutral',
                'importance': 0.5,
                'confidence': 0.5,
                'source': 'InCA',
                'timeframe': '1분봉'
            }

    def _extract_sela_learning_data(self, sela_result: Dict[str, Any]) -> Dict[str, Any]:
        """SELA 결과에서 학습용 데이터 추출"""
        try:
            action = sela_result.get('action', 'none')
            strategy_type = sela_result.get('type', 'none')
            confidence = sela_result.get('confidence', 0.5)

            # SELA의 tree_search_score를 importance로 사용 (정규화)
            tree_search_score = sela_result.get('tree_search_score', 5.0)
            importance = tree_search_score / 10.0 if isinstance(tree_search_score, (int, float)) else 0.5

            return {
                'action': action,
                'type': strategy_type,
                'importance': importance,
                'confidence': confidence,
                'source': 'SELA',
                'timeframe': '1시간봉'
            }

        except Exception as e:
            self.logger.warning(f"SELA 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'none',
                'type': 'none',
                'importance': 0.5,
                'confidence': 0.5,
                'source': 'SELA',
                'timeframe': '1시간봉'
            }

    def _extract_long_term_learning_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """시장 데이터에서 장기 추세 학습용 데이터 추출"""
        try:
            # 일봉 데이터가 있으면 추세 분석
            daily_candles = market_data.get('daily_candles', [])

            if not daily_candles or len(daily_candles) < 7:
                return {
                    'action': 'neutral',
                    'trend': 'sideways',
                    'trend_change_pct': 0.0,
                    'importance': 0.5,
                    'confidence': 0.3,
                    'source': 'LongTerm',
                    'timeframe': '일봉',
                    'note': '일봉 데이터 부족'
                }

            # 7일 추세 변화율 계산
            first_candle = daily_candles[0]
            last_candle = daily_candles[-1]

            if isinstance(first_candle, list) and isinstance(last_candle, list):
                first_close = float(first_candle[4])
                last_close = float(last_candle[4])
            else:
                first_close = float(first_candle.get('close', 0))
                last_close = float(last_candle.get('close', 0))

            if first_close > 0:
                trend_change_pct = ((last_close - first_close) / first_close) * 100
            else:
                trend_change_pct = 0.0

            # 추세에 따른 액션 및 중요도 결정
            if trend_change_pct > 5:
                action = 'strong_buy'
                trend = 'strong_uptrend'
                importance = 0.8
                confidence = 0.8
            elif trend_change_pct > 2:
                action = 'buy'
                trend = 'uptrend'
                importance = 0.65
                confidence = 0.7
            elif trend_change_pct < -5:
                action = 'strong_sell'
                trend = 'strong_downtrend'
                importance = 0.8
                confidence = 0.8
            elif trend_change_pct < -2:
                action = 'sell'
                trend = 'downtrend'
                importance = 0.65
                confidence = 0.7
            else:
                action = 'neutral'
                trend = 'sideways'
                importance = 0.5
                confidence = 0.6

            return {
                'action': action,
                'trend': trend,
                'trend_change_pct': trend_change_pct,
                'importance': importance,
                'confidence': confidence,
                'source': 'LongTerm',
                'timeframe': '일봉'
            }

        except Exception as e:
            self.logger.warning(f"장기 추세 학습 데이터 추출 중 오류: {e}")
            return {
                'action': 'neutral',
                'trend': 'sideways',
                'trend_change_pct': 0.0,
                'importance': 0.5,
                'confidence': 0.3,
                'source': 'LongTerm',
                'timeframe': '일봉',
                'error': str(e)
            }

    def _add_to_learning_queue(self, record: Dict[str, Any]) -> None:
        """
        학습 큐에 레코드 추가

        Args:
            record: 학습 큐에 추가할 레코드
        """
        try:
            # 중복 체크 (timestamp와 symbol로 구분)
            timestamp = record.get('timestamp', 0)
            symbol = record.get('symbol', '')

            # 이미 동일한 레코드가 있는지 확인
            if any(r.get('timestamp') == timestamp and r.get('symbol') == symbol for r in self.learning_queue):
                return

            # 최대 큐 크기 제한
            max_queue_size = self.config.get('max_learning_queue_size', 100)

            if len(self.learning_queue) >= max_queue_size:
                self.learning_queue.pop(0)  # 가장 오래된 항목 제거

            self.learning_queue.append(record)
            self.logger.debug(f"[{symbol}] 학습 큐에 추가됨 (큐 크기: {len(self.learning_queue)})")

        except Exception as e:
            self.logger.error(f"학습 큐 추가 중 오류: {str(e)}")

    def _manage_existing_positions(self, symbol: str, market_data: Dict[str, Any]) -> None:
        """
        기존 포지션 관리 (InCA + SELA 기반)

        Args:
            symbol: 코인 심볼
            market_data: 현재 시장 데이터
        """
        try:
            # 포트폴리오가 없으면 건너뛰기
            if not hasattr(self, 'portfolio') or not self.portfolio:
                return

            # 현재 포지션 확인
            existing_positions = []
            if hasattr(self.portfolio, 'get_positions'):
                existing_positions = self.portfolio.get_positions(symbol)
            elif hasattr(self.portfolio, 'open_positions'):
                existing_positions = [pos for pos in self.portfolio.open_positions
                                    if pos.get('symbol') == symbol]

            if not existing_positions:
                logger.debug(f"[{symbol}] 관리할 포지션 없음")
                return

            logger.info(f"🎯 [{symbol}] {len(existing_positions)}개 포지션 관리 시작")

            for position in existing_positions:
                try:
                    # 1. SELA: 포지션 관리 전략 직접 생성 (InCA 우회)
                    logger.info(f"🎯 [{symbol}] SELA 포지션 관리 전략 직접 생성")

                    # 🔧 풍부한 시장 데이터 사용 (캔들/뉴스 데이터 포함)
                    rich_market_data = self._get_rich_market_data(symbol, market_data)

                    # 간단한 추론 카드 생성 (포지션 관리용)
                    reasoning_card = {
                        "title": f"{symbol} 포지션 관리 분석",
                        "position_analysis": f"포지션 방향: {position.get('direction', 'unknown')}, PnL: {position.get('current_pnl_pct', 0):.2f}%",
                        "recommendation": "SELA 직접 포지션 관리"
                    }

                    # 🔧 InCA 평가 결과 생성 (SELA 호출용)
                    inca_evaluation = {
                        "signal_direction": "neutral",
                        "situation_type": "neutral",
                        "hold_importance": 5,
                        "should_close": False,
                        "confidence": 0.5
                    }

                    # SELA가 InCA 평가와 포지션 정보를 분석하여 관리 전략 생성
                    position_strategy = self.sela_agent.generate_position_management_strategy(
                        symbol, position, inca_evaluation, reasoning_card
                    )

                    # 2. 전략에 따른 포지션 관리
                    if position_strategy.get("action") == "close":
                        logger.info(f"🔄 [{symbol}] SELA 포지션 종료 결정: {position_strategy.get('reason', 'unknown')}")
                        self._close_position_with_strategy(symbol, position, position_strategy)

                        # 🔧 SELA 기반 반대 포지션 진입 비활성화 (불필요한 포지션 변경 방지)
                        # self._check_opposite_position_entry_sela(symbol, position_strategy, rich_market_data)
                        logger.info(f"🛡️ [{symbol}] SELA 기반 반대 포지션 진입 비활성화됨")
                    else:
                        logger.info(f"📊 [{symbol}] SELA 포지션 유지 결정: {position_strategy.get('reason', 'unknown')}")

                except Exception as e:
                    logger.error(f"❌ [{symbol}] 개별 포지션 관리 실패: {e}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 관리 전체 실패: {e}")



    def _should_close_position(self, position_importance: Dict[str, Any]) -> bool:
        """🔥 LLM 기반 포지션 종료 판단 + 강제 종료 조건"""
        try:
            # LLM이 직접 판단한 should_close 값 사용
            should_close = position_importance.get("should_close", False)

            # 로깅을 위한 추가 정보
            situation_type = position_importance.get("situation_type", "neutral")
            hold_importance = position_importance.get("hold_importance", 5)
            confidence = position_importance.get("confidence", 0.5)
            reasoning = position_importance.get("reasoning", "LLM 판단")

            # 🔧 강제 종료 조건 추가 (LLM 판단 무시)
            if "강제 종료" in reasoning or "30분 초과" in reasoning:
                should_close = True
                logger.warning(f"🔧 강제 종료 조건 감지: {reasoning}")
            elif "시간 기반 정리" in reasoning:
                should_close = True
                logger.warning(f"⏰ 시간 기반 강제 종료: {reasoning}")

            logger.info(f"🔍 LLM 클로즈 판단: should_close={should_close}, situation={situation_type}, importance={hold_importance}, confidence={confidence:.2f}")
            logger.info(f"🔍 LLM 판단 근거: {reasoning}")

            return should_close

        except Exception as e:
            logger.error(f"포지션 종료 판단 실패: {e}")
            return False

    def _close_position_with_strategy(self, symbol: str, position: Dict[str, Any],
                                    strategy: Dict[str, Any]) -> None:
        """전략에 따른 포지션 종료"""
        try:
            logger.info(f"🔄 [{symbol}] 포지션 종료 실행: {strategy.get('reason', 'unknown')}")

            # 🔧 실제 바이낸스 포지션 종료 먼저 실행
            close_reason = f"llm_strategy_{strategy.get('reason', 'unknown')}"
            self._close_real_binance_position(symbol, close_reason)
            logger.info(f"🔄 [{symbol}] 실제 바이낸스 포지션 종료 완료: {close_reason}")

            # 포트폴리오의 포지션 종료 메서드 호출
            if hasattr(self.portfolio, 'close_position'):
                result = self.portfolio.close_position(
                    symbol,
                    reason=f"llm_strategy_{strategy.get('reason', 'unknown')}"
                )

                if result.get("success", False):
                    logger.info(f"✅ [{symbol}] 포지션 종료 성공: {result}")

                    # 포지션 종료 결과를 학습 큐에 추가
                    learning_record = {
                        "symbol": symbol,
                        "type": "position_management",
                        "timestamp": int(time.time()),
                        "position_data": position,
                        "strategy": strategy,
                        "close_result": result,
                        "success": True
                    }
                    self._add_to_learning_queue(learning_record)
                else:
                    logger.error(f"❌ [{symbol}] 포지션 종료 실패: {result}")
            else:
                logger.warning(f"⚠️ [{symbol}] 포트폴리오에 close_position 메서드 없음")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 종료 실행 실패: {e}")

    def _check_opposite_position_entry(self, symbol: str, position_importance: Dict[str, Any], market_data: Dict[str, Any]) -> None:
        """포지션 종료 후 반대 방향 포지션 진입 검토"""
        try:
            signal_direction = position_importance.get('signal_direction', 'neutral')
            situation_type = position_importance.get('situation_type', 'neutral')

            logger.info(f"🔍 [{symbol}] 반대 포지션 진입 검토: 신호={signal_direction}, 상황={situation_type}")

            # bearish 신호일 때 SHORT 포지션 진입 요청 저장 (사이클 마지막에 처리)
            if signal_direction == 'bearish' or situation_type == 'bearish':
                logger.info(f"🔥 [{symbol}] bearish 신호 감지 → SHORT 포지션 진입 요청 저장 (사이클 마지막에 처리)")
                self._store_opposite_position_request(symbol, 'short', market_data, position_importance)

            # bullish 신호일 때 LONG 포지션 진입 요청 저장 (사이클 마지막에 처리)
            elif signal_direction == 'bullish' or situation_type == 'bullish':
                logger.info(f"🔥 [{symbol}] bullish 신호 감지 → LONG 포지션 진입 요청 저장 (사이클 마지막에 처리)")
                self._store_opposite_position_request(symbol, 'long', market_data, position_importance)

            else:
                logger.info(f"📊 [{symbol}] 중립 신호로 새 포지션 진입 안함: {signal_direction}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 반대 포지션 진입 검토 실패: {e}")

    def _store_opposite_position_request(self, symbol: str, direction: str, market_data: Dict[str, Any], position_importance: Dict[str, Any]) -> None:
        """반대 포지션 진입 요청을 포트폴리오에 저장 (사이클 마지막에 처리)"""
        try:
            if hasattr(self, 'portfolio') and self.portfolio:
                # 반대 포지션 전략 생성
                current_price = market_data.get('price', 0)
                if direction == 'short':
                    opposite_strategy = {
                        'strategy_id': f"opposite_short_{symbol}_{int(time.time())}",
                        'symbol': symbol,
                        'type': 'sell',
                        'direction': 'short',
                        'entry_price': current_price,
                        'stop_loss': current_price * 1.03,  # 3% 손절
                        'take_profit': current_price * 0.97,  # 3% 익절
                        'reasoning': f"bearish 신호 감지 후 반대 포지션 진입: {position_importance.get('reasoning', 'N/A')}",
                        'confidence': position_importance.get('confidence', 0.7),
                        'risk_level': 'medium',
                        'opposite_entry': True
                    }
                else:  # long
                    opposite_strategy = {
                        'strategy_id': f"opposite_long_{symbol}_{int(time.time())}",
                        'symbol': symbol,
                        'type': 'buy',
                        'direction': 'long',
                        'entry_price': current_price,
                        'stop_loss': current_price * 0.97,  # 3% 손절
                        'take_profit': current_price * 1.03,  # 3% 익절
                        'reasoning': f"bullish 신호 감지 후 반대 포지션 진입: {position_importance.get('reasoning', 'N/A')}",
                        'confidence': position_importance.get('confidence', 0.7),
                        'risk_level': 'medium',
                        'opposite_entry': True
                    }

                # 포트폴리오에 반대 포지션 요청 저장
                self.portfolio._store_pending_position_change(
                    symbol, direction, market_data,
                    self.exchange if hasattr(self, 'exchange') else None,
                    {'direction': 'opposite', 'strategy': opposite_strategy}
                )
                logger.info(f"📝 [{symbol}] 반대 포지션 진입 요청 저장 완료: {direction}")
            else:
                logger.error(f"❌ [{symbol}] 포트폴리오가 없어 반대 포지션 요청 저장 실패")
        except Exception as e:
            logger.error(f"❌ [{symbol}] 반대 포지션 요청 저장 실패: {e}")

    def _execute_immediate_short_entry(self, symbol: str, market_data: Dict[str, Any], position_importance: Dict[str, Any]) -> None:
        """즉시 SHORT 포지션 진입"""
        try:
            current_price = market_data.get('price', 0)
            if not current_price:
                logger.error(f"❌ [{symbol}] SHORT 진입 실패: 현재가 없음")
                return

            # SHORT 전략 생성
            short_strategy = {
                'strategy_id': f"immediate_short_{symbol}_{int(time.time())}",
                'symbol': symbol,
                'type': 'sell',
                'direction': 'short',
                'entry_price': current_price,
                'stop_loss': current_price * 1.03,  # 3% 손절
                'take_profit': current_price * 0.97,  # 3% 익절
                'reasoning': f"bearish 신호 감지 후 즉시 SHORT 진입: {position_importance.get('reasoning', 'N/A')}",
                'confidence': position_importance.get('confidence', 0.7),
                'risk_level': 'medium',
                'immediate_entry': True
            }

            logger.info(f"🔥 [{symbol}] SHORT 전략 생성: 진입가=${current_price}, 손절가=${short_strategy['stop_loss']:.4f}")

            # 즉시 실행
            execution_result = self.execute_strategy(symbol, short_strategy, market_data)

            if execution_result and execution_result.get('success'):
                logger.info(f"✅ [{symbol}] 즉시 SHORT 포지션 진입 성공: {execution_result}")
            else:
                logger.error(f"❌ [{symbol}] 즉시 SHORT 포지션 진입 실패: {execution_result}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 즉시 SHORT 진입 실행 실패: {e}")

    def _execute_immediate_long_entry(self, symbol: str, market_data: Dict[str, Any], position_importance: Dict[str, Any]) -> None:
        """즉시 LONG 포지션 진입"""
        try:
            current_price = market_data.get('price', 0)
            if not current_price:
                logger.error(f"❌ [{symbol}] LONG 진입 실패: 현재가 없음")
                return

            # LONG 전략 생성
            long_strategy = {
                'strategy_id': f"immediate_long_{symbol}_{int(time.time())}",
                'symbol': symbol,
                'type': 'buy',
                'direction': 'long',
                'entry_price': current_price,
                'stop_loss': current_price * 0.97,  # 3% 손절
                'take_profit': current_price * 1.03,  # 3% 익절
                'reasoning': f"bullish 신호 감지 후 즉시 LONG 진입: {position_importance.get('reasoning', 'N/A')}",
                'confidence': position_importance.get('confidence', 0.7),
                'risk_level': 'medium',
                'immediate_entry': True
            }

            logger.info(f"🔥 [{symbol}] LONG 전략 생성: 진입가=${current_price}, 손절가=${long_strategy['stop_loss']:.4f}")

            # 즉시 실행
            execution_result = self.execute_strategy(symbol, long_strategy, market_data)

            if execution_result and execution_result.get('success'):
                logger.info(f"✅ [{symbol}] 즉시 LONG 포지션 진입 성공: {execution_result}")
            else:
                logger.error(f"❌ [{symbol}] 즉시 LONG 포지션 진입 실패: {execution_result}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 즉시 LONG 진입 실행 실패: {e}")

    def manage_positions_v2(self, symbol: str, market_data: Dict[str, Any]) -> None:
        """
        기존 포지션 관리 v2 (InCA + SELA 기반) - 새로운 이름으로 재구현

        Args:
            symbol: 코인 심볼
            market_data: 현재 시장 데이터
        """
        try:
            logger.info(f"🎯 [{symbol}] 포지션 관리 v2 시작")

            # 포트폴리오가 없으면 건너뛰기
            if not hasattr(self, 'portfolio') or not self.portfolio:
                logger.debug(f"[{symbol}] 포트폴리오 없음, 포지션 관리 건너뛰기")
                return

            # 현재 포지션 확인
            existing_positions = []
            if hasattr(self.portfolio, 'get_positions'):
                existing_positions = self.portfolio.get_positions(symbol)
            elif hasattr(self.portfolio, 'open_positions'):
                existing_positions = [pos for pos in self.portfolio.open_positions
                                    if pos.get('symbol') == symbol]

            if not existing_positions:
                logger.debug(f"[{symbol}] 관리할 포지션 없음")
                return

            logger.info(f"🎯 [{symbol}] {len(existing_positions)}개 포지션 관리 시작")

            # 🔧 캔들 데이터가 포함된 풍부한 시장 데이터 수집
            enriched_market_data = self.collect_market_data()
            symbol_market_data = enriched_market_data.get(symbol, {})

            # 🎯 기존 market_data와 병합 (캔들 데이터 우선)
            combined_market_data = market_data.copy()
            combined_market_data.update(symbol_market_data)

            logger.info(f"🔧 [{symbol}] 포지션 관리용 시장 데이터 키: {list(combined_market_data.keys())}")

            for position in existing_positions:
                try:
                    # 🔧 실제 PnL 계산 및 포지션 업데이트
                    updated_position = self._update_position_with_real_pnl(position, combined_market_data)

                    # 1. 포지션 관리 건너뛰기 (InCA 포지션 평가 제거)
                    logger.info(f"🔒 [{symbol}] InCA 포지션 평가 제거됨 - 포지션 관리 건너뛰기 v2")
                    logger.info(f"🔒 [{symbol}] InCA는 시장 평가만 수행, 포지션 관리는 별도 로직에서 처리")

                    if should_close:
                        # 🔧 SELA에 전달할 inca_evaluation 업데이트 (should_close 반영)
                        updated_inca_evaluation = position_importance.copy()
                        updated_inca_evaluation["should_close"] = True  # 강제로 True 설정
                        logger.info(f"🔧 [{symbol}] SELA 전달용 inca_evaluation 업데이트: should_close=True")

                        # 3. SELA: 포지션 관리 전략 생성
                        logger.info(f"🎯 [{symbol}] SELA 포지션 관리 전략 생성 (should_close=True)")

                        # 간단한 추론 카드 생성 (포지션 관리용)
                        reasoning_card = {
                            "title": f"{symbol} 포지션 관리 분석",
                            "position_analysis": position_importance.get("reasoning", ""),
                            "recommendation": "포지션 종료 검토"
                        }

                        # SELA 에이전트에 포지션 관리 메서드가 있는지 확인
                        if hasattr(self.sela_agent, 'generate_position_management_strategy'):
                            position_strategy = self.sela_agent.generate_position_management_strategy(
                                symbol, position, updated_inca_evaluation, reasoning_card  # 🔧 업데이트된 평가 결과 전달
                            )
                        else:
                            logger.warning(f"[{symbol}] SELA 에이전트에 generate_position_management_strategy 메서드 없음")
                            position_strategy = {"action": "close", "reason": "fallback_close"}

                        # 4. 전략에 따른 포지션 종료
                        if position_strategy.get("action") == "close":
                            self._close_position_with_strategy_v2(symbol, position, position_strategy)
                        else:
                            logger.info(f"📊 [{symbol}] 포지션 유지 결정: {position_strategy.get('reason', 'unknown')}")
                    else:
                        logger.info(f"📈 [{symbol}] 포지션 보유 계속: {position_importance.get('situation_type', 'unknown')}")

                except Exception as e:
                    logger.error(f"❌ [{symbol}] 개별 포지션 관리 실패: {e}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 관리 v2 전체 실패: {e}")

    def _update_position_with_real_pnl(self, position: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """포지션에 실제 PnL 계산 결과 업데이트"""
        try:
            # 포지션 정보 추출
            entry_price = position.get("entry_price", 0)
            direction = position.get("direction", "unknown")
            current_price = market_data.get("price", 0)

            if entry_price > 0 and current_price > 0:
                # 🔧 실제 PnL 계산
                if direction == "long":
                    pnl_pct = ((current_price - entry_price) / entry_price) * 100
                elif direction == "short":
                    pnl_pct = ((entry_price - current_price) / entry_price) * 100
                else:
                    pnl_pct = 0.0

                # 포지션 복사 후 PnL 업데이트
                updated_position = position.copy()
                updated_position["current_pnl_pct"] = pnl_pct

                logger.info(f"🔧 실제 PnL 계산: {direction} 포지션, 진입가 ${entry_price}, 현재가 ${current_price}, PnL {pnl_pct:.3f}%")
                return updated_position
            else:
                logger.warning(f"PnL 계산 불가: entry_price={entry_price}, current_price={current_price}")
                return position

        except Exception as e:
            logger.error(f"PnL 계산 실패: {e}")
            return position

    def _get_real_binance_pnl(self, symbol: str) -> float:
        """
        바이낸스에서 실제 PnL을 가져옵니다.

        Args:
            symbol: 심볼명

        Returns:
            float: 실제 PnL 퍼센트, 없으면 None
        """
        try:
            if hasattr(self, 'portfolio') and self.portfolio:
                # 🔧 1. get_positions 메서드로 포지션 찾기
                if hasattr(self.portfolio, 'get_positions'):
                    try:
                        positions = self.portfolio.get_positions(symbol)
                        for pos in positions:
                            if isinstance(pos, dict):
                                real_pnl = pos.get('current_pnl_pct') or pos.get('current_pnl')
                                if real_pnl is not None:
                                    logger.info(f"🔧 [{symbol}] get_positions에서 실제 PnL 발견: {real_pnl:.3f}%")
                                    return float(real_pnl)
                    except Exception as e:
                        logger.warning(f"🔧 [{symbol}] get_positions 메서드 오류: {e}")

                # 🔧 2. open_positions 속성에서 포지션 찾기 (백업)
                open_positions = getattr(self.portfolio, 'open_positions', [])
                for pos in open_positions:
                    if isinstance(pos, dict) and pos.get('symbol') == symbol:
                        # current_pnl_pct 또는 current_pnl 필드 확인
                        real_pnl = pos.get('current_pnl_pct') or pos.get('current_pnl')
                        if real_pnl is not None:
                            logger.info(f"🔧 [{symbol}] open_positions에서 실제 PnL 발견: {real_pnl:.3f}%")
                            return float(real_pnl)

                logger.warning(f"⚠️ [{symbol}] 바이낸스 실제 PnL 찾을 수 없음 (총 {len(open_positions)}개 포지션)")
                return None
            else:
                logger.warning(f"⚠️ [{symbol}] 포트폴리오 연결 없음")
                return None

        except Exception as e:
            logger.error(f"❌ [{symbol}] 바이낸스 실제 PnL 조회 실패: {e}")
            return None

    def _fallback_position_evaluation_v2(self, position: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """포지션 평가 Fallback v2 (InCA 없을 때) - 강화된 종료 조건"""
        try:
            current_pnl = position.get("current_pnl_pct", 0)
            hold_time_minutes = (time.time() - position.get("entry_timestamp", time.time())) / 60
            price_change_24h = market_data.get("percent_change_24h", 0)

            # 🔧 강화된 평가 로직 - 더 적극적인 포지션 정리
            if hold_time_minutes > 30:  # 30분 이상 보유 (최우선)
                situation_type = "should_close"
                reasoning = f"🔧 강제 종료: {hold_time_minutes:.1f}분 보유 (30분 초과)"
                should_close = True
            elif current_pnl > 1.0:  # 1% 이상 수익 (기존 2%에서 하향)
                situation_type = "should_close"
                reasoning = f"🎯 수익 실현: +{current_pnl:.2f}% 수익"
                should_close = True
            elif current_pnl < -1.0:  # 1% 이상 손실 (기존 1.5%에서 하향)
                situation_type = "should_close"
                reasoning = f"🛑 손절: {current_pnl:.2f}% 손실"
                should_close = True
            elif hold_time_minutes > 20:  # 20분 이상 보유 시 추가 조건
                if abs(current_pnl) < 0.5:  # PnL이 0.5% 미만이면 정리
                    situation_type = "should_close"
                    reasoning = f"⏰ 시간 기반 정리: {hold_time_minutes:.1f}분 보유, PnL={current_pnl:.2f}%"
                    should_close = True
                else:
                    situation_type = "should_hold"
                    reasoning = f"보유 유지: {hold_time_minutes:.1f}분, PnL={current_pnl:.2f}%"
                    should_close = False
            else:
                situation_type = "should_hold"
                reasoning = f"현재 상태 유지: {hold_time_minutes:.1f}분, PnL={current_pnl:.2f}%"
                should_close = False

            return {
                "hold_importance": 2 if situation_type == "should_close" else 7,  # 종료 시 더 낮은 중요도
                "situation_type": situation_type,
                "reasoning": reasoning,
                "confidence": 0.8,  # 더 높은 신뢰도
                "should_close": should_close  # 명시적 종료 플래그
            }

        except Exception as e:
            logger.error(f"Fallback 포지션 평가 v2 실패: {e}")
            return {
                "hold_importance": 5,
                "situation_type": "neutral",
                "reasoning": f"평가 오류: {e}",
                "confidence": 0.3
            }

    def _should_close_position_v2(self, position_importance: Dict[str, Any]) -> bool:
        """🔥 LLM 기반 포지션 종료 판단 v2 + 강화된 강제 종료 조건 + 5분마다 0.2% 수익 실현"""
        try:
            # 🔍 디버깅: 함수 호출 확인
            symbol = position_importance.get("symbol", "UNKNOWN")
            logger.info(f"🔍 [{symbol}] _should_close_position_v2 함수 호출됨!")

            # LLM이 직접 판단한 should_close 값 사용
            should_close = position_importance.get("should_close", False)

            # 로깅을 위한 추가 정보
            situation_type = position_importance.get("situation_type", "neutral")
            hold_importance = position_importance.get("hold_importance", 5)
            confidence = position_importance.get("confidence", 0.5)
            reasoning = position_importance.get("reasoning", "LLM 판단")

            # 🔧 강화된 강제 종료 조건 (LLM 판단 무시)
            force_close_reasons = []

            # 🎯 환경변수에서 ROI 임계값 가져오기
            import os
            roi_threshold = float(os.getenv("TRADING_ROI_THRESHOLD", "0.2"))

            # 🎯 5분마다 ROI 임계값 이상 수익 시 포지션 클로즈 체크 (강화된 로직)
            current_time = time.time()
            pnl_pct = position_importance.get("pnl_pct", 0.0)
            hold_time_minutes = position_importance.get("hold_time_minutes", 0.0)

            # 🔍 디버깅: 핵심 값들 확인
            logger.info(f"🔍 [{symbol}] position_importance 전체 키: {list(position_importance.keys())}")
            logger.info(f"🔍 [{symbol}] pnl_pct 값: {pnl_pct} (타입: {type(pnl_pct)})")
            logger.info(f"🔍 [{symbol}] hold_time_minutes 값: {hold_time_minutes} (타입: {type(hold_time_minutes)})")
            logger.info(f"🔍 [{symbol}] should_close (LLM): {should_close}")

            # 상세 로깅으로 디버깅
            logger.info(f"🔍 [{position_importance.get('symbol', 'UNKNOWN')}] 5분 수익 실현 체크:")
            logger.info(f"🔍 - 보유시간: {hold_time_minutes:.2f}분")
            logger.info(f"🔍 - 현재 PnL: {pnl_pct:.3f}%")
            logger.info(f"🔍 - ROI 임계값: {roi_threshold:.3f}%")
            logger.info(f"🔍 - 5분 조건: {hold_time_minutes >= 5.0}")
            logger.info(f"🔍 - 수익 조건: {pnl_pct >= roi_threshold}")

            # 5분 이상 보유하고 ROI 임계값 이상 수익인 경우 강제 종료
            if hold_time_minutes >= 5.0 and pnl_pct >= roi_threshold:
                should_close = True
                force_close_reasons.append(f"5분 수익 실현 (보유: {hold_time_minutes:.1f}분, 수익: {pnl_pct:.3f}%, 임계값: {roi_threshold:.3f}%)")
                logger.warning(f"💰 [{position_importance.get('symbol', 'UNKNOWN')}] 5분 수익 실현 조건 충족!")
                logger.warning(f"💰 - 보유시간: {hold_time_minutes:.2f}분 >= 5.0분")
                logger.warning(f"💰 - PnL: {pnl_pct:.3f}% >= {roi_threshold:.3f}%")

            # 추가 강화: 3분 이상 보유하고 0.5% 이상 수익인 경우도 고려
            elif hold_time_minutes >= 3.0 and pnl_pct >= 0.5:
                should_close = True
                force_close_reasons.append(f"3분 고수익 실현 (보유: {hold_time_minutes:.1f}분, 수익: {pnl_pct:.3f}%)")
                logger.warning(f"💎 [{position_importance.get('symbol', 'UNKNOWN')}] 3분 고수익 실현 조건 충족!")

            # 추가 강화: 10분 이상 보유하고 0.1% 이상 수익인 경우도 고려
            elif hold_time_minutes >= 10.0 and pnl_pct >= 0.1:
                should_close = True
                force_close_reasons.append(f"10분 소수익 실현 (보유: {hold_time_minutes:.1f}분, 수익: {pnl_pct:.3f}%)")
                logger.warning(f"⏰ [{position_importance.get('symbol', 'UNKNOWN')}] 10분 소수익 실현 조건 충족!")

            # 시간 기반 강제 종료
            if any(keyword in reasoning for keyword in ["강제 종료", "30분 초과", "시간 기반 정리"]):
                should_close = True
                force_close_reasons.append("시간 초과")

            # 손실 기반 강제 종료
            if any(keyword in reasoning for keyword in ["손절", "손실", "-1.0%", "-1.5%"]):
                should_close = True
                force_close_reasons.append("손실 한계")

            # 수익 실현 강제 종료
            if any(keyword in reasoning for keyword in ["수익 실현", "+1.0%", "+2.0%", "익절"]):
                should_close = True
                force_close_reasons.append("수익 실현")

            # 신호 방향 변화 강제 종료 (숏 포지션 + 상승 신호)
            if "short" in reasoning.lower() and any(keyword in reasoning.lower() for keyword in ["bullish", "상승", "buy"]):
                should_close = True
                force_close_reasons.append("신호 방향 변화")

            # 강제 종료 로깅
            if force_close_reasons:
                logger.warning(f"🔧 강제 종료 조건 감지 v2: {', '.join(force_close_reasons)}")
                logger.warning(f"🔧 강제 종료 근거: {reasoning}")

            logger.info(f"🔍 LLM 클로즈 판단 v2: should_close={should_close}, situation={situation_type}, importance={hold_importance}, confidence={confidence:.2f}")
            logger.info(f"🔍 LLM 판단 근거 v2: {reasoning}")

            return should_close

        except Exception as e:
            logger.error(f"포지션 종료 판단 v2 실패: {e}")
            return False

    def _close_position_with_strategy_v2(self, symbol: str, position: Dict[str, Any],
                                       strategy: Dict[str, Any]) -> None:
        """전략에 따른 포지션 종료 v2"""
        try:
            logger.info(f"🔄 [{symbol}] 포지션 종료 실행 v2: {strategy.get('reason', 'unknown')}")

            # 🔧 실제 바이낸스 포지션 종료 먼저 실행
            close_reason = f"llm_strategy_{strategy.get('reason', 'unknown')}"
            self._close_real_binance_position(symbol, close_reason)
            logger.info(f"🔄 [{symbol}] 실제 바이낸스 포지션 종료 완료 v2: {close_reason}")

            # 포트폴리오의 포지션 종료 메서드 호출
            if hasattr(self.portfolio, 'close_position'):
                result = self.portfolio.close_position(
                    symbol,
                    reason=f"llm_strategy_{strategy.get('reason', 'unknown')}"
                )

                if result.get("success", False):
                    logger.info(f"✅ [{symbol}] 포지션 종료 성공 v2: {result}")
                else:
                    logger.error(f"❌ [{symbol}] 포지션 종료 실패 v2: {result}")
            else:
                logger.warning(f"⚠️ [{symbol}] 포트폴리오에 close_position 메서드 없음")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 종료 실행 v2 실패: {e}")



