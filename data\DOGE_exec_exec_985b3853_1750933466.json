{"execution_id": "exec_985b3853_1750933466", "symbol": "DOGE", "timestamp": 1750933466, "datetime": "2025-06-26 19:24:26", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "bbd0a723-0012-4f08-83c4-4cdf6f61bc96", "timestamp": 1750933466, "market_data": {"id": "market_DOGE_1750933446", "symbol": "DOGE", "timestamp": 1750933446, "datetime": "2025-06-26 19:24:06", "date": "2025-06-26", "time": "19:24:06", "price": 0.16251, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.16251, "volume": 4966433322.0, "volume_24h": 4966433322.0, "high_24h": 0.16809, "low_24h": 0.16202, "percent_change_24h": -1.824, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8664109, "social_dominance": 0.88, "social_contributors": 86641, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750933446, "news_count": 38, "ema_7": 0.16259142857142855, "ema_14": 0.16250785714285715, "ema_25": 0.16271200000000005, "ema_50": 0.16363139999999998, "ema_99": 0.16413191919191922, "ema_200": 0.16473024999999986, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0207894736842102, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 24399780072.89, "recent_news_titles": ["<PERSON> Threatens to Double Spanish Tariffs Over NATO Spending", "DOGE Price Analysis: Dogecoin Bulls Defend 16 Cent Support Amid <PERSON> Musk’s X Payments Speculation", "DOGE Price News: <PERSON><PERSON><PERSON><PERSON> Higher as Bulls Break 16 Cent Resistance", "Is DOGE doomed to fail? Some experts are ready to call it. &#x2d; Ars Technica", "DOGE layoffs are starting to leave their mark on D.C.’s housing market"], "top_social_posts": [{"text": "Permission for hugs?", "sentiment": 3.5, "platform": "tweet"}, {"text": "🎯", "sentiment": 3, "platform": "tweet"}, {"text": "@elonmusk Off you go🔥", "sentiment": 3, "platform": "tweet"}, {"text": "Nobody cares.", "sentiment": 3.57, "platform": "tweet"}, {"text": "Crowd caused chaos in Santa Ana by getting in the way of police during an arrest.\n\n", "sentiment": 2.52, "platform": "tweet"}, {"text": "An illegal from Iran was arrested in Los Angeles. A bystander yelled at ICE officers during the arrest, and then a woman, said to be Iranian ran out upset.\n\n", "sentiment": 2.47, "platform": "tweet"}, {"text": "‘Liver King’ shows ankle monitor following arrest over threats to <PERSON>.\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "ICE agents caught and arrested an illegal person at a Home Depot in Huntington Park, CA.\n\nThe guy recording and yelling should be arrested too..\n\n", "sentiment": 2.53, "platform": "tweet"}, {"text": "Friends help make sure his wheelchair doesn’t get in the way of a great wedding.\n\n", "sentiment": 3.6, "platform": "tweet"}, {"text": "Flashback: Maryland parents express worry over <PERSON>'s move to stop \"gender-affirming care\" for minors.\n\n", "sentiment": 2.79, "platform": "tweet"}], "recent_candles": [[1750932300000, "0.162530", "0.162790", "0.162520", "0.162670", "12224472", 1750932359999, "1988779.562210", 1990, "9194071", "1495778.039750", "0"], [1750932360000, "0.162660", "0.162810", "0.162660", "0.162790", "4775747", 1750932419999, "777081.821510", 1281, "2892153", "470595.285110", "0"], [1750932420000, "0.162780", "0.162870", "0.162710", "0.162770", "7659873", 1750932479999, "1247119.196920", 1440, "3081541", "501745.832500", "0"], [1750932480000, "0.162760", "0.162770", "0.162680", "0.162720", "2317171", 1750932539999, "377039.848710", 669, "1423279", "231579.483620", "0"], [1750932540000, "0.162730", "0.162830", "0.162730", "0.162780", "6416364", 1750932599999, "1044529.092470", 1252, "4394926", "715457.064460", "0"], [1750932600000, "0.162770", "0.162820", "0.162700", "0.162710", "3890568", 1750932659999, "633244.892670", 827, "2112400", "343834.435610", "0"], [1750932660000, "0.162700", "0.162790", "0.162680", "0.162680", "1961267", 1750932719999, "319155.646900", 748, "1301256", "211758.582350", "0"], [1750932720000, "0.162690", "0.162690", "0.162500", "0.162520", "2403444", 1750932779999, "390704.972390", 826, "1073214", "174453.026280", "0"], [1750932780000, "0.162510", "0.162510", "0.162450", "0.162480", "1868423", 1750932839999, "303566.520910", 569, "581011", "94396.748960", "0"], [1750932840000, "0.162480", "0.162570", "0.162470", "0.162540", "2025923", 1750932899999, "329268.601620", 719, "1329913", "216135.328700", "0"], [1750932900000, "0.162550", "0.162580", "0.162470", "0.162570", "2637955", 1750932959999, "428805.859380", 707, "1613914", "262340.507190", "0"], [1750932960000, "0.162580", "0.162710", "0.162540", "0.162640", "3552647", 1750933019999, "577722.310540", 916, "2155720", "350551.542030", "0"], [1750933020000, "0.162640", "0.162650", "0.162460", "0.162460", "4114208", 1750933079999, "668832.753610", 740, "941563", "153096.146980", "0"], [1750933080000, "0.162450", "0.162480", "0.162380", "0.162410", "2335001", 1750933139999, "379263.521830", 698, "810623", "131668.123440", "0"], [1750933140000, "0.162410", "0.162700", "0.162410", "0.162660", "1768983", 1750933199999, "287579.633610", 844, "1390996", "226101.901450", "0"], [1750933200000, "0.162660", "0.162800", "0.162660", "0.162720", "3015360", 1750933259999, "490769.302620", 707, "1315282", "214044.590860", "0"], [1750933260000, "0.162720", "0.162770", "0.162630", "0.162640", "1526250", 1750933319999, "248367.399190", 573, "510055", "83010.830230", "0"], [1750933320000, "0.162640", "0.162660", "0.162520", "0.162590", "3157903", 1750933379999, "513474.267040", 867, "2071393", "336791.468800", "0"], [1750933380000, "0.162590", "0.162590", "0.162460", "0.162510", "1374985", 1750933439999, "223429.265760", 596, "530918", "86271.642370", "0"], [1750933440000, "0.162510", "0.162520", "0.162430", "0.162440", "457038", 1750933499999, "74255.704320", 183, "24809", "4030.098010", "0"]], "candles_count": 20, "data_timestamp": 1750933446, "has_timeseries_data": true, "short_term_change_pct": -0.17207472959685502, "recent_high": 0.1628, "recent_low": 0.16243}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "Short-term bearish trend confirmed by 5 consecutive bearish candles with volume confirmation. Strong downward pressure evident in price action. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish trend", "volume confirmation", "short-term pattern"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term bearish trend confirmed by 5 consecutive bearish candles with volume confirm...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_c4fbecae_1750933453"}, "strategy": {"strategy_id": "bbd0a723-0012-4f08-83c4-4cdf6f61bc96", "symbol": "DOGE", "timestamp": 1750933465, "type": "sell", "entry_price": 0.16251, "stop_loss": 0.16454, "take_profit": 0.15786, "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 $0.16251로, 이 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정했습니다. 기술적 지표(RSI, MACD)와 볼린저 밴드 분석을 통해 단기 하락 흐름이 지속될 가능성이 높아 보입니다.", "confidence": 0.75, "reasoning_card_id": "card_c4fbecae_1750933453", "risk_level": "medium", "key_points": ["InCA 신호는 SELL로 일치", "기술적 지표는 하락 추세를 보여줌", "리스크/리워드 비율 1.2 이상 유지"], "market_context": {"price": 0.16251, "percent_change_24h": -1.824, "timestamp": 1750933446}, "paper_based": false, "risk_reward": 2.2906403940886593, "importance": 9.166094631951742, "consensus_signal": "sell", "consensus_confidence": 0.75, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.75, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.75, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_985b3853_1750933466"}}