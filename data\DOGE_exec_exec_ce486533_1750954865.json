{"execution_id": "exec_ce486533_1750954865", "symbol": "DOGE", "timestamp": 1750954865, "datetime": "2025-06-27 01:21:05", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "9dfc541b-86ad-4ad4-8605-073bd73ef096", "timestamp": 1750954865, "market_data": {"id": "market_DOGE_1750954845", "symbol": "DOGE", "timestamp": 1750954845, "datetime": "2025-06-27 01:20:45", "date": "2025-06-27", "time": "01:20:45", "price": 0.15914, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15914, "volume": 5155952925.0, "volume_24h": 5155952925.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -3.028, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8518504, "social_dominance": 0.478, "social_contributors": 85185, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750954845, "news_count": 47, "ema_7": 0.15951714285714286, "ema_14": 0.15972642857142857, "ema_25": 0.159774, "ema_50": 0.15961319999999998, "ema_99": 0.16013030303030307, "ema_200": 0.16194025, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.041914893617021, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23894326365.95, "recent_news_titles": ["Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing", "TSMC to Inject $10 Billion Capital in Unit to Counter FX Swings"], "top_social_posts": [{"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "🚨$470,000,000.00 saved!  Thank you DOGE!", "sentiment": 3.21, "platform": "tweet"}, {"text": "DOGE just terminated 312 government contracts with a ceiling value of $2.8B and savings of $470M...in just the last week\n\nNotably a $286k professional and management development contract for an “entrepreneurship course at Harvard University”and $485k USAID contract for a “senior general development advisor at USAID Madagascar.\"", "sentiment": 3.17, "platform": "tweet"}, {"text": "Young woman doesn't believe civilians should have firearms and wants the 2nd amendment removed from the constitution.  \n\n", "sentiment": 3.09, "platform": "tweet"}, {"text": "Dr. <PERSON><PERSON> went to buy champagne in anticipation of <PERSON><PERSON>’s win and laughed at the guy who checked her out when he said the race was close.\n\nWhat’s she up to these days?\n\n", "sentiment": 3.32, "platform": "tweet"}, {"text": "Will you be able to resist the temptation OR you HODL BabyDoge and never let go?", "sentiment": 3, "platform": "tweet"}, {"text": "Which #memecoin is printing money today? Shill me the next $DOGE 👀", "sentiment": 3, "platform": "tweet"}, {"text": "Closed $NPC. But holding wouldn't hurt either. Some memes are gonna take a hit early next week as dog<PERSON> is gonna take a hit. <PERSON><PERSON> of Tesla", "sentiment": 2.88, "platform": "tweet"}, {"text": "How it feels to be 27", "sentiment": 3.14, "platform": "tweet"}, {"text": "@DOGE Hey @<PERSON><PERSON><PERSON><PERSON> are you going to codify these DOGE cuts?\n\nOr are you gonna just take more vacation and do nothing at all for the American people???", "sentiment": 2.94, "platform": "tweet"}], "recent_candles": [[1750953660000, "0.159980", "0.160160", "0.159980", "0.160040", "3657230", 1750953719999, "585385.085290", 1321, "2412955", "386238.158890", "0"], [1750953720000, "0.160050", "0.160100", "0.159980", "0.160020", "5724962", 1750953779999, "916096.980220", 931, "4331570", "693092.405270", "0"], [1750953780000, "0.160020", "0.160080", "0.159870", "0.159940", "2942007", 1750953839999, "470714.460460", 879, "1109330", "177523.122200", "0"], [1750953840000, "0.159950", "0.160020", "0.159880", "0.159990", "1342655", 1750953899999, "214745.369520", 660, "314482", "50299.752440", "0"], [1750953900000, "0.159990", "0.159990", "0.159640", "0.159720", "3287281", 1750953959999, "525186.422910", 929, "1202789", "192121.761670", "0"], [1750953960000, "0.159720", "0.159720", "0.159520", "0.159530", "3777784", 1750954019999, "602950.839980", 1415, "435852", "69571.133270", "0"], [1750954020000, "0.159540", "0.159590", "0.159420", "0.159580", "4451635", 1750954079999, "709993.416890", 1107, "2104565", "335704.909860", "0"], [1750954080000, "0.159570", "0.159610", "0.159490", "0.159570", "1455180", 1750954139999, "232162.178730", 776, "721767", "115148.512420", "0"], [1750954140000, "0.159560", "0.159570", "0.159470", "0.159560", "1633720", 1750954199999, "260625.070590", 664, "504834", "80535.376560", "0"], [1750954200000, "0.159560", "0.159580", "0.159300", "0.159350", "6099528", 1750954259999, "972525.645340", 1083, "1196848", "190768.966460", "0"], [1750954260000, "0.159350", "0.159360", "0.159230", "0.159230", "1707870", 1750954319999, "272041.089690", 971, "498462", "79405.318490", "0"], [1750954320000, "0.159230", "0.159480", "0.159230", "0.159330", "1186214", 1750954379999, "189069.763460", 867, "610490", "97301.006660", "0"], [1750954380000, "0.159330", "0.159390", "0.159330", "0.159340", "871816", 1750954439999, "138929.634530", 625, "505977", "80632.606830", "0"], [1750954440000, "0.159350", "0.159580", "0.159350", "0.159560", "983577", 1750954499999, "156842.792820", 681, "670328", "106910.123540", "0"], [1750954500000, "0.159560", "0.159560", "0.159360", "0.159380", "2538680", 1750954559999, "404748.239540", 607, "1499732", "239078.541170", "0"], [1750954560000, "0.159390", "0.159490", "0.159180", "0.159190", "2797075", 1750954619999, "445619.622080", 997, "654678", "104305.118380", "0"], [1750954620000, "0.159180", "0.159420", "0.159120", "0.159380", "3214851", 1750954679999, "511743.496460", 959, "1045282", "166403.411020", "0"], [1750954680000, "0.159380", "0.159380", "0.159180", "0.159180", "1655176", 1750954739999, "263630.200030", 908, "469486", "74781.037120", "0"], [1750954740000, "0.159170", "0.159250", "0.159130", "0.159130", "1051832", 1750954799999, "167436.620160", 640, "385373", "61348.637230", "0"], [1750954800000, "0.159130", "0.159200", "0.159120", "0.159120", "1307597", 1750954859999, "208107.836070", 563, "786553", "125179.485500", "0"]], "candles_count": 20, "data_timestamp": 1750954845, "has_timeseries_data": true, "short_term_change_pct": -0.0439726113449254, "recent_high": 0.15949, "recent_low": 0.15912}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "Short-term bearish trend confirmed by 5 consecutive bearish candles with volume consistency and a clear downward trendline formation. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish trend", "volume confirmation", "downward trendline"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term bearish trend confirmed by 5 consecutive bearish candles with volume consist...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_f7e91e2b_1750954852"}, "strategy": {"strategy_id": "9dfc541b-86ad-4ad4-8605-073bd73ef096", "symbol": "DOGE", "timestamp": 1750954864, "type": "sell", "entry_price": 0.15914, "stop_loss": 0.16243, "take_profit": 0.15555, "reasoning": "InCA 신호는 SELL을 추천하며, 현재 시장 데이터 분석 결과 가격이 저항선을 테스트하고 있는 것으로 보임. RSI는 과열 상태를 나타내며, MACD는 하향 추세를 강화하고 있음. 볼린저 밴드 상단을 돌파한 후 빠른 조정이 예상됨.", "confidence": 0.78, "reasoning_card_id": "card_f7e91e2b_1750954852", "risk_level": "medium", "key_points": ["InCA 신호와 일치한 SELL 추천", "RSI 과열 상태", "MACD 하향 추세 강화"], "market_context": {"price": 0.15914, "percent_change_24h": -3.028, "timestamp": 1750954845}, "paper_based": false, "risk_reward": 1.0911854103343537, "importance": 8.884789929538346, "consensus_signal": "sell", "consensus_confidence": 0.78, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.78, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.78, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_ce486533_1750954865"}}