2025-06-27 00:25:11 - database.news_vectordb - INFO - huggingface_hub에 cached_download 함수가 없어 패치 적용
2025-06-27 00:25:11 - database.news_vectordb - INFO - huggingface_hub.cached_download 패치 완료
2025-06-27 00:25:15 - database.market_data - INFO - SentenceTransformer 모듈 로드 성공
2025-06-27 00:25:15 - simulator.utils.market_utils - INFO - LunarCrush API 키 상태: 설정됨
2025-06-27 00:25:15 - lunar_monitor - INFO - LunarCrush API initialized with API key
2025-06-27 00:25:15 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-27 00:25:15 - database.news_vectordb - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-27 00:25:15 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:25:17 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-27 00:25:17 - database.news_vectordb - INFO - Successfully loaded model: all-MiniLM-L6-v2
2025-06-27 00:25:17 - lunar_monitor - INFO - Successfully initialized NewsVectorDB
2025-06-27 00:25:17 - database.market_data - INFO - Initializing MarketDataDB
2025-06-27 00:25:17 - database.market_data - INFO - ChromaDB client initialized successfully
2025-06-27 00:25:17 - database.market_data - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-27 00:25:17 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:25:17 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-27 00:25:17 - database.market_data - INFO - SentenceTransformer model loaded successfully
2025-06-27 00:25:17 - database.market_data - INFO - Successfully accessed collection: topic_metrics
2025-06-27 00:25:17 - database.market_data - INFO - Successfully accessed collection: time_series
2025-06-27 00:25:17 - database.market_data - INFO - MarketDataDB initialized successfully
2025-06-27 00:25:17 - lunar_monitor - INFO - Successfully initialized MarketDataDB
2025-06-27 00:25:17 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:25:17 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:25:17 - simulator.utils.market_utils - INFO - LunarMonitor API & Collector 연결 초기화 완료
2025-06-27 00:25:17 - simulator.trading.portfolio - INFO - 🎯 통합 생각카드 시스템 로드 성공
2025-06-27 00:25:17 - hybrid_simulator - INFO - PyTorch 임포트 성공
2025-06-27 00:25:17 - hybrid_simulator - INFO - ChatTS 클라이언트 임포트 성공
2025-06-27 00:25:17 - hybrid_simulator - INFO - 로깅 시스템 초기화 완료 - 타임스탬프 테스트
2025-06-27 00:25:17 - hybrid_simulator - INFO - ===== 하이브리드 거래 시스템 초기화 =====
2025-06-27 00:25:17 - hybrid_simulator - INFO - 시스템 인코딩: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - 표준 출력 인코딩: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - 환경 변수 PYTHONIOENCODING: utf-8
2025-06-27 00:25:17 - hybrid_simulator - INFO - .env 파일 로드 완료
2025-06-27 00:25:17 - hybrid_simulator - INFO - OPENAI_API_KEY 환경 변수가 설정되어 있습니다.
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 환경 변수 확인: USE_VLLM=true, 파싱 결과=True
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 사용 여부: 커맨드라인=True, 환경변수=True, 최종=True
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 모델 설정: 환경변수=Qwen/Qwen3-14B-AWQ, 커맨드라인=Qwen/Qwen3-14B-AWQ, 최종=Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 서버 사용: http://localhost:8001/v1, 모델: Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - hybrid_simulator - INFO - VLLMClientFactory 클래스 임포트 성공
2025-06-27 00:25:17 - hybrid_simulator - INFO - vLLM 클라이언트 초기화 시작: URL=http://localhost:8001/v1, 모델=Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:17 - models.vllm_client_factory - INFO - Using timeout from environment: 600 seconds
2025-06-27 00:25:17 - models.vllm_client_factory - INFO - Creating enhanced VLLM client (model: Qwen/Qwen3-14B-AWQ, timeout: 600s, max_tokens: 8192)
2025-06-27 00:25:17 - models.vllm_client_enhanced - INFO - Qwen3 모델 감지됨: Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:19 - models.vllm_client_enhanced - INFO - VLLM 서버 설정 확인: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': 1750951519, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-b72d18643736453298a8bacbd2036929', 'object': 'model_permission', 'created': 1750951519, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-27 00:25:19 - models.vllm_client_enhanced - INFO - VLLM 서버 max_model_len: 9096
2025-06-27 00:25:19 - models.vllm_client_enhanced - INFO - VLLM 클라이언트 설정: 타임아웃=600초, 최대토큰=8192
2025-06-27 00:25:19 - models.vllm_session_manager - INFO - VLLMSessionManager 초기화 완료
2025-06-27 00:25:19 - models.vllm_client_enhanced - INFO - Qwen3 모델 최적화 설정 적용: temperature=0.6, top_p=0.95, top_k=20, presence_penalty=1.5
2025-06-27 00:25:19 - models.vllm_client_enhanced - INFO - Enhanced VLLM client initialized (server: http://localhost:8001/v1, model: Qwen/Qwen3-14B-AWQ)
2025-06-27 00:25:19 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 초기화 완료: <models.vllm_client_enhanced.VLLMClientEnhanced object at 0x0000025E2F115960>
2025-06-27 00:25:19 - hybrid_simulator - INFO - vLLM 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:25:19 - hybrid_simulator - INFO - vLLM 클라이언트 속성: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_initialized', '_instance', 'default_presence_penalty', 'default_temperature', 'default_top_k', 'default_top_p', 'generate', 'generate_fast', 'generate_json', 'health_check', 'is_qwen3', 'max_tokens', 'model_name', 'prompt_processor', 'response_parser', 'server_url', 'session_manager', 'timeout']
2025-06-27 00:25:19 - hybrid_simulator - INFO - vLLM 클라이언트 서버 URL: http://localhost:8001/v1
2025-06-27 00:25:19 - hybrid_simulator - INFO - vLLM 클라이언트 모델명: Qwen/Qwen3-14B-AWQ
2025-06-27 00:25:19 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 시작
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - VLLM server health check successful: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': **********, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-c6ae6c25544d4c31a587f042d809ebc4', 'object': 'model_permission', 'created': **********, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-27 00:25:21 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 결과: True
2025-06-27 00:25:21 - hybrid_simulator - INFO - vLLM 서버 연결 성공
2025-06-27 00:25:21 - hybrid_simulator - INFO - vLLM 서버 테스트 중 (짧은 프롬프트)...
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 90자
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-27 00:25:21 - models.vllm_prompt_processor - WARNING - 프롬프트에서 심볼을 추출할 수 없습니다.
2025-06-27 00:25:21 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 2/8)
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:25:21 - models.vllm_session_manager - INFO - Created new session: f8e8f70d-113c-4b8d-ad22-6e6ec1c21338 for sela agent
2025-06-27 00:25:21 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: sela_execution_default → f8e8f70d-113c-4b8d-ad22-6e6ec1c21338
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - sela execution 세션 ID: f8e8f70d-113c-4b8d-ad22-6e6ec1c21338 (기본)
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 504
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Session ID: f8e8f70d-113c-4b8d-ad22-6e6ec1c21338
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think



market data, HiAR organizes reasoning, SELA executes strategies.






CRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT U...
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\n\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 100, 'temperature': 0.0, 'top_p': 0.7, 'stop': []}
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 126
2025-06-27 00:25:21 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - Text generation complete (time: 3.49s)
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "analysis": "The provided statement outlines a system where market data is processed by HiAR for reasoning, and SELA is responsible for executing strategies. This suggests a division of labor betw...
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 480자):
2025-06-27 00:25:24 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {
  "analysis": "The provided statement outlines a system where market data is processed by HiAR for reasoning, and SELA is responsible for executing strategies. This suggests a division of labor between analysis and execution, which is common in automated trading systems.",
  "confidence": 0.85,
  "reasoning": "The system appears to separate high-level analysis (HiAR) from strategy execution (SELA), which aligns with typical architectures in financial systems. The confidence
2025-06-27 00:25:24 - models.vllm_client_enhanced - WARNING - 🔍 HiAR 응답에 불완전한 JSON 구조 발견 (토큰 제한으로 잘림)
2025-06-27 00:25:24 - hybrid_simulator - INFO - vLLM 서버 테스트 성공
2025-06-27 00:25:24 - hybrid_simulator - WARNING - !!!!! 주의: 실제 거래 모드로 실행합니다 !!!!!
2025-06-27 00:25:24 - hybrid_simulator - WARNING - !!!!! 실제 자금이 사용됩니다 !!!!!
2025-06-27 00:27:37 - hybrid_simulator - INFO - ChatTS 모델 대신 vLLM 클라이언트를 사용합니다.
2025-06-27 00:27:37 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 래퍼 함수 생성
2025-06-27 00:27:37 - models.vllm_client_factory - INFO - Created wrapper for Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:37 - hybrid_simulator - INFO - vLLM 모델 래퍼 생성 완료: Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:37 - hybrid_simulator - INFO - vLLM 모델 설정 완료: Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:37 - hybrid_simulator - INFO - LLM 모델 설정 준비 완료 (vLLM 사용 시 나중에 설정됨)
2025-06-27 00:27:37 - binance.binance_utils - INFO - 프로덕션 모드 사용 중: https://fapi.binance.com
2025-06-27 00:27:37 - binance.binance_utils - INFO - API 키: iVljo...EypUp
2025-06-27 00:27:37 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:37 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951656427}
2025-06-27 00:27:37 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951656427
2025-06-27 00:27:37 - binance.binance_utils - INFO - ✅ 바이낸스 서버 시간 동기화 성공: 1750951656427, 로컬 시간: 1750951657699, 차이: 1272ms
2025-06-27 00:27:37 - binance.binance_utils - WARNING - ⚠️ 로컬 시간과 서버 시간의 차이가 큽니다: 1272ms
2025-06-27 00:27:37 - binance.binance_utils - INFO - 바이낸스 API 연결 테스트 성공
2025-06-27 00:27:37 - binance.binance_utils - INFO - API 키: 설정됨
2025-06-27 00:27:37 - hybrid_simulator - INFO - 바이낸스 API 초기화 완료 (테스트넷: False)
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - ✅ Portfolio 초기화: 바이낸스 유틸리티 연결됨 (<class 'binance.binance_utils.BinanceUtils'>)
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 실제 거래 모드가 사용됩니다. 바이낸스 API를 통해 잔액을 동기화하세요.
2025-06-27 00:27:37 - simulator.trading.portfolio - WARNING - !!!!! 실제 거래 모드로 실행 중입니다 !!!!!
2025-06-27 00:27:37 - simulator.trading.portfolio - WARNING - !!!!! 실제 자금이 사용됩니다 !!!!!
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 기존 포트폴리오 로드 완료: 잔액 $614.81 USDT
2025-06-27 00:27:37 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750948983_1750951657
2025-06-27 00:27:37 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [SOL] 통합 생각카드 생성: card_trade_SOL_1750949230_1750951657
2025-06-27 00:27:37 - trading.thinking_cards.position_card_manager - INFO - 🔧 최근 카드 로딩 완료: 2개 로드, 12개 스킵 (1시간 이내)
2025-06-27 00:27:37 - trading.thinking_cards.position_card_manager - INFO - 🎯 포지션 카드 매니저 초기화 완료 (활성 카드: 2개, 완료 카드: 0개)
2025-06-27 00:27:37 - trading.thinking_cards.position_card_manager - INFO - 🔧 오래된 카드 파일 정리 완료: 2개 삭제 (6시간 이전)
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 🔧 시스템 시작 시 오래된 카드 파일 정리: 2개 삭제
2025-06-27 00:27:37 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750948983_1750951657
2025-06-27 00:27:37 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [SOL] 통합 생각카드 생성: card_trade_SOL_1750949230_1750951657
2025-06-27 00:27:37 - trading.thinking_cards.position_card_manager - INFO - 🔧 최근 카드 로딩 완료: 2개 로드, 10개 스킵 (1시간 이내)
2025-06-27 00:27:37 - trading.thinking_cards.position_card_manager - INFO - 🎯 포지션 카드 매니저 초기화 완료 (활성 카드: 2개, 완료 카드: 0개)
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 🎯 통합 생각카드 매니저 초기화 성공: G:\ai_bot_trading\data\thinking_cards
2025-06-27 00:27:37 - simulator.trading.portfolio - INFO - 포트폴리오 로드 완료
2025-06-27 00:27:37 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 환경변수 원본 값:
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_CONFIDENCE (str): '0.01'
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_IMPORTANCE (str): '0.001'
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_RISK_REWARD_RATIO (str): '0.001'
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 적응형 임계값 로드 완료:
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_CONFIDENCE: 0.01
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_IMPORTANCE: 0.001
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 🔧 - MIN_RISK_REWARD_RATIO: 0.001
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - ChromaDB에 저장된 패턴이 없습니다. 새로 시작합니다.
2025-06-27 00:27:37 - trading.strategies.intelligent_position_sizing - INFO - 패턴 파일이 없습니다. 새로 시작합니다: G:\ai_bot_trading\data\trading_patterns\learned_patterns.json
2025-06-27 00:27:37 - hybrid_simulator - INFO - 고급 트레이딩 시스템 초기화 완료
2025-06-27 00:27:37 - models.vllm_request_queue - INFO - vLLM 요청 큐 초기화 완료
2025-06-27 00:27:37 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-27 00:27:37 - trading.hybrid_architecture.data_logger - INFO - 데이터 로거 초기화 완료: G:\ai_bot_trading\data
2025-06-27 00:27:37 - hybrid_simulator - WARNING - LLM 모델이 설정되지 않았습니다. 하이브리드 컨트롤러 초기화를 건너뜁니다.
2025-06-27 00:27:37 - hybrid_simulator - WARNING - 나중에 chatts_model을 설정한 후 initialize_controller() 메서드를 호출하세요.
2025-06-27 00:27:37 - models.vllm_request_queue - INFO - vLLM 클라이언트 설정 완료
2025-06-27 00:27:37 - hybrid_simulator - INFO - vLLM 요청 큐 초기화 완료
2025-06-27 00:27:37 - hybrid_simulator - INFO - 최소 신뢰도 임계값 설정 (환경변수): 0.01
2025-06-27 00:27:37 - hybrid_simulator - INFO - 최소 중요도 임계값 설정 (환경변수): 0.001
2025-06-27 00:27:37 - hybrid_simulator - INFO - 연속 모드 설정 완료
2025-06-27 00:27:37 - hybrid_simulator - INFO - 하이브리드 시스템에 vLLM 모델 설정 완료
2025-06-27 00:27:37 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-27 00:27:37 - trading.hybrid_architecture.data_logger - INFO - 데이터 로거 초기화 완료: G:\ai_bot_trading\data
2025-06-27 00:27:37 - hybrid_simulator - INFO - LLM 모델 사용: vllm - Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:40 - simulator.utils.vllm_client - INFO - vLLM API 연결 성공: 1개 모델 사용 가능
2025-06-27 00:27:40 - simulator.utils.vllm_client - INFO - 모델 'Qwen/Qwen3-14B-AWQ' 사용 가능
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 llm_client 설정 완료: Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:40 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-27 00:27:40 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:27:40 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 임베딩 함수 생성 완료: all-MiniLM-L6-v2
2025-06-27 00:27:40 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'inca_experiences_v3' 호환성 확인 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 벡터 DB 초기화 완료: 139개 경험 데이터
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 초기화 완료 (중요도 임계값: 0.3)
2025-06-27 00:27:40 - hybrid_simulator - INFO - InCA 에이전트에 시스템 설명 전달 완료 (학습 기능 활성화, LLM: 있음)
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.hiar_factory - INFO - FastHiAR 어댑터 생성
2025-06-27 00:27:40 - offline_pattern_generator - INFO - 5개 패턴 로드 완료
2025-06-27 00:27:40 - fast_hiar_agent - INFO - FastHiAR 에이전트 초기화 완료: 5개 패턴 로드
2025-06-27 00:27:40 - trading.reasoning.hiar_adapter - INFO - 기존 FastHiAR 에이전트 로드 완료
2025-06-27 00:27:40 - hybrid_simulator - INFO - HiAR 에이전트에 시스템 설명 전달 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: VLLMWrapper (VLLMWrapper 불필요)
2025-06-27 00:27:40 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-27 00:27:40 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: G:\ai_bot_trading\data\strategy_db\strategies.db
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-27 00:27:40 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 Tree Search 기반 SELA 에이전트 초기화 완료
2025-06-27 00:27:40 - hybrid_simulator - INFO - SELA 에이전트에 시스템 설명 전달 완료
2025-06-27 00:27:40 - HybridInterface - INFO - HybridInterface initialized
2025-06-27 00:27:40 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: G:\ai_bot_trading\data\hybrid_store.db
2025-06-27 00:27:40 - trading.hybrid_architecture.data.execution_log - INFO - 실행 로그 데이터베이스 초기화 완료: G:\ai_bot_trading\data\execution_logs.db
2025-06-27 00:27:40 - trading.hybrid_architecture.data.data_integration - INFO - 하이브리드 데이터 통합 초기화 완료
2025-06-27 00:27:40 - HybridInterface - INFO - Vector database initialized
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 인터페이스 초기화 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 계층적 합의 시스템 비활성화 완료 - SELA 직접 사용
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 신호 안정화 메커니즘 초기화 완료 (연속모드: True, 활성화: False)
2025-06-27 00:27:40 - trading.hybrid_architecture.lunar_data_collector - INFO - 데이터베이스 초기화 완료: data/lunar_data.db
2025-06-27 00:27:40 - trading.hybrid_architecture.data_store - INFO - 데이터베이스 초기화 완료: data/hybrid_store.db
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🧹 시스템 시작 시 내부 포지션 강제 정리 실행
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🧹 내부 포지션 강제 정리 시작: ['SOL', 'BNB']
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🗑️ 제거할 포지션: SOL long (ID: trade_SOL_1750949230)
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🗑️ 제거할 포지션: BNB long (ID: sync_BNB_1750948983)
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - ✅ 포지션 제거 완료: BNB long
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - ✅ 포지션 제거 완료: SOL long
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - ✅ 자산 제거 완료: SOL 0.72
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🎯 내부 포지션 강제 정리 완료: 2개 포지션 제거
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ 내부 포지션 강제 정리 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔥 시스템 시작 시 바이낸스와 강제 동기화 실행
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔥 바이낸스와 강제 동기화 시작
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-27 00:27:40 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:40 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951659056}
2025-06-27 00:27:40 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951659056
2025-06-27 00:27:40 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951659056
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 523
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: SOLUSDT = 0.72 (PnL: -0.1728)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -1.00372)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.1809)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -1.94544)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - positionAmt: 0.72 (float: 0.72)
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - entryPrice: 143.47
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - unrealizedProfit: -0.17280000
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:27:40 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - unrealizedProfit: -1.94544000
2025-06-27 00:27:40 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 바이낸스 실제 활성 포지션: 4개
2025-06-27 00:27:40 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:40 - binance.binance_utils - INFO - 변화율(%): -0.576
2025-06-27 00:27:40 - binance.binance_utils - INFO - 거래량: 18844375.88
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 [SOLUSDT] long 포지션:
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 수량: 0.72
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 진입가: $143.4700
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 현재가: $143.2000
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-0.1728
2025-06-27 00:27:40 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:40 - binance.binance_utils - INFO - 변화율(%): 1.310
2025-06-27 00:27:40 - binance.binance_utils - INFO - 거래량: 5259293.197
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 [ETHUSDT] long 포지션:
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 수량: 0.023
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 진입가: $2486.1300
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 현재가: $2442.3200
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-1.0037
2025-06-27 00:27:40 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:40 - binance.binance_utils - INFO - 변화율(%): 0.137
2025-06-27 00:27:40 - binance.binance_utils - INFO - 거래량: 295901.94
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 [BNBUSDT] long 포지션:
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 수량: 0.09
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 진입가: $646.6100
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 현재가: $644.6000
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-0.1809
2025-06-27 00:27:40 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-27 00:27:40 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:40 - binance.binance_utils - INFO - 변화율(%): -1.890
2025-06-27 00:27:40 - binance.binance_utils - INFO - 거래량: 5091700141
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 [DOGEUSDT] long 포지션:
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 수량: 504.0
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 진입가: $0.1643
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 현재가: $0.1604
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO -    - 미실현 손익: $-1.9454
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 내부 포지션: 2개
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 내부 [ETH] long 포지션: 진입가 $2486.1300, PnL -1.70%
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔍 내부 [DOGE] long 포지션: 진입가 $0.1643, PnL -2.42%
2025-06-27 00:27:40 - simulator.trading.portfolio - INFO - 🔥 바이낸스와 강제 동기화 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ 바이낸스 강제 동기화 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.data_store - INFO - 🔧 오래된 카드 정리 완료: 1개 삭제 (3일 이전)
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 시스템 시작 시 오래된 카드 정리: 1개 삭제
2025-06-27 00:27:40 - trading.hybrid_architecture.utils.virtual_position_tracker - INFO - 가상 포지션 추적기 초기화 완료 (중립 임계값: 0.5%)
2025-06-27 00:27:40 - trading.hybrid_architecture.utils.neutral_signal_evaluator - INFO - 중립 신호 평가기 초기화 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 🔮 가상 포지션 추적기 초기화 완료 (중립 임계값: 0.5%, 평가 주기: 3분)
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 컨트롤러 초기화 완료
2025-06-27 00:27:40 - models.vllm_request_queue - INFO - vLLM 클라이언트 설정 완료
2025-06-27 00:27:40 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (직접 접근 및 큐 모두)
2025-06-27 00:27:40 - hybrid_simulator - INFO - 하이브리드 컨트롤러에 vLLM 클라이언트 설정 완료 (client 속성)
2025-06-27 00:27:40 - hybrid_simulator - INFO - 하이브리드 컨트롤러 초기화 완료
2025-06-27 00:27:40 - simulator.bridge - INFO - 데이터 수집 간격: 60초
2025-06-27 00:27:40 - simulator.bridge - INFO - 학습 간격(일반 모드): 60초
2025-06-27 00:27:40 - simulator.bridge - INFO - 연속 모드 대기 시간: 60초
2025-06-27 00:27:40 - simulator.bridge - INFO - 🔍 LLM 클라이언트 디버깅: config.get('llm_client') = True
2025-06-27 00:27:40 - simulator.bridge - INFO - ✅ 설정에서 LLM 클라이언트 참조 가져오기 성공: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:27:40 - simulator.bridge - INFO - LunarDataCollector 인스턴스 연결 완료
2025-06-27 00:27:40 - simulator.bridge - INFO - 크로마DB 컬렉션 초기화 완료: execution_results, market_data, reasoning_traces, trading_strategies
2025-06-27 00:27:40 - database.news_vectordb - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-27 00:27:40 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:27:40 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-27 00:27:40 - database.news_vectordb - INFO - Successfully loaded model: all-MiniLM-L6-v2
2025-06-27 00:27:40 - trading.hybrid_architecture.news_vector_bridge - INFO - NewsVectorBridge 초기화 완료
2025-06-27 00:27:40 - simulator.bridge - INFO - 뉴스 벡터 브릿지 초기화 완료
2025-06-27 00:27:41 - simulator.trading.prediction_history - INFO - 예측 히스토리 로드 완료: 16578개 예측
2025-06-27 00:27:41 - simulator.trading.prediction_history - INFO - 예측 히스토리 관리자 초기화 완료: 16578개 예측 로드됨
2025-06-27 00:27:41 - flow - INFO - 흐름 로거 초기화 완료 - 타임스탬프 테스트
2025-06-27 00:27:41 - flow - INFO - --------- 실행 플로우 시뮬레이터 시작 ---------
2025-06-27 00:27:41 - flow - INFO - 거래 모드: real
2025-06-27 00:27:41 - flow - INFO - 테스트넷: False
2025-06-27 00:27:41 - flow - INFO - 중요도 임계값: 0.3
2025-06-27 00:27:41 - flow - INFO - 처리 심볼: BTC, ETH, SOL, BNB, DOGE
2025-06-27 00:27:41 - flow - INFO - ----------------------------------------
2025-06-27 00:27:41 - simulator.execution_flow.simulator - INFO - ExecutionFlowSimulator 초기화: symbols=['BTC', 'ETH', 'SOL', 'BNB', 'DOGE'], testnet=False, trading_mode=real, importance_threshold=0.3
2025-06-27 00:27:41 - simulator.bridge - INFO - ExecutionFlowSimulator에 hybrid_controller 참조 추가 완료
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - LearningLoopAnalyzer 초기화: 심볼=['BTC', 'ETH', 'SOL', 'BNB', 'DOGE']
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - 전달받은 심볼: ['BTC', 'ETH', 'SOL', 'BNB', 'DOGE']
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - LearningLoopAnalyzer: vLLM 클라이언트 사용 설정됨
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - LLM 클라이언트 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - LLM 모델명: Qwen/Qwen3-14B-AWQ
2025-06-27 00:27:41 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-27 00:27:41 - simulator.learning_loop.analyzer - INFO - HNSW 컬렉션 최적화 시작...
2025-06-27 00:27:41 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' 데이터 백업 중...
2025-06-27 00:27:42 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' HNSW 테스트 통과 - 재생성 불필요
2025-06-27 00:27:42 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' 데이터 백업 중...
2025-06-27 00:27:42 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' HNSW 테스트 통과 - 재생성 불필요
2025-06-27 00:27:42 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' 데이터 백업 중...
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' HNSW 테스트 통과 - 재생성 불필요
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - HNSW 컬렉션 최적화 완료
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_strategies' 정상 작동
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_reasonings' 정상 작동
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 컬렉션 'learning_evaluations' 정상 작동
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 임베딩 함수 생성 완료: all-MiniLM-L6-v2
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_strategies' 호환성 확인 완료
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_reasonings' 호환성 확인 완료
2025-06-27 00:27:43 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 기존 컬렉션 'learning_evaluations' 호환성 확인 완료
2025-06-27 00:27:43 - simulator.learning_loop.analyzer - INFO - ChromaDB 컬렉션 초기화 완료 (안전 모드)
2025-06-27 00:27:43 - simulator.learning_loop.analyzer - INFO - ChromaDB 클라이언트 초기화 완료
2025-06-27 00:27:43 - simulator.bridge - INFO - HybridSimulatorBridge 초기화 완료
2025-06-27 00:27:43 - hybrid_simulator - INFO - 하이브리드 시뮬레이터 브릿지 초기화 완료
2025-06-27 00:27:43 - hybrid_simulator - INFO - 하이브리드 컨트롤러 초기화 성공
2025-06-27 00:27:43 - hybrid_simulator - INFO - 연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)
2025-06-27 00:27:43 - hybrid_simulator - INFO - 하이브리드 거래 시스템 시작: 5 심볼, 모드: 실제 거래
2025-06-27 00:27:43 - hybrid_simulator - INFO - 연속 모드 활성화: 실행 → 학습 → 실행 → 학습 순서로 연속 실행
2025-06-27 00:27:43 - hybrid_simulator - INFO - 초기 시장 데이터 로드 중...
2025-06-27 00:27:43 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:43 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951661900}
2025-06-27 00:27:43 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951661900
2025-06-27 00:27:43 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951661900
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:27:43 - hybrid_simulator - INFO - USDT 잔액 정보 (API: 메인넷):
2025-06-27 00:27:43 - hybrid_simulator - INFO -   지갑 잔액: 610.87 USDT
2025-06-27 00:27:43 - hybrid_simulator - INFO -   사용 가능 잔액: 309.33 USDT
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 포트폴리오 잔액 업데이트: $614.81 -> $610.87 USDT
2025-06-27 00:27:43 - hybrid_simulator - INFO - 포트폴리오 잔액 업데이트: $614.81 → $610.87 USDT
2025-06-27 00:27:43 - hybrid_simulator - INFO - 포트폴리오 총 가치: $610.87
2025-06-27 00:27:43 - hybrid_simulator - INFO - 초기 잔액: $10000.00
2025-06-27 00:27:43 - hybrid_simulator - INFO - 총 손익: $-9389.13 (-93.89%)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 포지션 동기화 시작: 강제=False, 마지막 동기화 이후 1750951663초 경과
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 바이낸스 API를 통해 포지션 정보 동기화 시작
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-27 00:27:43 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:43 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951662058}
2025-06-27 00:27:43 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951662058
2025-06-27 00:27:43 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951662058
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 523
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: SOLUSDT = 0.72 (PnL: -0.179352)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -1.00551845)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.1782)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -1.9404)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - positionAmt: 0.72 (float: 0.72)
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - entryPrice: 143.47
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - unrealizedProfit: -0.17935200
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:27:43 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - unrealizedProfit: -1.94040000
2025-06-27 00:27:43 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 응답 타입: <class 'list'>
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 전체 포지션 수: 523
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 활성 포지션 개수: 4
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 수: 1
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 수: 1
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 상세: positionAmt=0.72, entryPrice=143.47
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 상세: positionAmt=504.0, entryPrice=0.16426
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'SOLUSDT', 'initialMargin': '103.11904800', 'maintMargin': '0.51559524', 'unrealizedProfit': '-0.17935200', 'positionInitialMargin': '103.11904800', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '143.47', 'breakEvenPrice': '143.541735', 'maxNotional': '400000000', 'positionSide': 'BOTH', 'positionAmt': '0.72', 'notional': '103.11904800', 'isolatedWallet': '0', 'updateTime': 1750949231645, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 주요 필드:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - symbol: SOLUSDT
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - positionAmt: 0.72 (타입: <class 'str'>)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.17935200
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - notional: 103.11904800
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 0.72 (타입: <class 'float'>)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [SOL] 포지션 방향 결정: positionAmt=0.72
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [SOL] 양수 포지션 → LONG 방향
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [SOL] 최종 결정된 방향: long
2025-06-27 00:27:43 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): -0.562
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 18844429.50
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [SOL] 🔧 강제 PnL 계산: -0.174% (진입: 143.47, 현재: 143.22, 방향: long)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 상세:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.179352
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.17%
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [SOL] 통합 생각카드 생성: card_sync_SOL_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.position_card_manager - INFO - 🎯 [SOL] 새 포지션 카드 생성: sync_SOL_1750951663 -> card_sync_SOL_1750951663_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🎯 [SOL] 동기화 포지션 통합 생각카드 생성: card_sync_SOL_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [SOL] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [ETH] 포지션 방향 결정: positionAmt=0.023
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [ETH] 양수 포지션 → LONG 방향
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [ETH] 최종 결정된 방향: long
2025-06-27 00:27:43 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): 1.310
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 5259293.197
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [ETH] 🔧 강제 PnL 계산: -1.762% (진입: 2486.13, 현재: 2442.32, 방향: long)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [ETH] 바이낸스 포지션 상세:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 2486.13
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.00551845
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -1.76%
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔒 [ETH] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔒 [ETH] 기존 포지션 ID: sync_ETH_1750928149, 동기화 포지션 ID: sync_ETH_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [BNB] 포지션 방향 결정: positionAmt=0.09
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [BNB] 양수 포지션 → LONG 방향
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [BNB] 최종 결정된 방향: long
2025-06-27 00:27:43 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): 0.137
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 295901.94
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [BNB] 🔧 강제 PnL 계산: -0.311% (진입: 646.61, 현재: 644.6, 방향: long)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [BNB] 바이낸스 포지션 상세:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 646.61
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.1782
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.31%
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 통합 생각카드 생성: card_sync_BNB_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.position_card_manager - INFO - 🎯 [BNB] 새 포지션 카드 생성: sync_BNB_1750951663 -> card_sync_BNB_1750951663_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🎯 [BNB] 동기화 포지션 통합 생각카드 생성: card_sync_BNB_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'DOGEUSDT', 'initialMargin': '80.84663999', 'maintMargin': '0.52550316', 'unrealizedProfit': '-1.94040000', 'positionInitialMargin': '80.84663999', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.16426', 'breakEvenPrice': '0.16434213', 'maxNotional': '200000000', 'positionSide': 'BOTH', 'positionAmt': '504', 'notional': '80.84664000', 'isolatedWallet': '0', 'updateTime': 1750927964135, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 주요 필드:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - symbol: DOGEUSDT
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - positionAmt: 504 (타입: <class 'str'>)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.94040000
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - notional: 80.84664000
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 504.0 (타입: <class 'float'>)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 포지션 방향 결정: positionAmt=504.0
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 양수 포지션 → LONG 방향
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 최종 결정된 방향: long
2025-06-27 00:27:43 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): -1.890
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 5091700141
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [DOGE] 🔧 강제 PnL 계산: -2.350% (진입: 0.16426, 현재: 0.1604, 방향: long)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 상세:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.9404
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -2.35%
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 기존 포지션 ID: sync_DOGE_1750927996, 동기화 포지션 ID: sync_DOGE_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 시작: 현재 2개 포지션, 거래소에서 가져온 4개 포지션
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - exchange_positions 상세:
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 현재 내부 포지션 키: ['ETH_long', 'DOGE_long']
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 거래소 포지션 키: ['SOL_long', 'ETH_long', 'BNB_long', 'DOGE_long']
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 바이낸스 API 포지션과 내부 포지션 스마트 매핑
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 새 포지션 발견: SOL long, 크기: 0.72
2025-06-27 00:27:43 - trading.thinking_cards.position_card_manager - WARNING - ⚠️ [SOL] 이미 존재하는 포지션 카드: sync_SOL_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🎯 [SOL] 동기화 포지션 통합 생각카드 생성: card_sync_SOL_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [SOL] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 포지션 업데이트: ETH long, PnL: -1.76%
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 새 포지션 발견: BNB long, 크기: 0.09
2025-06-27 00:27:43 - trading.thinking_cards.position_card_manager - WARNING - ⚠️ [BNB] 이미 존재하는 포지션 카드: sync_BNB_1750951663
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🎯 [BNB] 동기화 포지션 통합 생각카드 생성: card_sync_BNB_1750951663_1750951663
2025-06-27 00:27:43 - trading.thinking_cards.integrated_thinking_card - INFO - 🎯 [BNB] 포지션 관리 업데이트: sync_position (이유: 바이낸스 동기화 long 포지션)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 포지션 업데이트: DOGE long, PnL: -2.35%
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 완료: 최종 4개 포지션
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions 배열 크기: 4
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 수: 4
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 1: SOL (크기: 0.72)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 2: ETH (크기: 0.023)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 3: BNB (크기: 0.09)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 수: 4
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 1: SOL (크기: 0.72)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 2: ETH (크기: 0.023)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 3: BNB (크기: 0.09)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 바이낸스 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 내부 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - ✅ 포지션 동기화 상태 양호: 불일치 없음
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔥 포지션 동기화 완료: 4개의 활성 포지션 (내부: 4개)
2025-06-27 00:27:43 - simulator.trading.portfolio - INFO - 🔥 동기화 변경사항: 5개
2025-06-27 00:27:43 - hybrid_simulator - INFO - 🔥 포지션 정보 동기화 완료
2025-06-27 00:27:43 - hybrid_simulator - INFO - 🔥 동기화 결과: 2 → 4 포지션
2025-06-27 00:27:43 - hybrid_simulator - INFO - 🔥 포지션 동기화 변경사항 (5개):
2025-06-27 00:27:43 - hybrid_simulator - INFO -    - 포지션 수 변경: 2 → 4
2025-06-27 00:27:43 - hybrid_simulator - INFO -    - 동기화: SOL long (PnL: -0.17%)
2025-06-27 00:27:43 - hybrid_simulator - INFO -    - 동기화: ETH long (PnL: -1.76%)
2025-06-27 00:27:43 - hybrid_simulator - INFO -    - 동기화: BNB long (PnL: -0.31%)
2025-06-27 00:27:43 - hybrid_simulator - INFO -    - 동기화: DOGE long (PnL: -2.35%)
2025-06-27 00:27:43 - hybrid_simulator - INFO - BTC 시장 데이터 로드 중...
2025-06-27 00:27:43 - simulator.utils.market_utils - INFO - BTC 실시간 시장 데이터 수집 중...
2025-06-27 00:27:43 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107223.0
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): 0.153
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 115932.216
2025-06-27 00:27:43 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:43 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:43 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107223.0
2025-06-27 00:27:43 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:43 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:43 - binance.binance_utils - INFO - 변화율(%): 0.153
2025-06-27 00:27:43 - binance.binance_utils - INFO - 거래량: 115930.510
2025-06-27 00:27:43 - simulator.utils.market_utils - INFO - BTC 24h 변화율: 0.15%
2025-06-27 00:27:43 - simulator.utils.market_utils - INFO - BTC 현재 가격: 107223.0, 24h 변화율: 0.15%
2025-06-27 00:27:43 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107223.00, 변동률 0.15%
2025-06-27 00:27:43 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 중...
2025-06-27 00:27:43 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:43 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:43 - data_collector.lunarcrush_collector - INFO - BTC(ID:1) 코인 데이터 요청 중...
2025-06-27 00:27:43 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/1/v1, 파라미터: {}
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/1/v1
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - BTC 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - BTC 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - BTC 거래량 정보: volume_24h=46372591734.23497
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - BTC 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - BTC 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:44 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/news/v1, 파라미터: {}
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/news/v1
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 응답 키: ['config', 'data']
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 99
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 데이터베이스에 저장 완료
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - BTC 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:27:45 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/posts/v1, 파라미터: {'start': 1750951485, 'end': 1750951665}
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/posts/v1
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 100개 수집 완료
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=463725917, 뉴스=99개, 포스트=100개
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 결합 완료: 바이낸스 거래량=115932, 소셜볼륨=463725917
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 1m 캔들 데이터 수집 시도...
2025-06-27 00:27:46 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=1m, 개수=50
2025-06-27 00:27:46 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1m, 개수: 50
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 1m 첫 번째 캔들 샘플: [1750948680000, '107570.30', '107604.20', '107536.70', '107536.70', '136.046', 1750948739999, '14635439.02290', 3078, '53.948', '5803521.19620', '0']
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ✅ BTC 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 단기 변동률: -0.074%
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107223.00, 24h변동률 0.15%, 단기변동률 -0.074%, 캔들 20개
2025-06-27 00:27:46 - hybrid_simulator - INFO - BTC 시장 데이터 로드 완료: $107223.00
2025-06-27 00:27:46 - hybrid_simulator - INFO - ETH 시장 데이터 로드 중...
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ETH 실시간 시장 데이터 수집 중...
2025-06-27 00:27:46 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:27:46 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:27:46 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2442.49
2025-06-27 00:27:46 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:27:46 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:46 - binance.binance_utils - INFO - 변화율(%): 1.317
2025-06-27 00:27:46 - binance.binance_utils - INFO - 거래량: 5259317.384
2025-06-27 00:27:46 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:46 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:46 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2442.49
2025-06-27 00:27:46 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:27:46 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:46 - binance.binance_utils - INFO - 변화율(%): 1.317
2025-06-27 00:27:46 - binance.binance_utils - INFO - 거래량: 5259317.384
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ETH 24h 변화율: 1.32%
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ETH 현재 가격: 2442.49, 24h 변화율: 1.32%
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2442.49, 변동률 1.32%
2025-06-27 00:27:46 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 중...
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - ETH(ID:2) 코인 데이터 요청 중...
2025-06-27 00:27:46 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/2/v1, 파라미터: {}
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/2/v1
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - ETH 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - ETH 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - ETH 거래량 정보: volume_24h=18242584460.464893
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - ETH 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - ETH 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:47 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/news/v1, 파라미터: {}
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/news/v1
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 응답 키: ['config', 'data']
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 68
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 68개 수집 완료
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 데이터베이스에 저장 완료
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 68개 수집 완료
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - ETH 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:27:48 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/posts/v1, 파라미터: {'start': 1750951488, 'end': 1750951668}
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/posts/v1
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 100개 수집 완료
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=182425844, 뉴스=68개, 포스트=100개
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 결합 완료: 바이낸스 거래량=5259317, 소셜볼륨=182425844
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 1m 캔들 데이터 수집 시도...
2025-06-27 00:27:49 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=1m, 개수=50
2025-06-27 00:27:49 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 1m, 개수: 50
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 1m 첫 번째 캔들 샘플: [1750948680000, '2444.57', '2446.34', '2443.51', '2443.52', '3914.853', 1750948739999, '9572843.15935', 4297, '1795.955', '4391685.52091', '0']
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ✅ ETH 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 단기 변동률: -0.064%
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2442.49, 24h변동률 1.32%, 단기변동률 -0.064%, 캔들 20개
2025-06-27 00:27:49 - hybrid_simulator - INFO - ETH 시장 데이터 로드 완료: $2442.49
2025-06-27 00:27:49 - hybrid_simulator - INFO - SOL 시장 데이터 로드 중...
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - SOL 실시간 시장 데이터 수집 중...
2025-06-27 00:27:49 - binance.binance_utils - INFO - 시장 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT)
2025-06-27 00:27:49 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-27 00:27:49 - binance.binance_utils - INFO - 최신 가격 조회 성공: SOLUSDT, 가격: 143.1
2025-06-27 00:27:49 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-27 00:27:49 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:49 - binance.binance_utils - INFO - 변화율(%): -0.576
2025-06-27 00:27:49 - binance.binance_utils - INFO - 거래량: 18845646.49
2025-06-27 00:27:49 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:49 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:49 - binance.binance_utils - INFO - 시장 데이터 조회 성공: SOLUSDT, 가격: 143.1
2025-06-27 00:27:49 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOLUSDT)
2025-06-27 00:27:49 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:49 - binance.binance_utils - INFO - 변화율(%): -0.576
2025-06-27 00:27:49 - binance.binance_utils - INFO - 거래량: 18845646.49
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - SOL 24h 변화율: -0.58%
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - SOL 현재 가격: 143.1, 24h 변화율: -0.58%
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - SOL 시장 데이터 수집 완료: 현재가 $143.10, 변동률 -0.58%
2025-06-27 00:27:49 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 수집 중...
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - SOL(ID:3079) 코인 데이터 요청 중...
2025-06-27 00:27:49 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/3079/v1, 파라미터: {}
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/3079/v1
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - SOL 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - SOL 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - SOL 거래량 정보: volume_24h=3145491291.4338818
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - SOL 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - SOL 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:50 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/sol/news/v1, 파라미터: {}
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/sol/news/v1
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 응답 키: ['config', 'data']
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 21
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 21개 수집 완료
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 데이터베이스에 저장 완료
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - SOL 뉴스 21개 수집 완료
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - SOL 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:27:51 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/sol/posts/v1, 파라미터: {'start': 1750951491, 'end': 1750951671}
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/sol/posts/v1
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 100개 수집 완료
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - SOL 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=31454912, 뉴스=21개, 포스트=100개
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 소셜 데이터 결합 완료: 바이낸스 거래량=18845646, 소셜볼륨=31454912
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 1m 캔들 데이터 수집 시도...
2025-06-27 00:27:52 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOLUSDT), 인터벌=1m, 개수=50
2025-06-27 00:27:52 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 1m, 개수: 50
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 1m 첫 번째 캔들 샘플: [1750948680000, '143.2100', '143.3100', '143.0500', '143.0700', '14016.45', 1750948739999, '2006937.165200', 1730, '4696.03', '672383.233500', '0']
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - ✅ SOL 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 단기 변동률: -0.133%
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - SOL 시장 데이터 수집 완료: 현재가 $143.10, 24h변동률 -0.58%, 단기변동률 -0.133%, 캔들 20개
2025-06-27 00:27:52 - hybrid_simulator - INFO - SOL 시장 데이터 로드 완료: $143.10
2025-06-27 00:27:52 - hybrid_simulator - INFO - BNB 시장 데이터 로드 중...
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - BNB 실시간 시장 데이터 수집 중...
2025-06-27 00:27:52 - binance.binance_utils - INFO - 시장 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT)
2025-06-27 00:27:52 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-27 00:27:52 - binance.binance_utils - INFO - 최신 가격 조회 성공: BNBUSDT, 가격: 644.6
2025-06-27 00:27:52 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-27 00:27:52 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:52 - binance.binance_utils - INFO - 변화율(%): 0.112
2025-06-27 00:27:52 - binance.binance_utils - INFO - 거래량: 295933.88
2025-06-27 00:27:52 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:52 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:52 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BNBUSDT, 가격: 644.6
2025-06-27 00:27:52 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNBUSDT)
2025-06-27 00:27:52 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:52 - binance.binance_utils - INFO - 변화율(%): 0.112
2025-06-27 00:27:52 - binance.binance_utils - INFO - 거래량: 295933.88
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - BNB 24h 변화율: 0.11%
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - BNB 현재 가격: 644.6, 24h 변화율: 0.11%
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - BNB 시장 데이터 수집 완료: 현재가 $644.60, 변동률 0.11%
2025-06-27 00:27:52 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 수집 중...
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - BNB(ID:6) 코인 데이터 요청 중...
2025-06-27 00:27:52 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/6/v1, 파라미터: {}
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/6/v1
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - BNB 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - BNB 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - BNB 거래량 정보: volume_24h=1459853042.2573357
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - BNB 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - BNB 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:53 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/binance/news/v1, 파라미터: {}
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/binance/news/v1
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 응답 키: ['config', 'data']
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 19
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 19개 수집 완료
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 데이터베이스에 저장 완료
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - BNB 뉴스 19개 수집 완료
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - BNB 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:27:54 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/binance/posts/v1, 파라미터: {'start': 1750951494, 'end': 1750951674}
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/binance/posts/v1
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 98
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 98개 수집 완료
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - BNB 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=14598530, 뉴스=19개, 포스트=98개
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 소셜 데이터 결합 완료: 바이낸스 거래량=295933, 소셜볼륨=14598530
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 1m 캔들 데이터 수집 시도...
2025-06-27 00:27:55 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNBUSDT), 인터벌=1m, 개수=50
2025-06-27 00:27:55 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 1m, 개수: 50
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 1m 첫 번째 캔들 샘플: [1750948680000, '644.750', '644.950', '644.740', '644.850', '339.00', 1750948739999, '218602.01760', 288, '226.41', '145994.47990', '0']
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - ✅ BNB 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 단기 변동률: -0.085%
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - BNB 시장 데이터 수집 완료: 현재가 $644.60, 24h변동률 0.11%, 단기변동률 -0.085%, 캔들 20개
2025-06-27 00:27:55 - hybrid_simulator - INFO - BNB 시장 데이터 로드 완료: $644.60
2025-06-27 00:27:55 - hybrid_simulator - INFO - DOGE 시장 데이터 로드 중...
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - DOGE 실시간 시장 데이터 수집 중...
2025-06-27 00:27:55 - binance.binance_utils - INFO - 시장 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-27 00:27:55 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-27 00:27:55 - binance.binance_utils - INFO - 최신 가격 조회 성공: DOGEUSDT, 가격: 0.16027
2025-06-27 00:27:55 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-27 00:27:55 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:55 - binance.binance_utils - INFO - 변화율(%): -1.970
2025-06-27 00:27:55 - binance.binance_utils - INFO - 거래량: 5093878843
2025-06-27 00:27:55 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:55 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:55 - binance.binance_utils - INFO - 시장 데이터 조회 성공: DOGEUSDT, 가격: 0.16027
2025-06-27 00:27:55 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGEUSDT)
2025-06-27 00:27:55 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:55 - binance.binance_utils - INFO - 변화율(%): -1.970
2025-06-27 00:27:55 - binance.binance_utils - INFO - 거래량: 5094597182
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - DOGE 24h 변화율: -1.97%
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - DOGE 현재 가격: 0.16027, 24h 변화율: -1.97%
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - DOGE 시장 데이터 수집 완료: 현재가 $0.16, 변동률 -1.97%
2025-06-27 00:27:55 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 수집 중...
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - DOGE(ID:29) 코인 데이터 요청 중...
2025-06-27 00:27:55 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/29/v1, 파라미터: {}
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/29/v1
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - DOGE 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - DOGE 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - DOGE 거래량 정보: volume_24h=862653380.6514071
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - DOGE 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:56 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/doge/news/v1, 파라미터: {}
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/doge/news/v1
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 응답 키: ['config', 'data']
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 4
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 47
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 47개 수집 완료
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 데이터베이스에 저장 완료
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 뉴스 47개 수집 완료
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/doge/posts/v1, 파라미터: {'start': 1750951497, 'end': 1750951677}
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/doge/posts/v1
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 100개 수집 완료
2025-06-27 00:27:57 - data_collector.lunarcrush_collector - INFO - DOGE 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:27:57 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=8626533, 뉴스=47개, 포스트=100개
2025-06-27 00:27:57 - simulator.utils.market_utils - INFO - DOGE 소셜 데이터 결합 완료: 바이낸스 거래량=5093878843, 소셜볼륨=8626533
2025-06-27 00:27:57 - simulator.utils.market_utils - INFO - DOGE 1m 캔들 데이터 수집 시도...
2025-06-27 00:27:57 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGEUSDT), 인터벌=1m, 개수=50
2025-06-27 00:27:58 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 1m, 개수: 50
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - DOGE 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - DOGE 1m 첫 번째 캔들 샘플: [1750948680000, '0.160220', '0.160380', '0.160170', '0.160180', '4837815', 1750948739999, '775394.724520', 1501, '2748638', '440598.268230', '0']
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - ✅ DOGE 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - DOGE 단기 변동률: -0.143%
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - DOGE 시장 데이터 수집 완료: 현재가 $0.16, 24h변동률 -1.97%, 단기변동률 -0.143%, 캔들 20개
2025-06-27 00:27:58 - hybrid_simulator - INFO - DOGE 시장 데이터 로드 완료: $0.16
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 기본 잔액: $610.87
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - BTC: 0.03700000 @ $107223.00 = $3967.25
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 포트폴리오 가치 계산 - 총 가치: $4578.12
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - ===== 포트폴리오 상태 =====
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 거래 모드: 실제 거래
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - USDT 잔액: $610.87
2025-06-27 00:27:58 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:58 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:58 - binance.binance_utils - INFO - 변화율(%): 0.081
2025-06-27 00:27:58 - binance.binance_utils - INFO - 거래량: 116041.853
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 포트폴리오 총 가치: $4,575.27 (USDT: $610.87 + 현물: $3,964.40)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 초기 잔액: $10,000.00
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 총 손익: $-5,424.73 (-54.25%)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - === 현물 자산 (Assets) ===
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   BTC: 0.03700000 개 × $107223.0000 = $3,967.25
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 현물 자산 총 가치: $3,967.25
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - === 선물 거래 상태 ===
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951676910}
2025-06-27 00:27:58 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951676910
2025-06-27 00:27:58 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951676910
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 미체결 주문: 없음
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951677018}
2025-06-27 00:27:58 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951677018
2025-06-27 00:27:58 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951677018
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - [SOL] 최근 주문 내역:
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750931066659
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750934575636
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750949231645
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:27:58 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951677161}
2025-06-27 00:27:58 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951677161
2025-06-27 00:27:58 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951677161
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - [DOGE] 최근 주문 내역:
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - SELL $0 (FILLED) 1750927456355
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927959912
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - BUY $0 (FILLED) 1750927964135
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - === 내부 포지션 ===
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   SOL long: 0.72000000 @ $143.47
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     현재가: $143.10 | ROI: -0.26% | PnL: $-0.18 [손절조건 충족! 기준:-0.15%]
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   ETH long: 0.02300000 @ $2486.13
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     현재가: $2442.49 | ROI: -1.76% | PnL: $-1.01 [손절조건 충족! 기준:-0.15%]
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   BNB long: 0.09000000 @ $646.61
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     현재가: $644.60 | ROI: -0.31% | PnL: $-0.18 [손절조건 충족! 기준:-0.15%]
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   DOGE long: 504.00000000 @ $0.16
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     현재가: $0.16 | ROI: -2.43% | PnL: $-1.94 [손절조건 충족! 기준:-0.15%]
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 총 거래 수: 998
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 승률: 19.08% (성공: 595, 실패: 2524)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 포트폴리오 총 손익: $-5421.88 (-54.22%)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 거부된 거래 수: 187
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 거부된 거래 검증 상태: 검증 완료 187건, 검증 대기 0건
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 거부 이유별 통계:
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - API 오류: 바이낸스 API 오류 -2019: Margin is insufficient.: 181건
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - 일반 오류: name 'action' is not defined: 6건
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - === 통합 생각카드 시스템 상태 ===
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 활성 카드 수: 4개
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 카드 완성도: 완료 0개, 진행중 4개, 생성됨 0개
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 심볼별 카드 현황:
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   BNB: 2개 카드
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750948983_1750948984: 완성도 50.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     - card_sync_BNB_1750951663_1750951663: 완성도 25.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   SOL: 2개 카드
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     - card_trade_SOL_1750949230_1750949232: 완성도 50.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -     - card_sync_SOL_1750951663_1750951663: 완성도 25.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 최근 업데이트된 카드: 2개
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - SOL: 완성도 25.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   - BNB: 완성도 25.0%
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - 시간대별 성공률:
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   short_term: 0.0% (0/2건, 평균수익: -12.60%)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   medium_term: 0.0% (0/27건, 평균수익: -23.67%)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO -   전체: 0.0% (0/29건)
2025-06-27 00:27:58 - simulator.trading.portfolio - INFO - ===========================
2025-06-27 00:27:58 - hybrid_simulator - INFO - 🚀 연속 모드: 하이브리드 컨트롤러 직접 시작
2025-06-27 00:27:58 - hybrid_simulator - INFO - 하이브리드 컨트롤러 연속 모드 설정: True
2025-06-27 00:27:58 - hybrid_simulator - INFO - 하이브리드 컨트롤러에 포트폴리오 시뮬레이터 연결
2025-06-27 00:27:58 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 루프 시작
2025-06-27 00:27:58 - models.vllm_request_queue - INFO - vLLM 요청 큐 워커 스레드 시작
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - vLLM 요청 큐 워커 시작됨
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 연속 모드로 시스템 시작 (실행 → 학습 → 실행 → 학습)
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 데이터 수집 루프 시작
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 시작
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 연속 실행-학습 루프 시작
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 하이브리드 시스템 가동 시작
2025-06-27 00:27:58 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 새로운 심볼 락 생성
2025-06-27 00:27:58 - hybrid_simulator - INFO - 🚀 하이브리드 컨트롤러 연속 모드 시작 완료
2025-06-27 00:27:58 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTC)
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] 처리 시작
2025-06-27 00:27:58 - hybrid_simulator - INFO - 하이브리드 컨트롤러 연속 모드 설정: True
2025-06-27 00:27:58 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 순차 처리 시작 (락 획득)
2025-06-27 00:27:58 - hybrid_simulator - INFO - 거래 간격: 5초
2025-06-27 00:27:58 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-27 00:27:58 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 대기 중...
2025-06-27 00:27:58 - hybrid_simulator - INFO - ===== 반복 1/100000 =====
2025-06-27 00:27:58 - hybrid_simulator - INFO - ===== 거래 주기 시작: 2025-06-27 00:27:58 =====
2025-06-27 00:27:58 - hybrid_simulator - INFO - 🔄 BTC 완전 순차 처리 시작
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - BTC 실시간 시장 데이터 수집 중...
2025-06-27 00:27:58 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:58 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:58 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107123.7
2025-06-27 00:27:58 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107123.7
2025-06-27 00:27:58 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:58 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:27:58 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-27 00:27:58 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:58 - binance.binance_utils - INFO - 변화율(%): 0.081
2025-06-27 00:27:58 - binance.binance_utils - INFO - 거래량: 116041.853
2025-06-27 00:27:58 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:27:58 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTC), 인터벌=1d, 개수=500
2025-06-27 00:27:58 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:58 - binance.binance_utils - INFO - 변화율(%): 0.081
2025-06-27 00:27:58 - binance.binance_utils - INFO - 거래량: 116041.853
2025-06-27 00:27:58 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=3m, 개수=500
2025-06-27 00:27:58 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1d, 개수: 1
2025-06-27 00:27:58 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107123.7
2025-06-27 00:27:58 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BTC/news/v1, 파라미터: None
2025-06-27 00:27:58 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 3m, 개수: 480
2025-06-27 00:27:58 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107123.7
2025-06-27 00:27:58 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTCUSDT)
2025-06-27 00:27:58 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:27:58 - binance.binance_utils - INFO - 변화율(%): 0.081
2025-06-27 00:27:58 - binance.binance_utils - INFO - 거래량: 116041.853
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - BTC 24h 변화율: 0.08%
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - BTC 현재 가격: 107123.7, 24h 변화율: 0.08%
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107123.70, 변동률 0.08%
2025-06-27 00:27:58 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 중...
2025-06-27 00:27:58 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:27:58 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:27:58 - data_collector.lunarcrush_collector - INFO - BTC(ID:1) 코인 데이터 요청 중...
2025-06-27 00:27:58 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/1/v1, 파라미터: {}
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/1/v1
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - BTC 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - BTC 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - BTC 거래량 정보: volume_24h=46372591734.23497
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - BTC 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - BTC 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:27:59 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BTC/v1, 파라미터: None
2025-06-27 00:27:59 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/BTC/posts/v1, 파라미터: {'start': 1750865279, 'end': 1750951679}
2025-06-27 00:27:59 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/news/v1, 파라미터: {}
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: BTC)
2025-06-27 00:28:00 - trading.hybrid_architecture.data_store - INFO - BTC 시장 데이터 저장 완료
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_market_market_7230a852_1750951680.json
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - BTC 시장 데이터 로깅 완료: market_7230a852_1750951680
2025-06-27 00:28:00 - trading.hybrid_architecture.data_store - WARNING - BTC 뉴스 데이터가 문자열로 전달됨: config...
2025-06-27 00:28:00 - trading.hybrid_architecture.data_store - INFO - BTC 뉴스 데이터 저장 완료: config
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_news_news_feb3da0b_1750951680.json
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - BTC 뉴스 데이터 로깅 완료: news_feb3da0b_1750951680
2025-06-27 00:28:00 - trading.hybrid_architecture.data_store - WARNING - BTC 뉴스 데이터가 문자열로 전달됨: data...
2025-06-27 00:28:00 - trading.hybrid_architecture.data_store - INFO - BTC 뉴스 데이터 저장 완료: data
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_news_news_bb3dc056_1750951680.json
2025-06-27 00:28:00 - trading.hybrid_architecture.data_logger - INFO - BTC 뉴스 데이터 로깅 완료: news_bb3dc056_1750951680
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 데이터 수집 완료 및 실행 큐 전달
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 플로우 시작
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 데이터 수집 시작
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 포지션 관리 시작
2025-06-27 00:28:00 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] 포지션 관리 v2 인라인 시작
2025-06-27 00:28:00 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETH)
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 포트폴리오 연결됨, 포지션 확인 시작
2025-06-27 00:28:00 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] open_positions 속성으로 포지션 확인: 0개
2025-06-27 00:28:00 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 전체 포지션 수: 4개
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - WARNING - 🔧 [BTC] 포트폴리오에서 포지션을 찾을 수 없음
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 관리할 포지션 없음 - 새 포지션 생성 검토
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - ERROR - ❌ [BTC] 포지션 관리 인라인 전체 실패: 'HybridController' object has no attribute '_check_new_position_creation'
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] InCA 시장 중요도 평가 시작 (순차 처리)
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] InCA 진짜 순차 실행 시작 (큐 우회)
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 순차 실행 변동률 추출: percent_change_24h=None, priceChangePercent=None, change_24h=0.081, 최종값=0.081
2025-06-27 00:28:00 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 시장 데이터 키: ['symbol', 'price', 'percent_change_24h', 'volume_24h', 'recent_candles', 'news']
2025-06-27 00:28:00 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 추출된 값: 가격=$107123.7, 거래량=116041.853
2025-06-27 00:28:00 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA 프롬프트 크기: 2481자
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 🔧 kwargs에서 호환 파라미터 'stop' 전달됨: ['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The']
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 🔧 원본 kwargs: ['stop'], 필터링 후: ['stop']
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 2481자
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - Qwen3 모델 프롬프트 감지됨
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: Symbol:\s*([A-Z]+))
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 71자
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 1948자
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - Qwen3 모델에 강화된 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:00 - models.vllm_prompt_processor - INFO - Qwen3 모델용 stop sequences 제거됨 - 완전한 JSON 응답 보장
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Qwen3 모델에 inca_execution 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: inca_execution
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: inca_execution
2025-06-27 00:28:00 - models.vllm_session_manager - INFO - Created new session: 2e208fdb-8a3e-483b-8fb3-40ca901de241 for inca_execution agent (symbol: BTC)
2025-06-27 00:28:00 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_execution_execution_BTC → 2e208fdb-8a3e-483b-8fb3-40ca901de241
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - inca_execution execution 세션 ID: 2e208fdb-8a3e-483b-8fb3-40ca901de241 (심볼: BTC)
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 2869
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Session ID: 2e208fdb-8a3e-483b-8fb3-40ca901de241
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.



market data, HiAR organizes reasoning, SELA executes strategies.



CRITICAL: V-shaped reversals, breakouts, volume ...
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\nIMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\nCRITICAL: V-shaped reversals, breakouts, volume spikes are STRONG signals\n2. 🎯 TRENDLINE ANALYSIS: Support/resistance breakouts indicate major direction changes\n3. 🎯 VOLUME CONFIRMATION: Price moves with high volume are more reliable than low-volume moves\n4. 🎯 PATTERN RECOGNITION: Look for double tops/bottoms, wedges, consecutive candles\n5. 🎯 SHORT-TERM TREND PRIORITY: Recent 5-minute patterns override 24h changes\n6. 🎯 BREAKOUT SIGNALS: When price breaks above resistance or below support with volume = STRONG signal\n7. 🎯 REVERSAL PATTERNS: V-shaped reversals from lows/highs with volume = immediate action needed\n8. News and social sentiment are secondary to clear technical patterns\n9. If technical analysis shows STRONG patterns (strength >50), prioritize over other factors\n\nPRIORITY ORDER:\n1. Short-term trend direction (from candle data) - HIGHEST PRIORITY\n2. Trend strength and consistency\n3. News sentiment\n4. Social sentiment\n5. 24h change (lowest priority)\n\n🚨 **CRITICAL TRADING SIGNAL RULES**:\n- If market_direction is "bearish" → action_recommendation MUST be "sell"\n- If market_direction is "bullish" → action_recommendation MUST be "buy"\n- If market_direction is "neutral" → action_recommendation MUST be "hold"\n- situation_type should match market_direction (bearish/bullish/neutral)\n\nRESPOND WITH ONLY A JSON OBJECT:\n{\n  "importance": <1-10 integer>,\n  "is_important": <true/false>,\n  "importance_score": <0.0-1.0 float>,\n  "reasoning": "<brief explanation prioritizing short-term trend>",\n  "market_direction": "bullish"/"bearish"/"neutral",\n  "situation_type": "bullish"/"bearish"/"neutral",\n  "action_recommendation": "buy"/"sell"/"hold",\n  "trading_direction": "long"/"short"/"neutral",\n  "confidence": <0.0-1.0 float>,\n  "keywords": ["<keyword1>", "<keyword2>", "<keyword3>"]\n}\n\nRate importance:\n1-3: Low activity, 4-6: Moderate activity, 7-10: High activity\n\nCRITICAL: Return ONLY valid JSON, no markdown, no explanations.\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT FOR INCA ANALYSIS (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "importance": 7,\n  "situation_type": "bullish/bearish/neutral",\n  "action_recommendation": "buy/sell/hold",\n  "trading_direction": "long/short/neutral",\n  "reasoning": "Based on market data and past experiences",\n  "news_impact": 0.7,\n  "market_impact": 0.8,\n  "confidence": 0.85\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 500, 'temperature': 0.0, 'top_p': 0.1, 'stop': []}
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 717
2025-06-27 00:28:00 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:00 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2438.12
2025-06-27 00:28:00 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:00 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/news/v1
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 응답 키: ['config', 'data']
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 99
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 데이터베이스에 저장 완료
2025-06-27 00:28:00 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - BTC 뉴스 99개 수집 완료
2025-06-27 00:28:00 - binance.binance_utils - INFO - 변화율(%): 1.072
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - BTC 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:28:00 - binance.binance_utils - INFO - 거래량: 5268239.886
2025-06-27 00:28:00 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:00 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETH), 인터벌=1d, 개수=500
2025-06-27 00:28:00 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 1d, 개수: 1
2025-06-27 00:28:00 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2438.12
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/ETH/news/v1, 파라미터: None
2025-06-27 00:28:00 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/btc/posts/v1, 파라미터: {'start': 1750951500, 'end': 1750951680}
2025-06-27 00:28:00 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/ETH/v1, 파라미터: None
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/btc/posts/v1
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 100개 수집 완료
2025-06-27 00:28:01 - data_collector.lunarcrush_collector - INFO - BTC 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=463725917, 뉴스=99개, 포스트=100개
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 소셜 데이터 결합 완료: 바이낸스 거래량=116041, 소셜볼륨=463725917
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 1m 캔들 데이터 수집 시도...
2025-06-27 00:28:01 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTCUSDT), 인터벌=1m, 개수=50
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/ETH/posts/v1, 파라미터: {'start': 1750865281, 'end': 1750951681}
2025-06-27 00:28:01 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1m, 개수: 50
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 1m 첫 번째 캔들 샘플: [1750948680000, '107570.30', '107604.20', '107536.70', '107536.70', '136.046', 1750948739999, '14635439.02290', 3078, '53.948', '5803521.19620', '0']
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - ✅ BTC 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 단기 변동률: -0.184%
2025-06-27 00:28:01 - simulator.utils.market_utils - INFO - BTC 시장 데이터 수집 완료: 현재가 $107123.70, 24h변동률 0.08%, 단기변동률 -0.184%, 캔들 20개
2025-06-27 00:28:01 - hybrid_simulator - INFO - 📊 BTC 시장 데이터 처리 시작 (순차 모드)
2025-06-27 00:28:01 - simulator.bridge - INFO - BTC 시장 데이터 처리 중...
2025-06-27 00:28:01 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 플로우 시작 (InCA → HiAR → SELA)
2025-06-27 00:28:01 - trading.hybrid_architecture.hybrid_controller - INFO - BTC InCA 시장 중요도 평가 시작
2025-06-27 00:28:01 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 시장 데이터 키: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-27 00:28:01 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - BTC 추출된 값: 가격=$107123.7, 거래량=116041.853
2025-06-27 00:28:01 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 심볼 추출 (Symbol:): BTC
2025-06-27 00:28:01 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(BTC) vLLM 큐에 요청 추가
2025-06-27 00:28:01 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_execution(BTC) - 우선순위 1, 큐 크기: 1
2025-06-27 00:28:01 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_execution(BTC) - 큐 대기: 0.00초
2025-06-27 00:28:01 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_execution_BTC_1750951681275
2025-06-27 00:28:01 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
/no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR BTC CRYPTOCURRENCY ONLY.

Analyze ALL avail...
2025-06-27 00:28:01 - models.vllm_session_manager - INFO - 기존 inca_execution_execution_BTC 세션 재사용: 2e208fdb-8a3e-483b-8fb3-40ca901de241
2025-06-27 00:28:01 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: 2e208fdb-8a3e-483b-8fb3-40ca901de241 (inca_execution_BTC)
2025-06-27 00:28:01 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_execution 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:01 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: 2e208fdb-8a3e-483b-8fb3-40ca901de241
2025-06-27 00:28:01 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: 2e208fdb-8a3e-483b-8fb3-40ca901de241)
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: ETH)
2025-06-27 00:28:01 - trading.hybrid_architecture.data_store - INFO - ETH 시장 데이터 저장 완료
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_market_market_aba57b88_1750951681.json
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - ETH 시장 데이터 로깅 완료: market_aba57b88_1750951681
2025-06-27 00:28:01 - trading.hybrid_architecture.data_store - WARNING - ETH 뉴스 데이터가 문자열로 전달됨: config...
2025-06-27 00:28:01 - trading.hybrid_architecture.data_store - INFO - ETH 뉴스 데이터 저장 완료: config
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_news_news_b81a388c_1750951681.json
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - ETH 뉴스 데이터 로깅 완료: news_b81a388c_1750951681
2025-06-27 00:28:01 - trading.hybrid_architecture.data_store - WARNING - ETH 뉴스 데이터가 문자열로 전달됨: data...
2025-06-27 00:28:01 - trading.hybrid_architecture.data_store - INFO - ETH 뉴스 데이터 저장 완료: data
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_news_news_2fa2288b_1750951681.json
2025-06-27 00:28:01 - trading.hybrid_architecture.data_logger - INFO - ETH 뉴스 데이터 로깅 완료: news_2fa2288b_1750951681
2025-06-27 00:28:01 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 데이터 수집 완료 및 실행 큐 전달
2025-06-27 00:28:01 - trading.hybrid_architecture.hybrid_controller - INFO - SOL 데이터 수집 시작
2025-06-27 00:28:01 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:01 - binance.binance_utils - INFO - 시장 데이터 요청: SOLUSDT (원본 심볼: SOL)
2025-06-27 00:28:01 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:01 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-27 00:28:01 - binance.binance_utils - INFO - 최신 가격 조회 성공: SOLUSDT, 가격: 142.91
2025-06-27 00:28:01 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:01 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-27 00:28:01 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:01 - binance.binance_utils - INFO - 변화율(%): -0.771
2025-06-27 00:28:01 - binance.binance_utils - INFO - 거래량: 18858538.04
2025-06-27 00:28:01 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:01 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: SOLUSDT (원본 심볼: SOL), 인터벌=1d, 개수=500
2025-06-27 00:28:01 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: SOLUSDT, 간격: 1d, 개수: 1
2025-06-27 00:28:01 - binance.binance_utils - INFO - 시장 데이터 조회 성공: SOLUSDT, 가격: 142.91
2025-06-27 00:28:01 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/SOL/news/v1, 파라미터: None
2025-06-27 00:28:02 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/SOL/v1, 파라미터: None
2025-06-27 00:28:02 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/SOL/posts/v1, 파라미터: {'start': 1750865282, 'end': 1750951682}
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: SOL)
2025-06-27 00:28:03 - trading.hybrid_architecture.data_store - INFO - SOL 시장 데이터 저장 완료
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_market_market_7ab876ff_1750951683.json
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - SOL 시장 데이터 로깅 완료: market_7ab876ff_1750951683
2025-06-27 00:28:03 - trading.hybrid_architecture.data_store - WARNING - SOL 뉴스 데이터가 문자열로 전달됨: config...
2025-06-27 00:28:03 - trading.hybrid_architecture.data_store - INFO - SOL 뉴스 데이터 저장 완료: config
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_news_news_16d5cc00_1750951683.json
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - SOL 뉴스 데이터 로깅 완료: news_16d5cc00_1750951683
2025-06-27 00:28:03 - trading.hybrid_architecture.data_store - WARNING - SOL 뉴스 데이터가 문자열로 전달됨: data...
2025-06-27 00:28:03 - trading.hybrid_architecture.data_store - INFO - SOL 뉴스 데이터 저장 완료: data
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\SOL_news_news_4f4902e3_1750951683.json
2025-06-27 00:28:03 - trading.hybrid_architecture.data_logger - INFO - SOL 뉴스 데이터 로깅 완료: news_4f4902e3_1750951683
2025-06-27 00:28:03 - trading.hybrid_architecture.hybrid_controller - INFO - SOL 데이터 수집 완료 및 실행 큐 전달
2025-06-27 00:28:03 - trading.hybrid_architecture.hybrid_controller - INFO - BNB 데이터 수집 시작
2025-06-27 00:28:03 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:03 - binance.binance_utils - INFO - 시장 데이터 요청: BNBUSDT (원본 심볼: BNB)
2025-06-27 00:28:03 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:03 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-27 00:28:03 - binance.binance_utils - INFO - 최신 가격 조회 성공: BNBUSDT, 가격: 644.2
2025-06-27 00:28:03 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:03 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-27 00:28:03 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:03 - binance.binance_utils - INFO - 변화율(%): 0.075
2025-06-27 00:28:03 - binance.binance_utils - INFO - 거래량: 296073.22
2025-06-27 00:28:03 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:03 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BNBUSDT (원본 심볼: BNB), 인터벌=1d, 개수=500
2025-06-27 00:28:03 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BNBUSDT, 간격: 1d, 개수: 1
2025-06-27 00:28:03 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BNBUSDT, 가격: 644.2
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BNB/news/v1, 파라미터: None
2025-06-27 00:28:03 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/BNB/v1, 파라미터: None
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - Text generation complete (time: 4.30s)
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - Generated text preview: {"importance": 8, "is_important": true, "importance_score": 0.85, "reasoning": "Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from re...
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 422자):
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {"importance": 8, "is_important": true, "importance_score": 0.85, "reasoning": "Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.", "market_direction": "bullish", "situation_type": "bullish", "action_recommendation": "buy", "trading_direction": "long", "confidence": 0.9, "keywords": ["breakout", "volume confirmation", "V-shaped reversal"]}
2025-06-27 00:28:04 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답에 완전한 JSON 구조 발견
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA vLLM 응답 수신: <class 'dict'>
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA vLLM 응답 구조: ['text', 'finish_reason', 'session_id', 'symbol']
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] InCA 생성된 텍스트 길이: 422자
2025-06-27 00:28:04 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'importance': 8, 'is_important': True, 'importance_score': 0.85, 'reasoning': 'Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.', 'market_direction': 'bullish', 'situation_type': 'bullish', 'action_recommendation': 'buy', 'trading_direction': 'long', 'confidence': 0.9, 'keywords': ['breakout', 'volume confirmation', 'V-shaped reversal']}
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] InCA 직접 실행 성공: 중요도 8
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/BNB/posts/v1, 파라미터: {'start': 1750865284, 'end': 1750951684}
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-27 00:28:04 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: BNB)
2025-06-27 00:28:04 - trading.hybrid_architecture.data_store - INFO - BNB 시장 데이터 저장 완료
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_market_market_f6a352d1_1750951684.json
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - BNB 시장 데이터 로깅 완료: market_f6a352d1_1750951684
2025-06-27 00:28:04 - trading.hybrid_architecture.data_store - WARNING - BNB 뉴스 데이터가 문자열로 전달됨: config...
2025-06-27 00:28:04 - trading.hybrid_architecture.data_store - INFO - BNB 뉴스 데이터 저장 완료: config
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_news_news_17992d8f_1750951684.json
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - BNB 뉴스 데이터 로깅 완료: news_17992d8f_1750951684
2025-06-27 00:28:04 - trading.hybrid_architecture.data_store - WARNING - BNB 뉴스 데이터가 문자열로 전달됨: data...
2025-06-27 00:28:04 - trading.hybrid_architecture.data_store - INFO - BNB 뉴스 데이터 저장 완료: data
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BNB_news_news_bafd622b_1750951684.json
2025-06-27 00:28:04 - trading.hybrid_architecture.data_logger - INFO - BNB 뉴스 데이터 로깅 완료: news_bafd622b_1750951684
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - BNB 데이터 수집 완료 및 실행 큐 전달
2025-06-27 00:28:04 - trading.hybrid_architecture.hybrid_controller - INFO - DOGE 데이터 수집 시작
2025-06-27 00:28:04 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:04 - binance.binance_utils - INFO - 시장 데이터 요청: DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:28:04 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:04 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:28:04 - binance.binance_utils - INFO - 최신 가격 조회 성공: DOGEUSDT, 가격: 0.16009
2025-06-27 00:28:04 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:04 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:28:05 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:05 - binance.binance_utils - INFO - 변화율(%): -1.948
2025-06-27 00:28:05 - binance.binance_utils - INFO - 거래량: 5093422922
2025-06-27 00:28:05 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:05 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: DOGEUSDT (원본 심볼: DOGE), 인터벌=1d, 개수=500
2025-06-27 00:28:05 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: DOGEUSDT, 간격: 1d, 개수: 1
2025-06-27 00:28:05 - binance.binance_utils - INFO - 시장 데이터 조회 성공: DOGEUSDT, 가격: 0.16009
2025-06-27 00:28:05 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/DOGE/news/v1, 파라미터: None
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] InCA 완료, 다음 단계 진행
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 중요도(0.80)가 임계값(0.30)을 초과하여 계속 진행
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] HiAR 사고 흐름 정리 시작 (순차 처리)
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] HiAR 진짜 순차 실행 시작 (큐 우회)
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] HiAR 프롬프트 데이터 추출: 가격=$107123.7, 변동률=0.081%, 거래량=116041.853
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR 프롬프트 크기: 418자
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔧 kwargs에서 호환 파라미터 'stop' 전달됨: ['}', '\n', 'Okay', 'Let', 'First', 'NO', 'The']
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔧 원본 kwargs: ['stop'], 필터링 후: ['stop']
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 418자
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 최소 시스템 설명 + 캐시 우회 식별자 추가됨
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 143자
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 66자
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 194자
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 모델에 hiar_mcts 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: hiar_mcts
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: hiar_mcts
2025-06-27 00:28:05 - models.vllm_session_manager - INFO - Created new session: 8e55b04c-3122-46f5-862b-3f6f2708719d for hiar_mcts agent (symbol: BTC)
2025-06-27 00:28:05 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: hiar_mcts_execution_BTC → 8e55b04c-3122-46f5-862b-3f6f2708719d
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - hiar_mcts execution 세션 ID: 8e55b04c-3122-46f5-862b-3f6f2708719d (심볼: BTC)
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1160
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Session ID: 8e55b04c-3122-46f5-862b-3f6f2708719d
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 ===
Strong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.



market data, HiAR organizes reasoning, SELA ...
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 ===\nStrong bullish breakout above key resistance with high volume confirmation and a clear V-shaped reversal pattern from recent lows.\n\n\n\nmarket data, HiAR organizes reasoning, SELA executes strategies.\n\n\n\nJSON 형식으로 사고 흐름을 정리하세요:\n{\n  "card_id": "reasoning_BTC_1750951685",\n  "symbol": "BTC",\n  "reasoning": "시장 상황 분석",\n  "market_analysis": "가격 및 변동성 분석",\n  "decision_factors": ["요인1", "요인2", "요인3"]\n}\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT FOR HIAR ANALYSIS (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "title": "Market Analysis Title",\n  "analysis": "Market analysis description",\n  "market_expectation": "Expected market movement",\n  "support_levels": [100.0, 95.0, 90.0],\n  "resistance_levels": [110.0, 115.0, 120.0],\n  "direction": "uptrend/downtrend/sideways",\n  "confidence": 7,\n  "key_factors": ["factor1", "factor2", "factor3"],\n  "quantity": 0.5,\n  "conclusion": "Trading recommendation"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 600, 'temperature': 0.0, 'top_p': 0.1, 'stop': []}
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 290
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:05 - trading.hybrid_architecture.lunar_data_collector - INFO - API 요청: https://lunarcrush.com/api4/public/topic/DOGE/v1, 파라미터: None
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Fast generation complete (4.36s)
2025-06-27 00:28:05 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: inca_execution(BTC) - 처리: 4.36초, 총: 4.36초
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(BTC) vLLM 응답 수신: 507자
2025-06-27 00:28:05 - trading.hybrid_architecture.utils.json_extractor - INFO - </think> 태그 발견, 제거 중...
2025-06-27 00:28:05 - trading.hybrid_architecture.utils.json_extractor - INFO - 태그 제거 완료. 원본 길이: 507, 정리 후 길이: 482
2025-06-27 00:28:05 - trading.hybrid_architecture.utils.json_extractor - INFO - 정리 후 텍스트 미리보기: {
  "importance": 7,
  "is_important": true,
  "importance_score": 0.75,
  "reasoning": "The short-term trend shows a bearish pattern with 3 consecutive bearish candles and a significant volume spike ...
2025-06-27 00:28:05 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'importance': 7, 'is_important': True, 'importance_score': 0.75, 'reasoning': 'The short-term trend shows a bearish pattern with 3 consecutive bearish candles and a significant volume spike during the decline, indicating strong downward momentum.', 'market_direction': 'bearish', 'situation_type': 'bearish', 'action_recommendation': 'sell', 'trading_direction': 'short', 'confidence': 0.8, 'keywords': ['bearish trend', 'volume spike', 'short-term decline']}
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 🎯 [BTC] LLM 직접 실행 결과 저장: market_direction=bearish
2025-06-27 00:28:05 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 쿼리 완료: 10개 결과 반환
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.734, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.734, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.733, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.733, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.732, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.731, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.731, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.731, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.731, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: BTC 유사도 0.730, 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 벡터 DB에서 10개 유사 경험 발견
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 과거 경험 분석: 5개 경험, 성공률 0.0%, 평균 수익 0.00%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - WARNING - BTC 성공률 낮음 (0.0%), 신호 강도 조정 (방향 유지)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - WARNING - BTC 성공률 0%이지만 LLM 신호(bearish) 방향 유지, 신뢰도만 감소
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 과거 경험 기반 조정 완료: 중요도 7→5, 신뢰도 0.50→0.25
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 중요도(0.50)가 임계값(0.30)을 초과하여 계속 진행
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - BTC HiAR 사고 흐름 정리 시작
2025-06-27 00:28:05 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:05 - offline_pattern_generator - INFO - 선택된 패턴: ['Standard_CoT']
2025-06-27 00:28:05 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:05 - trading.hybrid_architecture.data_store - INFO - BTC 사고 카드 저장 완료: card_61de18ea_1750951685
2025-06-27 00:28:05 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_card_card_61de18ea_1750951685.json
2025-06-27 00:28:05 - trading.hybrid_architecture.data_logger - INFO - BTC 사고 카드 로깅 완료: card_61de18ea_1750951685
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 전략 생성 시작
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR → SELA 데이터 전달 디버깅
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card direction: N/A
2025-06-27 00:28:05 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] reasoning_card 내용: {'id': 'card_1', 'title': '패턴 분석 1', 'analysis': '현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.', 'reasoning': "사고 카드 'Standard_CoT' 실행 결과", 'confidence': 0.6, 'key_factors': ['패턴: S...
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search SELA 전략 생성 시작 (v2.0 - inca_result 지원)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 내용: {'importance': 5, 'is_important': True, 'importance_score': 0.5, 'situation_type': 'bearish', 'reasoning': 'The short-term trend shows a bearish pattern with 3 consecutive bearish candles and a significant volume spike during the decline, indicating strong downward momentum. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)', 'action_recommendation': 'sell', 'trading_direction': 'short', 'signal_direction': 'bearish', 'keywords': ['bearish trend', 'volume spike', 'short-term decline'], 'raw_response': ' NO MARKDOWN.\n\n</think>\n\n{\n  "importance": 7,\n  "is_important": true,\n  "importance_score": 0.75,\n  "reasoning": "The short-term trend shows a bearish pattern with 3 consecutive bearish candles and a ...', 'confidence': 0.25, 'historical_analysis': {'total_experiences': 5, 'success_rate': 0.0, 'avg_profit': 0.0, 'adjustment_applied': True}}
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 키들: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 시작
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - id: card_1
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: card_61de18ea_1750951685
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: bearish (분석용)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: sell (분석용)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: 5 (분석용)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: The short-term trend shows a bearish pattern with ... (분석용)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] InCA 결과 분석: sell, 중요도: 5 (SELA 독립 분석)
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - InCA BEARISH 분석 확인!
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107123.7
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.081%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116041.853
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: $2135.78B
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107123.7
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): -18.359%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.08%
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 5000자
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951685743_4879', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:05 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951685743_4879', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:05 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951685743_4879'}
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:05 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951685743_4879'}
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5000자
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-27 00:28:05 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1564
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107123.7,\n  "stop_loss": [현재가 $107123.7의 ±1-3% 범위],\n  "take_profit": [현재가 $107123.7의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: SELL** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 391
2025-06-27 00:28:05 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 호출: https://lunarcrush.com/api4/public/topic/DOGE/posts/v1, 파라미터: {'start': 1750865286, 'end': 1750951686}
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - 소셜 포스트 API 응답 유형: <class 'dict'>
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - 응답 최상위 키: ['config', 'data']
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - data 필드는 리스트, 길이: 100
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - 첫 번째 포스트 키: ['id', 'post_type', 'post_title', 'post_created', 'post_sentiment', 'post_link', 'post_image', 'interactions_total', 'creator_id', 'creator_name', 'creator_display_name', 'creator_followers', 'creator_avatar']
2025-06-27 00:28:06 - trading.hybrid_architecture.lunar_data_collector - INFO - 총 100개 포스트 가공 완료 (토픽: DOGE)
2025-06-27 00:28:06 - trading.hybrid_architecture.data_store - INFO - DOGE 시장 데이터 저장 완료
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_market_market_c252afe0_1750951686.json
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - DOGE 시장 데이터 로깅 완료: market_c252afe0_1750951686
2025-06-27 00:28:06 - trading.hybrid_architecture.data_store - WARNING - DOGE 뉴스 데이터가 문자열로 전달됨: config...
2025-06-27 00:28:06 - trading.hybrid_architecture.data_store - INFO - DOGE 뉴스 데이터 저장 완료: config
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_news_news_33914063_1750951686.json
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - DOGE 뉴스 데이터 로깅 완료: news_33914063_1750951686
2025-06-27 00:28:06 - trading.hybrid_architecture.data_store - WARNING - DOGE 뉴스 데이터가 문자열로 전달됨: data...
2025-06-27 00:28:06 - trading.hybrid_architecture.data_store - INFO - DOGE 뉴스 데이터 저장 완료: data
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\DOGE_news_news_a2a5feb9_1750951686.json
2025-06-27 00:28:06 - trading.hybrid_architecture.data_logger - INFO - DOGE 뉴스 데이터 로깅 완료: news_a2a5feb9_1750951686
2025-06-27 00:28:06 - trading.hybrid_architecture.hybrid_controller - INFO - DOGE 데이터 수집 완료 및 실행 큐 전달
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - Text generation complete (time: 3.63s)
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "card_id": "reasoning_BTC_1750951685",
  "symbol": "BTC",
  "reasoning": "시장 상황 분석",
  "market_analysis": "가격 및 변동성 분석",
  "decision_factors": ["강한 상승 돌파", "주요 저항선 상승", "고체적 거래량 확인", "V자형 반전 패턴"]
...
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - 🔍 HiAR 전체 응답 내용 (길이: 201자):
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답: {
  "card_id": "reasoning_BTC_1750951685",
  "symbol": "BTC",
  "reasoning": "시장 상황 분석",
  "market_analysis": "가격 및 변동성 분석",
  "decision_factors": ["강한 상승 돌파", "주요 저항선 상승", "고체적 거래량 확인", "V자형 반전 패턴"]
}
2025-06-27 00:28:09 - models.vllm_client_enhanced - INFO - 🔍 HiAR 응답에 완전한 JSON 구조 발견
2025-06-27 00:28:09 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR vLLM 응답 수신: <class 'dict'>
2025-06-27 00:28:09 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR vLLM 응답 구조: ['text', 'finish_reason', 'session_id', 'symbol']
2025-06-27 00:28:09 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] HiAR 생성된 텍스트 길이: 201자
2025-06-27 00:28:09 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'card_id': 'reasoning_BTC_1750951685', 'symbol': 'BTC', 'reasoning': '시장 상황 분석', 'market_analysis': '가격 및 변동성 분석', 'decision_factors': ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']}
2025-06-27 00:28:09 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 직접 실행 성공
2025-06-27 00:28:10 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 완료, 다음 단계 진행
2025-06-27 00:28:10 - trading.hybrid_architecture.data_store - INFO - BTC 사고 카드 저장 완료: reasoning_BTC_1750951685
2025-06-27 00:28:10 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_card_reasoning_BTC_1750951685.json
2025-06-27 00:28:10 - trading.hybrid_architecture.data_logger - INFO - BTC 사고 카드 로깅 완료: reasoning_BTC_1750951685
2025-06-27 00:28:10 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] SELA 전략 생성 시작 (순차 처리)
2025-06-27 00:28:10 - trading.hybrid_architecture.hybrid_controller - INFO - 🔄 [BTC] SELA 진짜 순차 실행 시작 (정상 SELA 에이전트 사용)
2025-06-27 00:28:10 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 정상 SELA 에이전트 호출
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search SELA 전략 생성 시작 (v2.0 - inca_result 지원)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 타입: <class 'dict'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 타입: <class 'NoneType'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - inca_result 내용: None
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - market_data 키들: ['symbol', 'formatted_symbol', 'price', 'lastPrice', 'change_24h', 'volume_24h', 'high_24h', 'low_24h', 'timestamp', 'prices', 'volumes', 'timestamps']
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 시작
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: reasoning_BTC_1750951685
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - symbol: BTC
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 시장 상황 분석
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - market_analysis: 가격 및 변동성 분석
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - decision_factors: ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - direct_execution: True
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: N/A (백업 추출)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: N/A (백업 추출)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: N/A (백업 추출)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: N/A... (백업 추출)
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: True
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - BULLISH 키워드 감지됨!
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107123.7
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.081%
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116041.853
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107123.7
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 0.000%
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.00%
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 3881자
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951690050_3268', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:10 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951690050_3268', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:10 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951690050_3268'}
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:10 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951690050_3268'}
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 3881자
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:10 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:10 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 85자
2025-06-27 00:28:10 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 8자
2025-06-27 00:28:10 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1526
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 참고 정보 (InCA 평가)
📊 시장 분석 정보:
- 시장 중요도: 5/10
- 시장 방향성: neutral
- InCA 추천 행동: hold



시장 데이터



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction": "[long|short|neutral]",
  "entry_pric...
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 참고 정보 (InCA 평가)\n📊 시장 분석 정보:\n- 시장 중요도: 5/10\n- 시장 방향성: neutral\n- InCA 추천 행동: hold\n\n\n\n시장 데이터\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107123.7,\n  "stop_loss": [현재가 $107123.7의 ±1-3% 범위],\n  "take_profit": [현재가 $107123.7의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 381
2025-06-27 00:28:10 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.65s)
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "sell",
  "direction": "short",
  "entry_price": 107123.7,
  "stop_loss": 109300.0,
  "take_profit": 104200.0,
  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(...
2025-06-27 00:28:11 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 107123.7,\n  "stop_loss": 109300.0,\n  "take_profit": 104200.0,\n  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(72)와 MACD(0.00)의 과열 상태와 볼린저 밴드 상단 돌파 후의 조정 가능성을 고려할 때 단기적으로 하락 흐름이 예상됨. 또한, 200일 이동 평균선(106,500)을 기준으로 현재 가격이 상대적으로 높은 수준에 머물고 있어 기술적 지표와 InCA 신호가 일치함.",\n  "confidence": 0.78,\n  "key_points": ["InCA SELL 신호", "RSI 과열 상태", "볼린저 밴드 상단 돌파 후 조정 예상"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.66초
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 107123.7,\n  "stop_loss": 109300.0,\n  "take_profit": 104200.0,\n  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며,...
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 447자
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 447자
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'sell', 'direction': 'short', 'entry_price': 107123.7, 'stop_loss': 109300.0, 'take_profit': 104200.0, 'reasoning': 'InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(72)와 MACD(0.00)의 과열 상태와 볼린저 밴드 상단 돌파 후의 조정 가능성을 고려할 때 단기적으로 하락 흐름이 예상됨. 또한, 200일 이동 평균선(106,500)을 기준으로 현재 가격이 상대적으로 높은 수준에 머물고 있어 기술적 지표와 InCA 신호가 일치함.', 'confidence': 0.78, 'key_points': ['InCA SELL 신호', 'RSI 과열 상태', '볼린저 밴드 상단 돌파 후 조정 예상']}
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: 816773cc-6dcf-4a25-a76a-6713aaaa1715
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.79
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.863613043808121
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.4636
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: sell
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.78
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(72)와 MACD(0.00)의 과열 상태와 볼린저 밴드 상단 돌파 후의...
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.66초
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - 최종 결과: SELL 전략 생성됨!
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 완료
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과 타입: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과: {'strategy_id': '816773cc-6dcf-4a25-a76a-6713aaaa1715', 'symbol': 'BTC', 'timestamp': 1750951691, 'type': 'sell', 'entry_price': 107123.7, 'stop_loss': 109300.0, 'take_profit': 104200.0, 'reasoning': 'InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, SELA 분석에서도 RSI(72)와 MACD(0.00)의 과열 상태와 볼린저 밴드 상단 돌파 후의 조정 가능성을 고려할 때 단기적으로 하락 흐름이 예상됨. 또한, 200일 이동 평균선(106,500)을 기준으로 현재 가격이 상대적으로 높은 수준에 머물고 있어 기술적 지표와 InCA 신호가 일치함.', 'confidence': 0.78, 'reasoning_card_id': 'card_61de18ea_1750951685', 'risk_level': 'medium', 'key_points': ['InCA SELL 신호', 'RSI 과열 상태', '볼린저 밴드 상단 돌파 후 조정 예상'], 'market_context': {'price': 107123.7, 'percent_change_24h': 0.081, 'timestamp': 1750951678}, 'paper_based': False, 'risk_reward': 1.***************, 'importance': 7.791457699508241}
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 클래스: <class 'super'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 메서드 목록: []
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 메서드 호출 시도
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: CachingLLMProxy (VLLMWrapper 불필요)
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: medium\strategies.db
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - id: card_1
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: card_61de18ea_1750951685
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: bearish (분석용)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: sell (분석용)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: 5 (분석용)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: The short-term trend shows a bearish pattern with ... (분석용)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] InCA 결과 분석: sell, 중요도: 5 (SELA 독립 분석)
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - InCA BEARISH 분석 확인!
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107123.7
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.081%
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116041.853
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: $2135.78B
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107123.7
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): -18.359%
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.08%
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 5000자
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951691417_8937', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:11 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951691417_8937', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:11 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951691417_8937'}
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:11 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951691417_8937'}
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5000자
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:11 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:11 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-27 00:28:11 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-27 00:28:11 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1564
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107123.7,\n  "stop_loss": [현재가 $107123.7의 ±1-3% 범위],\n  "take_profit": [현재가 $107123.7의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: SELL** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 391
2025-06-27 00:28:11 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.53s)
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107123.7,
  "stop_loss": [105981.3, 108256.1],
  "take_profit": [105000.0, 109000.0],
  "reasoning": "시장 중요도가 낮고 방향성이 중립적임. InCA는 hold를 추...
2025-06-27 00:28:15 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107123.7,\n  "stop_loss": [105981.3, 108256.1],\n  "take_profit": [105000.0, 109000.0],\n  "reasoning": "시장 중요도가 낮고 방향성이 중립적임. InCA는 hold를 추천하며, 현재 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정. 단기/중기 트렌드가 명확하지 않으며, 기술적 지표도 혼잡한 상태. 리스크/리워드 비율을 유지하면서 보수적인 접근이 필요.",\n  "confidence": 0.75,\n  "key_points": ["시장 중요도 낮음", "InCA 추천 hold", "리스크/리워드 비율 1.2 이상 유지"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.56초
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107123.7,\n  "stop_loss": [105981.3, 108256.1],\n  "take_profit": [105000.0, 109000.0],\n  "reasoning": "시장 중요도가 낮고 방향성이 중립적...
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 417자
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 417자
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107123.7, 'stop_loss': [105981.3, 108256.1], 'take_profit': [105000.0, 109000.0], 'reasoning': '시장 중요도가 낮고 방향성이 중립적임. InCA는 hold를 추천하며, 현재 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정. 단기/중기 트렌드가 명확하지 않으며, 기술적 지표도 혼잡한 상태. 리스크/리워드 비율을 유지하면서 보수적인 접근이 필요.', 'confidence': 0.75, 'key_points': ['시장 중요도 낮음', 'InCA 추천 hold', '리스크/리워드 비율 1.2 이상 유지']}
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: b80aea8e-bf21-40c1-9866-912379b52c72
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.32
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.474053299862108
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0741
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 시장 중요도가 낮고 방향성이 중립적임. InCA는 hold를 추천하며, 현재 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정. 단기/중기 트렌드가 ...
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.56초
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [BTC] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 완료
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과 타입: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 반환된 결과: {'strategy_id': 'b80aea8e-bf21-40c1-9866-912379b52c72', 'symbol': 'BTC', 'timestamp': 1750951695, 'type': 'none', 'entry_price': 107123.7, 'stop_loss': 105981.3, 'take_profit': 105000.0, 'reasoning': '시장 중요도가 낮고 방향성이 중립적임. InCA는 hold를 추천하며, 현재 가격 기준으로 ±1-3% 범위의 스톱로스와 ±2-5% 범위의 테이크프로핏을 설정. 단기/중기 트렌드가 명확하지 않으며, 기술적 지표도 혼잡한 상태. 리스크/리워드 비율을 유지하면서 보수적인 접근이 필요.', 'confidence': 0.75, 'reasoning_card_id': 'reasoning_BTC_1750951685', 'risk_level': 'medium', 'key_points': ['시장 중요도 낮음', 'InCA 추천 hold', '리스크/리워드 비율 1.2 이상 유지'], 'market_context': {'price': 107123.7, 'percent_change_24h': 0.0, 'timestamp': 1750951678}, 'paper_based': False, 'risk_reward': 0.0, 'importance': 7.3230541743104745}
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 클래스: <class 'super'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - super() 메서드 목록: []
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 메서드 호출 시도
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: CachingLLMProxy (VLLMWrapper 불필요)
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: medium\strategies.db
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [BTC] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - symbol: BTC
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning_card 키들: ['card_id', 'symbol', 'reasoning', 'market_analysis', 'decision_factors', 'direct_execution']
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - card_id: reasoning_BTC_1750951685
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - symbol: BTC
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - reasoning: 시장 상황 분석
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - market_analysis: 가격 및 변동성 분석
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - decision_factors: ['강한 상승 돌파', '주요 저항선 상승', '고체적 거래량 확인', 'V자형 반전 패턴']
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - direct_execution: True
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - situation_type: N/A (백업 추출)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - action_recommendation: N/A (백업 추출)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - importance: N/A (백업 추출)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - reasoning: N/A... (백업 추출)
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bullish 키워드 발견: True
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - BULLISH 키워드 감지됨!
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107123.7
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.081%
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116041.853
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 변동률 분석:
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 현재 가격: $107123.7
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 단기 변동률 (3분봉): 0.000%
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24시간 변동률: 0.00%
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 프롬프트 생성 완료: 3881자
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951695620_5916', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:15 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_BTC_1750951695620_5916', 'agent_type': 'sela', 'symbol': 'BTC'}
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:15 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951695620_5916'}
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:15 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 BTC 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_BTC_1750951695620_5916'}
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 3881자
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:15 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: BTC (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:15 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 85자
2025-06-27 00:28:15 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 8자
2025-06-27 00:28:15 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 995자
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: BTC
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1526
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Symbol: BTC
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 참고 정보 (InCA 평가)
📊 시장 분석 정보:
- 시장 중요도: 5/10
- 시장 방향성: neutral
- InCA 추천 행동: hold



시장 데이터



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction": "[long|short|neutral]",
  "entry_pric...
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 참고 정보 (InCA 평가)\n📊 시장 분석 정보:\n- 시장 중요도: 5/10\n- 시장 방향성: neutral\n- InCA 추천 행동: hold\n\n\n\n시장 데이터\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 107123.7,\n  "stop_loss": [현재가 $107123.7의 ±1-3% 범위],\n  "take_profit": [현재가 $107123.7의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: HOLD** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 381
2025-06-27 00:28:15 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:17 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:17 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:17 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:17 - models.vllm_client_enhanced - INFO - Text generation complete (time: 6.04s)
2025-06-27 00:28:17 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "sell",
  "direction": "short",
  "entry_price": 107123.7,
  "stop_loss": 109366.1,
  "take_profit": 103159.3,
  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123...
2025-06-27 00:28:17 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:17 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 107123.7,\n  "stop_loss": 109366.1,\n  "take_profit": 103159.3,\n  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123.7로, 1-3% 범위 내의 스톱로스와 2-5% 범위 내의 테이크프로핏을 설정했습니다. 기술적 지표(RSI, MACD)와 시장 트렌드 분석을 종합적으로 고려한 결과, 단기 및 중기적으로 하락 흐름이 지속될 가능성이 높습니다.",\n  "confidence": 0.75,\n  "key_points": ["InCA 신호는 SELL로, 시장 데이터 분석에 기반합니다.", "현재 가격 대비 스톱로스와 테이크프로핏은 리스크/리워드 비율 1.2 이상을 유지합니다.", "기술적 지표와 시장 트렌드 분석을 종합적으로 고려한 결과입니다."]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 6.05초
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 107123.7,\n  "stop_loss": 109366.1,\n  "take_profit": 103159.3,\n  "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다...
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 496자
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 496자
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'sell', 'direction': 'short', 'entry_price': 107123.7, 'stop_loss': 109366.1, 'take_profit': 103159.3, 'reasoning': 'InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123.7로, 1-3% 범위 내의 스톱로스와 2-5% 범위 내의 테이크프로핏을 설정했습니다. 기술적 지표(RSI, MACD)와 시장 트렌드 분석을 종합적으로 고려한 결과, 단기 및 중기적으로 하락 흐름이 지속될 가능성이 높습니다.', 'confidence': 0.75, 'key_points': ['InCA 신호는 SELL로, 시장 데이터 분석에 기반합니다.', '현재 가격 대비 스톱로스와 테이크프로핏은 리스크/리워드 비율 1.2 이상을 유지합니다.', '기술적 지표와 시장 트렌드 분석을 종합적으로 고려한 결과입니다.']}
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: 19124539-eaa8-46bb-a3c5-6b14a2b490ac
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.91
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.9827291256306743
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.5827
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: sell
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123.7로, 1-3% 범위 내의 스톱로스와 2-5% 범위 내의 테이크프로핏...
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 6.05초
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [BTC] SELA_IMPL - 최종 결과: SELL 전략 생성됨!
2025-06-27 00:28:17 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 호출 결과: <class 'dict'>
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] SELA 직접 사용: sell (신뢰도: 0.750)
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] 합의 데이터 수집 완료 (학습용)
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 실제 학습 데이터 추출 완료: InCA=sell, SELA=none, 장기=neutral
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 거래 결정 메서드 호출 시작
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 메서드 존재 확인: True
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 거래 신호(sell) 감지 - 신뢰도(0.750)와 관계없이 실행
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 거래 신호(sell) 실행 - 신뢰도: 0.750
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 거래 결정 완료: True
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 신호(sell)가 거래 가능한 신호이므로 강제 실행
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실제 거래 실행 시작: sell
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [BTC] 실행 모드 확인: real
2025-06-27 00:28:17 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실제 거래 모드로 실행
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [BTC] 전략에서 direction 추출: type=sell, action=unknown → direction=sell
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [디버그] execute_trade 시작: 심볼=BTC, 전략={'strategy_id': '19124539-eaa8-46bb-a3c5-6b14a2b490ac', 'symbol': 'BTC', 'timestamp': 1750951697, 'type': 'sell', 'entry_price': 107123.7, 'stop_loss': 109366.1, 'take_profit': 103159.3, 'reasoning': 'InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123.7로, 1-3% 범위 내의 스톱로스와 2-5% 범위 내의 테이크프로핏을 설정했습니다. 기술적 지표(RSI, MACD)와 시장 트렌드 분석을 종합적으로 고려한 결과, 단기 및 중기적으로 하락 흐름이 지속될 가능성이 높습니다.', 'confidence': 0.75, 'reasoning_card_id': 'card_61de18ea_1750951685', 'risk_level': 'medium', 'key_points': ['InCA 신호는 SELL로, 시장 데이터 분석에 기반합니다.', '현재 가격 대비 스톱로스와 테이크프로핏은 리스크/리워드 비율 1.2 이상을 유지합니다.', '기술적 지표와 시장 트렌드 분석을 종합적으로 고려한 결과입니다.'], 'market_context': {'price': 107123.7, 'percent_change_24h': 0.081, 'timestamp': 1750951678}, 'paper_based': False, 'risk_reward': 1.7679272208348105, 'importance': 7.9107460914910055, 'consensus_signal': 'sell', 'consensus_confidence': 0.75, 'consensus_breakdown': {'short_term': {'action': 'sell', 'situation': 'bearish', 'importance': 0.5, 'confidence': 0.25, 'source': 'InCA', 'timeframe': '1분봉'}, 'medium_term': {'action': 'none', 'type': 'sell', 'importance': 0.5, 'confidence': 0.75, 'source': 'SELA', 'timeframe': '1시간봉'}, 'long_term': {'action': 'neutral', 'trend': 'sideways', 'trend_change_pct': 0.0, 'importance': 0.5, 'confidence': 0.3, 'source': 'LongTerm', 'timeframe': '일봉', 'note': '일봉 데이터 부족'}}}
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [디버그] 시장 데이터: {'id': 'market_BTC_1750951678', 'symbol': 'BTC', 'timestamp': 1750951678, 'datetime': '2025-06-27 00:27:58', 'date': '2025-06-27', 'time': '00:27:58', 'price': 107123.7, 'open': 0.0, 'high': 0.0, 'low': 0.0, 'close': 107110.0, 'volume': 116041.853, 'volume_24h': 116041.853, 'high_24h': 108249.9, 'low_24h': 106700.0, 'percent_change_24h': 0.081, 'volatility': 0.0, 'rsi': 50.0, 'average_sentiment': 0.5, 'sentiment_score': 0.5, 'social_volume': 463725917, 'social_dominance': 0.097, 'social_contributors': 4637259, 'bullish_sentiment': 0.5, 'bearish_sentiment': 0.5, 'data_source': 'binance_api', 'is_real_data': True, 'has_news': True, 'execution_timestamp': 1750951678, 'news_count': 99, 'ema_7': 107258.62857142858, 'ema_14': 107347.61428571427, 'ema_25': 107343.92, 'ema_50': 107172.37, 'ema_99': 107192.95555555556, 'ema_200': 107414.16000000003, 'news': [{'title': 'Bitcoin Price Surges', 'content': 'Bitcoin price surges to new high.', 'sentiment': 0.8}, {'title': 'Ethereum Price Drops', 'content': 'Ethereum price drops to new low.', 'sentiment': 0.2}], 'news_sentiment': 3.1356565656565665, 'post_count': 100, 'bullish_ratio': 0.0, 'bearish_ratio': 0, 'galaxy_score': 0, 'alt_rank': 0, 'market_cap': 2135776990706.41, 'recent_news_titles': ['Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily', 'Indian Politician Calls for Bitcoin Reserve Pilot as US, Bhutan Adopt Crypto Strategies', "Fannie And Freddie Directed To Consider Crypto Mortgages By US Housing Regulator, But Michael Saylor Thinks It's Only Bitcoin That Has 'Entered The American Dream' - Federal Home Loan (OTC:FMCC) - Benzinga", 'ASX-Listed Biotech Firm Latest to Adopt a Bitcoin Strategy to Stay Afloat - Decrypt', 'Aurora Mobile to Allocate 20% of Treasury to Bitcoin and Other Crypto Assets – Crypto News Bitcoin News'], 'top_social_posts': [{'text': "● POWELL TALKING LIKE HE WANT TO DO RATE CUTS\n\n● MIDDLE EAST SITUATION HAS BEEN RESOLVED\n\n● ETF INFLOWS ARE RISING AGAIN\n\n● DXY IS DUMPING HARD\n\n● CRYPTO IS SOON TO BE ACCEPTED AS COLLATERAL\n\n● US GOVERNMENT IS FOCUSING ON BUYING BTC\n\n● NASDAQ HAS HIT A NEW ATH\n\nPLEASE, DON'T START ANY BLACK SWAN EVENT AND LET THE MARKET PUMP FOR AT LEAST A MONTH.", 'sentiment': 3.45, 'platform': 'tweet'}, {'text': 'JUST IN: Coinbase stock hit a new all time high since going public 🚀', 'sentiment': 3, 'platform': 'tweet'}, {'text': 'BUY! Bitcoin.', 'sentiment': 3, 'platform': 'tweet'}, {'text': 'LIVE FOREX ANALYSIS I NYC SESSION I GOLD & BITCOIN I FOREX HINDI I JUNE 26 | DAY 330 |TopG Trader', 'sentiment': 3.17, 'platform': 'youtube-video'}, {'text': 'Bitcoin supply on exchanges has dropped to a new all-time low 👀 \n\nWith liquidity supply surpassing 14M it looks like $BTC holders are moving to long term storage 👀 \n\nYou know what that means right ?', 'sentiment': 3.21, 'platform': 'tweet'}, {'text': 'JUST IN: 🇬🇧 UK head of Citi calls for review of restrictive capital requirements on banks holding Bitcoin and crypto for clients - Bloomberg\n\nCiti wants to get in the game... 😉', 'sentiment': 2.94, 'platform': 'tweet'}, {'text': 'JUST IN: Binance Founder CZ says "the current American Dream is to own a home."\n\n"The future American Dream will be to own 0.1 BTC, which will be more than the value of a house in the US."', 'sentiment': 3.19, 'platform': 'tweet'}, {'text': 'This is great to see, BTC count as assets for mortgage!\n\nThe current American Dream is to own a home.\n\nThe future American Dream will be to own 0.1 BTC, which will be more than the value of a house in the US.', 'sentiment': 3.39, 'platform': 'tweet'}, {'text': 'Nasdaq ATH\nS&P Almost ATH\nBitcoin: not even close and that despite all those treasury companies and ETFs buying up $1.5 billion in the first 3 days of the week.\n\nNot suspicious at all.', 'sentiment': 3.1, 'platform': 'tweet'}, {'text': "Pradeep Bhandari, the national spokesperson for India's ruling party, called for the launch of a Bitcoin reserve pilot, arguing that India should seize the opportunity to develop a sovereign BTC strategy in the context of the United States setting up a strategic Bitcoin reserve and Bhutan promoting national mining.", 'sentiment': 3.19, 'platform': 'tweet'}], 'recent_candles': [[1750950480000, '107361.90', '107393.10', '107350.40', '107380.10', '26.993', 1750950539999, '2898155.29510', 1148, '17.719', '1902431.41350', '0'], [1750950540000, '107380.00', '107392.60', '107326.50', '107351.00', '59.311', 1750950599999, '6368199.35780', 1158, '26.169', '2809510.60790', '0'], [1750950600000, '107350.90', '107400.00', '107326.70', '107399.90', '97.022', 1750950659999, '10416411.73880', 1739, '45.146', '4847223.52370', '0'], [1750950660000, '107400.00', '107400.00', '107303.30', '107303.30', '68.054', 1750950719999, '7304321.08720', 1689, '21.380', '2294631.81090', '0'], [1750950720000, '107303.30', '107337.10', '107281.80', '107319.00', '74.289', 1750950779999, '7971607.12670', 2077, '24.022', '2577860.14910', '0'], [1750950780000, '107319.00', '107327.80', '107289.50', '107292.80', '66.204', 1750950839999, '7103908.83370', 1402, '25.860', '2774972.63490', '0'], [1750950840000, '107292.70', '107295.00', '107225.00', '107226.10', '80.016', 1750950899999, '8583122.29850', 1887, '13.836', '1484212.40750', '0'], [1750950900000, '107226.20', '107352.30', '107226.10', '107292.10', '104.599', 1750950959999, '11223838.12930', 2522, '75.338', '8084086.66680', '0'], [1750950960000, '107292.00', '107334.30', '107289.60', '107304.20', '62.397', 1750951019999, '6695311.71780', 1434, '26.129', '2803687.06080', '0'], [1750951020000, '107304.10', '107334.20', '107280.00', '107280.10', '39.768', 1750951079999, '4267427.26190', 1335, '11.987', '1286321.35300', '0'], [1750951080000, '107280.10', '107334.20', '107263.50', '107334.20', '66.291', 1750951139999, '7112479.26930', 1479, '40.758', '4372881.85600', '0'], [1750951140000, '107334.20', '107334.20', '107300.90', '107329.90', '30.391', 1750951199999, '3261692.39630', 978, '12.573', '1349406.52660', '0'], [1750951200000, '107329.90', '107350.00', '107255.00', '107323.50', '96.621', 1750951259999, '10366571.71080', 1774, '37.215', '3993277.70200', '0'], [1750951260000, '107323.50', '107352.30', '107323.40', '107337.10', '69.558', 1750951319999, '7466078.16760', 1609, '50.007', '5367533.50120', '0'], [1750951320000, '107337.10', '107343.20', '107273.50', '107308.10', '59.910', 1750951379999, '6428532.17490', 1707, '19.131', '2052723.44830', '0'], [1750951380000, '107308.10', '107336.20', '107302.00', '107302.00', '31.245', 1750951439999, '3353127.96160', 1159, '20.743', '2226042.98370', '0'], [1750951440000, '107302.10', '107337.80', '107294.50', '107327.60', '91.532', 1750951499999, '9822788.17290', 1368, '31.809', '3413476.07500', '0'], [1750951500000, '107327.60', '107344.60', '107302.50', '107303.80', '39.486', 1750951559999, '4237800.48890', 1056, '21.385', '2295109.66640', '0'], [1750951560000, '107303.80', '107309.90', '107257.30', '107265.40', '38.024', 1750951619999, '4079351.20850', 1284, '20.011', '2146824.12970', '0'], [1750951620000, '107265.40', '107273.40', '107104.90', '107105.00', '212.175', 1750951679999, '22739913.02080', 4080, '31.847', '3413458.47380', '0']], 'candles_count': 20, 'data_timestamp': 1750951678, 'has_timeseries_data': True, 'short_term_change_pct': -0.18359396842556525, 'recent_high': 107344.6, 'recent_low': 107104.9}
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [디버그] 바이낸스 유틸리티: True
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [디버그] 고급 트레이딩 시스템: False
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 동기화 시작: 강제=False, 마지막 동기화 이후 34초 경과
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 바이낸스 API를 통해 포지션 정보 동기화 시작
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-27 00:28:17 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:28:17 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951696271}
2025-06-27 00:28:17 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951696271
2025-06-27 00:28:17 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951696271
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 523
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: SOLUSDT = 0.72 (PnL: -0.4896)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -1.24223)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.2439)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -2.15208)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - positionAmt: 0.72 (float: 0.72)
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - entryPrice: 143.47
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - unrealizedProfit: -0.48960000
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:28:17 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - unrealizedProfit: -2.15208000
2025-06-27 00:28:17 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 응답 타입: <class 'list'>
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 전체 포지션 수: 523
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 활성 포지션 개수: 4
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 수: 1
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 수: 1
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 상세: positionAmt=0.72, entryPrice=143.47
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 상세: positionAmt=504.0, entryPrice=0.16426
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'SOLUSDT', 'initialMargin': '102.80880000', 'maintMargin': '0.51404400', 'unrealizedProfit': '-0.48960000', 'positionInitialMargin': '102.80880000', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '143.47', 'breakEvenPrice': '143.541735', 'maxNotional': '400000000', 'positionSide': 'BOTH', 'positionAmt': '0.72', 'notional': '102.80880000', 'isolatedWallet': '0', 'updateTime': 1750949231645, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 주요 필드:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - symbol: SOLUSDT
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - positionAmt: 0.72 (타입: <class 'str'>)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.48960000
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - notional: 102.80880000
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 0.72 (타입: <class 'float'>)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [SOL] 포지션 방향 결정: positionAmt=0.72
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [SOL] 양수 포지션 → LONG 방향
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [SOL] 최종 결정된 방향: long
2025-06-27 00:28:17 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:17 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:17 - binance.binance_utils - INFO - 변화율(%): -0.834
2025-06-27 00:28:17 - binance.binance_utils - INFO - 거래량: 18878385.33
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [SOL] 🔧 강제 PnL 계산: -0.544% (진입: 143.47, 현재: 142.69, 방향: long)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 상세:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.4896
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.54%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [SOL] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [SOL] 기존 포지션 ID: sync_SOL_1750951663, 동기화 포지션 ID: sync_SOL_1750951697
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [ETH] 포지션 방향 결정: positionAmt=0.023
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [ETH] 양수 포지션 → LONG 방향
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [ETH] 최종 결정된 방향: long
2025-06-27 00:28:17 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:17 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:17 - binance.binance_utils - INFO - 변화율(%): 0.890
2025-06-27 00:28:17 - binance.binance_utils - INFO - 거래량: 5291313.042
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [ETH] 🔧 강제 PnL 계산: -2.253% (진입: 2486.13, 현재: 2430.11, 방향: long)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [ETH] 바이낸스 포지션 상세:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 2486.13
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.24223
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -2.25%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [ETH] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [ETH] 기존 포지션 ID: sync_ETH_1750928149, 동기화 포지션 ID: sync_ETH_1750951697
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [BNB] 포지션 방향 결정: positionAmt=0.09
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [BNB] 양수 포지션 → LONG 방향
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [BNB] 최종 결정된 방향: long
2025-06-27 00:28:17 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:17 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:17 - binance.binance_utils - INFO - 변화율(%): 0.126
2025-06-27 00:28:17 - binance.binance_utils - INFO - 거래량: 295882.50
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [BNB] 🔧 강제 PnL 계산: -0.384% (진입: 646.61, 현재: 644.13, 방향: long)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [BNB] 바이낸스 포지션 상세:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 646.61
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.2439
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.38%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [BNB] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [BNB] 기존 포지션 ID: sync_BNB_1750951663, 동기화 포지션 ID: sync_BNB_1750951697
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'DOGEUSDT', 'initialMargin': '80.63496000', 'maintMargin': '0.52412724', 'unrealizedProfit': '-2.15208000', 'positionInitialMargin': '80.63496000', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.16426', 'breakEvenPrice': '0.16434213', 'maxNotional': '200000000', 'positionSide': 'BOTH', 'positionAmt': '504', 'notional': '80.63496000', 'isolatedWallet': '0', 'updateTime': 1750927964135, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 주요 필드:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - symbol: DOGEUSDT
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - positionAmt: 504 (타입: <class 'str'>)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -2.15208000
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - notional: 80.63496000
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 504.0 (타입: <class 'float'>)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 포지션 방향 결정: positionAmt=504.0
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 양수 포지션 → LONG 방향
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 최종 결정된 방향: long
2025-06-27 00:28:17 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:17 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:17 - binance.binance_utils - INFO - 변화율(%): -2.009
2025-06-27 00:28:17 - binance.binance_utils - INFO - 거래량: 5095276969
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [DOGE] 🔧 강제 PnL 계산: -2.600% (진입: 0.16426, 현재: 0.15999, 방향: long)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 상세:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -2.15208
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -2.60%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 기존 포지션 ID: sync_DOGE_1750927996, 동기화 포지션 ID: sync_DOGE_1750951697
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 시작: 현재 4개 포지션, 거래소에서 가져온 4개 포지션
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - exchange_positions 상세:
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 현재 내부 포지션 키: ['SOL_long', 'ETH_long', 'BNB_long', 'DOGE_long']
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 거래소 포지션 키: ['SOL_long', 'ETH_long', 'BNB_long', 'DOGE_long']
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 바이낸스 API 포지션과 내부 포지션 스마트 매핑
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 업데이트: SOL long, PnL: -0.54%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 업데이트: ETH long, PnL: -2.25%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 업데이트: BNB long, PnL: -0.38%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 업데이트: DOGE long, PnL: -2.60%
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 완료: 최종 4개 포지션
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions 배열 크기: 4
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 수: 4
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 1: SOL (크기: 0.72)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 2: ETH (크기: 0.023)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 3: BNB (크기: 0.09)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 수: 4
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 1: SOL (크기: 0.72)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 2: ETH (크기: 0.023)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 3: BNB (크기: 0.09)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 바이낸스 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 내부 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - ✅ 포지션 동기화 상태 양호: 불일치 없음
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔥 포지션 동기화 완료: 4개의 활성 포지션 (내부: 4개)
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 🔥 동기화 변경사항: 4개
2025-06-27 00:28:17 - simulator.trading.portfolio - INFO - 모든 포지션 정보 동기화 완료 (요청 심볼: BTC)
2025-06-27 00:28:17 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:28:18 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951696729}
2025-06-27 00:28:18 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951696729
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - [디버그] 현재 가격: 107123.7
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - [디버그] type 필드에서 direction 추출: sell
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - [디버그] 방향 정규화: sell → short
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🤖 LLM 완전 신뢰 모드 활성화 - 임계값 무시하고 LLM 판단 존중
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - [디버그] 임계값 확인: MIN_CONFIDENCE=0.0, MIN_IMPORTANCE=0.0
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🤖 LLM 완전 신뢰 모드: 신뢰도(0.75), 중요도(7.9107460914910055) - 임계값 무시하고 LLM 판단 존중
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🚀 LLM이 거래 신호를 보냈으므로 무조건 실행합니다!
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - [디버그] 전략 정보: 타입=unknown, 방향=short, 진입가=107123.7, TP=103159.3, SL=109366.1
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🔧 [BTC] 강한신호임계값 정규화: 5.0 → 0.500
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🚀 새로운 투자 전략 적용: 모드=equal_split, 강한신호임계값=0.500
2025-06-27 00:28:18 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:28:18 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951696811}
2025-06-27 00:28:18 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951696811
2025-06-27 00:28:18 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951696811
2025-06-27 00:28:18 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 💰 [BTC] 바이낸스 실제 USDT 잔액: $309.41
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 💰 [BTC] 4개 코인 균등 분할: 실제잔액=$309.41, 코인당 할당=$77.35
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🎯 신호 강도 분석: 신뢰도=0.75, 임계값=0.5, 강한신호=True
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🆕 새로운 포지션 생성: BTC short
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🚀 [BTC] 학습된 성과 반영: {'symbol': {'strategy_id': '19124539-eaa8-46bb-a3c5-6b14a2b490ac', 'symbol': 'BTC', 'timestamp': 1750951697, 'type': 'sell', 'entry_price': 107123.7, 'stop_loss': 109366.1, 'take_profit': 103159.3, 'reasoning': 'InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있습니다. 현재 가격은 107,123.7로, 1-3% 범위 내의 스톱로스와 2-5% 범위 내의 테이크프로핏을 설정했습니다. 기술적 지표(RSI, MACD)와 시장 트렌드 분석을 종합적으로 고려한 결과, 단기 및 중기적으로 하락 흐름이 지속될 가능성이 높습니다.', 'confidence': 0.75, 'reasoning_card_id': 'card_61de18ea_1750951685', 'risk_level': 'medium', 'key_points': ['InCA 신호는 SELL로, 시장 데이터 분석에 기반합니다.', '현재 가격 대비 스톱로스와 테이크프로핏은 리스크/리워드 비율 1.2 이상을 유지합니다.', '기술적 지표와 시장 트렌드 분석을 종합적으로 고려한 결과입니다.'], 'market_context': {'price': 107123.7, 'percent_change_24h': 0.081, 'timestamp': 1750951678}, 'paper_based': False, 'risk_reward': 1.7679272208348105, 'importance': 7.9107460914910055, 'consensus_signal': 'sell', 'consensus_confidence': 0.75, 'consensus_breakdown': {'short_term': {'action': 'sell', 'situation': 'bearish', 'importance': 0.5, 'confidence': 0.25, 'source': 'InCA', 'timeframe': '1분봉'}, 'medium_term': {'action': 'none', 'type': 'sell', 'importance': 0.5, 'confidence': 0.75, 'source': 'SELA', 'timeframe': '1시간봉'}, 'long_term': {'action': 'neutral', 'trend': 'sideways', 'trend_change_pct': 0.0, 'importance': 0.5, 'confidence': 0.3, 'source': 'LongTerm', 'timeframe': '일봉', 'note': '일봉 데이터 부족'}}}, 'total_trades': 0, 'successful_trades': 0, 'success_rate': 0.0, 'avg_profit_pct': 0.0, 'max_profit_pct': 0.0, 'max_loss_pct': 0.0, 'avg_holding_time': 0, 'last_updated': 1750951698} → 배수 0.70
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🚀 [BTC] 동적 포지션 크기 활성화: 0.75 → 18% ($106.90)
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 🚀 [BTC] 최종 선택된 포지션 크기: $106.90
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 💰 [BTC] 4개 코인 균등 분할 제한: $77.35
2025-06-27 00:28:18 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:28:18 - simulator.trading.portfolio - WARNING - ⚠️ [BTC] 포지션 크기(77.35)가 최소 주문 금액(100.00)보다 작음
2025-06-27 00:28:18 - simulator.trading.portfolio - ERROR - ❌ [BTC] 최소 주문 금액(100.00)이 안전 한도(92.82)를 초과
2025-06-27 00:28:18 - simulator.trading.portfolio - INFO - 💡 [BTC] 거래 건너뜀 - 잔액 부족
2025-06-27 00:28:18 - trading.hybrid_architecture.hybrid_controller - ERROR - BTC 실제 거래 실행 실패: No result returned
2025-06-27 00:28:18 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 유효한 전략으로 판단: sell (SELA: sell, 신뢰도: 0.750)
2025-06-27 00:28:18 - trading.hybrid_architecture.data_store - INFO - BTC 실행 결과 저장 완료: exec_ae801afe_1750951698
2025-06-27 00:28:18 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\BTC_exec_exec_ae801afe_1750951698.json
2025-06-27 00:28:18 - trading.hybrid_architecture.data_logger - INFO - BTC 실행 결과 로깅 완료: exec_ae801afe_1750951698
2025-06-27 00:28:18 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 기반 전략 생성 완료: 19124539-eaa8-46bb-a3c5-6b14a2b490ac, sell @ $107123.70 [SELA: sell, 신뢰도: 0.750, 실행: True]
2025-06-27 00:28:18 - hybrid_simulator - INFO - ⏳ BTC 처리 완료 대기 시작 (최대 300초)
2025-06-27 00:28:18 - hybrid_simulator - INFO - ✅ BTC 처리 완료 확인 (0.00초)
2025-06-27 00:28:18 - hybrid_simulator - INFO - ✅ BTC 시장 데이터 처리 완료 (순차 모드)
2025-06-27 00:28:18 - hybrid_simulator - INFO - 🔄 ETH 완전 순차 처리 시작
2025-06-27 00:28:18 - simulator.utils.market_utils - INFO - ETH 실시간 시장 데이터 수집 중...
2025-06-27 00:28:18 - binance.binance_utils - INFO - 시장 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:28:18 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:28:18 - binance.binance_utils - INFO - 최신 가격 조회 성공: ETHUSDT, 가격: 2434.18
2025-06-27 00:28:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:28:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:18 - binance.binance_utils - INFO - 변화율(%): 1.059
2025-06-27 00:28:18 - binance.binance_utils - INFO - 거래량: 5272478.567
2025-06-27 00:28:18 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=3m, 개수=500
2025-06-27 00:28:18 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 3m, 개수: 480
2025-06-27 00:28:18 - binance.binance_utils - INFO - 시장 데이터 조회 성공: ETHUSDT, 가격: 2434.18
2025-06-27 00:28:18 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETHUSDT)
2025-06-27 00:28:18 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:18 - binance.binance_utils - INFO - 변화율(%): 1.059
2025-06-27 00:28:18 - binance.binance_utils - INFO - 거래량: 5272478.567
2025-06-27 00:28:18 - simulator.utils.market_utils - INFO - ETH 24h 변화율: 1.06%
2025-06-27 00:28:18 - simulator.utils.market_utils - INFO - ETH 현재 가격: 2434.18, 24h 변화율: 1.06%
2025-06-27 00:28:18 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2434.18, 변동률 1.06%
2025-06-27 00:28:18 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 중...
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH(ID:2) 코인 데이터 요청 중...
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/coins/2/v1, 파라미터: {}
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/coins/2/v1
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH 코인 데이터 응답 키: ['config', 'data']
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH 코인 객체 키: ['id', 'name', 'symbol', 'price', 'price_btc', 'market_cap', 'percent_change_24h', 'percent_change_7d', 'percent_change_30d', 'volume_24h', 'max_supply', 'circulating_supply', 'close', 'galaxy_score', 'alt_rank', 'volatility', 'market_cap_rank']
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH 거래량 정보: volume_24h=18242584460.464893
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH 소셜 데이터 수집 성공 (coin/v1 엔드포인트)
2025-06-27 00:28:18 - data_collector.lunarcrush_collector - INFO - ETH 관련 뉴스 요청 중... (topic 엔드포인트)
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/news/v1, 파라미터: {}
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/news/v1
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 응답 키: ['config', 'data']
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'config' 타입: <class 'dict'>, 배열 길이: 6
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - 뉴스 응답 키 'data' 타입: <class 'list'>, 배열 길이: 68
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 68개 수집 완료
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 데이터베이스에 저장 완료
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - ETH 뉴스 68개 수집 완료
2025-06-27 00:28:19 - data_collector.lunarcrush_collector - INFO - ETH 관련 포스트 요청 중... (topic 엔드포인트, 최근 3분 데이터)
2025-06-27 00:28:20 - data_collector.lunarcrush_collector - INFO - LunarCrush API v4 요청: https://lunarcrush.com/api4/public/topic/eth/posts/v1, 파라미터: {'start': 1750951519, 'end': 1750951699}
2025-06-27 00:28:20 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:20 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:20 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:20 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.29s)
2025-06-27 00:28:20 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "none",
  "direction": "neutral",
  "entry_price": 107123.7,
  "stop_loss": [105981.3, 108256.1],
  "take_profit": [105000.0, 109000.0],
  "reasoning": "현재 시장은 InCA 평가에 따라 중립적 방향성을 보이고 있으며...
2025-06-27 00:28:20 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:20 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107123.7,\n  "stop_loss": [105981.3, 108256.1],\n  "take_profit": [105000.0, 109000.0],\n  "reasoning": "현재 시장은 InCA 평가에 따라 중립적 방향성을 보이고 있으며, 단기 및 중기 트렌드가 명확하지 않음. 기술적 지표(RSI, MACD)는 과열 또는 과냉 상태가 아니며, 볼린저 밴드 내부에서 횡보 중. 리스크/리워드 비율을 유지하면서 보류 전략이 적절함.",\n  "confidence": 0.75,\n  "key_points": ["InCA 추천은 HOLD로 시장 방향성 중립", "기술적 지표는 과도한 변동성 없음", "리스크 관리와 수익 기회 균형 유지"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'BTC'}, 소요시간: 5.30초
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "none",\n  "direction": "neutral",\n  "entry_price": 107123.7,\n  "stop_loss": [105981.3, 108256.1],\n  "take_profit": [105000.0, 109000.0],\n  "reasoning": "현재 시장은 InCA 평가에 따라 ...
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 424자
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 424자
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - JSON 추출 완료: {'type': 'none', 'direction': 'neutral', 'entry_price': 107123.7, 'stop_loss': [105981.3, 108256.1], 'take_profit': [105000.0, 109000.0], 'reasoning': '현재 시장은 InCA 평가에 따라 중립적 방향성을 보이고 있으며, 단기 및 중기 트렌드가 명확하지 않음. 기술적 지표(RSI, MACD)는 과열 또는 과냉 상태가 아니며, 볼린저 밴드 내부에서 횡보 중. 리스크/리워드 비율을 유지하면서 보류 전략이 적절함.', 'confidence': 0.75, 'key_points': ['InCA 추천은 HOLD로 시장 방향성 중립', '기술적 지표는 과도한 변동성 없음', '리스크 관리와 수익 기회 균형 유지']}
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 전략 ID 생성: c9bd8cf5-879e-4cdb-a320-bb4db10d6446
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [BTC] SELA Tree Search importance 계산 시작
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA Tree Search importance 완료: 7.32
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - UCB 점수: 1.4750136505079
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 방문 횟수: 50
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] - 평균 보상: 0.0750
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 최종 전략 유형: none
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 신뢰도: 0.75
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 전략 추론: 현재 시장은 InCA 평가에 따라 중립적 방향성을 보이고 있으며, 단기 및 중기 트렌드가 명확하지 않음. 기술적 지표(RSI, MACD)는 과열 또는 과냉 상태가 아니며, 볼린저 ...
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [BTC] SELA_IMPL 소요 시간: 5.30초
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⚠️ [BTC] SELA_IMPL - 최종 결과: HOLD 전략 생성됨!
2025-06-27 00:28:20 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [BTC] SELA_AGENT - 직접 호출 결과: <class 'dict'>
2025-06-27 00:28:20 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] 정상 SELA 에이전트 전략 생성 성공: none
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - LunarCrush API 응답 성공: https://lunarcrush.com/api4/public/topic/eth/posts/v1
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 응답 키: ['config', 'data']
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - 응답 키 'config' 타입: <class 'dict'>, 값 타입: <class 'dict'>
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - 응답 키 'data' 타입: <class 'list'>, 배열 길이: 100
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 100개 수집 완료
2025-06-27 00:28:21 - data_collector.lunarcrush_collector - INFO - ETH 소셜 포스트 데이터베이스에 저장 완료
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 수집 완료: 감성=0.50, 소셜볼륨=182425844, 뉴스=68개, 포스트=100개
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 소셜 데이터 결합 완료: 바이낸스 거래량=5272478, 소셜볼륨=182425844
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 1m 캔들 데이터 수집 시도...
2025-06-27 00:28:21 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: ETHUSDT (원본 심볼: ETHUSDT), 인터벌=1m, 개수=50
2025-06-27 00:28:21 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: ETHUSDT, 간격: 1m, 개수: 50
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 1m 캔들 응답: <class 'list'>, 길이: 50
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 1m 첫 번째 캔들 샘플: [1750948740000, '2443.52', '2444.77', '2442.29', '2444.73', '3263.834', 1750948799999, '7975300.87281', 3662, '1498.122', '3660668.74777', '0']
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ✅ ETH 1m 캔들스틱 데이터 수집 성공: 20개
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 단기 변동률: -0.510%
2025-06-27 00:28:21 - simulator.utils.market_utils - INFO - ETH 시장 데이터 수집 완료: 현재가 $2434.18, 24h변동률 1.06%, 단기변동률 -0.510%, 캔들 20개
2025-06-27 00:28:21 - hybrid_simulator - INFO - 📊 ETH 시장 데이터 처리 시작 (순차 모드)
2025-06-27 00:28:21 - simulator.bridge - INFO - ETH 시장 데이터 처리 중...
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 실행 플로우 시작 (InCA → HiAR → SELA)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - ETH InCA 시장 중요도 평가 시작
2025-06-27 00:28:21 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - ETH 시장 데이터 키: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-27 00:28:21 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 프롬프트 생성 - ETH 추출된 값: 가격=$2434.18, 거래량=5272478.567
2025-06-27 00:28:21 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA 에이전트 심볼 추출 (Symbol:): ETH
2025-06-27 00:28:21 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(ETH) vLLM 큐에 요청 추가
2025-06-27 00:28:21 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_execution(ETH) - 우선순위 1, 큐 크기: 1
2025-06-27 00:28:21 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_execution(ETH) - 큐 대기: 0.00초
2025-06-27 00:28:21 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_execution_ETH_1750951701091
2025-06-27 00:28:21 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
/no_think

IMPORTANT: THIS ANALYSIS IS SPECIFICALLY FOR ETH CRYPTOCURRENCY ONLY.

Analyze ALL avail...
2025-06-27 00:28:21 - models.vllm_session_manager - INFO - Created new session: ce35d0eb-e211-4214-9845-aac618e97210 for inca_execution agent (symbol: ETH)
2025-06-27 00:28:21 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_execution_execution_ETH → ce35d0eb-e211-4214-9845-aac618e97210
2025-06-27 00:28:21 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: ce35d0eb-e211-4214-9845-aac618e97210 (inca_execution_ETH)
2025-06-27 00:28:21 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_execution 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:21 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: ce35d0eb-e211-4214-9845-aac618e97210
2025-06-27 00:28:21 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: ce35d0eb-e211-4214-9845-aac618e97210)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] SELA 완료, 다음 단계 진행
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] sela_result 타입: <class 'dict'>
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] sela_result 값: {'strategies': [{'strategy_id': 'c9bd8cf5-879e-4cdb-a320-bb4db10d6446', 'symbol': 'BTC', 'timestamp': 1750951700, 'type': 'none', 'entry_price': 107123.7, 'stop_loss': 105981.3, 'take_profit': 105000.0, 'reasoning': '현재 시장은 InCA 평가에 따라 중립적 방향성을 보이고 있으며, 단기 및 중기 트렌드가 명확하지 않음. 기술적 지표(RSI, MACD)는 과열 또는 과냉 상태가 아니며, 볼린저 밴드 내부에서 횡보 중. 리스크/리워드 비율을 유지하면서 보류 전략이 적절함.', 'confidence': 0.75, 'reasoning_card_id': 'reasoning_BTC_1750951685', 'risk_level': 'medium', 'key_points': ['InCA 추천은 HOLD로 시장 방향성 중립', '기술적 지표는 과도한 변동성 없음', '리스크 관리와 수익 기회 균형 유지'], 'market_context': {'price': 107123.7, 'percent_change_24h': 0.0, 'timestamp': 1750951678}, 'paper_based': False, 'risk_reward': 0.0, 'importance': 7.324364163658827}], 'direct_execution': False}
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - WARNING - ⚠️ [BTC] InCA 신호(buy)와 일치하는 SELA 전략 없음, 최고 신뢰도 전략 선택: none (신뢰도: 0.750)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] SELA 논문 방법론 적용: type=none, confidence=0.750
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [BTC] SELA 직접 사용: none (신뢰도: 0.750)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [BTC] 합의 데이터 수집 완료 (학습용)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 실제 학습 데이터 추출 완료: InCA=buy, SELA=none, 장기=neutral
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM이 none 신호로 판단하여 거래하지 않음
2025-06-27 00:28:21 - trading.hybrid_architecture.utils.virtual_position_tracker - INFO - 🔮 [BTC] 가상 포지션 생성: neutral 예측 (가격: $107123.7000, 신뢰도: 0.50, 평가 기간: 180초 = 3분)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🔮 [BTC] 중립 신호 가상 포지션 생성 완료: 가격=$107123.7000, 신뢰도=0.50, 중요도=5, 3분 후 평가
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - BTC SELA 신호가 중립이므로 거래하지 않음
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 학습 루프 시작
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - [BTC] 학습 데이터 저장 완료 (큐 크기: 1)
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - BTC 실행 로그 50개 검색됨
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] SELA 학습 에이전트 실행 시작
2025-06-27 00:28:21 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] 과거 전략 50개, 성과 데이터 50개 추출
2025-06-27 00:28:21 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:28:21 - binance.binance_utils - INFO - 시장 데이터 요청: BTCUSDT (원본 심볼: BTC)
2025-06-27 00:28:21 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:28:21 - binance.binance_utils - INFO - 최신 가격 요청: https://fapi.binance.com/fapi/v1/ticker/price?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-27 00:28:21 - binance.binance_utils - INFO - 최신 가격 조회 성공: BTCUSDT, 가격: 107047.5
2025-06-27 00:28:21 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:28:21 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BTCUSDT (원본 심볼: BTC)
2025-06-27 00:28:22 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BTCUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:22 - binance.binance_utils - INFO - 변화율(%): 0.052
2025-06-27 00:28:22 - binance.binance_utils - INFO - 거래량: 116176.026
2025-06-27 00:28:22 - binance.binance_utils - INFO - Converting symbol BTC to Binance format: BTCUSDT
2025-06-27 00:28:22 - binance.binance_utils - INFO - 캔들스틱 데이터 요청: BTCUSDT (원본 심볼: BTC), 인터벌=1d, 개수=500
2025-06-27 00:28:22 - binance.binance_utils - INFO - 캔들스틱 데이터 조회 성공: BTCUSDT, 간격: 1d, 개수: 1
2025-06-27 00:28:22 - binance.binance_utils - INFO - 시장 데이터 조회 성공: BTCUSDT, 가격: 107047.5
2025-06-27 00:28:22 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [BTC] 학습용 실제 시장 데이터: 가격=$107047.5
2025-06-27 00:28:22 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] SELA 학습 에이전트 호출 - 실험적 전략 생성
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [BTC] Tree Search 기반 SELA 학습 전략 5개 생성 시작
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 Tree Search SELA 다양한 전략 5개 생성 시작
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - BTC 다양한 전략 5개 생성 시작
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] 외부 시장 데이터 사용: 가격=$107047.5
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] 다양한 전략 생성용 시장 데이터:
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107047.5
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.052%
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116176.026
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 가격: $107047.5
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 24h 변동률: 0.052%
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 거래량: 116176.026
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] - 시가총액: N/A
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성 프롬프트 (처음 200자):
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 Generate 5 trading strategies for BTC.

Market: BTC $107047.5 (0.05%)

IMPORTANT: Use current price $107047.5 as base for all entry_price calculations.

Required JSON format:
{"strategies": [
  {
    ...
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [BTC] SELA 다양한 전략 생성 프롬프트 (마지막 200자):
2025-06-27 00:28:22 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 ..., "point2"]
  }
]}

Create 5 different strategies with varied types (buy/sell/none), risk levels (low/medium/high), and strategy types.
All entry_price values must be based on current price $107047.5.
2025-06-27 00:28:22 - models.vllm_request_queue - INFO - vLLM 요청 추가: sela_diverse(BTC) - 우선순위 2, 큐 크기: 1
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Fast generation complete (4.64s)
2025-06-27 00:28:25 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: inca_execution(ETH) - 처리: 4.65초, 총: 4.65초
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - InCA(ETH) vLLM 응답 수신: 574자
2025-06-27 00:28:25 - trading.hybrid_architecture.utils.json_extractor - INFO - </think> 태그 발견, 제거 중...
2025-06-27 00:28:25 - trading.hybrid_architecture.utils.json_extractor - INFO - 태그 제거 완료. 원본 길이: 574, 정리 후 길이: 550
2025-06-27 00:28:25 - trading.hybrid_architecture.utils.json_extractor - INFO - 정리 후 텍스트 미리보기: {
  "importance": 7,
  "is_important": true,
  "importance_score": 0.75,
  "reasoning": "The 5-minute candle data shows a clear bearish trend with 4 consecutive bearish candles and a significant volum...
2025-06-27 00:28:25 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'importance': 7, 'is_important': True, 'importance_score': 0.75, 'reasoning': 'The 5-minute candle data shows a clear bearish trend with 4 consecutive bearish candles and a significant volume spike on the last candle, confirming the downward momentum. The trend strength is at 19.3/100, indicating a strong bearish move.', 'market_direction': 'bearish', 'situation_type': 'bearish', 'action_recommendation': 'sell', 'trading_direction': 'short', 'confidence': 0.85, 'keywords': ['bearish', 'volume spike', 'downward momentum']}
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 🎯 [ETH] LLM 직접 실행 결과 저장: market_direction=bearish
2025-06-27 00:28:25 - trading.hybrid_architecture.utils.chromadb_utils - INFO - 쿼리 완료: 10개 결과 반환
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.723, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.722, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.722, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.721, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.721, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.721, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.720, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.720, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.719, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - 벡터 DB 유사 경험: ETH 유사도 0.719, 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - ETH 벡터 DB에서 10개 유사 경험 발견
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - ETH 과거 경험 분석: 5개 경험, 성공률 0.0%, 평균 수익 0.00%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - WARNING - ETH 성공률 낮음 (0.0%), 신호 강도 조정 (방향 유지)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - WARNING - ETH 성공률 0%이지만 LLM 신호(bearish) 방향 유지, 신뢰도만 감소
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.inca_agent - INFO - ETH 과거 경험 기반 조정 완료: 중요도 7→5, 신뢰도 0.50→0.25
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 중요도(0.50)가 임계값(0.30)을 초과하여 계속 진행
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - ETH HiAR 사고 흐름 정리 시작
2025-06-27 00:28:25 - fast_hiar_agent - INFO - [ETH] FastHiAR 분석 시작
2025-06-27 00:28:25 - offline_pattern_generator - INFO - 선택된 패턴: ['Standard_CoT']
2025-06-27 00:28:25 - fast_hiar_agent - INFO - [ETH] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:25 - trading.hybrid_architecture.data_store - INFO - ETH 사고 카드 저장 완료: card_42024a55_1750951705
2025-06-27 00:28:25 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_card_card_42024a55_1750951705.json
2025-06-27 00:28:25 - trading.hybrid_architecture.data_logger - INFO - ETH 사고 카드 로깅 완료: card_42024a55_1750951705
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 전략 생성 시작
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] HiAR → SELA 데이터 전달 디버깅
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] reasoning_card analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] reasoning_card direction: N/A
2025-06-27 00:28:25 - trading.hybrid_architecture.hybrid_controller - INFO - 🔍 [ETH] reasoning_card 내용: {'id': 'card_1', 'title': '패턴 분석 1', 'analysis': '현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.', 'reasoning': "사고 카드 'Standard_CoT' 실행 결과", 'confidence': 0.6, 'key_factors': ['패턴: S...
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🚀 [ETH] Tree Search SELA 전략 생성 시작 (v2.0 - inca_result 지원)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - market_data 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - inca_result 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - inca_result 내용: {'importance': 5, 'is_important': True, 'importance_score': 0.5, 'situation_type': 'bearish', 'reasoning': 'The 5-minute candle data shows a clear bearish trend with 4 consecutive bearish candles and a significant volume spike on the last candle, confirming the downward momentum. The trend strength is at 19.3/100, indicating a strong bearish move. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)', 'action_recommendation': 'sell', 'trading_direction': 'short', 'signal_direction': 'bearish', 'keywords': ['bearish', 'volume spike', 'downward momentum'], 'raw_response': ' NO MARKDOWN.\n</think>\n\n{\n  "importance": 7,\n  "is_important": true,\n  "importance_score": 0.75,\n  "reasoning": "The 5-minute candle data shows a clear bearish trend with 4 consecutive bearish candles...', 'confidence': 0.25, 'historical_analysis': {'total_experiences': 5, 'success_rate': 0.0, 'avg_profit': 0.0, 'adjustment_applied': True}}
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - market_data 키들: ['id', 'symbol', 'timestamp', 'datetime', 'date', 'time', 'price', 'open', 'high', 'low', 'close', 'volume', 'volume_24h', 'high_24h', 'low_24h', 'percent_change_24h', 'volatility', 'rsi', 'average_sentiment', 'sentiment_score', 'social_volume', 'social_dominance', 'social_contributors', 'bullish_sentiment', 'bearish_sentiment', 'data_source', 'is_real_data', 'has_news', 'execution_timestamp', 'news_count', 'ema_7', 'ema_14', 'ema_25', 'ema_50', 'ema_99', 'ema_200', 'news', 'news_sentiment', 'post_count', 'bullish_ratio', 'bearish_ratio', 'galaxy_score', 'alt_rank', 'market_cap', 'recent_news_titles', 'top_social_posts', 'recent_candles', 'candles_count', 'data_timestamp', 'has_timeseries_data', 'short_term_change_pct', 'recent_high', 'recent_low']
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 시작
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [ETH] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - symbol: ETH
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - id: card_1
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - card_id: card_42024a55_1750951705
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - situation_type: bearish (분석용)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - action_recommendation: sell (분석용)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - importance: 5 (분석용)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - reasoning: The 5-minute candle data shows a clear bearish tre... (분석용)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] InCA 결과 분석: sell, 중요도: 5 (SELA 독립 분석)
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [ETH] SELA_IMPL - InCA BEARISH 분석 확인!
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 가격: $2434.18
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 24h 변동률: 1.059%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 거래량: 5272478.567
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 시가총액: $295.22B
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA 변동률 분석:
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 현재 가격: $2434.18
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 단기 변동률 (3분봉): -51.030%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 24시간 변동률: 1.06%
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 프롬프트 생성 완료: 5068자
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_ETH_1750951705859_7712', 'agent_type': 'sela', 'symbol': 'ETH'}
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:25 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: sela_diverse(BTC) - 큐 대기: 3.74초
2025-06-27 00:28:25 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_ETH_1750951705859_7712', 'agent_type': 'sela', 'symbol': 'ETH'}
2025-06-27 00:28:25 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_sela_diverse_BTC_1750951702125
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:25 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): Generate 5 trading strategies for BTC.

Market: BTC $107047.5 (0.05%)

IMPORTANT: Use current price ...
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:25 - models.vllm_session_manager - INFO - Created new session: 03774e02-7865-4c78-9d72-e7151bde46a1 for sela_diverse agent (symbol: BTC)
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:25 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: sela_diverse_execution_BTC → 03774e02-7865-4c78-9d72-e7151bde46a1
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: 03774e02-7865-4c78-9d72-e7151bde46a1 (sela_diverse_BTC)
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: 03774e02-7865-4c78-9d72-e7151bde46a1
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 180s, session: 03774e02-7865-4c78-9d72-e7151bde46a1)
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:25 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 ETH 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_ETH_1750951705859_7712'}
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:25 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 ETH 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_ETH_1750951705859_7712'}
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5068자
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:25 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: ETH (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:25 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-27 00:28:25 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-27 00:28:25 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 992자
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: ETH
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1561
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Symbol: ETH
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 2434.18,\n  "stop_loss": [현재가 $2434.18의 ±1-3% 범위],\n  "take_profit": [현재가 $2434.18의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: SELL** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 390
2025-06-27 00:28:25 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.51s)
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "sell",
  "direction": "short",
  "entry_price": 2434.18,
  "stop_loss": 2482.45,
  "take_profit": 2341.05,
  "reasoning": "InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 구매 영역에 진입했으며, MA...
2025-06-27 00:28:31 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 2434.18,\n  "stop_loss": 2482.45,\n  "take_profit": 2341.05,\n  "reasoning": "InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 구매 영역에 진입했으며, MACD는 상승 추세가 둔화되고 있음. 볼린저 밴드 상단에 가까워진 현재 가격은 반등 가능성이 높음. 단기적인 하락 압력이 지속될 것으로 예상됨.",\n  "confidence": 0.78,\n  "key_points": ["InCA 신호와 SELA 분석 모두 매도 추천", "RSI(14) 72.3으로 과열 구매 영역", "MACD 라인과 신호선 교차 후 하락 추세"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'ETH'}, 소요시간: 5.54초
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 2434.18,\n  "stop_loss": 2482.45,\n  "take_profit": 2341.05,\n  "reasoning": "InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 ...
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 405자
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 405자
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - JSON 추출 완료: {'type': 'sell', 'direction': 'short', 'entry_price': 2434.18, 'stop_loss': 2482.45, 'take_profit': 2341.05, 'reasoning': 'InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 구매 영역에 진입했으며, MACD는 상승 추세가 둔화되고 있음. 볼린저 밴드 상단에 가까워진 현재 가격은 반등 가능성이 높음. 단기적인 하락 압력이 지속될 것으로 예상됨.', 'confidence': 0.78, 'key_points': ['InCA 신호와 SELA 분석 모두 매도 추천', 'RSI(14) 72.3으로 과열 구매 영역', 'MACD 라인과 신호선 교차 후 하락 추세']}
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 전략 ID 생성: c8154143-3cc7-4d67-8134-f93ee16f0573
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [ETH] SELA Tree Search importance 계산 시작
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA Tree Search importance 완료: 8.86
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - UCB 점수: 2.047699230766008
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - 방문 횟수: 50
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - 평균 보상: 0.6477
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 최종 전략 유형: sell
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 신뢰도: 0.78
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 추론: InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 구매 영역에 진입했으며, MACD는 상승 추세가 둔화되고 있음. 볼린저 밴드 상단에 가까워진 ...
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 소요 시간: 5.54초
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [ETH] SELA_IMPL - 최종 결과: SELL 전략 생성됨!
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - ✅ [ETH] SELA_AGENT - SELAStrategyGenerator.generate_strategy 호출 완료
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - 반환된 결과 타입: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - 반환된 결과: {'strategy_id': 'c8154143-3cc7-4d67-8134-f93ee16f0573', 'symbol': 'ETH', 'timestamp': 1750951711, 'type': 'sell', 'entry_price': 2434.18, 'stop_loss': 2482.45, 'take_profit': 2341.05, 'reasoning': 'InCA 신호와 SELA 분석 모두 매도 신호를 보이고 있음. RSI(14)는 과도한 구매 영역에 진입했으며, MACD는 상승 추세가 둔화되고 있음. 볼린저 밴드 상단에 가까워진 현재 가격은 반등 가능성이 높음. 단기적인 하락 압력이 지속될 것으로 예상됨.', 'confidence': 0.78, 'reasoning_card_id': 'card_42024a55_1750951705', 'risk_level': 'medium', 'key_points': ['InCA 신호와 SELA 분석 모두 매도 추천', 'RSI(14) 72.3으로 과열 구매 영역', 'MACD 라인과 신호선 교차 후 하락 추세'], 'market_context': {'price': 2434.18, 'percent_change_24h': 1.059, 'timestamp': 1750951698}, 'paper_based': False, 'risk_reward': 1.9293557074787588, 'importance': 8.857149322740751}
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - super() 클래스: <class 'super'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - super() 메서드 목록: []
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - 직접 메서드 호출 시도
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 🔧 SELAAgentBase LLM 모델 타입: CachingLLMProxy (VLLMWrapper 불필요)
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - LLM 캐시 초기화 완료: G:\ai_bot_trading\trading\hybrid_architecture\..\..\data\llm_cache\llm_cache.db (TTL: 86400초, 최대 항목: 1000)
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 캐싱 LLM 프록시 초기화 완료 (캐싱 활성화)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent_base - INFO - 전략 데이터베이스 초기화 완료: medium\strategies.db
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_agent_base - INFO - SELA 에이전트 기본 클래스 초기화 완료 (위험 수준: medium)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🚀 [ETH] SELA_IMPL 전략 생성 시작 (실제 전략 생성기)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 메서드 진입 성공
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - symbol: ETH
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - market_data 타입: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning_card 타입: <class 'dict'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning_card 키들: ['id', 'title', 'analysis', 'reasoning', 'confidence', 'key_factors', 'card_id']
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - id: card_1
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - title: 패턴 분석 1
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - analysis: 현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다....
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - reasoning: 사고 카드 'Standard_CoT' 실행 결과
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - confidence: 0.6
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - key_factors: ['패턴: Standard_CoT', '액션: HOLD', '신뢰도: 0.60']
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - card_id: card_42024a55_1750951705
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL InCA 분석 결과:
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - situation_type: bearish (분석용)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - action_recommendation: sell (분석용)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - importance: 5 (분석용)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - reasoning: The 5-minute candle data shows a clear bearish tre... (분석용)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] InCA 결과 분석: sell, 중요도: 5 (SELA 독립 분석)
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - bullish 키워드 발견: False
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL 디버깅 - bearish 키워드 발견: False
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [ETH] SELA_IMPL - InCA BEARISH 분석 확인!
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 프롬프트 생성 시작
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA 전략 생성용 시장 데이터 확인:
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 가격: $2434.18
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 24h 변동률: 1.059%
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 거래량: 5272478.567
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 시가총액: $295.22B
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA 변동률 분석:
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 현재 가격: $2434.18
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 단기 변동률 (3분봉): -51.030%
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] - 24시간 변동률: 1.06%
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 프롬프트 생성 완료: 5068자
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 호출 시작
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 파라미터: {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_ETH_1750951711409_7267', 'agent_type': 'sela', 'symbol': 'ETH'}
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐⭐⭐ SELA에서 self.llm_model.generate() 호출! ⭐⭐⭐
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA LLM 모델 타입: <class 'trading.hybrid_architecture.llm_cache.CachingLLMProxy'>
2025-06-27 00:28:31 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ⭐ SELA 전달 파라미터: kwargs={'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 2048, 'session_id': 'sela_ETH_1750951711409_7267', 'agent_type': 'sela', 'symbol': 'ETH'}
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 파라미터 필터링: 원본=0, 필터링후=4
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🔧 CachingLLMProxy 최종 kwargs: ['max_tokens', 'temperature', 'top_p', 'session_id']
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯🎯🎯 CachingLLMProxy에서 self.llm_model.generate() 호출! 🎯🎯🎯
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 래핑된 LLM 모델 타입: <class 'models.vllm_client_factory.VLLMWrapper'>
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy VLLMClientEnhanced frequency_penalty 파라미터 존재: True
2025-06-27 00:28:31 - trading.hybrid_architecture.llm_cache - INFO - 🎯 CachingLLMProxy 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 ETH 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_ETH_1750951711409_7267'}
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🔧 VLLMWrapper 파라미터 필터링: 원본=4, 필터링후=4
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀🚀🚀 VLLMWrapper에서 self.client.generate() 호출! 🚀🚀🚀
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 ID: 2603539847520
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 시그니처: ('self', 'prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs', 'vllm_incompatible_params', 'internal_params', 'filtered_kwargs', 'key', 'value', 'start_time', 'prompt_id', 'lines', 'prompt_length', 'request_id', 'timestamp', 'cache_buster', 'is_sela_diverse_strategies', 'is_sela_general_strategy', 'is_sela_prompt', 'strategies_json_check', 'urgent_crypto_check', 'system_description', 'default_stops', 'thinking_mode', 'json_instruction', 'estimated_prompt_tokens', 'max_total_tokens', 'available_tokens', 'sela_patterns', 'sela_match_count', 'use_session', 'mode', 'final_agent_type', 'final_symbol', 'headers', 'all_stop_sequences', 'request_data', 'url', 'estimated_tokens', 'actual_timeout', 'response', 'result', 'generated_text', 'finish_reason', 'think_end', 'elapsed_time', 'cleaned_text', 'e', 'error_text')
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 메서드 파라미터 개수: 15
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 실제 시그니처: (prompt: str, max_tokens: int = None, temperature: float = None, top_p: float = None, top_k: int = None, repetition_penalty: float = None, frequency_penalty: float = None, presence_penalty: float = None, stop_sequences: List = None, chat_template_kwargs: Dict = None, session_id: str = None, symbol: str = None, agent_type: str = None, metadata: Dict = None, *args, **kwargs) -> Dict
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 클라이언트 파라미터 목록: ['prompt', 'max_tokens', 'temperature', 'top_p', 'top_k', 'repetition_penalty', 'frequency_penalty', 'presence_penalty', 'stop_sequences', 'chat_template_kwargs', 'session_id', 'symbol', 'agent_type', 'metadata', 'args', 'kwargs']
2025-06-27 00:28:31 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 전달 파라미터: prompt=🔥 URGENT CRYPTO ANALYSIS REQUEST 🔥
당신은 ETH 전문 암호화폐..., kwargs={'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'session_id': 'sela_ETH_1750951711409_7267'}
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 🔧 파라미터 필터링 완료: 모든 비호환 파라미터 완전 무시됨
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 원본 프롬프트 길이: 5068자
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 🔍 SELA 프롬프트 감지 체크:
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - startswith RESPOND ONLY WITH JSON: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - GENERATE + DIFFERENT TRADING STRATEGIES: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - strategies + entry_price + stop_loss: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - strategies JSON: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - NO EXPLANATIONS: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - URGENT CRYPTO REQUEST: True
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - 다양한 전략 감지: False
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - 일반 전략 감지: True
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO -   - 최종 SELA 감지 결과: True
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 캐시 우회 식별자 추가됨 (SELA 일반 전략 생성 프롬프트 감지)
2025-06-27 00:28:31 - models.vllm_prompt_processor - INFO - 프롬프트에서 심볼 추출: ETH (패턴: 분석 대상:\s*([A-Z]+))
2025-06-27 00:28:31 - models.vllm_prompt_processor - INFO - 지시사항 부분 추출 성공: 30자
2025-06-27 00:28:31 - models.vllm_prompt_processor - INFO - 데이터 부분 추출 성공: 101자
2025-06-27 00:28:31 - models.vllm_prompt_processor - INFO - 응답 형식 부분 추출 성공: 992자
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 프롬프트에서 심볼 추출됨: ETH
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Qwen3 비생각 모드 감지됨: 비생각 모드 최적화 파라미터 적용
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Qwen3 모델에 None 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 🎯 SELA 에이전트 감지됨 (매치: 4/8)
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - ✅ 최종 감지된 에이전트 타입: sela
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - ✅ 에이전트별 JSON 형식 처리 완료: sela
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 세션 우회 모드: 세션 없이 직접 요청
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - VLLM request: http://localhost:8001/v1/completions, prompt length: 1561
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Session ID: no_session
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Symbol: ETH
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Prompt preview: /no_think

중요도 분석 결과 (필수 준수)
InCA는 시장 데이터

분석 결과 (필수 준수)
InCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.
⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**



응답 형식:**
{
  "type": "[buy|sell|none]",
  "direction":...
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Request data keys: ['model', 'prompt', 'max_tokens', 'temperature', 'top_p', 'stop']
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Request data: {'model': 'Qwen/Qwen3-14B-AWQ', 'prompt': '/no_think\n\n중요도 분석 결과 (필수 준수)\nInCA는 시장 데이터\n\n분석 결과 (필수 준수)\nInCA는 시장 데이터를 분석하여 거래 시점의 중요도를 평가하는 시스템입니다.\n⚠️ **InCA 분석 결과를 반드시 우선 고려하여 전략을 생성하세요.**\n\n\n\n응답 형식:**\n{\n  "type": "[buy|sell|none]",\n  "direction": "[long|short|neutral]",\n  "entry_price": 2434.18,\n  "stop_loss": [현재가 $2434.18의 ±1-3% 범위],\n  "take_profit": [현재가 $2434.18의 ±2-5% 범위],\n  "reasoning": "[실제 시장 데이터 기반 구체적 분석]",\n  "confidence": "[0.6-0.9 범위]",\n  "key_points": ["[실제 분석 포인트 1]", "[실제 분석 포인트 2]", "[실제 분석 포인트 3]"]\n}\n\n🚨 **SELA 종합 판단 전략 (균형잡힌 접근)**:\n- InCA 추천을 **참고**하되, SELA 자체 분석을 통한 **독립적 판단** 수행\n- 시장 데이터, 기술적 지표, 패턴 분석을 종합하여 최적 전략 결정\n- InCA와 다른 판단도 가능 (단, 명확한 근거 제시 필요)\n\n🎯 **SELA 역할**:\n- **전략적 분석**: 단기/중기 시장 트렌드 분석\n- **리스크 관리**: 적절한 진입/청산 타이밍 결정\n- **종합 판단**: InCA + 시장데이터 + 기술분석 종합\n- **균형**: 보수성과 적극성의 적절한 균형 유지\n\n⚠️ **현재 InCA 신호: SELL** - 참고하되 SELA 독립 분석으로 최종 결정\n\n**📊 SELA 분석 체크리스트**:\n1. **시장 트렌드**: 단기/중기 방향성 확인\n2. **기술적 지표**: RSI, MACD, 볼린저 밴드 종합 판단\n3. **InCA 신호 검토**: 동의/반대 여부 및 근거\n4. **리스크 관리**: 손실 제한 vs 수익 기회\n5. **최종 결정**: none/buy/sell 중 최적 선택\n\n**🎯 리스크/리워드 비율 1.2 이상 유지**\n\nCRITICAL: RESPOND ONLY WITH JSON. NO OTHER TEXT.\n\nSTART WITH { AND END WITH }. NO OTHER TEXT.\n\n\nCRITICAL: YOUR RESPONSE MUST BE ONLY A VALID JSON OBJECT. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON. DO NOT USE MARKDOWN FORMATTING.\n\nEXAMPLE FORMAT (ANALYZE THE ACTUAL DATA AND CREATE YOUR OWN VALUES):\n{\n  "analysis": "your analysis here",\n  "confidence": 0.85,\n  "reasoning": "your reasoning here"\n}\n\nFINAL REMINDER: YOUR RESPONSE MUST BE ONLY THE JSON OBJECT WITH ALL REQUIRED FIELDS. NO TEXT BEFORE OR AFTER.\n', 'max_tokens': 2048, 'temperature': 0.3, 'top_p': 0.8, 'stop': []}
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - 추정 토큰 수: 390
2025-06-27 00:28:31 - models.vllm_client_enhanced - INFO - Sending request to VLLM server: http://localhost:8001/v1/completions (timeout: 600s)
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - Fast generation complete (9.66s)
2025-06-27 00:28:35 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: sela_diverse(BTC) - 처리: 9.66초, 총: 13.39초
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 다양한 전략 생성 - vLLM 큐 호출 성공 (소요 시간: 13.46초)
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 LLM 응답 전체: {'text': ' NO EXPLANATIONS. NO MARKDOWN.\n</think>\n\n{"strategies": [{"type": "buy", "entry_price": 107047.5, "stop_loss": 104906.55, "take_profit": 111329.40, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 109154.45, "take_profit": 104906.55, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "mean_reversion", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107047.5, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with low volatility and no clear directional bias", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volume"]}, {"type": "buy", "entry_price": 107047.5, "stop_loss": 105342.88, "take_profit": 112752.30, "reasoning": "Breakout above key resistance level with strong volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 108152.63, "take_profit": 105342.88, "reasoning": "Fibonacci retracement level with bearish divergence and weak support", "confidence": 0.7, "risk_level": "medium", "strategy_type": "pattern_recognition", "key_points": ["Fibonacci level", "divergence"]}]}', 'finish_reason': 'stop', 'elapsed_time': 9.656166315078735, 'fast_mode': True, 'symbol': 'BTC', 'agent_type': 'sela_diverse', 'session_id': '03774e02-7865-4c78-9d72-e7151bde46a1'}
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 응답 텍스트 길이: 1610자
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 다양한 전략 생성 응답 텍스트 전체:  NO EXPLANATIONS. NO MARKDOWN.
</think>

{"strategies": [{"type": "buy", "entry_price": 107047.5, "stop_loss": 104906.55, "take_profit": 111329.40, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 109154.45, "take_profit": 104906.55, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "mean_reversion", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107047.5, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with low volatility and no clear directional bias", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volume"]}, {"type": "buy", "entry_price": 107047.5, "stop_loss": 105342.88, "take_profit": 112752.30, "reasoning": "Breakout above key resistance level with strong volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 108152.63, "take_profit": 105342.88, "reasoning": "Fibonacci retracement level with bearish divergence and weak support", "confidence": 0.7, "risk_level": "medium", "strategy_type": "pattern_recognition", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 SELA 다양한 전략 생성에서 </think> 태그 감지됨, 제거 중...
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 SELA 다양한 전략 생성 </think> 태그 제거 완료
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔧 정리된 응답 텍스트: {"strategies": [{"type": "buy", "entry_price": 107047.5, "stop_loss": 104906.55, "take_profit": 111329.40, "reasoning": "Bullish trend continuation with RSI below 70 and strong volume confirmation", "confidence": 0.8, "risk_level": "medium", "strategy_type": "trend_following", "key_points": ["RSI divergence", "volume increase"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 109154.45, "take_profit": 104906.55, "reasoning": "Overbought conditions with RSI above 70 and bearish candlestick patterns forming", "confidence": 0.75, "risk_level": "high", "strategy_type": "mean_reversion", "key_points": ["RSI overbought", "bearish engulfing pattern"]}, {"type": "none", "entry_price": 107047.5, "stop_loss": 0, "take_profit": 0, "reasoning": "Sideways consolidation with low volatility and no clear directional bias", "confidence": 0.65, "risk_level": "low", "strategy_type": "range_trading", "key_points": ["tight range", "low volume"]}, {"type": "buy", "entry_price": 107047.5, "stop_loss": 105342.88, "take_profit": 112752.30, "reasoning": "Breakout above key resistance level with strong volume and positive momentum", "confidence": 0.85, "risk_level": "high", "strategy_type": "breakout", "key_points": ["resistance breakout", "volume surge"]}, {"type": "sell", "entry_price": 107047.5, "stop_loss": 108152.63, "take_profit": 105342.88, "reasoning": "Fibonacci retracement level with bearish divergence and weak support", "confidence": 0.7, "risk_level": "medium", "strategy_type": "pattern_recognition", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 길이: 1569자
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 첫 100자: {"strategies": [{"type": "buy", "entry_price": 107047.5, "stop_loss": 104906.55, "take_profit": 1113
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 전 텍스트 마지막 100자: "medium", "strategy_type": "pattern_recognition", "key_points": ["Fibonacci level", "divergence"]}]}
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 char 413 위치 문자: '1'
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 char 410-420 범위: '": 104906.'
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 1569자
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 파싱 성공!
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - BTC 다양한 전략 생성 완료 (소요 시간: 13.46초, 생성된 전략: 5개)
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] SELA 학습 에이전트 성공: 5개 실험적 전략 생성됨
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 실행 시작
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 1/5 분석 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107123.7, 변동률=0.081%
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:35 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 1 사고 카드 1개 생성
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 2/5 분석 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107123.7, 변동률=0.081%
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:35 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 2 사고 카드 1개 생성
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 3/5 분석 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107123.7, 변동률=0.081%
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:35 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 3 사고 카드 1개 생성
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 4/5 분석 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107123.7, 변동률=0.081%
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:35 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 4 사고 카드 1개 생성
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] HiAR 학습 에이전트 - 전략 5/5 분석 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 학습 루프 HiAR에 실행 단계 데이터 스냅샷 전달
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🔒 [BTC] 실행 단계 데이터: 가격=$107123.7, 변동률=0.081%
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 시작
2025-06-27 00:28:35 - offline_pattern_generator - INFO - 선택된 패턴: ['Simple_Analysis']
2025-06-27 00:28:35 - fast_hiar_agent - INFO - [BTC] FastHiAR 분석 완료: 0.00초
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 - 전략 5 사고 카드 1개 생성
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] HiAR 학습 에이전트 성공: 5개 사고 카드 생성됨
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 실행 시작
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 5개 전략-사고카드 쌍 준비됨
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 전략 1/5 평가 중
2025-06-27 00:28:35 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 과거 성과 데이터 50개 사용
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 0
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING - 캔들 데이터가 없어서 직접 바이낸스 API 호출 시도...
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 직접 API 호출로 캔들 데이터 5개 수집 성공!
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 캔들 패턴 분석 시작: 5개 캔들
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 캔들 패턴 분석 완료: bearish
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING - 🔍 [BTC] 전략 평가 프롬프트 캔들 데이터 상태:
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - market_data 키들: ['symbol', 'formatted_symbol', 'price', 'lastPrice', 'change_24h', 'volume_24h', 'high_24h', 'low_24h', 'timestamp', 'prices', 'volumes', 'timestamps']
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 5
2025-06-27 00:28:35 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 첫 캔들 샘플: [1750951440000, '107302.10', '107337.80', '107294.50', '107327.60', '91.532', 1750951499999, '9822788.17290', 1368, '31.809', '3413476.07500', '0']
2025-06-27 00:28:35 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_learning(BTC) - 우선순위 4, 큐 크기: 1
2025-06-27 00:28:35 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_learning(BTC) - 큐 대기: 0.00초
2025-06-27 00:28:35 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_learning_BTC_1750951715667
2025-06-27 00:28:35 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
BTC 전략 평가 요청

=== 시장 데이터 ===
현재가: $107123.7
캔들 분석: 캔들 패턴: bearish - 4연속 하락 캔들 (강한), 추세: -0.26%
거래량:...
2025-06-27 00:28:35 - models.vllm_session_manager - INFO - Created new session: 18aa3e2f-16e1-4267-82de-27514c70b30f for inca_learning agent (symbol: BTC)
2025-06-27 00:28:35 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_learning_execution_BTC → 18aa3e2f-16e1-4267-82de-27514c70b30f
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: 18aa3e2f-16e1-4267-82de-27514c70b30f (inca_learning_BTC)
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_learning 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: 18aa3e2f-16e1-4267-82de-27514c70b30f
2025-06-27 00:28:35 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: 18aa3e2f-16e1-4267-82de-27514c70b30f)
2025-06-27 00:28:36 - models.vllm_client_enhanced - INFO - VLLM response received: ['id', 'object', 'created', 'model', 'choices', 'usage']
2025-06-27 00:28:36 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 감지됨, 제거 중...
2025-06-27 00:28:36 - models.vllm_client_enhanced - INFO - 🔧 Qwen3 thinking 태그 제거 완료
2025-06-27 00:28:36 - models.vllm_client_enhanced - INFO - Text generation complete (time: 5.13s)
2025-06-27 00:28:36 - models.vllm_client_enhanced - INFO - Generated text preview: {
  "type": "sell",
  "direction": "short",
  "entry_price": 2434.18,
  "stop_loss": 2487.52,
  "take_profit": 2325.47,
  "reasoning": "InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에 진입했고 MACD가 하향 돌...
2025-06-27 00:28:36 - models.vllm_session_manager - WARNING - Attempted to update non-existent session: no_session
2025-06-27 00:28:36 - models.vllm_client_factory - INFO - 🚀 VLLMWrapper 응답 성공: <class 'dict'>
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 파라미터 호출 성공
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 타입: <class 'dict'>
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 수신: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 2434.18,\n  "stop_loss": 2487.52,\n  "take_profit": 2325.47,\n  "reasoning": "InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에 진입했고 MACD가 하향 돌파를 보이고 있다. 볼린저 밴드 상단을 돌파한 후 빠른 하락세를 보이며 단기적 상승 압력이 약화되고 있다. 이러한 기술적 지표와 InCA의 SELL 신호를 종합적으로 고려하여 단기적 매도 전략을 추천한다.",\n  "confidence": 0.78,\n  "key_points": ["RSI 과열 상태", "MACD 하향 돌파", "볼린저 밴드 상단 돌파 후 하락세"]\n}', 'finish_reason': 'stop', 'session_id': 'no_session', 'symbol': 'ETH'}, 소요시간: 5.13초
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - LLM 응답 미리보기: {'text': '{\n  "type": "sell",\n  "direction": "short",\n  "entry_price": 2434.18,\n  "stop_loss": 2487.52,\n  "take_profit": 2325.47,\n  "reasoning": "InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에...
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - JSON 추출 시작
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 입력이 이미 dict 형태임, 'text' 키에서 문자열 추출 시도
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 dict에서 텍스트 추출 성공, 길이: 406자
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 JSON 추출 시도, 텍스트 길이: 406자
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 직접 JSON 파싱 성공
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - JSON 추출 완료: {'type': 'sell', 'direction': 'short', 'entry_price': 2434.18, 'stop_loss': 2487.52, 'take_profit': 2325.47, 'reasoning': 'InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에 진입했고 MACD가 하향 돌파를 보이고 있다. 볼린저 밴드 상단을 돌파한 후 빠른 하락세를 보이며 단기적 상승 압력이 약화되고 있다. 이러한 기술적 지표와 InCA의 SELL 신호를 종합적으로 고려하여 단기적 매도 전략을 추천한다.', 'confidence': 0.78, 'key_points': ['RSI 과열 상태', 'MACD 하향 돌파', '볼린저 밴드 상단 돌파 후 하락세']}
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 전략 ID 생성: 021b1739-0cc5-4962-86af-6ebb9dc79d10
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 필수 필드 검증 시작
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🔍 [ETH] SELA_IMPL - 필수 필드 검증 완료
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🌳 [ETH] SELA Tree Search importance 계산 시작
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA Tree Search importance 완료: 8.89
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - UCB 점수: 2.0796530452599384
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - 방문 횟수: 50
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] - 평균 보상: 0.6797
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 생성 완료!
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 최종 전략 유형: sell
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 신뢰도: 0.78
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 전략 추론: InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에 진입했고 MACD가 하향 돌파를 보이고 있다. 볼린저 밴드 상단을 돌파한 후 빠른 하락세를 ...
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - 🎯 [ETH] SELA_IMPL 소요 시간: 5.13초
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_strategy_generator - INFO - ✅ [ETH] SELA_IMPL - 최종 결과: SELL 전략 생성됨!
2025-06-27 00:28:36 - trading.hybrid_architecture.agents.sela_agent - INFO - 🔍 [ETH] SELA_AGENT - 직접 호출 결과: <class 'dict'>
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 계층적 합의 시스템 비활성화됨 - SELA 결과 직접 사용
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 🎯 [ETH] SELA 직접 사용: sell (신뢰도: 0.780)
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 📊 [ETH] 합의 데이터 수집 완료 (학습용)
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - [ETH] 실제 학습 데이터 추출 완료: InCA=sell, SELA=none, 장기=neutral
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 기반 거래 결정 메서드 호출 시작
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 메서드 존재 확인: True
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 기반 의사결정 모드: 임계값 무시하고 LLM 판단 존중
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 거래 신호(sell) 감지 - 신뢰도(0.780)와 관계없이 실행
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 🤖 LLM 거래 신호(sell) 실행 - 신뢰도: 0.780
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 기반 거래 결정 완료: True
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 신호(sell)가 거래 가능한 신호이므로 강제 실행
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 실제 거래 실행 시작: sell
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - 🔧 [ETH] 실행 모드 확인: real
2025-06-27 00:28:36 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 실제 거래 모드로 실행
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [ETH] 전략에서 direction 추출: type=sell, action=unknown → direction=sell
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [디버그] execute_trade 시작: 심볼=ETH, 전략={'strategy_id': '021b1739-0cc5-4962-86af-6ebb9dc79d10', 'symbol': 'ETH', 'timestamp': 1750951716, 'type': 'sell', 'entry_price': 2434.18, 'stop_loss': 2487.52, 'take_profit': 2325.47, 'reasoning': 'InCA 신호는 SELL을 권장하며, 현재 시장 데이터 분석 결과 RSI가 과열 상태에 진입했고 MACD가 하향 돌파를 보이고 있다. 볼린저 밴드 상단을 돌파한 후 빠른 하락세를 보이며 단기적 상승 압력이 약화되고 있다. 이러한 기술적 지표와 InCA의 SELL 신호를 종합적으로 고려하여 단기적 매도 전략을 추천한다.', 'confidence': 0.78, 'reasoning_card_id': 'card_42024a55_1750951705', 'risk_level': 'medium', 'key_points': ['RSI 과열 상태', 'MACD 하향 돌파', '볼린저 밴드 상단 돌파 후 하락세'], 'market_context': {'price': 2434.18, 'percent_change_24h': 1.059, 'timestamp': 1750951698}, 'paper_based': False, 'risk_reward': 2.0380577427821476, 'importance': 8.889097764132623, 'consensus_signal': 'sell', 'consensus_confidence': 0.78, 'consensus_breakdown': {'short_term': {'action': 'sell', 'situation': 'bearish', 'importance': 0.5, 'confidence': 0.25, 'source': 'InCA', 'timeframe': '1분봉'}, 'medium_term': {'action': 'none', 'type': 'sell', 'importance': 0.5, 'confidence': 0.78, 'source': 'SELA', 'timeframe': '1시간봉'}, 'long_term': {'action': 'neutral', 'trend': 'sideways', 'trend_change_pct': 0.0, 'importance': 0.5, 'confidence': 0.3, 'source': 'LongTerm', 'timeframe': '일봉', 'note': '일봉 데이터 부족'}}}
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [디버그] 시장 데이터: {'id': 'market_ETH_1750951698', 'symbol': 'ETH', 'timestamp': 1750951698, 'datetime': '2025-06-27 00:28:18', 'date': '2025-06-27', 'time': '00:28:18', 'price': 2434.18, 'open': 0.0, 'high': 0.0, 'low': 0.0, 'close': 2432.29, 'volume': 5272478.567, 'volume_24h': 5272478.567, 'high_24h': 2520.0, 'low_24h': 2395.42, 'percent_change_24h': 1.059, 'volatility': 0.0, 'rsi': 50.0, 'average_sentiment': 0.5, 'sentiment_score': 0.5, 'social_volume': 182425844, 'social_dominance': 0.049, 'social_contributors': 1824258, 'bullish_sentiment': 0.5, 'bearish_sentiment': 0.5, 'data_source': 'binance_api', 'is_real_data': True, 'has_news': True, 'execution_timestamp': 1750951698, 'news_count': 68, 'ema_7': 2441.2642857142855, 'ema_14': 2443.8664285714285, 'ema_25': 2442.1728, 'ema_50': 2438.1868000000004, 'ema_99': 2442.4516161616157, 'ema_200': 2461.2108500000004, 'news': [{'title': 'Bitcoin Price Surges', 'content': 'Bitcoin price surges to new high.', 'sentiment': 0.8}, {'title': 'Ethereum Price Drops', 'content': 'Ethereum price drops to new low.', 'sentiment': 0.2}], 'news_sentiment': 3.079411764705882, 'post_count': 100, 'bullish_ratio': 0.0, 'bearish_ratio': 0, 'galaxy_score': 0, 'alt_rank': 0, 'market_cap': 295223110183.92, 'recent_news_titles': ['Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily', 'Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds', 'Page Not Found | 404 | Cointelegraph', 'Bit Digital Pivot to Ethereum from Bitcoin', 'Coinbase Brings Cardano and Litecoin to Ethereum DeFi With cbADA, cbLTC'], 'top_social_posts': [{'text': 'Think of Streamr like the highway for real-time data.\n$DATA is the toll\nAnd every AI bot, smart device, or dApp that needs live info will use it\n\nThis is how Web3 meets real-time intelligence\n\nWhy is no one talking about $DATA yet?\nBecause they’re sleeping on the infrastructure layer.\nBut those who understood Ethereum early know:\nInfrastructure wins long-term\n\nStreamr is a sleeping giant!\n\nStreamr isn’t hype\nIt’s hard tech\nWith real users\nReal utility\nAnd a big mission:\nTake back control of your data.\n\nFollow 👉 @streamr_tv\nExplore 👉 @streamr\nTag someone who needs to wake up on $DATA 👇', 'sentiment': 3.14, 'platform': 'tweet'}, {'text': 'June 26 Update:\n\n10 #Bitcoin ETFs\nNetFlow: +5,236 $BTC(+$561.21M)🟢\n#iShares(Blackrock) inflows 3,158 $BTC($338.43M) and currently holds 692,877 $BTC($74.26B).\n\n9 #Ethereum ETFs\nNetFlow: +13,642 $ETH(+$33.2M)🟢\n#iShares(Blackrock) inflows 22,698 $ETH($55.25M) and currently holds 1,743,756 $ETH($4.24B).\n', 'sentiment': 3, 'platform': 'tweet'}, {'text': "gUNIT, this is your intern reporting!😼\n\n@hyperunit is the decentralized asset tokenization layer for all of finance, built exclusively on Hyperliquid. I'm here to scavenge through the entire HL eco -- finding all the insights around Unit assets/Unit, arbs, listings, protocol integrations, HIP-3, and more.\n\nYou might occasionally find me dropping some info about [REDACTED]\n\nTrade BTC, ETH, SOL, and Fartcoin on Hyperliquid!", 'sentiment': 3.19, 'platform': 'tweet'}, {'text': 'Woah this is INSANE 🔥\n\nThis could be massive for crypto.\n\nBill Pulte, director at the Federal Housing Finance Agency, just told Fannie Mae & Freddie Mac to prep for accepting crypto as an asset for mortgages.\n\nIf this goes through you know what is coming next?\n\nYou could literally use your Bitcoin or ETH as collateral to buy a house.\n\nThis changes the  whole game👇\n\n• Crypto finally gets real-world utility beyond speculation\n\n• No need to sell your bags just use them to unlock value\n\nHODLing just got a whole new meaning now!', 'sentiment': 3.19, 'platform': 'tweet'}, {'text': 'Cadê a alta das altcoins? Analise bitcoin (BTC), ethereum (ETH), solana (SOL), XRP, criptomoedas', 'sentiment': 3, 'platform': 'youtube-video'}, {'text': 'Invesco and Galaxy just filed for a spot Solana ETF!\n\nFor those with a short memory, this is the ninth such filing to date.\n\nThe fund would hold SOL directly, list under “QSOL” on Cboe, and could stake assets for yield. Coinbase Custody is named as custodian.\n\nETF summer might go beyond BTC and ETH. $SOL next?', 'sentiment': 3.06, 'platform': 'tweet'}, {'text': '$NPC 📷', 'sentiment': 3.08, 'platform': 'tweet'}, {'text': 'After months of exploring new L2s and scaling solutions, I’ve finally found something that actually lives up to the hype\n\nLet me introduce you to a project that blends real utility with forward-thinking tech - @LightLinkChain \n\nA Layer 2 designed to bring Ethereum to the masses\n\nHere’s what you need to know\n\n🧵', 'sentiment': 3.15, 'platform': 'tweet'}, {'text': 'Expansive Rectal-Coin looking good here.\n\n#ERC69', 'sentiment': 3.21, 'platform': 'tweet'}, {'text': 'infinite money glitch on the levered portoflio:\n\nlong: BTC, ETH, HYPE, AAVE\nshort: all the high FDV coins with terrible tokenomics and no PMF\n\nmight make a Hyperliquid vault for @rektradio_', 'sentiment': 2.77, 'platform': 'tweet'}], 'recent_candles': [[1750950540000, '2446.84', '2447.34', '2445.19', '2446.60', '1334.681', 1750950599999, '3265151.45920', 1950, '511.382', '1250931.89831', '0'], [1750950600000, '2446.60', '2447.09', '2445.08', '2446.90', '1266.933', 1750950659999, '3099099.55696', 2119, '639.950', '1565401.45708', '0'], [1750950660000, '2446.90', '2446.90', '2442.55', '2442.56', '2117.064', 1750950719999, '5173108.64538', 2475, '547.807', '1338495.66686', '0'], [1750950720000, '2442.56', '2444.86', '2442.15', '2444.61', '1792.845', 1750950779999, '4381111.92882', 2667, '1013.599', '2476881.73608', '0'], [1750950780000, '2444.60', '2444.89', '2443.28', '2443.62', '1897.150', 1750950839999, '4636317.57536', 2347, '768.853', '1878959.50072', '0'], [1750950840000, '2443.63', '2443.77', '2438.58', '2439.27', '5013.794', 1750950899999, '12237871.03616', 4583, '741.213', '1809439.91438', '0'], [1750950900000, '2439.26', '2442.64', '2439.12', '2441.33', '6723.884', 1750950959999, '16416027.83740', 4228, '5346.378', '13052977.74727', '0'], [1750950960000, '2441.33', '2445.00', '2441.20', '2443.57', '2393.352', 1750951019999, '5847723.08209', 3269, '1207.467', '2949801.72907', '0'], [1750951020000, '2443.56', '2444.44', '2442.01', '2442.01', '1650.199', 1750951079999, '4032555.72352', 2323, '698.466', '1706874.25716', '0'], [1750951080000, '2442.00', '2445.00', '2441.77', '2445.00', '1488.410', 1750951139999, '3636572.70287', 2266, '966.354', '2361225.55971', '0'], [1750951140000, '2444.99', '2445.27', '2443.96', '2444.52', '1429.420', 1750951199999, '3494468.66867', 2120, '601.963', '1471717.92237', '0'], [1750951200000, '2444.53', '2445.72', '2442.32', '2445.71', '2351.738', 1750951259999, '5747238.69111', 2473, '760.687', '1859431.56757', '0'], [1750951260000, '2445.72', '2446.40', '2444.36', '2444.73', '1943.875', 1750951319999, '4753683.37640', 2592, '1136.287', '2778770.48817', '0'], [1750951320000, '2444.74', '2444.86', '2441.88', '2443.72', '1876.228', 1750951379999, '4583081.82387', 2739, '849.441', '2074774.66407', '0'], [1750951380000, '2443.72', '2445.00', '2443.42', '2443.42', '1987.889', 1750951439999, '4859013.54832', 2492, '1303.700', '3186557.08207', '0'], [1750951440000, '2443.43', '2445.63', '2443.43', '2445.62', '865.972', 1750951499999, '2116728.16413', 2234, '493.403', '1206021.08375', '0'], [1750951500000, '2445.62', '2446.68', '2444.62', '2444.65', '1038.915', 1750951559999, '2540731.06171', 1796, '572.280', '1399554.14240', '0'], [1750951560000, '2444.66', '2445.49', '2443.21', '2443.59', '1208.716', 1750951619999, '2954542.16125', 2243, '401.907', '982387.77681', '0'], [1750951620000, '2443.60', '2443.91', '2435.92', '2436.29', '11317.305', 1750951679999, '27603686.34739', 8678, '3652.280', '8909145.79026', '0'], [1750951680000, '2436.29', '2436.57', '2428.41', '2433.14', '28305.873', 1750951739999, '68824993.00338', 11372, '14695.429', '35722161.29738', '0']], 'candles_count': 20, 'data_timestamp': 1750951698, 'has_timeseries_data': True, 'short_term_change_pct': -0.5103000466139473, 'recent_high': 2446.68, 'recent_low': 2428.41}
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [디버그] 바이낸스 유틸리티: True
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [디버그] 고급 트레이딩 시스템: False
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 동기화 시작: 강제=False, 마지막 동기화 이후 19초 경과
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 바이낸스 API를 통해 포지션 정보 동기화 시작
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 조회 시작
2025-06-27 00:28:36 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:28:36 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951715355}
2025-06-27 00:28:36 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951715355
2025-06-27 00:28:36 - binance.binance_utils - INFO - 사용된 타임스탬프: 1750951715355
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 바이낸스 계정 정보 API 응답: 200
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 바이낸스 API에서 받은 전체 포지션 수: 523
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: SOLUSDT = 0.72 (PnL: -0.52266189)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: ETHUSDT = 0.023 (PnL: -1.26776)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: BNBUSDT = 0.09 (PnL: -0.24522314)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 활성 포지션 발견: DOGEUSDT = 504.0 (PnL: -2.2031856)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 활성 포지션 총 개수: 4
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 [SOLUSDT] 상세 정보:
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - positionAmt: 0.72 (float: 0.72)
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - entryPrice: 143.47
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - unrealizedProfit: -0.52266189
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:28:36 - binance.binance_utils - INFO - 🔍 [DOGEUSDT] 상세 정보:
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - positionAmt: 504 (float: 504.0)
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - markPrice: None
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - unrealizedProfit: -2.20318560
2025-06-27 00:28:36 - binance.binance_utils - INFO -   - percentage: None
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 응답 타입: <class 'list'>
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 전체 포지션 수: 523
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 바이낸스 API 활성 포지션 개수: 4
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 수: 1
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 수: 1
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 SOL 포지션 상세: positionAmt=0.72, entryPrice=143.47
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 DOGE 포지션 상세: positionAmt=504.0, entryPrice=0.16426
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'SOLUSDT', 'initialMargin': '102.77573810', 'maintMargin': '0.51387869', 'unrealizedProfit': '-0.52266189', 'positionInitialMargin': '102.77573810', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '143.47', 'breakEvenPrice': '143.541735', 'maxNotional': '400000000', 'positionSide': 'BOTH', 'positionAmt': '0.72', 'notional': '102.77573810', 'isolatedWallet': '0', 'updateTime': 1750949231645, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 주요 필드:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - symbol: SOLUSDT
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - positionAmt: 0.72 (타입: <class 'str'>)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.52266189
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - notional: 102.77573810
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 0.72 (타입: <class 'float'>)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [SOL] 포지션 방향 결정: positionAmt=0.72
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [SOL] 양수 포지션 → LONG 방향
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [SOL] 최종 결정된 방향: long
2025-06-27 00:28:36 - binance.binance_utils - INFO - Converting symbol SOL to Binance format: SOLUSDT
2025-06-27 00:28:36 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=SOLUSDT (원본 심볼: SOL)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 티커 데이터 조회 성공: SOLUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:36 - binance.binance_utils - INFO - 변화율(%): -0.764
2025-06-27 00:28:36 - binance.binance_utils - INFO - 거래량: 18890990.00
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [SOL] 🔧 강제 PnL 계산: -0.474% (진입: 143.47, 현재: 142.79, 방향: long)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [SOL] 바이낸스 포지션 상세:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 143.47
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.52266189
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.47%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [SOL] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [SOL] 기존 포지션 ID: sync_SOL_1750951663, 동기화 포지션 ID: sync_SOL_1750951716
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [ETH] 포지션 방향 결정: positionAmt=0.023
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [ETH] 양수 포지션 → LONG 방향
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [ETH] 최종 결정된 방향: long
2025-06-27 00:28:36 - binance.binance_utils - INFO - Converting symbol ETH to Binance format: ETHUSDT
2025-06-27 00:28:36 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=ETHUSDT (원본 심볼: ETH)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 티커 데이터 조회 성공: ETHUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:36 - binance.binance_utils - INFO - 변화율(%): 0.998
2025-06-27 00:28:36 - binance.binance_utils - INFO - 거래량: 5297419.604
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [ETH] 🔧 강제 PnL 계산: -2.148% (진입: 2486.13, 현재: 2432.73, 방향: long)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [ETH] 바이낸스 포지션 상세:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 2486.13
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -1.26776
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -2.15%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [ETH] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [ETH] 기존 포지션 ID: sync_ETH_1750928149, 동기화 포지션 ID: sync_ETH_1750951716
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [BNB] 포지션 방향 결정: positionAmt=0.09
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [BNB] 양수 포지션 → LONG 방향
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [BNB] 최종 결정된 방향: long
2025-06-27 00:28:36 - binance.binance_utils - INFO - Converting symbol BNB to Binance format: BNBUSDT
2025-06-27 00:28:36 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=BNBUSDT (원본 심볼: BNB)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 티커 데이터 조회 성공: BNBUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:36 - binance.binance_utils - INFO - 변화율(%): 0.118
2025-06-27 00:28:36 - binance.binance_utils - INFO - 거래량: 296940.22
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [BNB] 🔧 강제 PnL 계산: -0.391% (진입: 646.61, 현재: 644.08, 방향: long)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [BNB] 바이낸스 포지션 상세:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 646.61
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -0.24522314
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -0.39%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [BNB] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [BNB] 기존 포지션 ID: sync_BNB_1750951663, 동기화 포지션 ID: sync_BNB_1750951716
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 전체 원본 데이터:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   전체 응답: {'symbol': 'DOGEUSDT', 'initialMargin': '80.58385440', 'maintMargin': '0.52379505', 'unrealizedProfit': '-2.20318560', 'positionInitialMargin': '80.58385440', 'openOrderInitialMargin': '0', 'leverage': '1', 'isolated': False, 'entryPrice': '0.16426', 'breakEvenPrice': '0.16434213', 'maxNotional': '200000000', 'positionSide': 'BOTH', 'positionAmt': '504', 'notional': '80.58385440', 'isolatedWallet': '0', 'updateTime': 1750927964135, 'bidNotional': '0', 'askNotional': '0'}
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 주요 필드:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - symbol: DOGEUSDT
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - positionAmt: 504 (타입: <class 'str'>)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -2.20318560
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - notional: 80.58385440
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - positionSide: BOTH
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 계산된 position_amt: 504.0 (타입: <class 'float'>)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 포지션 방향 결정: positionAmt=504.0
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 양수 포지션 → LONG 방향
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔧 [DOGE] 최종 결정된 방향: long
2025-06-27 00:28:36 - binance.binance_utils - INFO - Converting symbol DOGE to Binance format: DOGEUSDT
2025-06-27 00:28:36 - binance.binance_utils - INFO - 퓨처스 티커 요청: https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT (원본 심볼: DOGE)
2025-06-27 00:28:36 - binance.binance_utils - INFO - 티커 데이터 조회 성공: DOGEUSDT, 필드: ['symbol', 'priceChange', 'priceChangePercent', 'weightedAvgPrice', 'lastPrice', 'lastQty', 'openPrice', 'highPrice', 'lowPrice', 'volume', 'quoteVolume', 'openTime', 'closeTime', 'firstId', 'lastId', 'count']
2025-06-27 00:28:36 - binance.binance_utils - INFO - 변화율(%): -2.015
2025-06-27 00:28:36 - binance.binance_utils - INFO - 거래량: 5100588106
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [DOGE] 🔧 강제 PnL 계산: -2.606% (진입: 0.16426, 현재: 0.15998, 방향: long)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - [DOGE] 바이낸스 포지션 상세:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - entryPrice: 0.16426
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - markPrice: 0.0
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - unrealizedProfit: -2.2031856
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - percentage: N/A
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO -   - 최종 PnL%: -2.61%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: long
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔒 [DOGE] 기존 포지션 ID: sync_DOGE_1750927996, 동기화 포지션 ID: sync_DOGE_1750951716
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 시작: 현재 4개 포지션, 거래소에서 가져온 4개 포지션
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - exchange_positions 상세:
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 입력 - 포지션[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 현재 내부 포지션 키: ['SOL_long', 'ETH_long', 'BNB_long', 'DOGE_long']
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 거래소 포지션 키: ['SOL_long', 'ETH_long', 'BNB_long', 'DOGE_long']
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 바이낸스 API 포지션과 내부 포지션 스마트 매핑
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 업데이트: SOL long, PnL: -0.47%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 업데이트: ETH long, PnL: -2.15%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 업데이트: BNB long, PnL: -0.39%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 업데이트: DOGE long, PnL: -2.61%
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 스마트 포지션 동기화 완료: 최종 4개 포지션
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions 배열 크기: 4
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[0]: SOL (positionAmt=0, size=0.72, quantity=0.72)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[1]: ETH (positionAmt=0, size=0.023, quantity=0.023)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[2]: BNB (positionAmt=0, size=0.09, quantity=0.09)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 동기화 결과 - exchange_positions[3]: DOGE (positionAmt=0, size=504.0, quantity=504.0)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 수: 4
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 1: SOL (크기: 0.72)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 2: ETH (크기: 0.023)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 3: BNB (크기: 0.09)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 바이낸스 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 수: 4
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 1: SOL (크기: 0.72)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 2: ETH (크기: 0.023)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 3: BNB (크기: 0.09)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 🔍 검증 단계 - 내부 포지션 4: DOGE (크기: 504.0)
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 바이낸스 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - 내부 활성 포지션: ['SOL(0.72)', 'ETH(0.023)', 'BNB(0.09)', 'DOGE(504.0)']
2025-06-27 00:28:36 - simulator.trading.portfolio - INFO - ✅ 포지션 동기화 상태 양호: 불일치 없음
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 🔥 포지션 동기화 완료: 4개의 활성 포지션 (내부: 4개)
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 🔥 동기화 변경사항: 4개
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 모든 포지션 정보 동기화 완료 (요청 심볼: ETH)
2025-06-27 00:28:37 - binance.binance_utils - INFO - 서버 시간 조회 URL: https://fapi.binance.com/fapi/v1/time
2025-06-27 00:28:37 - binance.binance_utils - INFO - 서버 시간 응답: {'serverTime': 1750951715813}
2025-06-27 00:28:37 - binance.binance_utils - INFO - 바이낸스 서버 시간 파싱 성공: 1750951715813
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - [디버그] 현재 가격: 2434.18
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - [디버그] type 필드에서 direction 추출: sell
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - [디버그] 방향 정규화: sell → short
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 🔄 [ETH] 반대 포지션 감지: 기존=long, 새=short
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - ⏳ [ETH] 사이클 진행 중 - 포지션 변화를 사이클 마지막까지 대기
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 🛡️ [ETH] 포지션 변화 요청 저장 비활성화됨: long → short
2025-06-27 00:28:37 - simulator.trading.portfolio - INFO - 🛡️ [ETH] 반대 신호가 아닌 경우 포지션 유지
2025-06-27 00:28:37 - trading.hybrid_architecture.hybrid_controller - INFO - ETH 실제 거래 실행 성공: unknown
2025-06-27 00:28:37 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 기반 유효한 전략으로 판단: sell (SELA: sell, 신뢰도: 0.780)
2025-06-27 00:28:37 - trading.hybrid_architecture.data_store - INFO - ETH 실행 결과 저장 완료: exec_e5e1bafc_1750951717
2025-06-27 00:28:37 - trading.hybrid_architecture.data_logger - INFO - JSON 로그 파일 작성 완료: G:\ai_bot_trading\data\ETH_exec_exec_e5e1bafc_1750951717.json
2025-06-27 00:28:37 - trading.hybrid_architecture.data_logger - INFO - ETH 실행 결과 로깅 완료: exec_e5e1bafc_1750951717
2025-06-27 00:28:37 - trading.hybrid_architecture.hybrid_controller - INFO - ETH SELA 기반 전략 생성 완료: 021b1739-0cc5-4962-86af-6ebb9dc79d10, sell @ $2434.18 [SELA: sell, 신뢰도: 0.780, 실행: True]
2025-06-27 00:28:37 - hybrid_simulator - INFO - ⏳ ETH 처리 완료 대기 시작 (최대 300초)
2025-06-27 00:28:37 - hybrid_simulator - INFO - ⏳ ETH 대기 중... 큐: 0, 처리중: 1
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - Fast generation complete (5.66s)
2025-06-27 00:28:41 - models.vllm_request_queue - INFO - vLLM 요청 처리 완료: inca_learning(BTC) - 처리: 5.66초, 총: 5.66초
2025-06-27 00:28:41 - trading.hybrid_architecture.utils.json_extractor - INFO - </think> 태그 발견, 제거 중...
2025-06-27 00:28:41 - trading.hybrid_architecture.utils.json_extractor - INFO - 태그 제거 완료. 원본 길이: 569, 정리 후 길이: 530
2025-06-27 00:28:41 - trading.hybrid_architecture.utils.json_extractor - INFO - 정리 후 텍스트 미리보기: {
  "overall_score": 0.75,
  "logic_score": 0.8,
  "market_alignment_score": 0.7,
  "risk_reward_score": 0.8,
  "historical_performance_score": 0.7,
  "virtual_trade_pnl": 0.19,
  "virtual_trade_succe...
2025-06-27 00:28:41 - trading.hybrid_architecture.utils.json_extractor - INFO - JSON 추출 성공: {'overall_score': 0.75, 'logic_score': 0.8, 'market_alignment_score': 0.7, 'risk_reward_score': 0.8, 'historical_performance_score': 0.7, 'virtual_trade_pnl': 0.19, 'virtual_trade_success': True, 'recommendation': 'consider', 'reasoning': '가상거래 시뮬레이션은 성공했으며 예상 PnL이 0.19%로 전략이 실행 가능함을 시사합니다. 그러나 과거 성과가 0/50로 매우 부진한 것으로 보여 신중한 검토가 필요합니다. 추세는 약간 하락 중이지만, 전략의 진입 조건이 불명확하여 추가 분석이 필요합니다.', 'strengths': ['가상거래 성공', '목표 수익률 달성 가능성'], 'weaknesses': ['진입 조건 미정의', '과거 성과 매우 부진'], 'expected_success_rate': 0.65}
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - INFO - BTC 전략 평가 완료 (학습 루프)
2025-06-27 00:28:41 - trading.hybrid_architecture.hybrid_controller - INFO - ✅ [BTC] InCA 학습 에이전트 - 전략 1 평가 완료
2025-06-27 00:28:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 전략 2/5 평가 중
2025-06-27 00:28:41 - trading.hybrid_architecture.hybrid_controller - INFO - 🎓 [BTC] InCA 학습 에이전트 - 과거 성과 데이터 50개 사용
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 0
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING - 캔들 데이터가 없어서 직접 바이낸스 API 호출 시도...
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 직접 API 호출로 캔들 데이터 5개 수집 성공!
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - INFO - 🔍 캔들 패턴 분석 시작: 5개 캔들
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - INFO - ✅ 캔들 패턴 분석 완료: bearish
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING - 🔍 [BTC] 전략 평가 프롬프트 캔들 데이터 상태:
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - market_data 키들: ['symbol', 'formatted_symbol', 'price', 'lastPrice', 'change_24h', 'volume_24h', 'high_24h', 'low_24h', 'timestamp', 'prices', 'volumes', 'timestamps']
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - recent_candles 개수: 0
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - candles 개수: 0
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 최종 candles 개수: 5
2025-06-27 00:28:41 - trading.hybrid_architecture.agents.inca_agent - WARNING -   - 첫 캔들 샘플: [1750951440000, '107302.10', '107337.80', '107294.50', '107327.60', '91.532', 1750951499999, '9822788.17290', 1368, '31.809', '3413476.07500', '0']
2025-06-27 00:28:41 - models.vllm_request_queue - INFO - vLLM 요청 추가: inca_learning(BTC) - 우선순위 4, 큐 크기: 1
2025-06-27 00:28:41 - models.vllm_request_queue - INFO - vLLM 요청 처리 시작: inca_learning(BTC) - 큐 대기: 0.00초
2025-06-27 00:28:41 - models.vllm_request_queue - INFO - 🔍 프롬프트 식별자: PROMPT_ID_inca_learning_BTC_1750951721449
2025-06-27 00:28:41 - models.vllm_request_queue - INFO - 🔍 프롬프트 미리보기 (처음 100자): 
BTC 전략 평가 요청

=== 시장 데이터 ===
현재가: $107123.7
캔들 분석: 캔들 패턴: bearish - 4연속 하락 캔들 (강한), 추세: -0.27%
거래량:...
2025-06-27 00:28:41 - models.vllm_session_manager - INFO - inca_learning_execution_BTC 세션 만료 (나이: 5.8초, 유휴: 0.0초)
2025-06-27 00:28:41 - models.vllm_session_manager - INFO - Closed session: 18aa3e2f-16e1-4267-82de-27514c70b30f
2025-06-27 00:28:41 - models.vllm_session_manager - INFO - Created new session: dcaf69dc-62cf-4a14-97cf-487f3d38e34c for inca_learning agent (symbol: BTC)
2025-06-27 00:28:41 - models.vllm_session_manager - INFO - 새 심볼별 세션 생성: inca_learning_execution_BTC → dcaf69dc-62cf-4a14-97cf-487f3d38e34c
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - generate_fast 세션 사용: dcaf69dc-62cf-4a14-97cf-487f3d38e34c (inca_learning_BTC)
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 /no_think 태그 추가됨
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - generate_fast: Qwen3 모델에 inca_learning 전용 JSON 응답 형식 강제 지시 추가됨
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - generate_fast 세션 헤더 사용: dcaf69dc-62cf-4a14-97cf-487f3d38e34c
2025-06-27 00:28:41 - models.vllm_client_enhanced - INFO - Fast request to VLLM (timeout: 600s, session: dcaf69dc-62cf-4a14-97cf-487f3d38e34c)
