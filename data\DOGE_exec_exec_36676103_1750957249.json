{"execution_id": "exec_36676103_1750957249", "symbol": "DOGE", "timestamp": 1750957249, "datetime": "2025-06-27 02:00:49", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "a9fdc869-bdab-47d7-a524-00e30482eb98", "timestamp": 1750957249, "market_data": {"id": "market_DOGE_1750957228", "symbol": "DOGE", "timestamp": 1750957228, "datetime": "2025-06-27 02:00:28", "date": "2025-06-27", "time": "02:00:28", "price": 0.15947, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15947, "volume": 5110342343.0, "volume_24h": 5110342343.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -2.554, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8461861, "social_dominance": 0.43, "social_contributors": 84618, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750957228, "news_count": 48, "ema_7": 0.15948142857142855, "ema_14": 0.15962785714285715, "ema_25": 0.1596608, "ema_50": 0.15981559999999997, "ema_99": 0.1598662626262627, "ema_200": 0.16158660000000002, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0454166666666667, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23900766316.83, "recent_news_titles": ["Musk Confidant A<PERSON><PERSON> Leaves Tesla in Latest High-Level Exit", "Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing"], "top_social_posts": [{"text": "Probably 98% of GOP voters still don't understand: the GOP hates <PERSON> and won't back him in Congress.\n\nThat's why they won't pass his legislative agenda or enact the DOGE cuts or impeach Dem judges who block his orders.\n \nThe GOP is just waiting him out for the next four years.", "sentiment": 2.75, "platform": "tweet"}, {"text": "The wife of <PERSON><PERSON><PERSON> says, \"We will not stop until my husband is free.\"\n\n", "sentiment": 3, "platform": "tweet"}, {"text": "Democrats are the biggest hypocrites... its unreal.\n\n", "sentiment": 2.67, "platform": "tweet"}, {"text": "Balance the budget. \n\nIt shouldn’t be up for discussion. \n\nIf we take in 4.3 TRILLION in tax revenue then we can only spend 4.3 TRILLION. \n\nIf us regular people did this, we would be in jail for printing money, but Congress thinks that they have a magic money tree in their backyard. \n\nRepublicans have pushed back against every spending cut starting from DOGE. \n\nAlmost feels like they want to lose the midterms.", "sentiment": 3.05, "platform": "tweet"}, {"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "American Airlines flight turns back to Las Vegas after engine catches fire midair.\n\nThat's scary.\n\n", "sentiment": 2.7, "platform": "tweet"}, {"text": "<PERSON> NOW REMOVED From Congress After THREATENING First Lady <PERSON> At DOGE Hearing", "sentiment": 3.36, "platform": "youtube-video"}, {"text": "Nice smack down!\n\n", "sentiment": 3.75, "platform": "tweet"}, {"text": "President <PERSON>: \"Look at Paris. Look at London. They’re no longer recognizable. We can’t let that happen to our country.\"\n\n", "sentiment": 3, "platform": "tweet"}, {"text": "“Y’all Are in a Cult” — <PERSON><PERSON>ett Destroys the DOGE Committee’s Spin", "sentiment": 2.9, "platform": "youtube-video"}], "recent_candles": [[1750956060000, "0.159610", "0.159780", "0.159610", "0.159770", "361969", 1750956119999, "57810.621360", 329, "294985", "47114.834570", "0"], [1750956120000, "0.159770", "0.159910", "0.159690", "0.159910", "1310782", 1750956179999, "209400.408490", 727, "929750", "148529.483820", "0"], [1750956180000, "0.159920", "0.159990", "0.159800", "0.159800", "1984011", 1750956239999, "317290.703320", 682, "492894", "78835.153320", "0"], [1750956240000, "0.159790", "0.159860", "0.159690", "0.159690", "853646", 1750956299999, "136419.781810", 509, "254892", "40738.504380", "0"], [1750956300000, "0.159690", "0.159700", "0.159450", "0.159600", "5924373", 1750956359999, "945011.132830", 1572, "1690919", "269799.971050", "0"], [1750956360000, "0.159600", "0.159640", "0.159360", "0.159360", "2114405", 1750956419999, "337137.664870", 810, "323483", "51591.935400", "0"], [1750956420000, "0.159370", "0.159440", "0.159140", "0.159320", "4792598", 1750956479999, "763156.789150", 1408, "1418210", "225897.157610", "0"], [1750956480000, "0.159330", "0.159570", "0.159310", "0.159520", "2279746", 1750956539999, "363461.380120", 976, "1772588", "282590.396370", "0"], [1750956540000, "0.159520", "0.159560", "0.159440", "0.159560", "420386", 1750956599999, "67054.041410", 400, "239428", "38191.707290", "0"], [1750956600000, "0.159550", "0.159620", "0.159510", "0.159540", "795603", 1750956659999, "126950.972810", 516, "270529", "43165.209100", "0"], [1750956660000, "0.159540", "0.159590", "0.159430", "0.159590", "2541486", 1750956719999, "405423.322900", 816, "542276", "86499.978440", "0"], [1750956720000, "0.159590", "0.159640", "0.159340", "0.159340", "1781855", 1750956779999, "284241.528200", 832, "638742", "101889.932520", "0"], [1750956780000, "0.159330", "0.159580", "0.159290", "0.159550", "2510416", 1750956839999, "400238.033890", 794, "2154887", "343548.864070", "0"], [1750956840000, "0.159550", "0.159550", "0.159380", "0.159390", "1685598", 1750956899999, "268860.067760", 596, "119500", "19053.882550", "0"], [1750956900000, "0.159390", "0.159490", "0.159320", "0.159450", "757198", 1750956959999, "120706.716380", 555, "588796", "93860.815560", "0"], [1750956960000, "0.159440", "0.159450", "0.159360", "0.159370", "1331730", 1750957019999, "212310.692070", 483, "1086055", "173145.824960", "0"], [1750957020000, "0.159380", "0.159380", "0.159220", "0.159240", "1564275", 1750957079999, "249204.776830", 677, "725379", "115568.444750", "0"], [1750957080000, "0.159240", "0.159340", "0.159240", "0.159250", "1532305", 1750957139999, "244082.382310", 548, "1276179", "203282.716390", "0"], [1750957140000, "0.159260", "0.159430", "0.159250", "0.159430", "2672395", 1750957199999, "425713.935270", 714, "1996185", "318007.457030", "0"], [1750957200000, "0.159430", "0.159500", "0.159410", "0.159470", "2584257", 1750957259999, "412074.748080", 691, "1037236", "165390.234880", "0"]], "candles_count": 20, "data_timestamp": 1750957228, "has_timeseries_data": true, "short_term_change_pct": 0.06274706657463072, "recent_high": 0.1595, "recent_low": 0.15922}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bullish", "reasoning": "Short-term bullish trend confirmed by 5 consecutive bullish candles with increasing volume and a strong breakout above resistance levels. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "buy", "trading_direction": "long", "signal_direction": "bullish", "keywords": ["bullish", "volume", "breakout"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"Short-term bullish trend confirmed by 5 consecutive bullish candles with increasing vol...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_228c317f_1750957235"}, "strategy": {"strategy_id": "a9fdc869-bdab-47d7-a524-00e30482eb98", "symbol": "DOGE", "timestamp": 1750957247, "type": "buy", "entry_price": 0.15947, "stop_loss": 0.15628, "take_profit": 0.16772, "reasoning": "InCA 시스템은 현재 시장 데이터를 기반으로 강한 구매 신호를 제공하고 있습니다. 가격은 볼린저 밴드 하단에서 상승 추세를 보이며 RSI(14)는 50 이상으로 상승장 진입을 시사합니다. MACD는 긍정적인 교차를 형성했으며, 200일 이동 평균선 위에서 안정적인 상승세를 유지하고 있습니다. 이러한 기술적 지표와 InCA의 분석 결과를 종합적으로 고려할 때, 현재 시점에서 장기적인 상승 전략이 적절합니다.", "confidence": 0.82, "reasoning_card_id": "card_228c317f_1750957235", "risk_level": "medium", "key_points": ["InCA의 구매 신호", "볼린저 밴드 하단 상승", "RSI 상승장 진입", "MACD 긍정적 교차", "200일 이동 평균선 위 유지"], "market_context": {"price": 0.15947, "percent_change_24h": -2.554, "timestamp": 1750957228}, "paper_based": false, "risk_reward": 2.5862068965517278, "importance": 9.087161408665146, "consensus_signal": "buy", "consensus_confidence": 0.82, "consensus_breakdown": {"short_term": {"action": "buy", "situation": "bullish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "buy", "importance": 0.5, "confidence": 0.82, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "buy", "consensus_confidence": 0.82, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_36676103_1750957249"}}