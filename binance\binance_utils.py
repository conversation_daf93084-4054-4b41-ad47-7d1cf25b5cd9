import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
import os
import hmac
import hashlib
import time
import requests  # requests 모듈 추가
from urllib.parse import urlencode, quote
import math

# 바이낸스 퓨처스 커넥터 오류 핸들링을 위한 플래그
HAS_FUTURES_CONNECTOR = True

logger = logging.getLogger(__name__)

class BinanceUtils:
    # 퓨처스 URL (프로덕션 환경)
    PROD_FUTURES_URL = 'https://fapi.binance.com'

    # 테스트넷 퓨처스 URL
    TESTNET_FUTURES_URL = 'https://testnet.binancefuture.com'

    # 기본값으로 프로덕션 환경 사용
    FUTURES_URL = PROD_FUTURES_URL

    def __init__(self, api_key=None, api_secret=None, testnet=False):
        """바이낸스 퓨처스 API 클라이언트 초기화

        Args:
            api_key (str, optional): API 키. None인 경우 환경 변수에서 로드
            api_secret (str, optional): API 시크릿. None인 경우 환경 변수에서 로드
            testnet (bool): 테스트넷 사용 여부
        """
        # 테스트넷 설정
        self.testnet = testnet

        # 심볼 정보 캐시 초기화
        self._symbol_info_cache = {}

        # 포지션 모드 캐시 초기화 (기본값: 단일 모드)
        self._position_mode_cache = {'dualSidePosition': False}

        # 환경 변수에서 적절한 API 키 가져오기
        if testnet:
            self.api_key = api_key or os.getenv('BINANCE_TESTNET_API_KEY', '')
            self.api_secret = api_secret or os.getenv('BINANCE_TESTNET_API_SECRET', '')
            self.FUTURES_URL = self.TESTNET_FUTURES_URL
            logger.info(f"테스트넷 모드 사용 중: {self.FUTURES_URL}")
        else:
            self.api_key = api_key or os.getenv('BINANCE_API_KEY', '')
            self.api_secret = api_secret or os.getenv('BINANCE_API_SECRET', '')
            self.FUTURES_URL = self.PROD_FUTURES_URL
            logger.info(f"프로덕션 모드 사용 중: {self.FUTURES_URL}")

        # API 키 설정 상태 로깅 (첫 5자와 마지막 5자만 표시)
        if self.api_key:
            masked_key = f"{self.api_key[:5]}...{self.api_key[-5:]}" if len(self.api_key) > 10 else "설정됨"
            logger.info(f"API 키: {masked_key}")
        else:
            logger.warning("API 키가 설정되지 않았습니다.")

        # 서버 시간 확인 및 로깅
        try:
            server_time = self.get_server_time()
            local_time = int(time.time() * 1000)

            # 서버 시간 검증 및 처리
            if isinstance(server_time, int) and server_time > 0:
                time_diff = local_time - server_time
                logger.info(f"✅ 바이낸스 서버 시간 동기화 성공: {server_time}, 로컬 시간: {local_time}, 차이: {time_diff}ms")

                if abs(time_diff) > 1000:
                    logger.warning(f"⚠️ 로컬 시간과 서버 시간의 차이가 큽니다: {time_diff}ms")
            else:
                logger.error(f"❌ 바이낸스 서버 시간 파싱 실패: {server_time} (타입: {type(server_time)})")
                logger.info("🔄 로컬 시간으로 폴백하여 계속 진행")
        except Exception as e:
            logger.error(f"시간 동기화 확인 중 오류 발생: {e}")

        # 연결 테스트
        self.test_connectivity()

        if self.api_key:
            logger.info("API 키: 설정됨")
        else:
            logger.warning("API 키가 설정되지 않았습니다.")

    def get_server_time(self):
        """
        바이낸스 서버 시간 조회 (개선된 버전)

        Returns:
            int: 서버 시간 (밀리초)
        """
        try:
            # 직접 API 호출 (requests 사용)
            endpoint = "/fapi/v1/time"
            url = f"{self.FUTURES_URL}{endpoint}"

            logger.info(f"서버 시간 조회 URL: {url}")

            response = requests.get(url, timeout=5)  # 타임아웃 5초 설정

            # 응답 상태 코드 확인
            if response.status_code != 200:
                logger.warning(f"서버 시간 조회 실패: 상태 코드 {response.status_code}, 응답: {response.text}")
                return int(time.time() * 1000) - 1000

            # 응답 내용 확인
            try:
                response_data = response.json()
                logger.info(f"서버 시간 응답: {response_data}")

                # 'serverTime' 키가 있는지 확인
                if 'serverTime' in response_data:
                    try:
                        server_time_raw = response_data['serverTime']

                        # 다양한 타입 처리
                        if isinstance(server_time_raw, (int, float)):
                            server_time = int(server_time_raw)
                        elif isinstance(server_time_raw, str) and server_time_raw.isdigit():
                            server_time = int(server_time_raw)
                        else:
                            raise ValueError(f"지원되지 않는 서버 시간 형식: {type(server_time_raw)} - {server_time_raw}")

                        logger.info(f"바이낸스 서버 시간 파싱 성공: {server_time}")
                        return server_time
                    except (ValueError, TypeError) as e:
                        logger.warning(f"서버 시간 파싱 실패: {response_data['serverTime']} (타입: {type(response_data['serverTime'])}), 오류: {e}")
                        return int(time.time() * 1000) - 1000
                else:
                    logger.warning(f"서버 시간 응답에 'serverTime' 키가 없습니다: {response_data}")
                    return int(time.time() * 1000) - 1000
            except ValueError as e:
                logger.warning(f"서버 시간 응답을 JSON으로 파싱할 수 없습니다: {response.text}, 오류: {e}")
                return int(time.time() * 1000) - 1000
        except Exception as e:
            logger.error(f"서버 시간 조회 실패: {e}")
            # 실패 시 로컬 시간 반환 (오프셋 적용)
            return int(time.time() * 1000) - 1000

    def _get_signed_params(self, params=None):
        """
        타임스탬프와 서명이 포함된 파라미터 생성

        서버 시간과의 동기화 문제를 해결하기 위해 서버 시간 사용
        """
        if params is None:
            params = {}

        # 서버 시간 조회
        server_time = self.get_server_time()

        # 서버 시간 검증 및 사용
        if isinstance(server_time, int) and server_time > 0:
            params['timestamp'] = server_time
            logger.debug(f"바이낸스 서버 시간 사용: {server_time}")
        else:
            # 서버 시간이 유효하지 않으면 로컬 시간 사용
            params['timestamp'] = int(time.time() * 1000) - 1000
            logger.debug(f"로컬 시간 사용: {params['timestamp']}")

        # 타임스탬프가 문자열이 아닌 정수인지 확인
        if not isinstance(params['timestamp'], int):
            logger.warning(f"타임스탬프가 정수가 아닙니다: {params['timestamp']}. 로컬 시간 사용")
            params['timestamp'] = int(time.time() * 1000) - 1000

        logger.info(f"사용된 타임스탬프: {params['timestamp']}")

        query_string = urlencode(params)

        # HMAC SHA256 서명 생성
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        params['signature'] = signature
        return params

    def _get_headers(self) -> Dict[str, str]:
        """API 요청 헤더 생성"""
        return {
            'X-MBX-APIKEY': self.api_key
        }

    def _request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False, headers: Dict = None) -> Any:
        """REST API 요청 처리

        Args:
            method: HTTP 메서드 (GET, POST 등)
            endpoint: API 엔드포인트 경로
            params: 요청 파라미터
            signed: 서명이 필요한지 여부
            headers: 추가 헤더

        Returns:
            JSON 응답 데이터
        """
        if params is None:
            params = {}

        if headers is None:
            headers = {}

        # API 키 헤더 추가
        if self.api_key:
            headers.update({'X-MBX-APIKEY': self.api_key})

        # 서명이 필요한 경우 파라미터에 서명 추가
        if signed and self.api_secret:
            params = self._get_signed_params(params)

        # 기본 URL 설정
        url = f"{self.FUTURES_URL}{endpoint}"

        try:
            # HTTP 요청 수행
            if method.upper() == 'GET':
                response = requests.get(url, params=params, headers=headers)
            elif method.upper() == 'POST':
                response = requests.post(url, data=params, headers=headers)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, params=params, headers=headers)
            elif method.upper() == 'PUT':
                response = requests.put(url, data=params, headers=headers)
            else:
                raise ValueError(f"지원되지 않는 HTTP 메서드: {method}")

            # 응답 상태 코드 확인
            response.raise_for_status()

            # JSON 응답 반환
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API 요청 오류 ({url}): {e}")
            raise

    def get_account(self) -> Dict:
        """계정 정보 조회 (인증 필요)

        Returns:
            Dict: 계정 정보 또는 에러 정보
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 퓨처스 API 엔드포인트 수정 - v2 사용
            endpoint = f"/fapi/v2/account"

            params = self._get_signed_params()
            headers = self._get_headers()
            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)

            # 🔥 핵심 정보만 로깅
            logger.info(f"🔍 바이낸스 계정 정보 API 응답: {response.status_code}")
            if response.status_code != 200:
                logger.error(f"🔴 API 오류 응답: {response.text[:500]}")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"get_account : 계정 정보 조회 실패: {e}")
            return {'error': str(e)}

    def get_futures_account_info(self) -> Dict:
        """선물 계정 정보 조회 (API 키 필요)

        Returns:
            Dict: 선물 계정 정보 (잔액, 포지션 등)
        """
        return self.get_account()

    def get_futures_balance(self) -> Dict[str, Dict[str, float]]:
        """선물 계정 자산 잔액 조회

        Returns:
            Dict: 자산별 잔액 정보
        """
        try:
            account_info = self.get_account()

            if 'error' in account_info:
                return {'error': account_info['error']}

            balances = {}
            for asset in account_info.get('assets', []):
                symbol = asset['asset']
                wallet_balance = float(asset['walletBalance'])
                unrealized_profit = float(asset['unrealizedProfit'])
                margin_balance = float(asset['marginBalance'])

                if wallet_balance > 0:
                    balances[symbol] = {
                        'wallet_balance': wallet_balance,
                        'unrealized_profit': unrealized_profit,
                        'margin_balance': margin_balance,
                        'available_balance': float(asset.get('availableBalance', margin_balance))
                    }

            return balances
        except Exception as e:
            logger.error(f"선물 잔액 조회 실패: {e}")
            return {'error': str(e)}

    def get_futures_account_balance(self) -> List[Dict[str, Any]]:
        """선물 계정 잔액 조회 (availableBalance 포함)

        Returns:
            List[Dict]: 자산별 잔액 정보 목록
        """
        try:
            endpoint = "/fapi/v2/balance"
            params = self._get_signed_params()
            headers = {'X-MBX-APIKEY': self.api_key}

            logger.info(f"선물 계정 잔액 조회 요청: {self.FUTURES_URL + endpoint}")

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)

            # 디버깅 로그 추가
            logger.info(f"[디버그] 응답 상태 코드: {response.status_code}")
            logger.info(f"[디버그] 응답 내용 (최대 500자): {response.text[:500]}")

            response.raise_for_status()
            result = response.json()

            # 결과가 리스트인지 확인
            if not isinstance(result, list):
                logger.warning(f"[디버그] 예상치 못한 응답 형식: {type(result)}")
                return []

            # 사용 가능한 잔액이 있는 자산만 필터링
            available_assets = [asset for asset in result if float(asset.get('availableBalance', 0)) > 0]

            logger.info(f"[디버그] 사용 가능한 자산 수: {len(available_assets)}")
            for asset in available_assets:
                logger.info(f"[디버그] 자산: {asset['asset']}, 사용 가능한 잔액: {asset['availableBalance']}")

            return result
        except Exception as e:
            logger.error(f"[디버그] 선물 계정 잔액 조회 실패: {e}")
            import traceback
            logger.error(f"[디버그] 스택 트레이스: {traceback.format_exc()}")
            return []

    def get_positions(self, symbol: str = None) -> List[Dict[str, Any]]:
        """포지션 정보 조회 (get_futures_positions의 별칭)

        Args:
            symbol: 조회할 심볼 (옵션, 없으면 모든 포지션 조회)

        Returns:
            List[Dict]: 포지션 정보 목록
        """
        return self.get_futures_positions(symbol)

    def get_futures_positions(self, symbol: str = None) -> List[Dict[str, Any]]:
        """선물 계정 포지션 정보 조회

        Args:
            symbol: 조회할 심볼 (옵션, 없으면 모든 포지션 조회)

        Returns:
            List[Dict]: 포지션 정보 목록
        """
        try:
            # 계정 정보 조회
            logger.info("🔍 바이낸스 계정 정보 조회 시작")
            account_info = self.get_account()

            if 'error' in account_info:
                logger.error(f"🔴 계정 정보 조회 실패: {account_info['error']}")
                return {'error': account_info['error']}

            # 포지션 정보 추출
            if 'positions' not in account_info:
                logger.error("🔴 계정 정보에서 포지션 목록을 찾을 수 없습니다.")
                logger.info(f"🔍 계정 정보 키들: {list(account_info.keys())}")
                return {'error': "Account info does not contain positions list"}

            positions = account_info['positions']
            logger.info(f"🔍 바이낸스 API에서 받은 전체 포지션 수: {len(positions)}")

            # 🔥 활성 포지션 필터링 및 상세 로깅 (더 엄격한 기준)
            active_positions = []
            for pos in positions:
                position_amt = float(pos.get('positionAmt', 0))
                unrealized_profit = float(pos.get('unrealizedProfit', 0))

                # 포지션 수량이 0이 아니거나 미실현 손익이 있는 경우 활성 포지션으로 간주
                if abs(position_amt) > 0.000001 or abs(unrealized_profit) > 0.01:  # 더 정밀한 기준
                    active_positions.append(pos)
                    logger.info(f"🔍 활성 포지션 발견: {pos.get('symbol')} = {position_amt} (PnL: {unrealized_profit})")

            logger.info(f"🔍 활성 포지션 총 개수: {len(active_positions)}")

            # 특정 심볼에 대한 포지션만 필터링
            if symbol:
                # 심볼 포맷팅 정리
                formatted_symbol = self._format_symbol(symbol)
                if not formatted_symbol:
                    return []

                filtered_positions = [pos for pos in positions if pos.get('symbol') == formatted_symbol]
                logger.info(f"🔍 {symbol} 필터링 결과: {len(filtered_positions)}개 포지션")
                return filtered_positions

            # 🔥 SOL, DOGE 포지션 상세 로깅
            for pos in positions:
                symbol_name = pos.get('symbol', '')
                if symbol_name in ['SOLUSDT', 'DOGEUSDT']:
                    position_amt = float(pos.get('positionAmt', 0))
                    logger.info(f"🔍 [{symbol_name}] 상세 정보:")
                    logger.info(f"  - positionAmt: {pos.get('positionAmt')} (float: {position_amt})")
                    logger.info(f"  - entryPrice: {pos.get('entryPrice')}")
                    logger.info(f"  - markPrice: {pos.get('markPrice')}")
                    logger.info(f"  - unrealizedProfit: {pos.get('unrealizedProfit')}")
                    logger.info(f"  - percentage: {pos.get('percentage')}")

            return positions
        except Exception as e:
            error_msg = f"포지션 정보 조회 실패: {e}"
            logger.error(error_msg)
            import traceback
            logger.error(f"스택 트레이스: {traceback.format_exc()}")
            return {'error': error_msg}

    def get_futures_open_orders(self, symbol: str = None) -> List[Dict]:
        """선물 미체결 주문 조회

        Args:
            symbol: 특정 심볼의 주문만 조회 (기본값: 모든 심볼)

        Returns:
            List[Dict]: 미체결 주문 목록
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return [{'error': 'API keys not configured'}]

        try:
            endpoint = f"/fapi/v1/openOrders"
            params = {}

            if symbol:
                formatted_symbol = self._format_symbol(symbol)
                if not formatted_symbol:
                    return []
                params['symbol'] = formatted_symbol

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"선물 미체결 주문 조회 실패: {e}")
            return [{'error': str(e)}]

    def get_futures_all_orders(self, symbol: str, limit: int = 500) -> List[Dict]:
        """선물 모든 주문 내역 조회

        Args:
            symbol: 조회할 심볼 (필수)
            limit: 조회할 주문 수 (기본값: 500, 최대: 1000)

        Returns:
            List[Dict]: 주문 내역 목록
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return [{'error': 'API keys not configured'}]

        try:
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return []

            endpoint = f"/fapi/v1/allOrders"
            params = {
                'symbol': formatted_symbol,
                'limit': min(limit, 1000)  # 최대 1000개로 제한
            }

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"선물 주문 내역 조회 실패 ({symbol}): {e}")
            return [{'error': str(e)}]

    def get_futures_risk_info(self) -> Dict[str, Any]:
        """선물 계정 위험 정보 조회

        Returns:
            Dict: 위험 정보 (마진 비율, 청산 가격 등)
        """
        try:
            account_info = self.get_account()

            if 'error' in account_info:
                return {'error': account_info['error']}

            # 주요 위험 지표 추출
            margin_balance = float(account_info.get('totalMarginBalance', 0))
            total_wallet_balance = float(account_info.get('totalWalletBalance', 0))
            total_unrealized_profit = float(account_info.get('totalUnrealizedProfit', 0))
            total_maintenance_margin = float(account_info.get('totalMaintMargin', 0))
            total_initial_margin = float(account_info.get('totalInitialMargin', 0))
            total_position_initial_margin = float(account_info.get('totalPositionInitialMargin', 0))

            # 마진 비율(%) 계산
            margin_ratio = (total_maintenance_margin / margin_balance * 100) if margin_balance > 0 else 0

            return {
                'margin_balance': margin_balance,
                'wallet_balance': total_wallet_balance,
                'unrealized_profit': total_unrealized_profit,
                'maintenance_margin': total_maintenance_margin,
                'initial_margin': total_initial_margin,
                'position_initial_margin': total_position_initial_margin,
                'margin_ratio': margin_ratio,
                'liquidation_risk': 'HIGH' if margin_ratio > 80 else 'MEDIUM' if margin_ratio > 50 else 'LOW',
                'available_balance': margin_balance - total_initial_margin
            }
        except Exception as e:
            logger.error(f"선물 위험 정보 조회 실패: {e}")
            return {'error': str(e)}

    def get_futures_market_price(self, symbol: str) -> Optional[float]:
        """선물 시장 현재가 조회

        Args:
            symbol: 심볼 (예: 'BTC', 'BTCUSDT')

        Returns:
            Optional[float]: 현재 시장 가격, 오류 시 None
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return None

            endpoint = f"/fapi/v1/ticker/price"
            params = {'symbol': formatted_symbol}
            response = requests.get(self.FUTURES_URL + endpoint, params=params)
            response.raise_for_status()
            data = response.json()
            return float(data['price'])
        except Exception as e:
            logger.error(f"선물 현재가 조회 실패 ({symbol}): {e}")
            return None

    def get_futures_mark_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """선물 마크 가격 조회

        Args:
            symbol: 심볼 (예: 'BTC/USDT', 'BTC', 'BTCUSDT')

        Returns:
            Optional[Dict]: 마크 가격 정보, 오류 시 None 또는 에러 딕셔너리
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return None

            endpoint = f"/fapi/v1/premiumIndex"
            params = {'symbol': formatted_symbol}
            response = requests.get(self.FUTURES_URL + endpoint, params=params)
            response.raise_for_status()
            data = response.json()

            # API가 단일 객체 또는 객체 목록을 반환할 수 있음
            if isinstance(data, list) and len(data) > 0:
                # 목록에서 첫 번째 항목 반환
                return data[0]
            return data
        except Exception as e:
            error_msg = f"선물 마크 가격 조회 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_position_mode(self) -> Dict[str, Any]:
        """계정의 포지션 모드 조회 (단일 모드 또는 헤지 모드)

        Returns:
            Dict: 포지션 모드 정보 {'dualSidePosition': True/False}
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 캐시된 값이 있으면 반환
            if self._position_mode_cache:
                logger.info(f"캐시된 포지션 모드 사용: {self._position_mode_cache}")
                return self._position_mode_cache

            # 포지션 모드 조회 API 호출
            endpoint = "/fapi/v1/positionSide/dual"
            params = self._get_signed_params()
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)
            response.raise_for_status()
            result = response.json()

            # 캐시 업데이트
            self._position_mode_cache = result
            logger.info(f"포지션 모드 조회 결과: {result}")
            return result
        except Exception as e:
            logger.error(f"포지션 모드 조회 실패: {e}")
            # 기본값으로 단일 모드 반환
            return {'dualSidePosition': False}

    def execute_futures_limit_order(self, symbol: str, side: str, quantity: float, price: float = None,
                             price_offset_pct: float = 0.1, timeout_seconds: int = 180,
                             position_side: str = None) -> Dict[str, Any]:
        """선물 지정가 주문 실행 (Maker 수수료 적용)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            side: 주문 방향 ('BUY', 'SELL')
            quantity: 주문 수량
            price: 지정가 (None인 경우 현재 가격에서 offset으로 계산)
            price_offset_pct: 현재 가격 대비 오프셋 비율(%) (BUY: 낮게, SELL: 높게)
            timeout_seconds: 지정가 주문 타임아웃(초) - 기본 3분
            position_side: 포지션 방향 (None, 'BOTH', 'LONG', 'SHORT')
                           None인 경우 계정 설정에 따라 자동 결정

        Returns:
            Dict: 주문 결과
        """
        logger.info(f"[디버그] execute_futures_limit_order 시작: 심볼={symbol}, 방향={side}, 수량={quantity}, 가격={price}, 오프셋={price_offset_pct}%, 타임아웃={timeout_seconds}초")

        if not self.api_key or not self.api_secret:
            logger.error("[디버그] API 키와 시크릿이 설정되지 않았습니다.")
            return {'error': 'API keys not configured'}

        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            logger.info(f"[디버그] 포맷팅된 심볼: {formatted_symbol}")

            if not formatted_symbol:
                logger.error("[디버그] 심볼 포맷팅 실패")
                return {'error': 'Invalid symbol'}

            # 수량이 0이거나 음수인 경우 에러 반환
            if quantity <= 0:
                logger.error(f"[디버그] 유효하지 않은 수량: {quantity}")
                return {'error': 'Quantity must be greater than 0'}

            # 심볼 정보 조회 및 수량 정밀도 조정
            logger.info(f"[디버그] 심볼 정보 조회 시작: {symbol}")
            symbol_info = self.get_symbol_info(symbol)
            logger.info(f"[디버그] 심볼 정보 조회 결과: {symbol_info}")

            if 'error' in symbol_info:
                logger.error(f"[디버그] 심볼 정보 조회 실패: {symbol_info['error']}")
                return {'error': f"심볼 정보 조회 실패: {symbol_info['error']}"}

            # 수량 정밀도 조정
            logger.info(f"[디버그] 수량 정밀도 조정 전: {quantity}")
            adjusted_quantity = self.calculate_valid_quantity(symbol, quantity)
            logger.info(f"[디버그] 수량 정밀도 조정 후: {adjusted_quantity}")

            # 현재 가격 조회
            logger.info(f"[디버그] 현재 가격 조회 시작: {symbol}")
            price_info = self.get_futures_price(symbol)
            logger.info(f"[디버그] 현재 가격 조회 결과: {price_info}")

            if 'error' in price_info:
                logger.error(f"[디버그] 현재가 조회 실패: {price_info['error']}")
                return {'error': f"현재가 조회 실패: {price_info['error']}"}

            current_price = float(price_info['price'])

            # 지정가 계산 (price가 None인 경우)
            if price is None:
                # 시장 변동성에 따른 오프셋 동적 조정
                try:
                    market_data = self.get_futures_market_data(symbol)
                    volume = float(market_data.get('volume', 0))
                    avg_volume = float(market_data.get('avgVolume', 1))
                    volume_ratio = volume / avg_volume if avg_volume > 0 else 1

                    # 거래량이 많을수록 오프셋 줄이기 (체결 가능성 높이기)
                    if volume_ratio > 1.5:  # 평균보다 50% 이상 거래량이 많은 경우
                        price_offset_pct = price_offset_pct * 0.7  # 오프셋 30% 감소
                        logger.info(f"[디버그] 거래량이 많아 오프셋 조정: {price_offset_pct}%")
                except Exception as e:
                    logger.warning(f"[디버그] 시장 데이터 조회 실패, 기본 오프셋 사용: {e}")

                # 지정가 계산 (BUY: 현재가보다 낮게, SELL: 현재가보다 높게)
                if side == "BUY":
                    limit_price = current_price * (1 - price_offset_pct/100)
                else:
                    limit_price = current_price * (1 + price_offset_pct/100)

                # 가격 정밀도 조정
                price_precision = symbol_info.get('pricePrecision', 2)
                limit_price = round(limit_price, price_precision)
            else:
                limit_price = price

            logger.info(f"[디버그] 지정가 주문 가격: {limit_price} (현재가: {current_price})")

            # 주문 금액 계산 및 검증
            order_value = adjusted_quantity * limit_price

            # 심볼별 최소 주문 금액 설정
            symbol_min_notional = {
                'BTCUSDT': 100.0,
                'ETHUSDT': 20.0,
                'SOLUSDT': 5.0,
                'BNBUSDT': 5.0,
                'DOGEUSDT': 10.0  # DOGE 최소 주문 금액 증가
            }

            # 기본 최소 주문 금액 (모든 코인에 적용)
            default_min_notional = 5.0

            # 심볼에 맞는 최소 주문 금액 가져오기
            min_order_value = symbol_min_notional.get(formatted_symbol, default_min_notional)

            # 주문 금액 검증
            if order_value < min_order_value:
                error_msg = f"주문 금액이 최소 요구사항({min_order_value} USDT)보다 작습니다: {order_value:.2f} USDT"
                logger.warning(error_msg)

                # 수량 자동 조정 시도
                suggested_quantity = round(min_order_value / limit_price, symbol_info.get('quantityPrecision', 3))

                # stepSize에 맞게 조정
                if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                    lot_size = symbol_info['filters']['lotSize']
                    step_size = float(lot_size.get('stepSize', 0))

                    if step_size > 0:
                        quantity_multiple = round(suggested_quantity / step_size)
                        suggested_quantity = quantity_multiple * step_size
                        suggested_quantity = round(suggested_quantity, symbol_info.get('quantityPrecision', 3))

                suggested_value = suggested_quantity * limit_price

                # 안전을 위해 약간 더 추가 (반올림 오차 방지)
                if suggested_value < min_order_value:
                    suggested_quantity_extra = round((min_order_value * 1.01) / limit_price, symbol_info.get('quantityPrecision', 3))

                    # stepSize에 맞게 조정
                    if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                        lot_size = symbol_info['filters']['lotSize']
                        step_size = float(lot_size.get('stepSize', 0))

                        if step_size > 0:
                            quantity_multiple = round(suggested_quantity_extra / step_size)
                            suggested_quantity_extra = quantity_multiple * step_size
                            suggested_quantity_extra = round(suggested_quantity_extra, symbol_info.get('quantityPrecision', 3))

                    suggested_value_extra = suggested_quantity_extra * limit_price

                    # 더 나은 제안 선택
                    if suggested_value_extra >= min_order_value:
                        suggested_quantity = suggested_quantity_extra
                        suggested_value = suggested_value_extra

                # 자동으로 수량 조정
                logger.info(f"주문 금액이 최소 요구사항보다 작아 수량을 자동으로 조정합니다: {adjusted_quantity} -> {suggested_quantity}")
                adjusted_quantity = suggested_quantity
                order_value = suggested_value
                logger.info(f"조정된 주문 금액: {order_value:.2f} USDT (최소 요구사항: {min_order_value} USDT)")

                # 여전히 최소 주문 금액보다 작으면 오류 반환
                if order_value < min_order_value:
                    return {
                        'error': f"조정 후에도 주문 금액이 최소 요구사항({min_order_value} USDT)보다 작습니다: {order_value:.2f} USDT",
                        'suggestion': {
                            'minOrderValue': min_order_value,
                            'currentOrderValue': order_value,
                            'suggestedQuantity': suggested_quantity,
                            'suggestedOrderValue': suggested_value
                        }
                    }

            # 포지션 모드 확인 및 포지션 사이드 설정
            position_mode = self.get_position_mode()

            # 포지션 사이드가 지정되지 않은 경우 계정 설정에 따라 자동 결정
            if position_side is None:
                if 'dualSidePosition' in position_mode and position_mode['dualSidePosition']:
                    # 헤지 모드인 경우 방향에 따라 LONG 또는 SHORT 설정
                    if side == 'BUY':
                        position_side = 'LONG'
                    else:
                        position_side = 'SHORT'
                    logger.info(f"헤지 모드 감지: 포지션 사이드를 '{position_side}'로 설정")
                else:
                    # 단일 모드인 경우 BOTH 설정
                    position_side = 'BOTH'
                    logger.info(f"단일 모드 감지: 포지션 사이드를 '{position_side}'로 설정")

            # API 요청 파라미터 준비
            params = {
                'symbol': formatted_symbol,
                'side': side,
                'type': 'LIMIT',
                'timeInForce': 'GTC',  # Good Till Cancel
                'quantity': str(adjusted_quantity),
                'price': str(limit_price),
                'positionSide': position_side
            }

            # 테스트넷 모드이지만 API 키가 없는 경우 시뮬레이션 응답 반환
            if self.testnet and (not self.api_key or not self.api_secret):
                logger.info(f"테스트넷 모드이지만 API 키가 없음: 시뮬레이션 응답 반환")
                # 시뮬레이션 주문 ID 생성
                order_id = int(time.time() * 1000)
                # 시뮬레이션 응답 생성
                return {
                    "orderId": order_id,
                    "symbol": formatted_symbol,
                    "status": "NEW",
                    "clientOrderId": f"simulated_limit_{order_id}",
                    "price": str(limit_price),
                    "avgPrice": "0",
                    "origQty": str(adjusted_quantity),
                    "executedQty": "0",
                    "cumQuote": "0",
                    "timeInForce": "GTC",
                    "type": "LIMIT",
                    "side": side,
                    "time": int(time.time() * 1000)
                }

            # 실제 API 호출
            logger.info(f"실제 API 호출: POST /fapi/v1/order, 파라미터: {params}")
            result = self.send_signed_request('POST', '/fapi/v1/order', params)

            # 결과 처리 및 로깅
            if 'error' not in result:
                logger.info(f"지정가 주문 성공: {side} {adjusted_quantity} {formatted_symbol} @ {limit_price}")
                logger.info(f"주문 ID: {result.get('orderId')}")

                # 주문 모니터링 시작 (별도 스레드)
                if 'orderId' in result:
                    order_id = result['orderId']
                    import threading
                    threading.Thread(
                        target=self._monitor_limit_order,
                        args=(symbol, order_id, timeout_seconds),
                        daemon=True
                    ).start()
            else:
                logger.error(f"지정가 주문 실패: {result['error']}")

            return result

        except Exception as e:
            error_msg = f"지정가 주문 실패: {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def _monitor_limit_order(self, symbol: str, order_id: int, timeout_seconds: int):
        """지정가 주문 모니터링 및 타임아웃 처리

        Args:
            symbol: 거래 심볼
            order_id: 주문 ID
            timeout_seconds: 타임아웃 시간(초)
        """
        import time
        import threading

        start_time = time.time()
        check_interval = 15  # 15초마다 주문 상태 확인

        logger.info(f"[{symbol}] 지정가 주문 모니터링 시작: 주문ID={order_id}, 타임아웃={timeout_seconds}초")

        while time.time() - start_time < timeout_seconds:
            try:
                # 주문 상태 확인
                order_status = self.get_order_status(symbol, order_id)

                # 주문이 이미 체결된 경우
                if order_status.get('status') == 'FILLED':
                    logger.info(f"[{symbol}] 지정가 주문 체결 완료: 주문ID={order_id}")
                    return

                # 일부 체결된 경우
                if order_status.get('status') == 'PARTIALLY_FILLED':
                    # 일부 체결 상태에서는 타임아웃 시간 연장 (30초)
                    start_time = time.time() - timeout_seconds + 30
                    logger.info(f"[{symbol}] 지정가 주문 일부 체결: 주문ID={order_id}, 타임아웃 연장")

                # 주문이 취소된 경우
                if order_status.get('status') == 'CANCELED' or order_status.get('status') == 'EXPIRED':
                    logger.info(f"[{symbol}] 지정가 주문이 이미 취소됨: 주문ID={order_id}")
                    return

                time.sleep(check_interval)
            except Exception as e:
                logger.error(f"[{symbol}] 주문 상태 확인 중 오류: {e}")
                time.sleep(check_interval)

        # 타임아웃 발생 - 미체결 주문 취소 및 시장가 전환
        logger.info(f"[{symbol}] 지정가 주문 타임아웃 발생: 주문ID={order_id}, 시장가로 전환")

        try:
            # 기존 주문 취소
            cancel_result = self.cancel_order(symbol, order_id)

            if cancel_result.get('status') == 'CANCELED':
                # 취소된 수량 계산
                executed_qty = float(cancel_result.get('executedQty', 0))
                original_qty = float(cancel_result.get('origQty', 0))
                remaining_qty = original_qty - executed_qty

                if remaining_qty > 0:
                    # 남은 수량에 대해 시장가 주문 실행
                    side = cancel_result.get('side')
                    position_side = cancel_result.get('positionSide')
                    logger.info(f"[{symbol}] 남은 수량 시장가 전환: {remaining_qty} {side}")

                    # 시장가 주문 실행
                    market_order_result = self.execute_futures_market_order(
                        symbol=symbol,
                        side=side,
                        quantity=remaining_qty,
                        position_side=position_side
                    )

                    logger.info(f"[{symbol}] 시장가 전환 결과: {market_order_result}")
            else:
                logger.warning(f"[{symbol}] 주문 취소 실패: {cancel_result}")
        except Exception as e:
            logger.error(f"[{symbol}] 주문 취소 및 시장가 전환 중 오류: {e}")

    def get_order_status(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """주문 상태 조회

        Args:
            symbol: 거래 심볼
            order_id: 주문 ID

        Returns:
            Dict: 주문 상태 정보
        """
        try:
            # 심볼 포맷팅
            formatted_symbol = self._format_symbol(symbol)

            # API 요청 파라미터
            params = {
                'symbol': formatted_symbol,
                'orderId': order_id
            }

            # 서명된 요청 전송
            result = self.send_signed_request('GET', '/fapi/v1/order', params)
            return result
        except Exception as e:
            logger.error(f"주문 상태 조회 실패 ({symbol}, {order_id}): {e}")
            return {'error': str(e)}

    def cancel_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """주문 취소

        Args:
            symbol: 거래 심볼
            order_id: 주문 ID

        Returns:
            Dict: 취소 결과
        """
        try:
            # 심볼 포맷팅
            formatted_symbol = self._format_symbol(symbol)

            # API 요청 파라미터
            params = {
                'symbol': formatted_symbol,
                'orderId': order_id
            }

            # 서명된 요청 전송
            result = self.send_signed_request('DELETE', '/fapi/v1/order', params)

            if 'status' in result and result['status'] == 'CANCELED':
                logger.info(f"주문 취소 성공: {symbol}, 주문ID={order_id}")
            else:
                logger.warning(f"주문 취소 결과 이상: {result}")

            return result
        except Exception as e:
            logger.error(f"주문 취소 실패 ({symbol}, {order_id}): {e}")
            return {'error': str(e)}

    def execute_futures_market_order(self, symbol: str, side: str, quantity: float, position_side: str = None) -> Dict[str, Any]:
        """선물 시장가 주문 실행

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            side: 주문 방향 ('BUY', 'SELL')
            quantity: 주문 수량
            position_side: 포지션 방향 (None, 'BOTH', 'LONG', 'SHORT')
                           None인 경우 계정 설정에 따라 자동 결정

        Returns:
            Dict: 주문 결과
        """
        logger.info(f"[디버그] execute_futures_market_order 시작: 심볼={symbol}, 방향={side}, 수량={quantity}, 포지션 방향={position_side}")

        if not self.api_key or not self.api_secret:
            logger.error("[디버그] API 키와 시크릿이 설정되지 않았습니다.")
            return {'error': 'API keys not configured'}

        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            logger.info(f"[디버그] 포맷팅된 심볼: {formatted_symbol}")

            if not formatted_symbol:
                logger.error("[디버그] 심볼 포맷팅 실패")
                return {'error': 'Invalid symbol'}

            # 수량이 0이거나 음수인 경우 에러 반환
            if quantity <= 0:
                logger.error(f"[디버그] 유효하지 않은 수량: {quantity}")
                return {'error': 'Quantity must be greater than 0'}

            # 심볼 정보 조회 및 수량 정밀도 조정
            logger.info(f"[디버그] 심볼 정보 조회 시작: {symbol}")
            symbol_info = self.get_symbol_info(symbol)
            logger.info(f"[디버그] 심볼 정보 조회 결과: {symbol_info}")

            if 'error' in symbol_info:
                logger.error(f"[디버그] 심볼 정보 조회 실패: {symbol_info['error']}")
                return {'error': f"심볼 정보 조회 실패: {symbol_info['error']}"}

            # 수량 정밀도 조정
            logger.info(f"[디버그] 수량 정밀도 조정 전: {quantity}")
            adjusted_quantity = self.calculate_valid_quantity(symbol, quantity)
            logger.info(f"[디버그] 수량 정밀도 조정 후: {adjusted_quantity}")

            # 현재 가격 조회하여 주문 금액 계산 및 검증
            logger.info(f"[디버그] 현재 가격 조회 시작: {symbol}")
            price_info = self.get_futures_price(symbol)
            logger.info(f"[디버그] 현재 가격 조회 결과: {price_info}")

            if 'error' in price_info:
                logger.error(f"[디버그] 현재가 조회 실패: {price_info['error']}")
                return {'error': f"현재가 조회 실패: {price_info['error']}"}

            current_price = float(price_info['price'])
            order_value = adjusted_quantity * current_price

            # 심볼별 최소 주문 금액 설정
            symbol_min_notional = {
                'BTCUSDT': 100.0,
                'ETHUSDT': 20.0,
                'SOLUSDT': 5.0,
                'BNBUSDT': 5.0,
                'DOGEUSDT': 10.0  # DOGE 최소 주문 금액 증가
            }

            # 기본 최소 주문 금액 (모든 코인에 적용)
            default_min_notional = 5.0

            # 심볼에 맞는 최소 주문 금액 가져오기
            min_order_value = symbol_min_notional.get(formatted_symbol, default_min_notional)

            # 주문 금액 검증
            if order_value < min_order_value:
                error_msg = f"주문 금액이 최소 요구사항({min_order_value} USDT)보다 작습니다: {order_value:.2f} USDT"
                logger.warning(error_msg)

                # 수량 자동 조정 시도
                suggested_quantity = round(min_order_value / current_price, symbol_info.get('quantityPrecision', 3))

                # stepSize에 맞게 조정
                if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                    lot_size = symbol_info['filters']['lotSize']
                    step_size = float(lot_size.get('stepSize', 0))

                    if step_size > 0:
                        quantity_multiple = round(suggested_quantity / step_size)
                        suggested_quantity = quantity_multiple * step_size
                        suggested_quantity = round(suggested_quantity, symbol_info.get('quantityPrecision', 3))

                suggested_value = suggested_quantity * current_price

                # 안전을 위해 약간 더 추가 (반올림 오차 방지)
                if suggested_value < min_order_value:
                    suggested_quantity_extra = round((min_order_value * 1.01) / current_price, symbol_info.get('quantityPrecision', 3))

                    # stepSize에 맞게 조정
                    if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                        lot_size = symbol_info['filters']['lotSize']
                        step_size = float(lot_size.get('stepSize', 0))

                        if step_size > 0:
                            quantity_multiple = round(suggested_quantity_extra / step_size)
                            suggested_quantity_extra = quantity_multiple * step_size
                            suggested_quantity_extra = round(suggested_quantity_extra, symbol_info.get('quantityPrecision', 3))

                    suggested_value_extra = suggested_quantity_extra * current_price

                    # 더 나은 제안 선택
                    if suggested_value_extra >= min_order_value:
                        suggested_quantity = suggested_quantity_extra
                        suggested_value = suggested_value_extra

                # 자동으로 수량 조정
                logger.info(f"주문 금액이 최소 요구사항보다 작아 수량을 자동으로 조정합니다: {adjusted_quantity} -> {suggested_quantity}")
                adjusted_quantity = suggested_quantity
                order_value = suggested_value
                logger.info(f"조정된 주문 금액: {order_value:.2f} USDT (최소 요구사항: {min_order_value} USDT)")

                # 여전히 최소 주문 금액보다 작으면 오류 반환
                if order_value < min_order_value:
                    return {
                        'error': f"조정 후에도 주문 금액이 최소 요구사항({min_order_value} USDT)보다 작습니다: {order_value:.2f} USDT",
                        'suggestion': {
                            'minOrderValue': min_order_value,
                            'currentOrderValue': order_value,
                            'suggestedQuantity': suggested_quantity,
                            'suggestedOrderValue': suggested_value
                        }
                    }

            # 테스트넷 또는 실제 API 키가 없는 경우 단일 모드 사용
            if self.testnet or not self.api_key or not self.api_secret:
                logger.info("테스트넷 또는 API 키 없음. 단일 모드(BOTH) 사용.")
                position_side = 'BOTH'
            else:
                # 포지션 모드 확인 시도
                try:
                    position_mode = self.get_position_mode()
                    is_dual_mode = position_mode.get('dualSidePosition', False)

                    # 양방향 모드에서 포지션 사이드 조정
                    if is_dual_mode and position_side == 'BOTH':
                        logger.warning("양방향 모드에서는 포지션 사이드를 'LONG' 또는 'SHORT'로 설정해야 합니다.")
                        position_side = 'LONG' if side == 'BUY' else 'SHORT'

                    # 단일 모드에서 포지션 사이드 조정
                    if not is_dual_mode and position_side != 'BOTH':
                        logger.warning("단일 모드에서는 포지션 사이드를 'BOTH'로 설정해야 합니다.")
                        position_side = 'BOTH'
                except Exception as e:
                    logger.warning(f"포지션 모드 확인 실패: {e}. 기본값(BOTH) 사용.")
                    position_side = 'BOTH'

            # API 요청 파라미터 준비
            params = {
                'symbol': formatted_symbol,
                'side': side,
                'type': 'MARKET',
                'quantity': str(adjusted_quantity)
            }

            # 포지션 모드 확인 및 포지션 사이드 설정
            position_mode = self.get_position_mode()

            # 포지션 사이드가 지정되지 않은 경우 계정 설정에 따라 자동 결정
            if position_side is None:
                if 'dualSidePosition' in position_mode and position_mode['dualSidePosition']:
                    # 헤지 모드인 경우 방향에 따라 LONG 또는 SHORT 설정
                    if side == 'BUY':
                        position_side = 'LONG'
                    else:
                        position_side = 'SHORT'
                    logger.info(f"헤지 모드 감지: 포지션 사이드를 '{position_side}'로 설정")
                else:
                    # 단일 모드인 경우 BOTH 설정
                    position_side = 'BOTH'
                    logger.info(f"단일 모드 감지: 포지션 사이드를 '{position_side}'로 설정")

            # 포지션 사이드 추가
            params['positionSide'] = position_side
            logger.info(f"최종 포지션 사이드: {position_side}")

            # 테스트넷 모드이지만 API 키가 없는 경우 시뮬레이션 응답 반환
            if self.testnet and (not self.api_key or not self.api_secret):
                logger.info(f"테스트넷 모드이지만 API 키가 없음: 시뮬레이션 응답 반환")
                # 시뮬레이션 주문 ID 생성
                order_id = int(time.time() * 1000)
                # 시뮬레이션 응답 생성
                return {
                    "orderId": order_id,
                    "symbol": formatted_symbol,
                    "status": "FILLED",
                    "clientOrderId": f"simulated_{order_id}",
                    "price": "0",
                    "avgPrice": str(current_price),
                    "origQty": str(adjusted_quantity),
                    "executedQty": str(adjusted_quantity),
                    "cumQuote": str(order_value),
                    "timeInForce": "GTC",
                    "type": "MARKET",
                    "side": side,
                    "time": int(time.time() * 1000)
                }

            # 실제 API 호출
            logger.info(f"실제 API 호출: POST /fapi/v1/order, 파라미터: {params}")
            result = self.send_signed_request('POST', '/fapi/v1/order', params)

            # 결과 처리 및 로깅
            if 'error' not in result:
                logger.info(f"시장가 주문 성공: {side} {adjusted_quantity} {formatted_symbol}")
                logger.info(f"주문 ID: {result.get('orderId')}")
            else:
                logger.error(f"시장가 주문 실패: {result['error']}")

            return result

        except Exception as e:
            error_msg = f"시장가 주문 실패: {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def open_long_position(self, symbol: str, quantity: float) -> Dict[str, Any]:
        """선물 롱 포지션 개설 (시장가)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 주문 수량

        Returns:
            Dict: 주문 결과
        """
        try:
            # 포지션 모드 확인
            position_mode = self.get_position_mode()
            if 'error' not in position_mode and position_mode.get('dualSidePosition', False):
                # 양방향 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'BUY', quantity, 'LONG')
            else:
                # 단일 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'BUY', quantity, 'BOTH')
        except Exception as e:
            error_msg = f"롱 포지션 개설 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def close_long_position(self, symbol: str, quantity: float) -> Dict[str, Any]:
        """선물 롱 포지션 종료 (시장가)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 주문 수량

        Returns:
            Dict: 주문 결과
        """
        try:
            # 포지션 모드 확인
            position_mode = self.get_position_mode()
            if 'error' not in position_mode and position_mode.get('dualSidePosition', False):
                # 양방향 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'SELL', quantity, 'LONG')
            else:
                # 단일 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'SELL', quantity, 'BOTH')
        except Exception as e:
            error_msg = f"롱 포지션 종료 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def open_short_position(self, symbol: str, quantity: float) -> Dict[str, Any]:
        """선물 숏 포지션 개설 (시장가)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 주문 수량

        Returns:
            Dict: 주문 결과
        """
        try:
            # 포지션 모드 확인
            position_mode = self.get_position_mode()
            if 'error' not in position_mode and position_mode.get('dualSidePosition', False):
                # 양방향 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'SELL', quantity, 'SHORT')
            else:
                # 단일 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'SELL', quantity, 'BOTH')
        except Exception as e:
            error_msg = f"숏 포지션 개설 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def close_short_position(self, symbol: str, quantity: float) -> Dict[str, Any]:
        """선물 숏 포지션 종료 (시장가)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 주문 수량

        Returns:
            Dict: 주문 결과
        """
        try:
            # 포지션 모드 확인
            position_mode = self.get_position_mode()
            if 'error' not in position_mode and position_mode.get('dualSidePosition', False):
                # 양방향 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'BUY', quantity, 'SHORT')
            else:
                # 단일 포지션 모드일 경우
                return self.execute_futures_market_order(symbol, 'BUY', quantity, 'BOTH')
        except Exception as e:
            error_msg = f"숏 포지션 종료 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def set_futures_leverage(self, symbol: str, leverage: int) -> Dict[str, Any]:
        """선물 거래 레버리지 설정

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            leverage: 레버리지 수준 (1-125)

        Returns:
            Dict: 설정 결과
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            # 레버리지 범위 검증
            if leverage < 1 or leverage > 125:
                return {'error': 'Leverage must be between 1 and 125'}

            endpoint = f"/fapi/v1/leverage"

            # 타임스탬프 및 서명 생성
            timestamp = int(time.time() * 1000)

            # 서명을 위한 쿼리 스트링 생성
            query_string = f"symbol={formatted_symbol}&leverage={leverage}&timestamp={timestamp}"
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 헤더 설정
            headers = {
                'X-MBX-APIKEY': self.api_key,
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            # POST 요청 전송 - 데이터를 본문에 전송
            data = f"{query_string}&signature={signature}"
            response = requests.post(self.FUTURES_URL + endpoint, headers=headers, data=data)
            response.raise_for_status()
            result = response.json()

            logger.info(f"레버리지 설정 성공: {formatted_symbol}, 레버리지: {leverage}x")
            return result
        except Exception as e:
            error_msg = f"레버리지 설정 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_futures_position_risk(self, symbol: str = None) -> List[Dict]:
        """선물 포지션 리스크 정보 조회

        Args:
            symbol: 조회할 심볼 (옵션, 없으면 모든 포지션 조회)

        Returns:
            List[Dict]: 포지션 리스크 정보 목록
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return [{'error': 'API keys not configured'}]

        try:
            endpoint = f"/fapi/v2/positionRisk"
            params = {}

            if symbol:
                formatted_symbol = self._format_symbol(symbol)
                if not formatted_symbol:
                    return []
                params['symbol'] = formatted_symbol

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"선물 포지션 리스크 조회 실패: {e}")
            return [{'error': str(e)}]

    def get_funding_rate_history(self, symbol, start_time=None, end_time=None, limit=100):
        """펀딩 비율 이력을 조회합니다.

        Args:
            symbol (str): 심볼 (예: BTCUSDT)
            start_time (int, optional): 조회 시작 시간 (밀리초 타임스탬프)
            end_time (int, optional): 조회 종료 시간 (밀리초 타임스탬프)
            limit (int, optional): 반환할 결과의 최대 개수 (기본값: 100, 최대: 1000)

        Returns:
            list: 펀딩 비율 이력 데이터
        """
        try:
            # 슬래시(/)가 포함된 심볼 형식(BTC/USDT)을 바이낸스 API 형식(BTCUSDT)으로 변환
            api_symbol = self._format_symbol(symbol)
            if not api_symbol:
                return []

            endpoint = "/fapi/v1/fundingRate"
            params = {
                "symbol": api_symbol,
                "limit": limit
            }

            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time

            response = self._request('GET', endpoint, params=params)
            logger.info(f"{api_symbol} 펀딩 비율 이력 조회 성공: {len(response)} 항목")
            return response

        except Exception as e:
            logger.error(f"펀딩 비율 이력 조회 중 오류 ({symbol}): {e}")
            return []

    def get_funding_info(self, symbol):
        """심볼의 펀딩 관련 정보를 조회합니다.

        Args:
            symbol (str): 심볼 (예: BTCUSDT)

        Returns:
            dict: 펀딩 정보
        """
        try:
            # 슬래시(/)가 포함된 심볼 형식(BTC/USDT)을 바이낸스 API 형식(BTCUSDT)으로 변환
            api_symbol = self._format_symbol(symbol)
            if not api_symbol:
                return {}

            endpoint = "/fapi/v1/premiumIndex"  # fundingInfo 대체
            params = {
                "symbol": api_symbol
            }

            response = self._request('GET', endpoint, params=params)
            logger.info(f"{api_symbol} 펀딩 정보 조회 성공: {response}")
            return response

        except Exception as e:
            logger.error(f"펀딩 정보 조회 중 오류 ({symbol}): {e}")
            return {}

    def get_mark_price(self, symbol: str = None):
        """마크 가격을 조회합니다.

        Args:
            symbol (str, optional): 심볼 (예: BTCUSDT 또는 BTC/USDT), 지정하지 않으면 모든 심볼의 마크 가격 반환

        Returns:
            dict/list: 마크 가격 정보
        """
        try:
            params = {}
            if symbol:
                formatted_symbol = self._format_symbol(symbol)
                if not formatted_symbol:
                    return []
                params["symbol"] = formatted_symbol

            endpoint = "/fapi/v1/premiumIndex"
            response = self._request('GET', endpoint, params=params)

            logger.info(f"마크 가격 조회 성공: {symbol if symbol else '모든 심볼'}")
            return response

        except Exception as e:
            logger.error(f"마크 가격 조회 중 오류: {e}")
            return [] if not symbol else {}

    def get_futures_commission_rate(self, symbol: str = None):
        """선물 거래 수수료 정보 가져오기

        Args:
            symbol (str, optional): 심볼 (예: BTCUSDT). 필수 파라미터입니다.

        Returns:
            Dict: 거래 수수료 정보 (maker와 taker 수수료율)
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {"makerCommissionRate": "0.0002", "takerCommissionRate": "0.0004"}

            # API 키와 시크릿이 설정되지 않았으면 기본값 반환
            if not self.api_key or not self.api_secret:
                logger.warning("API 키와 시크릿이 설정되지 않아 기본 수수료율을 반환합니다.")
                return {"makerCommissionRate": "0.0002", "takerCommissionRate": "0.0004"}

            try:
                # 수수료 정보를 API에서 직접 조회하는 코드
                # 무한 재귀 호출 대신 실제 API 호출을 구현

                # 현재는 기본 수수료율을 반환
                logger.info(f"{formatted_symbol} 심볼의 수수료 정보를 기본값으로 사용합니다.")
                return {"makerCommissionRate": "0.0002", "takerCommissionRate": "0.0004"}

                # TODO: 실제 API 호출 코드 구현
                # endpoint = "/fapi/v1/commissionRate"
                # params = {"symbol": formatted_symbol}
                # return self._make_signed_futures_request("GET", endpoint, params)

            except Exception as e:
                logger.error(f"{symbol} 수수료 정보 조회 중 오류: {str(e)}")
                return {"makerCommissionRate": "0.0002", "takerCommissionRate": "0.0004"}

        except Exception as e:
            logger.error(f"수수료 정보 조회 중 오류 ({symbol}): {e}")
            return {"makerCommissionRate": "0.0002", "takerCommissionRate": "0.0004"}

    def get_fee_for_symbol(self, symbol):
        """특정 심볼에 대한 거래 수수료 정보를 반환

        선물 거래와 현물 거래의 수수료는 다를 수 있습니다.
        심볼별로 다른 수수료를 적용해야 할 경우 이 함수에서 처리합니다.

        Args:
            symbol: 심볼 (예: BTCUSDT 또는 BTC/USDT)

        Returns:
            Dict: 해당 심볼의 수수료 정보 {"maker": float, "taker": float}
        """
        # 심볼 포맷팅 정리
        formatted_symbol = self._format_symbol(symbol)
        if not formatted_symbol:
            return {"maker": 0.0002, "taker": 0.0004}

        # API 키와 시크릿이 설정되지 않았으면 기본값 반환
        if not self.api_key or not self.api_secret:
            logger.warning("API 키와 시크릿이 설정되지 않아 기본 수수료율을 반환합니다.")
            return {"maker": 0.0002, "taker": 0.0004}

        try:
            # 현재는 모든 심볼에 동일한 수수료를 적용
            # 추후 심볼별 차등 적용 필요 시 여기서 구현
            return self.get_futures_commission_rate(formatted_symbol)
        except Exception as e:
            logger.error(f"{symbol} 수수료 정보 조회 중 오류: {str(e)}")
            return {"maker": 0.0002, "taker": 0.0004}

    def get_futures_exchange_info(self) -> Dict[str, Any]:
        """선물 거래소 정보 조회

        Returns:
            Dict: 거래소 정보, 심볼 정보, 거래 규칙 등
        """
        try:
            endpoint = f"/fapi/v1/exchangeInfo"
            response = requests.get(self.FUTURES_URL + endpoint)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"거래소 정보 조회 실패: {response.text}")
                return {"error": f"API 요청 실패 (코드: {response.status_code})"}
        except Exception as e:
            logger.error(f"거래소 정보 조회 중 오류: {str(e)}")
            return {"error": str(e)}

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """거래 심볼에 대한 상세 정보 조회

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')

        Returns:
            Dict: 심볼 정보 (최소 주문 수량, 가격 정밀도 등)
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            # 캐시된 정보 확인 (캐시 업데이트 필요 시 주석 처리)
            if formatted_symbol in self._symbol_info_cache:
                return self._symbol_info_cache[formatted_symbol]

            # 선물 거래소 정보 조회
            exchange_info = self.get_futures_exchange_info()
            if 'error' in exchange_info:
                return exchange_info

            # 심볼 정보 찾기
            symbol_info = None
            for sym in exchange_info.get('symbols', []):
                if sym.get('symbol') == formatted_symbol:
                    symbol_info = sym
                    break

            if not symbol_info:
                logger.error(f"심볼 정보를 찾을 수 없음: {formatted_symbol}")
                return {'error': f"Symbol not found: {formatted_symbol}"}

            # 필요한 정보 추출
            result = {
                'symbol': formatted_symbol,
                'baseAsset': symbol_info.get('baseAsset', ''),
                'quoteAsset': symbol_info.get('quoteAsset', ''),
                'status': symbol_info.get('status', ''),
                'filters': {}
            }

            # 필터 정보 처리
            for filter_info in symbol_info.get('filters', []):
                filter_type = filter_info.get('filterType')

                # 최소 주문 수량 필터
                if filter_type == 'LOT_SIZE':
                    result['filters']['lotSize'] = {
                        'minQty': float(filter_info.get('minQty', 0)),
                        'maxQty': float(filter_info.get('maxQty', 0)),
                        'stepSize': float(filter_info.get('stepSize', 0))
                    }

                # 가격 필터
                elif filter_type == 'PRICE_FILTER':
                    result['filters']['priceFilter'] = {
                        'minPrice': float(filter_info.get('minPrice', 0)),
                        'maxPrice': float(filter_info.get('maxPrice', 0)),
                        'tickSize': float(filter_info.get('tickSize', 0))
                    }

                # 최소 금액 필터
                elif filter_type == 'MIN_NOTIONAL':
                    result['filters']['minNotional'] = float(filter_info.get('notional', 0))

                # 마켓 로트 필터
                elif filter_type == 'MARKET_LOT_SIZE':
                    result['filters']['marketLotSize'] = {
                        'minQty': float(filter_info.get('minQty', 0)),
                        'maxQty': float(filter_info.get('maxQty', 0)),
                        'stepSize': float(filter_info.get('stepSize', 0))
                    }

            # 정밀도 추가
            if 'priceFilter' in result['filters'] and 'tickSize' in result['filters']['priceFilter']:
                tick_size = result['filters']['priceFilter']['tickSize']
                price_precision = self._get_precision_from_tick_size(tick_size)
                result['pricePrecision'] = price_precision

            if 'lotSize' in result['filters'] and 'stepSize' in result['filters']['lotSize']:
                step_size = result['filters']['lotSize']['stepSize']
                qty_precision = self._get_precision_from_tick_size(step_size)
                result['quantityPrecision'] = qty_precision

            # 캐시에 저장
            self._symbol_info_cache[formatted_symbol] = result
            return result

        except Exception as e:
            error_msg = f"심볼 정보 조회 중 오류 발생: {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def _get_precision_from_tick_size(self, tick_size: float) -> int:
        """Tick size(최소 변동폭)에서 정밀도(소수점 자릿수) 계산

        Args:
            tick_size: 최소 변동폭 (예: 0.00001)

        Returns:
            int: 정밀도 (소수점 자릿수)
        """
        if tick_size == 0:
            return 0

        tick_size_str = f"{tick_size:.10f}".rstrip('0').rstrip('.')
        if '.' in tick_size_str:
            return len(tick_size_str.split('.')[1])
        return 0

    def calculate_valid_quantity(self, symbol: str, quantity: float) -> float:
        """거래소 규칙에 맞는 유효한 주문 수량 계산

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 원래 계산된 수량

        Returns:
            float: 유효한 주문 수량
        """
        try:
            # 심볼 형식 표준화
            formatted_symbol = self._format_symbol(symbol)

            # DOGE 특별 처리 - 최소 1개 단위로 주문
            if formatted_symbol == 'DOGEUSDT':
                if quantity < 1.0:
                    logger.warning(f"DOGE 수량 {quantity}이 최소 주문 수량(1 DOGE)보다 작습니다. 1 DOGE로 조정합니다.")
                    quantity = 1.0
                # DOGE는 정수 단위로만 주문 가능
                adjusted_quantity = float(int(quantity))
                logger.info(f"DOGE 수량 조정: {quantity} -> {adjusted_quantity} (정수 단위)")
                return adjusted_quantity

            symbol_info = self.get_symbol_info(symbol)
            if 'error' in symbol_info:
                logger.warning(f"심볼 정보를 가져올 수 없어 원래 수량을 반환합니다: {symbol_info['error']}")
                return quantity

            # lot_size 필터 정보 가져오기
            if 'lotSize' not in symbol_info.get('filters', {}):
                logger.warning("LOT_SIZE 필터 정보를 찾을 수 없어 원래 수량을 반환합니다.")
                return quantity

            lot_size = symbol_info['filters']['lotSize']
            min_qty = lot_size.get('minQty', 0)
            step_size = lot_size.get('stepSize', 0)

            # 최소 수량 검사
            if quantity < min_qty:
                logger.warning(f"계산된 수량 {quantity}이 최소 주문 수량 {min_qty}보다 작습니다. 최소 수량으로 조정합니다.")
                quantity = min_qty

            # 수량 정밀도 조정
            if step_size > 0:
                qty_precision = symbol_info.get('quantityPrecision', self._get_precision_from_tick_size(step_size))
                # 비율 계산 후 반올림
                quantity_multiple = round(quantity / step_size)
                adjusted_quantity = quantity_multiple * step_size

                # 소수점 자릿수에 맞게 반올림
                adjusted_quantity = round(adjusted_quantity, qty_precision)

                logger.info(f"주문 수량 조정: {quantity} -> {adjusted_quantity} (stepSize: {step_size}, precision: {qty_precision})")
                return adjusted_quantity

            return quantity
        except Exception as e:
            logger.error(f"주문 수량 조정 중 오류 발생: {e}")
            return quantity

    def check_min_notional(self, symbol: str, quantity: float, price: float = None) -> bool:
        """최소 주문 금액 확인

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            quantity: 주문 수량
            price: 주문 가격 (시장가 주문인 경우 현재 가격)

        Returns:
            bool: 최소 주문 금액 충족 여부
        """
        try:
            symbol_info = self.get_symbol_info(symbol)
            if 'error' in symbol_info:
                logger.warning(f"심볼 정보를 가져올 수 없어 True를 반환합니다: {symbol_info['error']}")
                return True

            # 최소 금액 가져오기
            min_notional = symbol_info.get('filters', {}).get('minNotional', 0)
            if min_notional <= 0:
                logger.warning("최소 주문 금액 정보를 찾을 수 없어 True를 반환합니다.")
                return True

            # 가격이 제공되지 않은 경우 현재 가격 조회
            if price is None:
                price_info = self.get_futures_price(symbol)
                if 'error' in price_info:
                    logger.warning(f"현재 가격을 가져올 수 없어 True를 반환합니다: {price_info['error']}")
                    return True
                price = float(price_info['price'])

            # 주문 금액 계산
            notional = quantity * price

            # 최소 주문 금액 확인
            if notional < min_notional:
                logger.warning(f"주문 금액 {notional}이 최소 주문 금액 {min_notional}보다 작습니다.")
                return False

            return True
        except Exception as e:
            logger.error(f"최소 주문 금액 확인 중 오류 발생: {e}")
            return True  # 오류 발생 시 기본적으로 True 반환

    def get_position_mode(self) -> Dict[str, Any]:
        """현재 계정의 포지션 모드 설정 조회

        Returns:
            Dict: 포지션 모드 설정 정보
        """
        # 테스트넷 또는 API 키가 없는 경우 기본값 반환
        if self.testnet or not self.api_key or not self.api_secret:
            logger.warning("테스트넷 또는 API 키 없음. 기본값(단일 모드) 사용.")
            return {'dualSidePosition': False}

        try:
            # 캐시된 값이 있으면 사용
            if hasattr(self, '_position_mode_cache'):
                logger.info("캐시된 포지션 모드 정보 사용")
                return self._position_mode_cache

            endpoint = f"/fapi/v1/positionSide/dual"
            timestamp = int(time.time() * 1000)

            # 서명 생성
            query_string = f"timestamp={timestamp}"
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 헤더 설정
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # GET 요청 전송
            url = f"{endpoint}?{query_string}&signature={signature}"
            response = requests.get(self.FUTURES_URL + url, headers=headers)

            # 응답 코드 확인
            if response.status_code == 200:
                result = response.json()
                # 캐시에 저장
                self._position_mode_cache = result
                return result
            else:
                logger.warning(f"포지션 모드 조회 실패 (HTTP {response.status_code}): {response.text}")
                logger.warning("기본값(단일 모드) 사용.")
                return {'dualSidePosition': False}

        except Exception as e:
            error_msg = f"포지션 모드 조회 실패: {e}"
            logger.error(error_msg)
            logger.warning("기본값(단일 모드) 사용.")
            return {'dualSidePosition': False}

    def set_position_mode(self, dual_side_position: bool = False) -> Dict[str, Any]:
        """포지션 모드 설정 (단일/양방향)

        Args:
            dual_side_position: True면 양방향 포지션 모드, False면 단일 포지션 모드

        Returns:
            Dict: 설정 결과
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            endpoint = f"/fapi/v1/positionSide/dual"
            timestamp = int(time.time() * 1000)

            # 요청 파라미터
            dual_side_position_str = "true" if dual_side_position else "false"
            query_string = f"dualSidePosition={dual_side_position_str}&timestamp={timestamp}"

            # 서명 생성
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 헤더 설정
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # POST 요청 전송
            url = f"{endpoint}?{query_string}&signature={signature}"
            response = requests.post(self.FUTURES_URL + url, headers=headers)
            response.raise_for_status()

            result = response.json()

            if 'code' in result and result['code'] != 200:
                return {'error': f"포지션 모드 설정 실패: {result}"}

            return {
                'success': True,
                'message': f"포지션 모드가 {'양방향' if dual_side_position else '단일'} 모드로 설정되었습니다.",
                'dualSidePosition': dual_side_position
            }
        except Exception as e:
            error_msg = f"포지션 모드 설정 실패: {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_futures_account_balance(self) -> Union[List[Dict[str, Any]], Dict[str, str]]:
        """선물 계정 잔고 조회

        Returns:
            Union[List[Dict], Dict]: 자산별 잔고 목록 또는 오류 정보
        """
        try:
            account_info = self.get_account()

            if 'error' in account_info:
                logger.error(f"get_futures_account_balance : 계정 정보 조회 실패 : {account_info['error']}")
                return []

            # 자산별 잔고 정보 추출
            if 'assets' in account_info:
                # 모든 자산 반환 (0인 잔고도 포함)
                return account_info['assets']
            else:
                logger.error("계정 정보에서 자산 목록을 찾을 수 없습니다.")
                return []
        except Exception as e:
            error_msg = f"계정 잔고 조회 실패: {e}"
            logger.error(error_msg)
            return []

    def get_futures_price(self, symbol: str) -> Dict[str, Any]:
        """선물 시장 마크 가격 조회

        Args:
            symbol: 조회할 심볼 (e.g. 'BTC', 'ETH', 'BTCUSDT')

        Returns:
            Dict: 현재 가격 정보 {'symbol': str, 'price': float}
        """
        try:
            # 심볼 형식 표준화 (BTC -> BTCUSDT)
            formatted_symbol = self._format_symbol(symbol)

            # API 요청 파라미터
            params = {
                'symbol': formatted_symbol
            }

            # 서명된 요청 전송
            result = self.send_signed_request('GET', '/fapi/v1/ticker/price', params)
            return result
        except Exception as e:
            error_msg = f"주문 상태 조회 실패: {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def send_signed_request(self, method: str, endpoint: str, params: dict = None, retry_count: int = 3) -> Dict[str, Any]:
        """서명이 필요한 API 요청 전송

        Args:
            method: HTTP 메서드 ('GET', 'POST', 'DELETE' 등)
            endpoint: API 엔드포인트 (예: '/fapi/v1/order')
            params: 요청 파라미터
            retry_count: 재시도 횟수

        Returns:
            Dict: API 응답 또는 오류
        """
        logger.info(f"[디버그] send_signed_request 시작: 메서드={method}, 엔드포인트={endpoint}")
        logger.info(f"[디버그] 요청 파라미터: {params}")

        if not self.api_key or not self.api_secret:
            logger.error("[디버그] API 키와 시크릿이 설정되지 않았습니다.")
            return {'error': 'API keys not configured'}

        # 서버 시간 기준으로 타임스탬프 생성
        try:
            # 서버 시간 조회 (최대 3회 시도)
            for i in range(3):
                try:
                    time_url = f"/fapi/v1/time"
                    time_response = requests.get(self.FUTURES_URL + time_url, timeout=3)
                    if time_response.status_code == 200:
                        server_time = time_response.json().get('serverTime')
                        break
                except:
                    if i == 2:  # 마지막 시도에서도 실패하면 로컬 시간 사용
                        logger.warning("서버 시간 조회 실패, 로컬 시간 사용")
                        server_time = int(time.time() * 1000)
                    else:
                        time.sleep(0.5)
                        continue

            # 파라미터가 None인 경우 빈 딕셔너리로 초기화
            if params is None:
                params = {}

            # 필수 파라미터 추가
            params['timestamp'] = str(server_time)
            params['recvWindow'] = '60000'  # 60초

            # 파라미터 정렬 및 쿼리 스트링 생성
            sorted_params = dict(sorted(params.items()))
            query_string = '&'.join([f"{k}={v}" for k, v in sorted_params.items()])

            # 서명 생성 및 추가
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            query_string = f"{query_string}&signature={signature}"
            url = f"{self.FUTURES_URL}{endpoint}"

            # API 요청 헤더
            headers = {'X-MBX-APIKEY': self.api_key}

            # API 요청 정보 로깅
            logger.info(f"API 요청: {method} {url}")
            logger.info(f"파라미터: {sorted_params}")

            # 요청 전송 (재시도 로직 포함)
            for attempt in range(retry_count):
                try:
                    if method.upper() == 'GET':
                        response = requests.get(url, headers=headers, params=query_string)
                    elif method.upper() == 'POST':
                        response = requests.post(url, headers=headers, data=query_string)
                    elif method.upper() == 'DELETE':
                        response = requests.delete(url, headers=headers, params=query_string)
                    else:
                        return {'error': f'지원하지 않는 HTTP 메서드: {method}'}

                    # 응답 로깅
                    logger.info(f"API 응답 코드: {response.status_code}")
                    logger.info(f"[디버그] API 응답 헤더: {dict(response.headers)}")

                    try:
                        # 응답 내용 로깅 (최대 500자)
                        response_text = response.text[:500]
                        logger.info(f"[디버그] API 응답 내용 (최대 500자): {response_text}")
                    except Exception as e:
                        logger.warning(f"[디버그] 응답 내용 로깅 실패: {e}")

                    # 성공 응답
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            logger.info(f"[디버그] API 응답 JSON 파싱 성공: {result}")
                            return result
                        except ValueError as e:
                            logger.error(f"[디버그] API 응답 JSON 파싱 실패: {e}")
                            return {'error': f"JSON 파싱 오류: {e}"}

                    # 바이낸스 API 오류 코드 처리 (200이 아닌 경우에도 JSON 응답이 있을 수 있음)
                    try:
                        error_json = response.json()
                        logger.error(f"[디버그] API 오류 응답 (JSON): {error_json}")

                        # 바이낸스 API 오류 코드 및 메시지 추출
                        if 'code' in error_json and 'msg' in error_json:
                            error_code = error_json.get('code')
                            error_msg = error_json.get('msg')
                            logger.error(f"[디버그] 바이낸스 API 오류: 코드={error_code}, 메시지={error_msg}")

                            # 특정 오류 코드에 대한 처리
                            if error_code == -1021:  # 타임스탬프 오류
                                logger.warning("타임스탬프 오류 발생. 서버 시간과 동기화 필요.")
                            elif error_code == -2010:  # 잔액 부족
                                logger.warning("계정 잔액이 부족합니다.")
                            elif error_code == -1022:  # 서명 오류
                                logger.warning("서명 오류 발생. API 키와 시크릿을 확인하세요.")

                            return {'error': f"바이낸스 API 오류 {error_code}: {error_msg}"}
                    except ValueError:
                        # JSON 파싱 실패 시 원본 텍스트 반환
                        logger.error(f"[디버그] API 오류 응답 (텍스트): {response.text}")

                    # 일시적인 오류 (408, 503, 504)인 경우 재시도
                    if response.status_code in [408, 503, 504]:
                        logger.warning(f"일시적인 API 오류: {response.status_code}. 재시도 {attempt+1}/{retry_count}")
                        if attempt < retry_count - 1:
                            time.sleep(2 ** attempt)  # 지수 백오프
                            continue

                    # 기타 오류 처리
                    logger.error(f"[디버그] API 오류 {response.status_code}: {response.text}")
                    return {'error': f"API 오류 {response.status_code}: {response.text}"}

                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    logger.warning(f"네트워크 오류: {str(e)}. 재시도 {attempt+1}/{retry_count}")
                    if attempt < retry_count - 1:
                        time.sleep(2 ** attempt)
                        continue

                    return {'error': f"네트워크 오류: {str(e)}"}

            return {'error': '최대 재시도 횟수 초과'}

        except Exception as e:
            logger.error(f"API 요청 실패: {str(e)}")
            return {'error': f"API 요청 실패: {str(e)}"}

    def execute_futures_limit_order(self, symbol: str, side: str, quantity: float, price: float,
                             position_side: str = 'BOTH', time_in_force: str = 'GTC',
                             reduce_only: bool = False) -> Dict[str, Any]:
        """선물 지정가 주문 실행

        Args:
            symbol: 거래 심볼 (e.g. 'BTC', 'ETH', 'BTCUSDT')
            side: 주문 방향 ('BUY' 또는 'SELL')
            quantity: 주문 수량
            price: 지정 가격
            position_side: 포지션 방향 ('BOTH', 'LONG', 'SHORT')
            time_in_force: 주문 유효시간 ('GTC': 취소될 때까지, 'IOC': 즉시 체결 아니면 취소, 'FOK': 전체 체결 아니면 취소)
            reduce_only: 포지션 감소 전용 여부

        Returns:
            Dict: 주문 결과
        """
        try:
            # 심볼 형식 표준화 (BTC -> BTCUSDT)
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            # 수량 정밀도 조정
            adjusted_quantity = self.calculate_valid_quantity(symbol, quantity)

            # 심볼 정보 조회 및 가격 정밀도 조정
            symbol_info = self.get_symbol_info(symbol)
            price_precision = symbol_info.get('pricePrecision', 1)
            adjusted_price = round(price, price_precision)

            # 주문 금액 검증 (코인별 최소 주문 금액 적용)
            order_value = adjusted_quantity * adjusted_price

            # 심볼별 최소 주문 금액 설정
            symbol_min_notional = {
                'BTCUSDT': 100.0,
                'ETHUSDT': 20.0,
                'SOLUSDT': 5.0,
                'BNBUSDT': 5.0,
                'DOGEUSDT': 10.0  # DOGE 최소 주문 금액 증가
            }

            # 기본 최소 주문 금액 (모든 코인에 적용)
            default_min_notional = 5.0

            # 심볼에 맞는 최소 주문 금액 가져오기
            min_order_value = symbol_min_notional.get(formatted_symbol, default_min_notional)

            if order_value < min_order_value and not reduce_only:
                error_msg = f"주문 금액이 최소 요구사항({min_order_value} USDT)보다 작습니다: {order_value:.2f} USDT"
                logger.error(error_msg)

                # 수량 자동 조정 시도
                suggested_quantity = round(min_order_value / adjusted_price, symbol_info.get('quantityPrecision', 3))

                # stepSize에 맞게 조정
                if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                    lot_size = symbol_info['filters']['lotSize']
                    step_size = float(lot_size.get('stepSize', 0))

                    if step_size > 0:
                        quantity_multiple = round(suggested_quantity / step_size)
                        suggested_quantity = quantity_multiple * step_size
                        suggested_quantity = round(suggested_quantity, symbol_info.get('quantityPrecision', 3))

                suggested_value = suggested_quantity * adjusted_price

                # 안전을 위해 약간 더 추가 (반올림 오차 방지)
                if suggested_value < min_order_value:
                    suggested_quantity_extra = round((min_order_value * 1.01) / adjusted_price, symbol_info.get('quantityPrecision', 3))

                    # stepSize에 맞게 조정
                    if 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                        lot_size = symbol_info['filters']['lotSize']
                        step_size = float(lot_size.get('stepSize', 0))

                        if step_size > 0:
                            quantity_multiple = round(suggested_quantity_extra / step_size)
                            suggested_quantity_extra = quantity_multiple * step_size
                            suggested_quantity_extra = round(suggested_quantity_extra, symbol_info.get('quantityPrecision', 3))

                    suggested_value_extra = suggested_quantity_extra * adjusted_price

                    # 더 나은 제안 선택
                    if suggested_value_extra >= min_order_value:
                        suggested_quantity = suggested_quantity_extra
                        suggested_value = suggested_value_extra

                return {
                    'error': error_msg,
                    'suggestion': {
                        'minOrderValue': min_order_value,
                        'currentOrderValue': order_value,
                        'suggestedQuantity': suggested_quantity,
                        'suggestedOrderValue': suggested_value
                    }
                }

            # 포지션 모드 확인
            position_mode = self.get_position_mode()
            is_dual_mode = position_mode.get('dualSidePosition', False) if not isinstance(position_mode, dict) or 'error' not in position_mode else False

            # 양방향 모드에서 포지션 사이드 조정
            if is_dual_mode and position_side == 'BOTH':
                logger.warning("양방향 모드에서는 포지션 사이드를 'LONG' 또는 'SHORT'로 설정해야 합니다.")
                position_side = 'LONG' if side == 'BUY' else 'SHORT'

            # 단일 모드에서 포지션 사이드 조정
            if not is_dual_mode and position_side != 'BOTH':
                logger.warning("단일 모드에서는 포지션 사이드를 'BOTH'로 설정해야 합니다.")
                position_side = 'BOTH'

            # API 요청 파라미터 준비
            params = {
                'symbol': formatted_symbol,
                'side': side,
                'type': 'LIMIT',
                'quantity': str(adjusted_quantity),
                'price': str(adjusted_price),
                'timeInForce': time_in_force
            }

            # 양방향 모드일 때만 positionSide 추가
            if is_dual_mode:
                params['positionSide'] = position_side

            # reduceOnly 파라미터는 필요할 때만 추가
            if reduce_only:
                params['reduceOnly'] = 'true'

            # 서명 요청 전송
            result = self.send_signed_request('POST', '/fapi/v1/order', params)

            # 결과 처리 및 로깅
            if 'error' not in result:
                logger.info(f"지정가 주문 성공: {side} {adjusted_quantity} {formatted_symbol} @ {adjusted_price}")
                logger.info(f"주문 ID: {result.get('orderId')}")
            else:
                logger.error(f"지정가 주문 실패: {result['error']}")

            return result

        except Exception as e:
            error_msg = f"지정가 주문 실패: {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_futures_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """선물 주문 상태 조회

        Args:
            symbol: 거래 심볼 (e.g. 'BTC', 'ETH', 'BTCUSDT')
            order_id: 조회할 주문 ID

        Returns:
            Dict: 주문 상태 정보
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 심볼 형식 표준화 (BTC -> BTCUSDT)
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            # 주문 조회 API 호출
            endpoint = f"/fapi/v1/order"
            timestamp = int(time.time() * 1000)

            # 요청 파라미터
            params = {
                'symbol': formatted_symbol,
                'orderId': order_id,
                'timestamp': timestamp
            }

            # 파라미터를 쿼리 스트링으로 변환
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])

            # 서명 생성
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 헤더 설정
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # GET 요청 전송
            url = f"{endpoint}?{query_string}&signature={signature}"
            response = requests.get(self.FUTURES_URL + url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            error_msg = f"주문 조회 실패: {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def cancel_futures_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """선물 주문 취소

        Args:
            symbol: 거래 심볼 (e.g. 'BTC', 'ETH', 'BTCUSDT')
            order_id: 취소할 주문 ID

        Returns:
            Dict: 취소 결과
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 심볼 형식 표준화 (BTC -> BTCUSDT)
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            # 주문 취소 API 호출
            endpoint = f"/fapi/v1/order"
            timestamp = int(time.time() * 1000)

            # 요청 파라미터
            params = {
                'symbol': formatted_symbol,
                'orderId': order_id,
                'timestamp': timestamp
            }

            # 파라미터를 쿼리 스트링으로 변환
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])

            # 서명 생성
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 헤더 설정
            headers = {
                'X-MBX-APIKEY': self.api_key
            }

            # DELETE 요청 전송
            url = f"{endpoint}?{query_string}&signature={signature}"
            response = requests.delete(self.FUTURES_URL + url, headers=headers)
            response.raise_for_status()

            result = response.json()
            logger.info(f"주문 취소 성공: {result.get('orderId')}")

            return result
        except Exception as e:
            error_msg = f"주문 취소 실패: {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_funding_rate(self, symbol: str):
        """펀딩 비율 정보 조회

        Args:
            symbol: 심볼 (예: 'BTC', 'BTC/USDT', 'BTCUSDT')

        Returns:
            Dict: 펀딩 정보
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return {'error': 'Invalid symbol'}

            endpoint = f"/fapi/v1/premiumIndex"
            params = {'symbol': formatted_symbol}
            response = requests.get(self.FUTURES_URL + endpoint, params=params)
            response.raise_for_status()
            data = response.json()

            # API가 단일 객체 또는 객체 목록을 반환할 수 있음
            if isinstance(data, list) and len(data) > 0:
                # 목록에서 첫 번째 항목 반환
                return data[0]
            return data
        except Exception as e:
            error_msg = f"펀딩 비율 조회 실패 ({symbol}): {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_current_price(self, symbol: str) -> Dict:
        """
        현재 가격 조회 (퓨처스)

        Args:
            symbol: 심볼 (예: BTCUSDT 또는 BTC/USDT)

        Returns:
            Dict: 현재 가격 정보가 담긴 딕셔너리, 실패 시 None 반환
        """
        # 심볼 형식 변환 (BTC/USDT -> BTCUSDT)
        formatted_symbol = self._format_symbol(symbol)
        if not formatted_symbol:
            logger.error(f"유효하지 않은 심볼: {symbol}")
            return None

        try:
            logger.info(f"현재 가격 요청: {formatted_symbol} (원본 심볼: {symbol})")

            endpoint = f"/fapi/v1/ticker/price"
            params = {'symbol': formatted_symbol}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, timeout=10)
            response.raise_for_status()
            price_data = response.json()

            result = {
                'symbol': symbol,  # 원본 심볼 유지
                'formatted_symbol': formatted_symbol,  # 포맷팅된 심볼 추가
                'price': float(price_data['price']),
                'time': datetime.now()  # 퓨처스 API는 타임스탬프를 제공하지 않음
            }

            logger.info(f"현재 가격 조회 성공: {formatted_symbol}, 가격: {result['price']}")
            return result

        except Exception as e:
            logger.error(f"{formatted_symbol} 가격 조회 중 오류 발생: {e}")
            return None

    def get_futures_price_simple(self, symbol: str) -> float:
        """퓨처스 현재가 조회 (간단 버전)

        Args:
            symbol: 심볼 (예: BTC, ETH, BTC/USDT)

        Returns:
            float: 현재 가격, 실패 시 None
        """
        # 기존 get_futures_price 메서드 호출
        price_data = self.get_futures_price(symbol)
        if price_data and 'price' in price_data:
            return price_data['price']
        return None

    def create_futures_order(self, symbol, side, order_type, quantity=None, price=None, **kwargs):
        """
        선물 거래 주문 생성

        Args:
            symbol (str): 거래쌍 심볼 (예: BTCUSDT)
            side (str): 'BUY' 또는 'SELL'
            order_type (str): 주문 타입 ('MARKET', 'LIMIT', 'STOP', 'TAKE_PROFIT' 등)
            quantity (float, optional): 주문 수량
            price (float, optional): 주문 가격 (리밋 주문에 필요)
            **kwargs: 추가 파라미터

        Returns:
            dict: 주문 정보
        """
        try:
            # 요청 전 로깅
            logger.info(f"바이낸스 퓨처스 주문 생성: {symbol} {side} {order_type}")

            # 수량 정밀도 조정
            if quantity is not None:
                # calculate_valid_quantity 메서드를 사용하여 바이낸스 API 규칙에 맞게 수량 조정
                quantity = self.calculate_valid_quantity(symbol, quantity)
                logger.info(f"주문 수량 조정됨: {quantity} {symbol}")

            # 파라미터 설정
            params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                **kwargs
            }

            if quantity is not None:
                params['quantity'] = quantity

            if price is not None:
                # 심볼 정보 조회
                symbol_info = self.get_symbol_info(symbol)
                if 'error' not in symbol_info and 'pricePrecision' in symbol_info:
                    price_precision = symbol_info['pricePrecision']
                    # 소수점 자릿수에 맞게 반올림
                    price = round(price, price_precision)

                params['price'] = price

            # 선물 API 엔드포인트 사용
            try:
                endpoint = f"/fapi/v1/order"
                headers = {'X-MBX-APIKEY': self.api_key}

                # 서명된 파라미터 가져오기
                signed_params = self._get_signed_params(params)

                # POST 요청 전송
                response = requests.post(self.FUTURES_URL + endpoint, headers=headers, params=signed_params)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                logger.error(f"Failed to create futures order for {symbol}: {e}")
                return {'error': str(e)}
        except Exception as e:
            logger.error(f"Failed to create futures order for {symbol}: {e}")
            return {'error': str(e)}

    def set_leverage(self, symbol: str, leverage: int) -> Dict:
        """퓨처스 레버리지 설정

        Args:
            symbol: 거래 심볼 (예: BTCUSDT)
            leverage: 레버리지 (1-125)

        Returns:
            Dict: API 응답
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            endpoint = f"/fapi/v1/leverage"

            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)

            params = {
                'symbol': formatted_symbol,
                'leverage': leverage
            }

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.post(self.FUTURES_URL + endpoint, params=params, headers=headers)
            logger.info(f"{symbol} 레버리지 설정 응답: {response.status_code}")

            response.raise_for_status()
            result = response.json()

            logger.info(f"{symbol} 레버리지 {leverage}배로 설정됨")
            return result

        except Exception as e:
            logger.error(f"{symbol} 레버리지 설정 실패: {e}")
            return {'error': str(e)}

    def set_margin_type(self, symbol: str, margin_type: str) -> Dict:
        """퓨처스 마진 타입 설정 (ISOLATED 또는 CROSSED)

        Args:
            symbol: 거래 심볼 (예: BTCUSDT)
            margin_type: 마진 타입 ('ISOLATED' 또는 'CROSSED')

        Returns:
            Dict: API 응답
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        if margin_type not in ['ISOLATED', 'CROSSED']:
            logger.error(f"유효하지 않은 마진 타입: {margin_type}. 'ISOLATED' 또는 'CROSSED'만 허용됩니다.")
            return {'error': 'Invalid margin type'}

        try:
            endpoint = f"/fapi/v1/marginType"

            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)

            params = {
                'symbol': formatted_symbol,
                'marginType': margin_type
            }

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.post(self.FUTURES_URL + endpoint, params=params, headers=headers)
            logger.info(f"{symbol} 마진 타입 설정 응답: {response.status_code}")

            response.raise_for_status()
            result = response.json()

            logger.info(f"{symbol} 마진 타입 {margin_type}로 설정됨")
            return result

        except Exception as e:
            logger.error(f"{symbol} 마진 타입 설정 실패: {e}")
            return {'error': str(e)}

    def get_position_information(self, symbol: str = None) -> List[Dict]:
        """퓨처스 포지션 정보 조회

        Args:
            symbol: 거래 심볼 (선택적, None이면 모든 포지션 조회)

        Returns:
            List[Dict]: 포지션 정보 목록
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return [{'error': 'API keys not configured'}]

        try:
            endpoint = f"/fapi/v2/positionRisk"

            params = {}
            if symbol:
                # 심볼 포맷팅 정리
                formatted_symbol = self._format_symbol(symbol)
                params['symbol'] = formatted_symbol

            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            response = requests.get(self.FUTURES_URL + endpoint, params=params, headers=headers)
            logger.info(f"포지션 정보 조회 응답: {response.status_code}")

            response.raise_for_status()
            positions = response.json()

            return positions

        except Exception as e:
            logger.error(f"포지션 정보 조회 실패: {e}")
            return [{'error': str(e)}]

    def create_order(self, symbol: str, side: str, order_type: str, quantity: float = None,
                  price: float = None, time_in_force: str = 'GTC',
                  reduce_only: bool = False, close_position: bool = False) -> Dict:
        """퓨처스 주문 생성

        Args:
            symbol: 거래 심볼 (예: BTC/USDT)
            side: 거래 방향 ('BUY' 또는 'SELL')
            order_type: 주문 유형 ('MARKET', 'LIMIT', 'STOP', 'TAKE_PROFIT' 등)
            quantity: 수량
            price: 가격 (LIMIT 주문에서 필수)
            time_in_force: 주문 유효시간 ('GTC': 취소될 때까지, 'IOC': 즉시 체결 아니면 취소, 'FOK': 전체 체결 아니면 취소)
            reduce_only: 포지션 감소 전용 여부
            close_position: 포지션 청산 주문 여부

        Returns:
            Dict: 주문 응답 정보
        """
        if not self.api_key or not self.api_secret:
            logger.error("API 키와 시크릿이 필요합니다.")
            return {'error': 'API keys not configured'}

        try:
            # 퓨처스 API 주문 엔드포인트 사용
            endpoint = f"/fapi/v1/order"

            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)

            # 기본 주문 파라미터 설정
            params = {
                'symbol': formatted_symbol,
                'side': side,
                'type': order_type
            }

            # 수량 설정 (MARKET 주문에는 필수)
            if quantity and not close_position:
                params['quantity'] = f"{quantity}"

            # 가격 설정 (LIMIT 주문에는 필수)
            if price and order_type != 'MARKET':
                params['price'] = f"{price}"

            # 시간 제한 설정 (LIMIT 주문에 필수)
            if order_type == 'LIMIT':
                params['timeInForce'] = time_in_force

            # reduceOnly 파라미터 설정 (필요시)
            if reduce_only:
                params['reduceOnly'] = 'true'

            # closePosition 파라미터 설정 (필요시)
            if close_position:
                params['closePosition'] = 'true'

            # 서명된 파라미터 생성
            params = self._get_signed_params(params)
            headers = {'X-MBX-APIKEY': self.api_key}

            # 주문 요청 전송 (POST)
            logger.info(f"퓨처스 주문 생성: {formatted_symbol} {side} {order_type} {quantity if quantity else 'close'}")
            response = requests.post(self.FUTURES_URL + endpoint, params=params, headers=headers)

            # 응답 상태 및 내용 로깅
            logger.info(f"주문 응답 상태: {response.status_code}")
            logger.info(f"주문 응답 내용: {response.text[:200]}")

            response.raise_for_status()
            order_response = response.json()

            logger.info(f"주문 생성 성공: {order_response.get('orderId')}")
            return order_response

        except Exception as e:
            logger.error(f"{symbol} 주문 생성 실패: {e}")
            return {'error': str(e)}

    def get_klines(self, symbol: str, interval: str = '1d', limit: int = 30, start_time: int = None, end_time: int = None) -> List[Dict[str, Any]]:
        """
        바이낸스 선물 K라인(캔들스틱) 데이터 조회

        Args:
            symbol (str): 심볼 (예: BTCUSDT)
            interval (str): 인터벌 (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            limit (int): 반환할 캔들 수 (최대 1500, 기본값 30)
            start_time (int): 시작 타임스탬프 (밀리초)
            end_time (int): 종료 타임스탬프 (밀리초)

        Returns:
            List[Dict]: 캔들스틱 데이터 목록
                [
                    {
                        "open_time": 타임스탬프(ms),
                        "open": 시가,
                        "high": 고가,
                        "low": 저가,
                        "close": 종가,
                        "volume": 거래량,
                        "close_time": 종료 타임스탬프(ms),
                        "quote_volume": 기준 화폐 거래량,
                        "trades": 거래 수,
                        "taker_buy_vol": 테이커 매수 거래량,
                        "taker_buy_quote_vol": 테이커 매수 기준 화폐 거래량
                    },
                    ...
                ]
        """
        try:
            # 심볼 포맷팅 정리
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                return []

            endpoint = "/fapi/v1/klines"
            params = {
                "symbol": formatted_symbol,
                "interval": interval,
                "limit": min(limit, 1500)  # 최대 1500개 제한
            }

            # 시작/종료 시간 설정 (있는 경우)
            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time

            logger.info(f"{symbol} 캔들스틱 데이터 요청: 인터벌={interval}, 개수={limit}")

            result = self._request("GET", endpoint, params=params)

            # 응답 형식 변환 (사용하기 쉬운 딕셔너리 구조로)
            formatted_data = []
            for candle in result[:-1]:  # 미완성 캔들 제외
                formatted_data.append({
                    "open_time": candle[0],
                    "open": float(candle[1]),
                    "high": float(candle[2]),
                    "low": float(candle[3]),
                    "close": float(candle[4]),
                    "volume": float(candle[5]),
                    "close_time": candle[6],
                    "quote_volume": float(candle[7]),
                    "trades": candle[8],
                    "taker_buy_vol": float(candle[9]),
                    "taker_buy_quote_vol": float(candle[10])
                })

            logger.info(f"{symbol} 캔들스틱 데이터 {len(formatted_data)}개 수신 완료")
            return formatted_data

        except Exception as e:
            logger.error(f"{symbol} 캔들스틱 데이터 요청 실패: {str(e)}")
            raise ValueError(f"캔들스틱 데이터 조회 실패: {str(e)}")

    def get_market_data(self, symbol: str, interval: str = '1d', days: int = 7) -> Dict[str, Any]:
        """
        시장 데이터 종합 조회 (InCA 에이전트에서 사용하기 위한 형식)

        Args:
            symbol (str): 심볼 (예: BTCUSDT)
            interval (str): 인터벌 (1m, 15m, 1h, 4h, 1d 등)
            days (int): 가져올 데이터 일수

        Returns:
            Dict: 다음 정보를 포함한 시장 데이터
                - prices: 종가 리스트
                - volumes: 거래량 리스트
                - highs: 고가 리스트
                - lows: 저가 리스트
                - timestamps: 타임스탬프 리스트
                - volatility: 변동성 지표
                - rsi_14: 14일 RSI 지표
                - current_price: 현재 가격
        """
        try:
            # 캔들 개수 계산 (일일 데이터 기준으로 변환)
            interval_to_day_multiplier = {
                '1m': 60*24, '3m': 20*24, '5m': 12*24, '15m': 4*24, '30m': 2*24,
                '1h': 24, '2h': 12, '4h': 6, '6h': 4, '8h': 3, '12h': 2,
                '1d': 1, '3d': 1/3, '1w': 1/7, '1M': 1/30
            }

            # 인터벌에 맞게 캔들 개수 계산
            multiplier = interval_to_day_multiplier.get(interval, 1)
            limit = math.ceil(days * multiplier)

            # 필요한 데이터보다 여유있게 가져오기 (최대 1500개 제한)
            limit = min(limit + 50, 1500)

            # 캔들스틱 데이터 가져오기
            candles = self.get_klines(symbol, interval, limit)

            if not candles:
                error_msg = f"{symbol} 시장 데이터를 가져올 수 없습니다."
                logger.error(error_msg)
                raise ValueError(error_msg)

            # 데이터 추출 (바이낸스 API는 리스트 형태로 데이터 반환)
            # 바이낸스 캔들스틱 데이터 형식:
            # [open_time, open, high, low, close, volume, close_time, quote_volume, trades, taker_buy_base, taker_buy_quote, ignore]
            timestamps = [int(c[0]/1000) for c in candles]  # 초 단위로 변환
            opens = [float(c[1]) for c in candles]
            highs = [float(c[2]) for c in candles]
            lows = [float(c[3]) for c in candles]
            closes = [float(c[4]) for c in candles]
            volumes = [float(c[5]) for c in candles]
            quote_volumes = [float(c[7]) for c in candles]  # 기준 화폐(USDT) 거래량

            # 디버깅: 거래량 데이터 확인
            logger.info(f"{symbol} 거래량 데이터 (첫번째/마지막 포함): base_vol={volumes[0]:.2f}/{volumes[-1]:.2f}, quote_vol={quote_volumes[0]:.2f}/{quote_volumes[-1]:.2f}")

            # 마지막 미완성 캔들은 거래량이 0일 가능성이 높으므로 제외
            if len(timestamps) > 1:
                # 마지막 캔들 제외
                timestamps = timestamps[:-1]
                closes = closes[:-1]
                opens = opens[:-1]
                highs = highs[:-1]
                lows = lows[:-1]
                volumes = volumes[:-1]
                quote_volumes = quote_volumes[:-1]

                # 거래량이 0이 아닌 가장 최근 캔들 찾기
                valid_candle_index = -1
                for i in range(len(volumes)-1, -1, -1):
                    if volumes[i] > 0 and quote_volumes[i] > 0:
                        valid_candle_index = i
                        break

                if valid_candle_index >= 0:
                    # 유효한 거래량이 있는 캔들을 찾았음
                    logger.info(f"{symbol} 유효한 거래량이 있는 캔들 찾음: 인덱스={valid_candle_index}, base_vol={volumes[valid_candle_index]:.2f}, quote_vol={quote_volumes[valid_candle_index]:.2f}")

                    # 추가 검증: 최소 5개의 유효한 캔들이 있는지 확인
                    valid_candle_count = 0
                    for i in range(len(volumes)):
                        if volumes[i] > 0 and quote_volumes[i] > 0:
                            valid_candle_count += 1

                    if valid_candle_count < 3:
                        logger.error(f"{symbol} 유효한 거래량 데이터가 있는 캔들이 부족합니다: {valid_candle_count}개 (최소 3개 필요)")
                        raise ValueError(f"{symbol} 유효한 거래량 데이터가 있는 캔들이 부족합니다: {valid_candle_count}개")
                    elif valid_candle_count < 5:
                        logger.warning(f"{symbol} 유효한 거래량 데이터가 있는 캔들이 부족하지만 진행: {valid_candle_count}개 (권장: 5개 이상)")

                    # 해당 캔들까지의 데이터만 사용
                    timestamps = timestamps[:valid_candle_index+1]
                    closes = closes[:valid_candle_index+1]
                    opens = opens[:valid_candle_index+1]
                    highs = highs[:valid_candle_index+1]
                    lows = lows[:valid_candle_index+1]
                    volumes = volumes[:valid_candle_index+1]
                    quote_volumes = quote_volumes[:valid_candle_index+1]
                else:
                    # 유효한 거래량이 있는 캔들이 없음
                    raise ValueError(f"{symbol} 유효한 거래량 데이터가 있는 캔들을 찾을 수 없습니다")
            else:
                # 캔들이 하나뿐인 경우
                if volumes[0] == 0 or quote_volumes[0] == 0:
                    raise ValueError(f"{symbol} 충분한 캔들스틱 데이터가 없습니다: {len(timestamps)}개")

            # 가격 변화율 계산 (일일 기준)
            daily_changes = []
            for i in range(1, len(closes)):
                daily_changes.append((closes[i] - closes[i-1]) / closes[i-1] * 100)

            # 변동성 계산 (일일 변화율의 표준편차)
            volatility = 0
            if len(daily_changes) > 0:
                import numpy as np
                volatility = np.std(daily_changes)

            # RSI 계산 (14일)
            rsi_14 = self._calculate_rsi(closes, 14)

            # 결과 구성
            result = {
                "timestamps": timestamps,
                "prices": closes,
                "opens": opens,
                "highs": highs,
                "lows": lows,
                "volumes": volumes,
                "quote_volumes": quote_volumes,  # 기준 화폐(USDT) 거래량
                "current_price": closes[-1],
                "daily_changes": daily_changes,
                "volatility": volatility,
                "rsi_14": rsi_14,
                "interval": interval,
                "days": days
            }

            # 🔥 recent_candles 필드 추가 (InCAAgent에서 사용)
            recent_candles = []
            for i in range(len(timestamps)):
                candle = {
                    "timestamp": timestamps[i],
                    "open": opens[i],
                    "high": highs[i],
                    "low": lows[i],
                    "close": closes[i],
                    "volume": volumes[i]
                }
                recent_candles.append(candle)

            # 최근 20개 캔들만 유지 (메모리 절약 및 성능 향상)
            if len(recent_candles) > 20:
                recent_candles = recent_candles[-20:]

            result['recent_candles'] = recent_candles  # InCAAgent가 사용하는 필드
            result['candles'] = recent_candles  # 별칭 제공
            result['symbol'] = symbol  # 심볼 매핑 디버깅용

            # 🔥 SELA가 기대하는 price 필드 추가
            result['price'] = result['current_price']  # SELA 호환성을 위한 별칭

            return result

        except Exception as e:
            logger.error(f"{symbol} 시장 데이터 종합 조회 실패: {str(e)}")
            raise ValueError(f"시장 데이터 종합 조회 실패: {str(e)}")

    def _calculate_rsi(self, prices, period=14):
        """RSI(Relative Strength Index) 계산

        Args:
            prices: 가격 목록 (종가)
            period: RSI 기간 (기본값 14)

        Returns:
            float: 최신 RSI 값 (0-100)
        """
        if len(prices) < period + 1:
            return 50  # 충분한 데이터가 없으면 중립값 반환

        # 가격 변화 계산
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]

        # 상승/하락 분리
        gains = [delta if delta > 0 else 0 for delta in deltas]
        losses = [-delta if delta < 0 else 0 for delta in deltas]

        # 초기 평균 계산
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period

        # 나머지 기간에 대한 평균 계산
        for i in range(period, len(deltas)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period

        # RS 및 RSI 계산
        if avg_loss == 0:
            return 100  # 손실이 없으면 RSI는 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _format_symbol(self, symbol: str) -> str:
        """심볼 형식을 바이낸스 API 형식으로 변환하는 통합 유틸리티 함수

        Args:
            symbol: 변환할 심볼 (예: 'BTC', 'BTC/USDT', 'BTC-USDT', 'BTCUSDT')

        Returns:
            str: 바이낸스 API 형식의 심볼 (예: 'BTCUSDT')
        """
        # 심볼이 None이거나 빈 문자열이면 None 반환
        if not symbol:
            return None

        formatted_symbol = symbol

        # 슬래시(/)가 포함된 경우 처리
        if '/' in symbol:
            parts = symbol.split('/')
            base = parts[0]
            quote = parts[1] if len(parts) > 1 and parts[1] else "USDT"

            # base에서 USDT 제거 (예: BTCUSDT/USDT -> BTC/USDT)
            if "USDT" in base:
                base = base.replace("USDT", "")

            # quote가 이미 USDT로 끝나는지 확인 (예: BTC/USDTUSDT -> BTC/USDT)
            if quote != "USDT" and quote.endswith("USDT"):
                quote = "USDT"

            formatted_symbol = f"{base}{quote}"
            logger.info(f"Converting symbol {symbol} to Binance format: {formatted_symbol}")
        # 슬래시가 없지만 USDT로 끝나지 않는 경우
        elif not symbol.endswith('USDT'):
            formatted_symbol = f"{symbol}USDT"
            logger.info(f"Converting symbol {symbol} to Binance format: {formatted_symbol}")

        return formatted_symbol

    # ===== 시장 데이터 관련 메소드 추가 =====

    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """심볼의 24시간 가격 변화 데이터 조회

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')

        Returns:
            Dict: 티커 정보
        """
        try:
            # 심볼 포맷팅
            if not symbol:
                return {'error': 'Symbol is required'}

            # _format_symbol 메서드를 사용하여 일관된 형식으로 변환
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                logger.error(f"유효하지 않은 심볼: {symbol}")
                return {'error': f'Invalid symbol: {symbol}'}

            # 올바른 퓨처스 엔드포인트 사용
            endpoint = '/fapi/v1/ticker/24hr'
            params = {'symbol': formatted_symbol}

            logger.info(f"퓨처스 티커 요청: {self.FUTURES_URL}{endpoint}?symbol={formatted_symbol} (원본 심볼: {symbol})")

            # GET 요청 (서명 불필요)
            response = requests.get(self.FUTURES_URL + endpoint, params=params, timeout=10)
            response.raise_for_status()
            ticker = response.json()
            logger.info(f"티커 데이터 조회 성공: {formatted_symbol}, 필드: {list(ticker.keys())}")

            # 필드 검증
            if 'priceChangePercent' in ticker:
                logger.info(f"변화율(%): {ticker['priceChangePercent']}")
            if 'volume' in ticker:
                logger.info(f"거래량: {ticker['volume']}")

            return ticker

        except Exception as e:
            error_msg = f"티커 데이터 조회 실패 ({symbol}): {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_last_price(self, symbol: str) -> Dict[str, Any]:
        """심볼의 최신 가격 조회

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')

        Returns:
            Dict: 가격 정보
        """
        try:
            # 심볼 포맷팅
            if not symbol:
                return {'error': 'Symbol is required'}

            # _format_symbol 메서드를 사용하여 일관된 형식으로 변환
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                logger.error(f"유효하지 않은 심볼: {symbol}")
                return {'error': f'Invalid symbol: {symbol}'}

            endpoint = '/fapi/v1/ticker/price'
            params = {'symbol': formatted_symbol}

            logger.info(f"최신 가격 요청: {self.FUTURES_URL}{endpoint}?symbol={formatted_symbol} (원본 심볼: {symbol})")

            # GET 요청 (서명 불필요)
            response = requests.get(self.FUTURES_URL + endpoint, params=params, timeout=10)
            response.raise_for_status()
            price_data = response.json()

            result = {
                'symbol': symbol,  # 원본 심볼 유지
                'formatted_symbol': formatted_symbol,  # 포맷팅된 심볼 추가
                'price': float(price_data['price'])
            }

            logger.info(f"최신 가격 조회 성공: {formatted_symbol}, 가격: {result['price']}")
            return result

        except Exception as e:
            error_msg = f"최신 가격 조회 실패 ({symbol}): {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def get_klines(self, symbol: str, interval: str, limit: int = 500, start_time: int = None, end_time: int = None) -> List:
        """캔들스틱(K라인) 데이터 조회

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            interval: 시간 간격 (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            limit: 반환할 캔들 수 (최대 1500)
            start_time: 시작 시간 (밀리초 타임스탬프)
            end_time: 종료 시간 (밀리초 타임스탬프)

        Returns:
            List: 캔들스틱 데이터 목록
        """
        try:
            # 심볼 포맷팅
            if not symbol:
                return []

            # _format_symbol 메서드를 사용하여 일관된 형식으로 변환
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                logger.error(f"유효하지 않은 심볼: {symbol}")
                return []

            endpoint = "/fapi/v1/klines"
            params = {
                "symbol": formatted_symbol,
                "interval": interval,
                "limit": min(limit, 1500)  # 최대 1500개로 제한
            }

            # 선택적 파라미터 추가
            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time

            logger.info(f"캔들스틱 데이터 요청: {formatted_symbol} (원본 심볼: {symbol}), 인터벌={interval}, 개수={limit}")

            # GET 요청 (서명 불필요)
            response = requests.get(self.FUTURES_URL + endpoint, params=params, timeout=10)
            response.raise_for_status()

            klines = response.json()
            logger.info(f"캔들스틱 데이터 조회 성공: {formatted_symbol}, 간격: {interval}, 개수: {len(klines)}")
            return klines

        except Exception as e:
            error_msg = f"캔들스틱 데이터 조회 실패 ({symbol}, {interval}): {str(e)}"
            logger.error(error_msg)
            return []

    def get_market_data(self, symbol: str, interval: str = '1d', days: int = 1) -> Dict[str, Any]:
        """
        시장 데이터 가져오기 (가격, 거래량, 변동률 등)

        Args:
            symbol: 거래 심볼 (예: 'BTC', 'BTCUSDT')
            interval: 캔들 간격 (예: '1m', '1h', '1d')
            days: 데이터 기간 (일)

        Returns:
            Dict: 시장 데이터
        """
        try:
            # 심볼 포맷팅
            formatted_symbol = self._format_symbol(symbol)
            if not formatted_symbol:
                logger.error(f"유효하지 않은 심볼: {symbol}")
                return {'error': f'Invalid symbol: {symbol}'}

            logger.info(f"시장 데이터 요청: {formatted_symbol} (원본 심볼: {symbol})")

            # 현재 가격 조회
            price_info = self.get_last_price(symbol)  # 이미 _format_symbol을 내부적으로 사용
            if 'error' in price_info:
                return price_info

            # 24시간 통계 조회
            ticker = self.get_ticker(symbol)  # 이미 _format_symbol을 내부적으로 사용
            if 'error' in ticker:
                return ticker

            # 캔들스틱 데이터 조회
            end_time = int(time.time() * 1000)
            start_time = end_time - (days * 24 * 60 * 60 * 1000)
            klines = self.get_klines(symbol, interval, limit=500, start_time=start_time, end_time=end_time)  # 이미 _format_symbol을 내부적으로 사용

            # 결과 구성
            result = {
                'symbol': symbol,
                'formatted_symbol': formatted_symbol,
                'price': price_info['price'],
                'lastPrice': price_info['price'],
                'change_24h': float(ticker.get('priceChangePercent', 0)),
                'volume_24h': float(ticker.get('volume', 0)),
                'high_24h': float(ticker.get('highPrice', 0)),
                'low_24h': float(ticker.get('lowPrice', 0)),
                'timestamp': int(time.time())
            }

            # 가격 및 거래량 데이터 추가
            if klines:
                prices = []
                volumes = []
                timestamps = []

                for kline in klines:
                    timestamps.append(kline[0])  # 시간
                    prices.append(float(kline[4]))  # 종가
                    volumes.append(float(kline[5]))  # 거래량

                result['prices'] = prices
                result['volumes'] = volumes
                result['timestamps'] = timestamps

            logger.info(f"시장 데이터 조회 성공: {formatted_symbol}, 가격: {result['price']}")
            return result

        except Exception as e:
            error_msg = f"시장 데이터 조회 실패 ({symbol}): {str(e)}"
            logger.error(error_msg)
            return {'error': error_msg}

    def test_connectivity(self):
        """
        바이낸스 API 연결 상태 테스트

        Returns:
            bool: 연결 성공 여부
        """
        try:
            endpoint = "/fapi/v1/ping"
            # 결과를 사용하지 않으므로 변수에 할당하지 않음
            self._request("GET", endpoint, signed=False)
            logger.info("바이낸스 API 연결 테스트 성공")
            return True
        except Exception as e:
            logger.error(f"바이낸스 API 연결 테스트 실패: {str(e)}")
            return False
            
    def get_current_price(self, symbol):
        """
        현재 가격 조회

        Args:
            symbol: 심볼 (예: 'BTC', 'BTCUSDT')

        Returns:
            float: 현재 가격
        """
        try:
            endpoint = "/fapi/v1/ticker/price"
            params = {}
            if symbol:
                # _format_symbol 메서드를 사용하여 일관된 형식으로 변환
                formatted_symbol = self._format_symbol(symbol)
                if not formatted_symbol:
                    logger.error(f"유효하지 않은 심볼: {symbol}")
                    return None

                params["symbol"] = formatted_symbol
                logger.info(f"가격 조회 심볼: {symbol} -> {formatted_symbol}")

            result = self._request("GET", endpoint, params=params, signed=False)

            # 단일 심볼 요청과 전체 요청 처리 구분
            if isinstance(result, list):
                # 모든 심볼에 대한 가격 정보
                for item in result:
                    if item.get("symbol") == formatted_symbol:
                        return float(item.get("price", 0))
                return None
            else:
                # 단일 심볼에 대한 가격 정보
                return float(result.get("price", 0))

        except Exception as e:
            logger.error(f"{symbol} 현재 가격 조회 실패: {str(e)}")
            return None