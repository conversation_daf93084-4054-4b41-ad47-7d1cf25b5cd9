{"execution_id": "exec_aef583f8_1750957064", "symbol": "DOGE", "timestamp": 1750957064, "datetime": "2025-06-27 01:57:44", "type": "execution_result", "data": {"symbol": "DOGE", "strategy_id": "9c79b979-b506-48ce-aba4-6391d2ebee00", "timestamp": 1750957064, "market_data": {"id": "market_DOGE_1750957042", "symbol": "DOGE", "timestamp": 1750957042, "datetime": "2025-06-27 01:57:22", "date": "2025-06-27", "time": "01:57:22", "price": 0.15936, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.15934, "volume": 5109907114.0, "volume_24h": 5109907114.0, "high_24h": 0.16809, "low_24h": 0.15782, "percent_change_24h": -2.627, "volatility": 0.0, "rsi": 50.0, "average_sentiment": 0.5, "sentiment_score": 0.5, "social_volume": 8468431, "social_dominance": 0.43, "social_contributors": 84684, "bullish_sentiment": 0.5, "bearish_sentiment": 0.5, "data_source": "binance_api", "is_real_data": true, "has_news": true, "execution_timestamp": 1750957042, "news_count": 48, "ema_7": 0.15951142857142855, "ema_14": 0.1596042857142857, "ema_25": 0.159678, "ema_50": 0.15983119999999998, "ema_99": 0.15988767676767682, "ema_200": 0.16161195000000003, "news": [{"title": "Bitcoin Price Surges", "content": "Bitcoin price surges to new high.", "sentiment": 0.8}, {"title": "Ethereum Price Drops", "content": "Ethereum price drops to new low.", "sentiment": 0.2}], "news_sentiment": 3.0454166666666667, "post_count": 100, "bullish_ratio": 0.0, "bearish_ratio": 0, "galaxy_score": 0, "alt_rank": 0, "market_cap": 23908984392.16, "recent_news_titles": ["Musk Confidant A<PERSON><PERSON> Leaves Tesla in Latest High-Level Exit", "Crypto Price Analysis 6-26: BITCOIN: BTC, ETHEREUM: ETH, SOLANA: SOL, DOGECOIN: DOGE, POLKADOT: DOT, FILECOIN: FIL - Crypto Daily", "Trading ETH, SOL, DOGE and XRP May Be Easier on Bitget Than Binance, CoinGeckoFinds", "Salesforce CEO Says 30% of Internal Work Is Being Handled by AI", "Palant<PERSON>llers Bail as Top S&P 500 Stock Keeps Climbing"], "top_social_posts": [{"text": "Probably 98% of GOP voters still don't understand: the GOP hates <PERSON> and won't back him in Congress.\n\nThat's why they won't pass his legislative agenda or enact the DOGE cuts or impeach Dem judges who block his orders.\n \nThe GOP is just waiting him out for the next four years.", "sentiment": 2.75, "platform": "tweet"}, {"text": "The wife of <PERSON><PERSON><PERSON> says, \"We will not stop until my husband is free.\"\n\n", "sentiment": 3, "platform": "tweet"}, {"text": "Democrats are the biggest hypocrites... its unreal.\n\n", "sentiment": 2.67, "platform": "tweet"}, {"text": "Balance the budget. \n\nIt shouldn’t be up for discussion. \n\nIf we take in 4.3 TRILLION in tax revenue then we can only spend 4.3 TRILLION. \n\nIf us regular people did this, we would be in jail for printing money, but Congress thinks that they have a magic money tree in their backyard. \n\nRepublicans have pushed back against every spending cut starting from DOGE. \n\nAlmost feels like they want to lose the midterms.", "sentiment": 3.05, "platform": "tweet"}, {"text": "Thousands of Muslims pray in Times Square, turning it into an open-air mosque.\n\n", "sentiment": 3.11, "platform": "tweet"}, {"text": "American Airlines flight turns back to Las Vegas after engine catches fire midair.\n\nThat's scary.\n\n", "sentiment": 2.7, "platform": "tweet"}, {"text": "<PERSON> NOW REMOVED From Congress After THREATENING First Lady <PERSON> At DOGE Hearing", "sentiment": 3.36, "platform": "youtube-video"}, {"text": "Nice smack down!\n\n", "sentiment": 3.75, "platform": "tweet"}, {"text": "President <PERSON>: \"Look at Paris. Look at London. They’re no longer recognizable. We can’t let that happen to our country.\"\n\n", "sentiment": 3, "platform": "tweet"}, {"text": "“Y’all Are in a Cult” — <PERSON><PERSON>ett Destroys the DOGE Committee’s Spin", "sentiment": 2.9, "platform": "youtube-video"}], "recent_candles": [[1750955880000, "0.159860", "0.159940", "0.159850", "0.159900", "1121230", 1750955939999, "179290.634400", 546, "743400", "118875.776490", "0"], [1750955940000, "0.159900", "0.159900", "0.159620", "0.159650", "1096927", 1750955999999, "175267.743870", 466, "105231", "16806.192190", "0"], [1750956000000, "0.159650", "0.159770", "0.159600", "0.159620", "692306", 1750956059999, "110561.331060", 567, "451745", "72141.353650", "0"], [1750956060000, "0.159610", "0.159780", "0.159610", "0.159770", "361969", 1750956119999, "57810.621360", 329, "294985", "47114.834570", "0"], [1750956120000, "0.159770", "0.159910", "0.159690", "0.159910", "1310782", 1750956179999, "209400.408490", 727, "929750", "148529.483820", "0"], [1750956180000, "0.159920", "0.159990", "0.159800", "0.159800", "1984011", 1750956239999, "317290.703320", 682, "492894", "78835.153320", "0"], [1750956240000, "0.159790", "0.159860", "0.159690", "0.159690", "853646", 1750956299999, "136419.781810", 509, "254892", "40738.504380", "0"], [1750956300000, "0.159690", "0.159700", "0.159450", "0.159600", "5924373", 1750956359999, "945011.132830", 1572, "1690919", "269799.971050", "0"], [1750956360000, "0.159600", "0.159640", "0.159360", "0.159360", "2114405", 1750956419999, "337137.664870", 810, "323483", "51591.935400", "0"], [1750956420000, "0.159370", "0.159440", "0.159140", "0.159320", "4792598", 1750956479999, "763156.789150", 1408, "1418210", "225897.157610", "0"], [1750956480000, "0.159330", "0.159570", "0.159310", "0.159520", "2279746", 1750956539999, "363461.380120", 976, "1772588", "282590.396370", "0"], [1750956540000, "0.159520", "0.159560", "0.159440", "0.159560", "420386", 1750956599999, "67054.041410", 400, "239428", "38191.707290", "0"], [1750956600000, "0.159550", "0.159620", "0.159510", "0.159540", "795603", 1750956659999, "126950.972810", 516, "270529", "43165.209100", "0"], [1750956660000, "0.159540", "0.159590", "0.159430", "0.159590", "2541486", 1750956719999, "405423.322900", 816, "542276", "86499.978440", "0"], [1750956720000, "0.159590", "0.159640", "0.159340", "0.159340", "1781855", 1750956779999, "284241.528200", 832, "638742", "101889.932520", "0"], [1750956780000, "0.159330", "0.159580", "0.159290", "0.159550", "2510416", 1750956839999, "400238.033890", 794, "2154887", "343548.864070", "0"], [1750956840000, "0.159550", "0.159550", "0.159380", "0.159390", "1685598", 1750956899999, "268860.067760", 596, "119500", "19053.882550", "0"], [1750956900000, "0.159390", "0.159490", "0.159320", "0.159450", "757198", 1750956959999, "120706.716380", 555, "588796", "93860.815560", "0"], [1750956960000, "0.159440", "0.159450", "0.159360", "0.159370", "1331730", 1750957019999, "212310.692070", 483, "1086055", "173145.824960", "0"], [1750957020000, "0.159380", "0.159380", "0.159310", "0.159340", "779274", 1750957079999, "124171.966100", 362, "497722", "79310.818330", "0"]], "candles_count": 20, "data_timestamp": 1750957042, "has_timeseries_data": true, "short_term_change_pct": -0.13162018176119586, "recent_high": 0.15958, "recent_low": 0.15929}, "importance": {"importance": 5, "is_important": true, "importance_score": 0.5, "situation_type": "bearish", "reasoning": "The short-term 5-minute candle data shows a bearish trend with 3 out of 5 candles closing lower, and a price decline of -0.132%. The trendline analysis indicates a forming bearish trend, and volume patterns support the downward movement. (과거 유사 상황 5개: 성공률 0.0%, 평균 수익 0.00%)", "action_recommendation": "sell", "trading_direction": "short", "signal_direction": "bearish", "keywords": ["bearish", "volume", "trendline"], "raw_response": " NO MARKDOWN.\n</think>\n\n{\n  \"importance\": 7,\n  \"is_important\": true,\n  \"importance_score\": 0.75,\n  \"reasoning\": \"The short-term 5-minute candle data shows a bearish trend with 3 out of 5 candles closi...", "confidence": 0.25, "historical_analysis": {"total_experiences": 5, "success_rate": 0.0, "avg_profit": 0.0, "adjustment_applied": true}}, "reasoning_card": {"id": "card_1", "title": "패턴 분석 1", "analysis": "현재 상황에서는 포지션 진입보다 관망이 바람직합니다. 시장이 중립적이므로 추가 지표를 모니터링하는 것이 좋습니다.", "reasoning": "사고 카드 'Standard_CoT' 실행 결과", "confidence": 0.6, "key_factors": ["패턴: Standard_CoT", "액션: HOLD", "신뢰도: 0.60"], "card_id": "card_6f5b23e4_1750957049"}, "strategy": {"strategy_id": "9c79b979-b506-48ce-aba4-6391d2ebee00", "symbol": "DOGE", "timestamp": 1750957060, "type": "sell", "entry_price": 0.15936, "stop_loss": 0.16265, "take_profit": 0.15517, "reasoning": "InCA 시스템은 현재 시장 데이터 분석을 통해 SELL 신호를 제공하고 있으며, 이는 가격이 저항선을 돌파한 후 빠른 하락세를 보이고 있음을 반영합니다. RSI는 과열 상태를 나타내며, MACD는 상승 추세가 끝났음을 시사합니다. 볼린저 밴드는 가격이 상단을 벗어나며 과도한 상승을 나타내고 있습니다.", "confidence": 0.78, "reasoning_card_id": "card_6f5b23e4_1750957049", "risk_level": "medium", "key_points": ["InCA의 SELL 신호", "RSI 과열 상태", "MACD 상승 추세 종료"], "market_context": {"price": 0.15936, "percent_change_24h": -2.627, "timestamp": 1750957042}, "paper_based": false, "risk_reward": 1.2735562310030442, "importance": 8.93624964835295, "consensus_signal": "sell", "consensus_confidence": 0.78, "consensus_breakdown": {"short_term": {"action": "sell", "situation": "bearish", "importance": 0.5, "confidence": 0.25, "source": "InCA", "timeframe": "1분봉"}, "medium_term": {"action": "none", "type": "sell", "importance": 0.5, "confidence": 0.78, "source": "SELA", "timeframe": "1시간봉"}, "long_term": {"action": "neutral", "trend": "sideways", "trend_change_pct": 0.0, "importance": 0.5, "confidence": 0.3, "source": "LongTerm", "timeframe": "일봉", "note": "일봉 데이터 부족"}}}, "execution_status": "created", "consensus_result": {"final_signal": "sell", "consensus_confidence": 0.78, "should_execute": true, "breakdown": {}, "reasoning": "SELA 직접 사용 모드 - DOGE"}, "execution_id": "exec_aef583f8_1750957064"}}