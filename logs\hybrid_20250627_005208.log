2025-06-27 00:52:08 - database.news_vectordb - INFO - huggingface_hub에 cached_download 함수가 없어 패치 적용
2025-06-27 00:52:08 - database.news_vectordb - INFO - huggingface_hub.cached_download 패치 완료
2025-06-27 00:52:12 - database.market_data - INFO - SentenceTransformer 모듈 로드 성공
2025-06-27 00:52:12 - simulator.utils.market_utils - INFO - LunarCrush API 키 상태: 설정됨
2025-06-27 00:52:12 - lunar_monitor - INFO - LunarCrush API initialized with API key
2025-06-27 00:52:12 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-27 00:52:12 - database.news_vectordb - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-27 00:52:12 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:52:13 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-27 00:52:13 - database.news_vectordb - INFO - Successfully loaded model: all-MiniLM-L6-v2
2025-06-27 00:52:13 - lunar_monitor - INFO - Successfully initialized NewsVectorDB
2025-06-27 00:52:13 - database.market_data - INFO - Initializing MarketDataDB
2025-06-27 00:52:13 - database.market_data - INFO - ChromaDB client initialized successfully
2025-06-27 00:52:13 - database.market_data - INFO - Initializing SentenceTransformer with model: all-MiniLM-L6-v2
2025-06-27 00:52:13 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-06-27 00:52:13 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device: cuda
2025-06-27 00:52:13 - database.market_data - INFO - SentenceTransformer model loaded successfully
2025-06-27 00:52:13 - database.market_data - INFO - Successfully accessed collection: topic_metrics
2025-06-27 00:52:13 - database.market_data - INFO - Successfully accessed collection: time_series
2025-06-27 00:52:13 - database.market_data - INFO - MarketDataDB initialized successfully
2025-06-27 00:52:13 - lunar_monitor - INFO - Successfully initialized MarketDataDB
2025-06-27 00:52:13 - data_collector.lunarcrush_collector - INFO - LunarCrush 데이터베이스 초기화 완료: lunarcrush_data.db
2025-06-27 00:52:13 - data_collector.lunarcrush_collector - INFO - LunarCrush 수집기 초기화 완료 (API v4): API KEY=584fo...
2025-06-27 00:52:13 - simulator.utils.market_utils - INFO - LunarMonitor API & Collector 연결 초기화 완료
2025-06-27 00:52:13 - simulator.trading.portfolio - INFO - 🎯 통합 생각카드 시스템 로드 성공
2025-06-27 00:52:13 - hybrid_simulator - INFO - PyTorch 임포트 성공
2025-06-27 00:52:13 - hybrid_simulator - INFO - ChatTS 클라이언트 임포트 성공
2025-06-27 00:52:13 - hybrid_simulator - INFO - 로깅 시스템 초기화 완료 - 타임스탬프 테스트
2025-06-27 00:52:13 - hybrid_simulator - INFO - ===== 하이브리드 거래 시스템 초기화 =====
2025-06-27 00:52:13 - hybrid_simulator - INFO - 시스템 인코딩: utf-8
2025-06-27 00:52:13 - hybrid_simulator - INFO - 표준 출력 인코딩: utf-8
2025-06-27 00:52:13 - hybrid_simulator - INFO - 환경 변수 PYTHONIOENCODING: utf-8
2025-06-27 00:52:13 - hybrid_simulator - INFO - .env 파일 로드 완료
2025-06-27 00:52:13 - hybrid_simulator - INFO - OPENAI_API_KEY 환경 변수가 설정되어 있습니다.
2025-06-27 00:52:13 - hybrid_simulator - INFO - vLLM 환경 변수 확인: USE_VLLM=true, 파싱 결과=True
2025-06-27 00:52:13 - hybrid_simulator - INFO - vLLM 사용 여부: 커맨드라인=True, 환경변수=True, 최종=True
2025-06-27 00:52:13 - hybrid_simulator - INFO - vLLM 모델 설정: 환경변수=Qwen/Qwen3-14B-AWQ, 커맨드라인=Qwen/Qwen3-14B-AWQ, 최종=Qwen/Qwen3-14B-AWQ
2025-06-27 00:52:13 - hybrid_simulator - INFO - vLLM 서버 사용: http://localhost:8001/v1, 모델: Qwen/Qwen3-14B-AWQ
2025-06-27 00:52:13 - hybrid_simulator - INFO - VLLMClientFactory 클래스 임포트 성공
2025-06-27 00:52:13 - hybrid_simulator - INFO - vLLM 클라이언트 초기화 시작: URL=http://localhost:8001/v1, 모델=Qwen/Qwen3-14B-AWQ
2025-06-27 00:52:13 - models.vllm_client_factory - INFO - Using timeout from environment: 600 seconds
2025-06-27 00:52:13 - models.vllm_client_factory - INFO - Creating enhanced VLLM client (model: Qwen/Qwen3-14B-AWQ, timeout: 600s, max_tokens: 8192)
2025-06-27 00:52:13 - models.vllm_client_enhanced - INFO - Qwen3 모델 감지됨: Qwen/Qwen3-14B-AWQ
2025-06-27 00:52:15 - models.vllm_client_enhanced - INFO - VLLM 서버 설정 확인: {'object': 'list', 'data': [{'id': 'Qwen/Qwen3-14B-AWQ', 'object': 'model', 'created': 1750953135, 'owned_by': 'vllm', 'root': 'Qwen/Qwen3-14B-AWQ', 'parent': None, 'max_model_len': 9096, 'permission': [{'id': 'modelperm-9de143ba47024c5a9fcc1fb2d81da225', 'object': 'model_permission', 'created': 1750953135, 'allow_create_engine': False, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}]}]}
2025-06-27 00:52:15 - models.vllm_client_enhanced - INFO - VLLM 서버 max_model_len: 9096
2025-06-27 00:52:15 - models.vllm_client_enhanced - INFO - VLLM 클라이언트 설정: 타임아웃=600초, 최대토큰=8192
2025-06-27 00:52:15 - models.vllm_session_manager - INFO - VLLMSessionManager 초기화 완료
2025-06-27 00:52:15 - models.vllm_client_enhanced - INFO - Qwen3 모델 최적화 설정 적용: temperature=0.6, top_p=0.95, top_k=20, presence_penalty=1.5
2025-06-27 00:52:15 - models.vllm_client_enhanced - INFO - Enhanced VLLM client initialized (server: http://localhost:8001/v1, model: Qwen/Qwen3-14B-AWQ)
2025-06-27 00:52:15 - hybrid_simulator - INFO - 향상된 vLLM 클라이언트 초기화 완료: <models.vllm_client_enhanced.VLLMClientEnhanced object at 0x0000024A183BDBD0>
2025-06-27 00:52:15 - hybrid_simulator - INFO - vLLM 클라이언트 타입: <class 'models.vllm_client_enhanced.VLLMClientEnhanced'>
2025-06-27 00:52:15 - hybrid_simulator - INFO - vLLM 클라이언트 속성: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_initialized', '_instance', 'default_presence_penalty', 'default_temperature', 'default_top_k', 'default_top_p', 'generate', 'generate_fast', 'generate_json', 'health_check', 'is_qwen3', 'max_tokens', 'model_name', 'prompt_processor', 'response_parser', 'server_url', 'session_manager', 'timeout']
2025-06-27 00:52:15 - hybrid_simulator - INFO - vLLM 클라이언트 서버 URL: http://localhost:8001/v1
2025-06-27 00:52:15 - hybrid_simulator - INFO - vLLM 클라이언트 모델명: Qwen/Qwen3-14B-AWQ
2025-06-27 00:52:15 - hybrid_simulator - INFO - vLLM 서버 헬스 체크 시작
