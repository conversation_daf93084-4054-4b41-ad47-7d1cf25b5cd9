"""
포트폴리오 관리 모듈 - 거래 실행 및 포트폴리오 업데이트
"""
import os
import json
import time
import math
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

# 🎯 통합 생각카드 시스템 import
try:
    from trading.thinking_cards.position_card_manager import PositionCardManager
    THINKING_CARDS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("🎯 통합 생각카드 시스템 로드 성공")
except ImportError as e:
    THINKING_CARDS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ 통합 생각카드 시스템 로드 실패: {e}")

logger = logging.getLogger(__name__)

class Portfolio:
    """
    포트폴리오 관리 클래스
    실제 거래 또는 시뮬레이션 거래 처리 및 포트폴리오 상태 추적
    """

    def __init__(self, initial_balance: float = 10000.0, mode: str = 'simulation',
                testnet: bool = True, data_dir: str = None, binance_utils=None):
        """
        포트폴리오 초기화

        Args:
            initial_balance: 초기 잔액 (USDT)
            mode: 거래 모드 ('simulation' 또는 'real')
            testnet: 테스트넷 사용 여부 (mode='real'일 때만 관련)
            data_dir: 데이터 저장 디렉터리
            binance_utils: 바이낸스 유틸리티 객체 (현재가 조회용)
        """
        self.mode = mode
        self.testnet = testnet
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'data')
        self.binance_utils = binance_utils  # 바이낸스 유틸리티 저장

        # 바이낸스 유틸리티 초기화 상태 로깅
        if self.binance_utils:
            logger.info(f"✅ Portfolio 초기화: 바이낸스 유틸리티 연결됨 ({type(self.binance_utils)})")
        else:
            logger.warning(f"⚠️ Portfolio 초기화: 바이낸스 유틸리티가 None입니다!")

        # 하이브리드 컨트롤러 연결 (나중에 설정됨)
        self.hybrid_controller = None

        # 디렉터리 존재 확인 및 생성
        os.makedirs(self.data_dir, exist_ok=True)

        # 포트폴리오 데이터 초기화
        self.balance = initial_balance  # USDT 잔고
        self.assets = {}  # 보유 자산 {symbol: amount}
        self.open_positions = []  # 오픈 포지션
        self.trade_history = []  # 거래 기록
        self.rejected_trades = []  # 거부된 거래 (자금 부족 등으로 실행되지 않은 거래)
        self.performance = {
            "initial_balance": initial_balance,
            "current_value": initial_balance,
            "total_profit_loss": 0.0,
            "total_profit_loss_pct": 0.0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_trades": 0
        }

        # 포지션 동기화 관련 변수
        self.last_position_sync_time = 0  # 마지막 포지션 동기화 시간
        self.position_sync_interval = 60  # 포지션 동기화 간격 (초)

        # 🔧 포지션 청산 쿨다운 관련 변수 (연속 같은 방향 거래 방지) - 비활성화
        self.position_close_history = {}  # {symbol: {'close_time': timestamp, 'direction': 'long/short'}}
        self.position_cooldown_seconds = 0  # 포지션 청산 후 같은 방향 재진입 쿨다운 시간 (비활성화)

        # 🔧 포지션 전환 최소 간격 제어 (LLM 신호와 별개로 실제 거래 실행 제어)
        self.last_position_change = {}  # {symbol: timestamp}
        self.min_position_change_interval = 600  # 최소 10분 간격

        # 🔧 사이클 마지막 포지션 변화 처리를 위한 속성
        self.pending_position_changes = {}  # {symbol: {direction, market_data, binance_utils, existing_position, timestamp}}
        self.cycle_completion_status = {}  # {symbol: {is_completed, completion_time}}

        # 포트폴리오 파일 경로
        self.portfolio_file = os.path.join(self.data_dir, 'portfolio.json')

        # 기존 포트폴리오 로드 (있으면)
        self.load_portfolio()

        # 실제 거래 모드일 경우, 바이낸스 API를 통해 잔액 동기화
        if self.mode == 'real':
            self._initial_balance_sync = False  # 최초 동기화 추적용 플래그
            logger.info("실제 거래 모드가 사용됩니다. 바이낸스 API를 통해 잔액을 동기화하세요.")

        # 실제 투자 모드 경고
        if mode == 'real' and not testnet:
            logger.warning("!!!!! 실제 거래 모드로 실행 중입니다 !!!!!")
            logger.warning("!!!!! 실제 자금이 사용됩니다 !!!!!")
        elif mode == 'real' and testnet:
            logger.info("바이낸스 테스트넷 모드로 실행 중입니다 (실제 API 호출이지만 테스트 코인 사용)")
        else:
            logger.info("시뮬레이션 모드로 실행 중입니다 (가상 거래)")

        # 포트폴리오 초기화 또는 로드
        if os.path.exists(self.portfolio_file):
            self.load_portfolio()
            logger.info(f"기존 포트폴리오 로드 완료: 잔액 ${self.balance:.2f} USDT")
        else:
            # 새 포트폴리오 생성
            self.save_portfolio()
            logger.info(f"새 포트폴리오 생성 완료: 초기 잔액 ${self.balance:.2f} USDT")

        # logger 속성 추가 (누락된 경우)
        if not hasattr(self, 'logger'):
            self.logger = logger

        # 🎯 통합 생각카드 시스템 초기화
        self.card_manager = None
        if THINKING_CARDS_AVAILABLE:
            try:
                card_data_dir = os.path.join(self.data_dir, "thinking_cards")

                # 🔧 시스템 시작 시 오래된 카드 파일 정리 (6시간 이전)
                if os.path.exists(card_data_dir):
                    temp_manager = PositionCardManager(data_dir=card_data_dir)
                    deleted_count = temp_manager.cleanup_old_card_files(max_age_hours=6)
                    if deleted_count > 0:
                        logger.info(f"🔧 시스템 시작 시 오래된 카드 파일 정리: {deleted_count}개 삭제")

                self.card_manager = PositionCardManager(data_dir=card_data_dir)
                logger.info(f"🎯 통합 생각카드 매니저 초기화 성공: {card_data_dir}")
            except Exception as e:
                logger.error(f"❌ 통합 생각카드 매니저 초기화 실패: {e}")
                self.card_manager = None
        else:
            logger.info("🎯 통합 생각카드 시스템이 비활성화되어 있습니다")

    def _store_pending_position_change(self, symbol: str, direction: str, market_data: Dict, binance_utils, existing_position: Dict):
        """🔧 포지션 변화 요청 저장 및 즉시 처리 (반대 신호 시 포지션 전환 허용)"""
        existing_direction = existing_position.get('direction', 'unknown')

        # 반대 방향 신호인지 확인
        is_opposite_signal = (
            (existing_direction == 'long' and direction == 'short') or
            (existing_direction == 'short' and direction == 'long') or
            (existing_direction == 'buy' and direction == 'sell') or
            (existing_direction == 'sell' and direction == 'buy')
        )

        if is_opposite_signal:
            logger.info(f"🔄 [{symbol}] 반대 신호 감지: {existing_direction} → {direction}")
            logger.info(f"🚀 [{symbol}] 포지션 전환 즉시 처리 시작")

            # 즉시 포지션 전환 처리
            try:
                # 1. 기존 포지션 즉시 클로즈 (바이낸스)
                current_price = market_data.get('price', 0)
                if binance_utils:
                    logger.info(f"🔥 [{symbol}] 바이낸스 기존 포지션 즉시 클로즈")
                    close_result = self._close_binance_position_immediately(symbol, existing_position, binance_utils)
                    if close_result.get('success'):
                        logger.info(f"✅ [{symbol}] 바이낸스 포지션 클로즈 성공")
                    else:
                        logger.warning(f"⚠️ [{symbol}] 바이낸스 포지션 클로즈 실패: {close_result.get('error')}")

                # 2. 내부 포지션 종료
                self._close_existing_position(symbol, existing_position, current_price, f"opposite_signal_{direction}")

                # 3. 새 포지션 즉시 생성 (바이낸스)
                new_strategy = {
                    'type': direction,
                    'direction': direction,
                    'entry_price': current_price,
                    'symbol': symbol,
                    'confidence': 0.8,
                    'timestamp': int(time.time())
                }

                logger.info(f"🚀 [{symbol}] 새 {direction} 포지션 즉시 생성")
                result = self.execute_trade(symbol, new_strategy, market_data, binance_utils)

                if result.get('success'):
                    logger.info(f"✅ [{symbol}] 포지션 전환 완료: {existing_direction} → {direction}")
                else:
                    logger.error(f"❌ [{symbol}] 새 포지션 생성 실패: {result}")

                return result

            except Exception as e:
                logger.error(f"❌ [{symbol}] 포지션 전환 실패: {e}")
                return {"success": False, "error": str(e)}
        else:
            logger.info(f"🛡️ [{symbol}] 같은 방향 신호로 포지션 유지: {existing_direction} (신호: {direction})")
            return {"success": True, "action": "position_maintained"}

    def process_pending_position_changes(self, symbol: str = None):
        """사이클 마지막에 저장된 포지션 변화 요청들을 일괄 처리"""
        if symbol:
            # 특정 심볼만 처리
            symbols_to_process = [symbol] if symbol in self.pending_position_changes else []
        else:
            # 모든 심볼 처리
            symbols_to_process = list(self.pending_position_changes.keys())

        if not symbols_to_process:
            logger.debug("처리할 포지션 변화 요청이 없습니다")
            return {}

        results = {}
        for sym in symbols_to_process:
            change_request = self.pending_position_changes.pop(sym)
            logger.info(f"🔄 [{sym}] 사이클 마지막 포지션 변화 처리 시작")

            # 기존 포지션 확인
            existing_position = None
            for pos in self.open_positions:
                if pos['symbol'] == sym:
                    existing_position = pos
                    break

            if existing_position:
                # 기존 포지션이 있으면 포지션 전환
                logger.info(f"🔄 [{sym}] 기존 포지션 있음 → 포지션 전환 실행")
                switch_result = self.switch_position(
                    sym,
                    change_request['direction'],
                    change_request['market_data'],
                    change_request['binance_utils']
                )
                results[sym] = switch_result

                if switch_result.get('success', False):
                    logger.info(f"✅ [{sym}] 사이클 마지막 포지션 전환 성공: {existing_position['direction']} → {change_request['direction']}")
                else:
                    logger.error(f"❌ [{sym}] 사이클 마지막 포지션 전환 실패: {switch_result.get('error', '알 수 없는 오류')}")
            else:
                # 기존 포지션이 없으면 신규 포지션 생성
                logger.info(f"🆕 [{sym}] 기존 포지션 없음 → 신규 포지션 생성 실행")

                # 저장된 전략 정보 사용
                strategy = change_request['existing_position'].get('strategy', {})
                if not strategy:
                    # 기본 전략 생성
                    current_price = change_request['market_data'].get('price', 0)
                    strategy = {
                        'symbol': sym,
                        'type': 'sell' if change_request['direction'] == 'short' else 'buy',
                        'direction': change_request['direction'],
                        'entry_price': current_price,
                        'reasoning': f"사이클 마지막 반대 포지션 진입: {change_request['direction']}",
                        'confidence': 0.7,
                        'strategy_id': f"cycle_end_{sym}_{int(time.time())}"
                    }

                # 신규 포지션 생성
                create_result = self.execute_strategy(
                    sym,
                    strategy,
                    change_request['market_data'],
                    change_request['binance_utils']
                )
                results[sym] = create_result

                if create_result.get('success', False):
                    logger.info(f"✅ [{sym}] 사이클 마지막 신규 포지션 생성 성공: {change_request['direction']}")
                else:
                    logger.error(f"❌ [{sym}] 사이클 마지막 신규 포지션 생성 실패: {create_result.get('error', '알 수 없는 오류')}")

        return results

    def mark_cycle_completion(self, symbol: str):
        """심볼의 사이클 완료 표시"""
        self.cycle_completion_status[symbol] = {
            'is_completed': True,
            'completion_time': time.time()
        }
        logger.info(f"🏁 [{symbol}] 사이클 완료 표시")

        # 사이클 완료 시 해당 심볼의 포지션 변화 처리
        if symbol in self.pending_position_changes:
            logger.info(f"🔓 [{symbol}] 저장된 포지션 변화 요청 처리 시작 - 보호 상태에서 실행")
            self.process_pending_position_changes(symbol)

            # 포지션 변화 처리 완료 후 보호 해제
            if symbol in self.pending_position_changes:
                del self.pending_position_changes[symbol]
                logger.info(f"🔓 [{symbol}] 포지션 변화 요청 처리 완료 - 보호 해제")

    def load_portfolio(self):
        """기존 포트폴리오 데이터 로드"""
        try:
            with open(self.portfolio_file, 'r', encoding='utf-8') as f:
                portfolio_data = json.load(f)

            self.balance = portfolio_data.get('balance', 10000.0)
            self.assets = portfolio_data.get('assets', {})

            # 오픈 포지션 로드 및 'quantity' 키 확인
            self.open_positions = portfolio_data.get('open_positions', [])

            # 각 포지션에 대해 필수 키 확인
            for position in self.open_positions:
                # 'quantity' 키 확인
                if 'quantity' not in position:
                    if 'size' in position:
                        logger.warning(f"{position.get('symbol', 'Unknown')} 포지션에 'quantity' 키가 없지만 'size' 키가 있습니다. 'size' 값을 'quantity'로 복사합니다.")
                        position['quantity'] = position['size']
                    else:
                        error_msg = f"{position.get('symbol', 'Unknown')} 포지션에 'quantity' 키와 'size' 키가 모두 없습니다. 포지션 정보: {position}"
                        logger.error(error_msg)
                        raise KeyError(error_msg)

                # 'current_pnl' 키 확인 및 자동 추가
                if 'current_pnl' not in position:
                    logger.warning(f"{position.get('symbol', 'Unknown')} 포지션에 'current_pnl' 키가 없습니다. 기본값 0.0으로 설정합니다.")
                    position['current_pnl'] = 0.0

                # 'take_profit' 키 확인
                if 'take_profit' not in position:
                    error_msg = f"{position.get('symbol', 'Unknown')} 포지션에 'take_profit' 키가 없습니다. 포지션 정보: {position}"
                    logger.error(error_msg)
                    raise KeyError(error_msg)

                # 'stop_loss' 키 확인
                if 'stop_loss' not in position:
                    error_msg = f"{position.get('symbol', 'Unknown')} 포지션에 'stop_loss' 키가 없습니다. 포지션 정보: {position}"
                    logger.error(error_msg)
                    raise KeyError(error_msg)

            self.trade_history = portfolio_data.get('trade_history', [])
            self.rejected_trades = portfolio_data.get('rejected_trades', [])  # 거부된 거래 목록 로드
            self.performance = portfolio_data.get('performance', {
                "initial_balance": self.balance,
                "current_value": self.balance,
                "total_profit_loss": 0.0,
                "total_profit_loss_pct": 0.0,
                "winning_trades": 0,
                "losing_trades": 0,
                "total_trades": 0
            })

            logger.info(f"포트폴리오 로드 완료")
            return True

        except Exception as e:
            logger.error(f"포트폴리오 로드 중 오류: {e}")
            # 기본값으로 초기화
            self.balance = 10000.0
            self.assets = {}
            self.open_positions = []
            self.trade_history = []
            self.rejected_trades = []  # 거부된 거래 목록 초기화
            self.performance = {
                "initial_balance": 10000.0,
                "current_value": 10000.0,
                "total_profit_loss": 0.0,
                "total_profit_loss_pct": 0.0,
                "winning_trades": 0,
                "losing_trades": 0,
                "total_trades": 0
            }
            return False

    def set_hybrid_controller(self, hybrid_controller):
        """
        하이브리드 컨트롤러 설정

        Args:
            hybrid_controller: 하이브리드 컨트롤러 객체
        """
        self.hybrid_controller = hybrid_controller
        logger.info("✅ 포트폴리오에 하이브리드 컨트롤러 연결 완료")

    def save_portfolio(self):
        """포트폴리오 데이터 저장"""
        try:
            os.makedirs(os.path.dirname(self.portfolio_file), exist_ok=True)

            portfolio_data = {
                'balance': self.balance,
                'assets': self.assets,
                'open_positions': self.open_positions,
                'trade_history': self.trade_history,
                'rejected_trades': self.rejected_trades,  # 거부된 거래 목록 저장
                'performance': self.performance,
                'last_updated': datetime.now().isoformat()
            }

            with open(self.portfolio_file, 'w', encoding='utf-8') as f:
                json.dump(portfolio_data, f, indent=2, ensure_ascii=False)

            logger.debug(f"포트폴리오 저장 완료")
            return True

        except Exception as e:
            logger.error(f"포트폴리오 저장 중 오류: {e}")
            return False

    def force_clear_internal_positions(self, symbols=None):
        """내부 포지션 강제 정리 (바이낸스와 동기화 문제 해결용)"""
        try:
            if symbols is None:
                symbols = ['SOL', 'BNB', 'DOGE']  # 🔧 DOGE 추가

            logger.info(f"🧹 내부 포지션 강제 정리 시작: {symbols}")

            # 지정된 심볼의 포지션 제거
            positions_to_remove = []
            for i, position in enumerate(self.open_positions):
                if position['symbol'] in symbols:
                    positions_to_remove.append(i)
                    # direction 키 안전하게 처리
                    direction = position.get('direction') or position.get('action', 'unknown')
                    trade_id = position.get('trade_id') or position.get('id', 'unknown')
                    logger.info(f"🗑️ 제거할 포지션: {position['symbol']} {direction} (ID: {trade_id})")

            # 역순으로 제거 (인덱스 변경 방지)
            for i in reversed(positions_to_remove):
                removed_position = self.open_positions.pop(i)
                # direction 키 안전하게 처리
                direction = removed_position.get('direction') or removed_position.get('action', 'unknown')
                logger.info(f"✅ 포지션 제거 완료: {removed_position['symbol']} {direction}")

            # 자산도 정리
            for symbol in symbols:
                if symbol in self.assets:
                    removed_amount = self.assets.pop(symbol)
                    logger.info(f"✅ 자산 제거 완료: {symbol} {removed_amount}")

            # 포트폴리오 저장
            self.save_portfolio()
            logger.info(f"🎯 내부 포지션 강제 정리 완료: {len(positions_to_remove)}개 포지션 제거")

            return True

        except Exception as e:
            logger.error(f"내부 포지션 강제 정리 실패: {e}")
            logger.error(traceback.format_exc())
            return False

    def auto_clear_orphaned_positions(self, binance_utils=None):
        """🔧 바이낸스에 없는 내부 포지션 자동 정리"""
        try:
            if not binance_utils:
                logger.warning("🔧 바이낸스 연결 없음 - 고아 포지션 정리 스킵")
                return False

            logger.info("🔧 고아 포지션 자동 정리 시작")

            # 바이낸스 포지션 조회
            binance_positions = binance_utils.get_futures_positions()
            if not isinstance(binance_positions, list):
                logger.error(f"🔧 바이낸스 포지션 조회 실패: {binance_positions}")
                return False

            # 바이낸스 활성 포지션 심볼 목록
            active_symbols = set()
            for pos in binance_positions:
                if float(pos.get('positionAmt', 0)) != 0:
                    symbol = pos.get('symbol', '').replace('USDT', '')
                    active_symbols.add(symbol)

            logger.info(f"🔧 바이낸스 활성 포지션 심볼: {active_symbols}")

            # 내부 포지션 중 바이낸스에 없는 것들 찾기
            orphaned_symbols = set()
            for pos in self.open_positions:
                # 🔧 포지션 객체 타입 검증
                if not isinstance(pos, dict):
                    logger.warning(f"🔧 고아 포지션 검사 중 잘못된 포지션 타입 발견: {type(pos)}, 스킵")
                    continue

                symbol = pos.get('symbol')
                if symbol and symbol not in active_symbols:
                    orphaned_symbols.add(symbol)

            if orphaned_symbols:
                logger.warning(f"🔧 고아 포지션 발견: {orphaned_symbols}")
                # 고아 포지션 정리
                return self.force_clear_internal_positions(list(orphaned_symbols))
            else:
                logger.info("🔧 고아 포지션 없음 - 정리 불필요")
                return True

        except Exception as e:
            logger.error(f"🔧 고아 포지션 자동 정리 실패: {e}")
            logger.error(traceback.format_exc())
            return False

    def force_sync_with_binance(self, binance_utils):
        """
        바이낸스와 강제 동기화 (실제 포지션 상태 확인)

        Args:
            binance_utils: 바이낸스 유틸리티 객체
        """
        if not binance_utils:
            logger.error("바이낸스 유틸리티가 없어 강제 동기화를 수행할 수 없습니다.")
            return

        logger.info("🔥 바이낸스와 강제 동기화 시작")

        try:
            # 실제 바이낸스 포지션 조회
            positions = binance_utils.get_futures_positions()

            if isinstance(positions, dict) and 'error' in positions:
                logger.error(f"🔴 바이낸스 포지션 조회 실패: {positions['error']}")
                return

            if not isinstance(positions, list):
                logger.error(f"🔴 예상치 못한 응답 형식: {type(positions)}")
                return

            # 활성 포지션 필터링
            active_positions = []
            for pos in positions:
                position_amt = float(pos.get('positionAmt', 0))
                if abs(position_amt) > 0.000001:  # 매우 작은 값 제외
                    active_positions.append(pos)

            logger.info(f"🔍 바이낸스 실제 활성 포지션: {len(active_positions)}개")

            # 활성 포지션 상세 로깅
            for pos in active_positions:
                symbol = pos.get('symbol', 'unknown')
                position_amt = float(pos.get('positionAmt', 0))
                entry_price = float(pos.get('entryPrice', 0))
                mark_price = float(pos.get('markPrice', 0))
                unrealized_pnl = float(pos.get('unrealizedProfit', 0))

                # 🔧 현재가 조회 개선: markPrice가 0이면 실시간 가격 API 사용
                current_price = mark_price
                if current_price <= 0 and hasattr(self, 'binance_utils') and self.binance_utils:
                    try:
                        ticker_data = self.binance_utils.get_ticker(symbol)
                        if ticker_data and 'lastPrice' in ticker_data:
                            current_price = float(ticker_data['lastPrice'])
                            logger.debug(f"🔧 [{symbol}] markPrice 0으로 ticker API 사용: ${current_price:.4f}")
                    except Exception as e:
                        logger.warning(f"⚠️ [{symbol}] ticker API 조회 실패: {e}")

                direction = 'long' if position_amt > 0 else 'short'
                logger.info(f"🔍 [{symbol}] {direction} 포지션:")
                logger.info(f"   - 수량: {position_amt}")
                logger.info(f"   - 진입가: ${entry_price:.4f}")
                logger.info(f"   - 현재가: ${current_price:.4f}")
                logger.info(f"   - 미실현 손익: ${unrealized_pnl:.4f}")

            # 내부 포지션과 비교
            logger.info(f"🔍 내부 포지션: {len(self.open_positions)}개")
            for pos in self.open_positions:
                symbol = pos.get('symbol', 'unknown')
                direction = pos.get('direction', pos.get('action', 'unknown'))
                entry_price = pos.get('entry_price', 0)
                current_pnl = pos.get('current_pnl_pct', 0)
                logger.info(f"🔍 내부 [{symbol}] {direction} 포지션: 진입가 ${entry_price:.4f}, PnL {current_pnl:.2f}%")

            # 불일치 감지 및 수정
            if len(active_positions) == 0 and len(self.open_positions) > 0:
                logger.warning("⚠️ 바이낸스에는 포지션이 없지만 내부에는 포지션이 있음 - 내부 포지션 정리")
                self.open_positions.clear()
                logger.info("✅ 모든 내부 포지션 정리 완료")

            elif len(active_positions) > 0 and len(self.open_positions) == 0:
                logger.warning("⚠️ 바이낸스에는 포지션이 있지만 내부에는 없음 - 내부 포지션 생성")
                # 바이낸스 포지션을 내부 포지션으로 변환
                for pos in active_positions:
                    symbol = pos.get('symbol', '').replace('USDT', '')
                    position_amt = float(pos.get('positionAmt', 0))
                    entry_price = float(pos.get('entryPrice', 0))

                    direction = 'long' if position_amt > 0 else 'short'

                    # 현재 가격 조회 (바이낸스 동기화 포지션용)
                    current_price = 0.0
                    if binance_utils:
                        try:
                            price_info = binance_utils.get_futures_price(symbol)
                            if 'error' not in price_info:
                                current_price = float(price_info['price'])
                                logger.debug(f"🔥 [{symbol}] 동기화 포지션 현재가 조회 성공: ${current_price:.2f}")
                            else:
                                logger.warning(f"⚠️ [{symbol}] 동기화 포지션 현재가 조회 실패: {price_info.get('error', 'Unknown error')}")
                        except Exception as price_error:
                            logger.warning(f"⚠️ [{symbol}] 동기화 포지션 현재가 조회 오류: {price_error}")

                    internal_position = {
                        'id': f'sync_{symbol}_{int(time.time())}',
                        'symbol': symbol,
                        'direction': direction,
                        'action': 'buy' if direction == 'long' else 'sell',
                        'entry_price': entry_price,
                        'quantity': abs(position_amt),
                        'timestamp': int(time.time()),
                        'source': 'binance_sync',
                        'mode': self.mode,
                        'current_price': current_price,  # 현재 가격 추가
                        'size': abs(position_amt),  # size 필드도 추가 (호환성)
                        'last_updated': int(time.time()),  # 마지막 업데이트 시간
                        'take_profit': 0.2,  # 기본 익절 기준 (0.2%)
                        'stop_loss': -0.15,  # 기본 손절 기준 (-0.15%)
                        'current_pnl': 0.0,  # 기본 PnL
                        'current_pnl_pct': 0.0  # 기본 PnL 퍼센트
                    }

                    self.open_positions.append(internal_position)
                    logger.info(f"✅ 내부 포지션 생성: {symbol} {direction} @ ${entry_price:.4f}")

            logger.info("🔥 바이낸스와 강제 동기화 완료")

        except Exception as e:
            logger.error(f"🔴 바이낸스 강제 동기화 중 오류: {e}")
            import traceback
            logger.error(f"🔴 스택 트레이스: {traceback.format_exc()}")

    def sync_balance_with_exchange(self, binance_utils, force=False):
        """
        바이낸스 API를 통해 실제 계좌 잔액 동기화 (퓨처스 API용)

        Args:
            binance_utils: 바이낸스 유틸리티 객체
            force: 강제 동기화 여부 (기본값: False)

        Returns:
            성공 여부 (불리언)
        """
        if not binance_utils:
            logger.error("바이낸스 유틸리티가 제공되지 않았습니다. 잔액 동기화를 건너뜁니다.")
            return False

        try:
            logger.info(f"바이낸스 API를 통해 잔액 정보 동기화 시작 (강제={force})")

            # 퓨처스 계정 잔액 가져오기 (futures_account_balance API 사용)
            futures_balance = binance_utils.get_futures_account_balance()
            logger.info(f"퓨처스 계정 잔액 응답 타입: {type(futures_balance)}")

            if isinstance(futures_balance, list):
                logger.info(f"퓨처스 계정 잔액 응답 길이: {len(futures_balance)}")
                # USDT 자산 찾기
                usdt_asset = next((asset for asset in futures_balance if asset.get('asset') == 'USDT'), None)

                if usdt_asset:
                    # 사용 가능한 잔액과 총 잔액 모두 로깅
                    available_balance = float(usdt_asset.get('availableBalance', 0))
                    wallet_balance = float(usdt_asset.get('balance', 0))
                    cross_un_pnl = float(usdt_asset.get('crossUnPnl', 0))

                    logger.info(f"바이낸스 USDT 잔액 정보:")
                    logger.info(f"  - 사용 가능한 잔액: {available_balance:.2f} USDT")
                    logger.info(f"  - 총 잔액: {wallet_balance:.2f} USDT")
                    logger.info(f"  - 미실현 손익: {cross_un_pnl:.2f} USDT")

                    # 사용 가능한 잔액으로 업데이트 (거래 가능한 금액)
                    if available_balance > 0:
                        self.balance = available_balance
                        logger.info(f"잔액 업데이트: {self.balance:.2f} USDT (사용 가능한 잔액 기준)")
                    elif wallet_balance > 0:
                        self.balance = wallet_balance
                        logger.info(f"잔액 업데이트: {self.balance:.2f} USDT (총 잔액 기준)")
                    else:
                        logger.warning(f"모든 잔액이 0입니다. 사용 가능: {available_balance}, 총: {wallet_balance}")
                        return False

                    # 포트폴리오 저장
                    self.save_portfolio()

                    if not self._initial_balance_sync:
                        self._initial_balance_sync = True
                        logger.info("최초 잔액 동기화 완료")

                    return True
                else:
                    logger.warning("퓨처스 계정 잔액에서 USDT 자산을 찾을 수 없습니다.")

            # 퓨처스 API 실패 시 일반 계좌 정보 시도
            logger.info("퓨처스 계정 잔액 조회 실패, 일반 계좌 정보 조회 시도")

            # 계좌 정보 가져오기
            account_info = binance_utils.get_account()
            logger.info(f"계정 정보 키: {list(account_info.keys()) if account_info and isinstance(account_info, dict) else '없음'}")

            if not account_info or not isinstance(account_info, dict):
                logger.error("계좌 정보를 가져오는데 실패했습니다.")
                return False

            # 퓨처스 API 응답 구조에서 USDT 잔액 찾기
            if 'assets' in account_info:
                # 퓨처스 API의 assets 배열에서 USDT 찾기
                for asset in account_info['assets']:
                    if asset['asset'] == 'USDT':
                        # walletBalance는 지갑 잔액, availableBalance는 사용 가능 잔액
                        # 여기서는 walletBalance 사용 (전체 자산)
                        usdt_balance = float(asset.get('walletBalance', 0))
                        logger.info(f"바이낸스 퓨처스 USDT 잔액 동기화: {usdt_balance:.2f}")

                        # 잔액 업데이트
                        self.balance = usdt_balance

                        # 포트폴리오 저장
                        self.save_portfolio()

                        if not self._initial_balance_sync:
                            self._initial_balance_sync = True
                            logger.info("최초 잔액 동기화 완료")

                        return True

                logger.warning("USDT 잔액을 assets 목록에서 찾을 수 없습니다.")

            # 이전 API 구조 호환성 (스팟 API)
            elif 'balances' in account_info:
                # 스팟 API의 balances 배열에서 USDT 찾기
                for balance in account_info['balances']:
                    if balance['asset'] == 'USDT':
                        usdt_balance = float(balance['free']) + float(balance.get('locked', 0))
                        logger.info(f"바이낸스 스팟 USDT 잔액 동기화: {usdt_balance:.2f}")

                        # 잔액 업데이트
                        self.balance = usdt_balance

                        # 포트폴리오 저장
                        self.save_portfolio()

                        if not self._initial_balance_sync:
                            self._initial_balance_sync = True
                            logger.info("최초 잔액 동기화 완료")

                        return True

                logger.warning("USDT 잔액을 balances 목록에서 찾을 수 없습니다.")

            else:
                # 기타 필드 존재 여부 확인
                for key, value in account_info.items():
                    if isinstance(value, str) and key.lower().endswith('balance'):
                        try:
                            usdt_balance = float(value)
                            logger.info(f"바이낸스 잔액 필드 '{key}'에서 동기화: {usdt_balance:.2f}")

                            # 잔액 업데이트
                            self.balance = usdt_balance

                            # 포트폴리오 저장
                            self.save_portfolio()

                            if not self._initial_balance_sync:
                                self._initial_balance_sync = True
                                logger.info("최초 잔액 동기화 완료")

                            return True
                        except:
                            pass

                logger.warning("계정 정보에서 USDT 잔액을 찾을 수 없습니다. 테스트넷 기본 잔액을 사용합니다.")
                if self.testnet:
                    # 테스트넷에서는 기본값 사용
                    self.balance = 10000.0
                    logger.info(f"테스트넷 기본 USDT 잔액 설정: {self.balance:.2f}")
                    return True

            return False

        except Exception as e:
            logger.error(f"잔액 동기화 중 오류 발생: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def _get_synchronized_timestamp(self, binance_utils=None) -> int:
        """
        바이낸스 서버 시간과 동기화된 타임스탬프 반환

        Args:
            binance_utils: 바이낸스 유틸리티 객체

        Returns:
            int: 동기화된 타임스탬프 (초 단위)
        """
        try:
            if binance_utils and self.mode == 'real':
                # 바이낸스 서버 시간 조회 (밀리초)
                server_time_ms = binance_utils.get_server_time()
                if isinstance(server_time_ms, int):
                    # 초 단위로 변환
                    server_time_sec = server_time_ms // 1000
                    logger.debug(f"바이낸스 서버 시간 사용: {server_time_sec}")
                    return server_time_sec
                else:
                    logger.warning(f"바이낸스 서버 시간이 정수가 아님: {server_time_ms}")

            # 실패 시 로컬 시간 사용
            local_time = int(time.time())
            logger.debug(f"로컬 시간 사용: {local_time}")
            return local_time

        except Exception as e:
            logger.warning(f"서버 시간 동기화 실패, 로컬 시간 사용: {e}")
            return int(time.time())

    def sync_positions_with_exchange(self, binance_utils, symbols=None, force=False):
        """
        🔥 강화된 실시간 바이낸스 포지션 동기화

        개선사항:
        - 실시간 포지션 상태 검증
        - 불일치 감지 및 자동 수정
        - 상세한 동기화 로깅
        - 오류 복구 메커니즘

        Args:
            binance_utils: 바이낸스 유틸리티 객체
            symbols: 동기화할 심볼 목록 (기본값: None, 모든 심볼)
            force: 강제 동기화 여부 (기본값: False)

        Returns:
            Dict: 동기화 결과 상세 정보
        """
        sync_result = {
            'success': False,
            'timestamp': int(time.time()),
            'changes_made': [],
            'errors': [],
            'positions_before': len(self.open_positions),
            'positions_after': 0,
            'exchange_positions': 0,
            'sync_type': 'forced' if force else 'scheduled'
        }

        if not binance_utils:
            error_msg = "바이낸스 유틸리티가 제공되지 않았습니다. 포지션 동기화를 건너뜁니다."
            logger.error(error_msg)
            sync_result['errors'].append(error_msg)
            return sync_result

        if self.mode != 'real':
            logger.info("시뮬레이션 모드에서는 포지션 동기화가 필요하지 않습니다.")
            sync_result['success'] = True
            sync_result['positions_after'] = len(self.open_positions)
            return sync_result

        # 현재 시간 확인
        current_time = int(time.time())

        # 🔥 강제 동기화가 아닌 경우에도 더 자주 동기화 (30초 → 10초)
        min_sync_interval = 10 if not force else 0

        if not force and current_time - self.last_position_sync_time < min_sync_interval:
            logger.debug(f"포지션 동기화 간격({min_sync_interval}초)이 지나지 않았습니다. 동기화를 건너뜁니다.")
            sync_result['success'] = True
            sync_result['positions_after'] = len(self.open_positions)
            return sync_result

        logger.info(f"포지션 동기화 시작: 강제={force}, 마지막 동기화 이후 {current_time - self.last_position_sync_time}초 경과")

        try:
            logger.info("바이낸스 API를 통해 포지션 정보 동기화 시작")

            # 포지션 정보 가져오기
            positions = binance_utils.get_futures_positions()

            # 🔥 강화된 디버깅: 포지션 정보 상세 로깅
            logger.info(f"🔍 바이낸스 API 응답 타입: {type(positions)}")

            if isinstance(positions, dict) and 'error' in positions:
                logger.error(f"🔴 포지션 정보 조회 실패: {positions['error']}")
                sync_result['errors'].append(f"바이낸스 API 오류: {positions['error']}")
                sync_result['success'] = False
                return sync_result

            if not isinstance(positions, list):
                logger.error(f"🔴 포지션 정보가 리스트 형식이 아닙니다: {type(positions)}")
                sync_result['errors'].append(f"예상치 못한 응답 형식: {type(positions)}")
                sync_result['success'] = False
                return sync_result

            # 🔥 상세 포지션 분석
            logger.info(f"🔍 바이낸스 API 전체 포지션 수: {len(positions)}")

            # 활성 포지션 개수 확인
            active_count = sum(1 for pos in positions if abs(float(pos.get('positionAmt', 0))) > 0)
            logger.info(f"🔍 바이낸스 API 활성 포지션 개수: {active_count}")

            # SOL, DOGE 포지션 특별 확인
            sol_positions = [pos for pos in positions if pos.get('symbol') == 'SOLUSDT']
            doge_positions = [pos for pos in positions if pos.get('symbol') == 'DOGEUSDT']

            logger.info(f"🔍 SOL 포지션 수: {len(sol_positions)}")
            logger.info(f"🔍 DOGE 포지션 수: {len(doge_positions)}")

            for pos in sol_positions:
                position_amt = float(pos.get('positionAmt', 0))
                logger.info(f"🔍 SOL 포지션 상세: positionAmt={position_amt}, entryPrice={pos.get('entryPrice')}")

            for pos in doge_positions:
                position_amt = float(pos.get('positionAmt', 0))
                logger.info(f"🔍 DOGE 포지션 상세: positionAmt={position_amt}, entryPrice={pos.get('entryPrice')}")

            # 🔥 빈 포지션 목록인 경우 특별 처리
            if len(positions) == 0:
                logger.warning("🟡 바이낸스 API가 빈 포지션 목록을 반환했습니다")
                logger.warning("🟡 이는 API 키 문제, 네트워크 문제, 또는 실제로 포지션이 없는 경우일 수 있습니다")

                # 내부 포지션이 있는 경우 불일치 상황
                if len(self.open_positions) > 0:
                    logger.error("🔴 심각한 불일치: 내부에는 포지션이 있지만 바이낸스 API는 빈 목록 반환")
                    sync_result['errors'].append("바이낸스 API 빈 응답 vs 내부 포지션 존재")

                    # 🔥 다중 재조회 시도 (더 강력한 재시도)
                    logger.info("🔄 바이낸스 포지션 다중 재조회 시도")
                    retry_count = 0
                    max_retries = 3

                    while retry_count < max_retries:
                        retry_count += 1
                        logger.info(f"🔄 재조회 시도 {retry_count}/{max_retries}")

                        time.sleep(1)  # 1초 대기
                        positions_retry = binance_utils.get_futures_positions()

                        if isinstance(positions_retry, list) and len(positions_retry) > 0:
                            # 활성 포지션 확인 (더 정밀한 기준)
                            active_retry = []
                            for pos in positions_retry:
                                position_amt = float(pos.get('positionAmt', 0))
                                unrealized_profit = float(pos.get('unrealizedProfit', 0))
                                if abs(position_amt) > 0.000001 or abs(unrealized_profit) > 0.01:
                                    active_retry.append(pos)

                            if len(active_retry) > 0:
                                logger.info(f"🎉 재조회 성공: {len(active_retry)}개 활성 포지션 발견")
                                positions = positions_retry
                                break

                        logger.warning(f"🔄 재조회 {retry_count} 실패")

                    if retry_count >= max_retries:
                        logger.warning("🔄 모든 재조회 실패 - 내부 포지션을 내부 전용으로 유지")
                        # 내부 포지션을 내부 전용으로 표시하고 유지
                        for pos in self.open_positions:
                            # 🔧 포지션 객체 타입 검증
                            if not isinstance(pos, dict):
                                logger.warning(f"🔧 동기화 중 잘못된 포지션 타입 발견: {type(pos)}, 스킵")
                                continue

                            pos['sync_status'] = 'internal_only'
                            pos['last_sync_time'] = current_time
                            logger.info(f"🔒 내부 전용 유지: {pos['symbol']} {pos.get('direction', 'unknown')}")

                        sync_result['success'] = True
                        sync_result['positions_after'] = len(self.open_positions)
                        sync_result['exchange_positions'] = 0
                        sync_result['changes_made'].append(f"내부 포지션 {len(self.open_positions)}개를 내부 전용으로 유지")
                        return sync_result

            # 활성 포지션만 필터링 (포지션 크기가 0이 아닌 것)
            active_positions = []
            for pos in positions:
                # 포지션 크기 확인
                position_amt = float(pos.get('positionAmt', 0))

                # 심볼 추출 (BTCUSDT -> BTC)
                symbol = pos.get('symbol', '')
                if symbol.endswith('USDT'):
                    symbol = symbol[:-4]

                # 특정 심볼만 동기화하는 경우 필터링
                if symbols and symbol not in symbols:
                    continue

                # 디버깅: 모든 포지션 정보 로깅 (SOL, DOGE만)
                if symbol in ['SOL', 'DOGE']:
                    logger.info(f"[{symbol}] 바이낸스 포지션 전체 원본 데이터:")
                    logger.info(f"  전체 응답: {str(pos)}")
                    logger.info(f"[{symbol}] 바이낸스 포지션 주요 필드:")
                    logger.info(f"  - symbol: {pos.get('symbol', 'N/A')}")
                    logger.info(f"  - positionAmt: {pos.get('positionAmt', 'N/A')} (타입: {type(pos.get('positionAmt', 'N/A'))})")
                    logger.info(f"  - entryPrice: {pos.get('entryPrice', 'N/A')}")
                    logger.info(f"  - markPrice: {pos.get('markPrice', 'N/A')}")
                    logger.info(f"  - unrealizedProfit: {pos.get('unrealizedProfit', 'N/A')}")
                    logger.info(f"  - percentage: {pos.get('percentage', 'N/A')}")
                    logger.info(f"  - notional: {pos.get('notional', 'N/A')}")
                    logger.info(f"  - positionSide: {pos.get('positionSide', 'N/A')}")
                    logger.info(f"  - 계산된 position_amt: {position_amt} (타입: {type(position_amt)})")

                # 포지션 크기가 0이 아니거나 미실현 손익이 있는 경우 처리 (더 정밀한 기준)
                unrealized_profit = float(pos.get('unrealizedProfit', 0))
                if abs(position_amt) > 0.000001 or abs(unrealized_profit) > 0.01:
                    # 🔧 포지션 방향 결정 (수정된 로직)
                    logger.info(f"🔧 [{symbol}] 포지션 방향 결정: positionAmt={position_amt}")
                    if position_amt > 0:
                        direction = "long"
                        logger.info(f"🔧 [{symbol}] 양수 포지션 → LONG 방향")
                    elif position_amt < 0:
                        direction = "short"
                        logger.info(f"🔧 [{symbol}] 음수 포지션 → SHORT 방향")
                    else:
                        direction = "neutral"
                        logger.info(f"🔧 [{symbol}] 제로 포지션 → NEUTRAL 방향")

                    logger.info(f"🔧 [{symbol}] 최종 결정된 방향: {direction}")

                    # 진입가, 마크 가격 등 정보 추출
                    entry_price = float(pos.get('entryPrice', 0))
                    mark_price = float(pos.get('markPrice', 0))
                    leverage = float(pos.get('leverage', 1))

                    # 미실현 손익 계산 (바이낸스에서 직접 제공)
                    unrealized_pnl = float(pos.get('unrealizedProfit', 0))

                    # 🔧 현재가 조회 개선: markPrice가 0이면 실시간 가격 API 사용
                    current_market_price = mark_price
                    if current_market_price <= 0 and hasattr(self, 'binance_utils') and self.binance_utils:
                        try:
                            ticker_data = self.binance_utils.get_ticker(symbol)
                            if ticker_data and 'lastPrice' in ticker_data:
                                current_market_price = float(ticker_data['lastPrice'])
                                logger.debug(f"🔧 [{symbol}] PnL 계산용 현재가 조회: ${current_market_price:.4f}")
                        except Exception as e:
                            logger.warning(f"⚠️ [{symbol}] PnL 계산용 현재가 조회 실패: {e}")

                    if entry_price > 0 and current_market_price > 0:
                        # 🎯 현재 시장 가격으로 강제 계산 (최우선)
                        if direction == "long":
                            pnl_pct = ((current_market_price - entry_price) / entry_price) * 100
                        else:  # short
                            pnl_pct = ((entry_price - current_market_price) / entry_price) * 100
                        logger.info(f"[{symbol}] 🔧 강제 PnL 계산: {pnl_pct:.3f}% (진입: {entry_price}, 현재: {current_market_price}, 방향: {direction})")
                    elif unrealized_pnl != 0 and entry_price > 0:
                        # unrealizedProfit을 사용해서 PnL% 계산 (백업)
                        position_size = abs(float(pos.get('positionAmt', 0)))
                        if position_size > 0:
                            position_value = position_size * entry_price
                            pnl_pct = (unrealized_pnl / position_value) * 100
                            logger.info(f"[{symbol}] unrealizedProfit 기반 PnL: {pnl_pct:.2f}%")
                        else:
                            pnl_pct = 0.0
                    elif entry_price > 0 and mark_price > 0:
                        # 가격 차이로 계산 (fallback)
                        if direction == "long":
                            pnl_pct = ((mark_price - entry_price) / entry_price) * 100
                        else:  # short
                            pnl_pct = ((entry_price - mark_price) / entry_price) * 100
                        logger.info(f"[{symbol}] 가격 차이 기반 PnL: {pnl_pct:.2f}%")
                    else:
                        # 🔄 바이낸스 API 백업 (최후 수단)
                        percentage_field = pos.get('percentage', None)
                        if percentage_field is not None and percentage_field != '':
                            pnl_pct = float(percentage_field)
                            logger.info(f"[{symbol}] 바이낸스 API PnL 사용: {pnl_pct:.2f}%")
                        else:
                            pnl_pct = 0.0
                            logger.warning(f"[{symbol}] PnL 계산 불가: entry_price={entry_price}, current_price={current_market_price}")

                    # 추가 로깅: 바이낸스 API 응답 상세 정보
                    logger.info(f"[{symbol}] 바이낸스 포지션 상세:")
                    logger.info(f"  - entryPrice: {entry_price}")
                    logger.info(f"  - markPrice: {mark_price}")
                    logger.info(f"  - unrealizedProfit: {unrealized_pnl}")
                    logger.info(f"  - percentage: {pos.get('percentage', 'N/A')}")
                    logger.info(f"  - 최종 PnL%: {pnl_pct:.2f}%")

                    # 포지션 정보 구성 (바이낸스 API 원본 데이터 포함)
                    position_info = {
                        "trade_id": f"sync_{symbol}_{current_time}",  # trade_id 추가
                        "symbol": symbol,
                        "direction": direction,
                        "size": abs(position_amt),
                        "quantity": abs(position_amt),  # size와 동일한 값으로 quantity 필드 추가
                        "entry_price": entry_price,
                        "current_price": mark_price,
                        "leverage": leverage,
                        "unrealized_pnl": unrealized_pnl,  # 바이낸스 API 달러 PnL
                        "percentage": pos.get('percentage', None),  # 바이낸스 API ROI 백분율
                        "current_pnl": pnl_pct,  # 계산된 PnL 백분율 (fallback)
                        "take_profit": 0.0,  # 기본값 설정
                        "stop_loss": 0.0,  # 기본값 설정
                        "timestamp": current_time,
                        "status": "open"
                    }

                    active_positions.append(position_info)

                    # 🔒 동기화된 포지션 중복 생성 방지: 기존 포지션 및 카드 확인
                    existing_trade_ids = [p.get("trade_id") for p in self.open_positions if isinstance(p, dict)]
                    existing_same_direction = self._get_existing_position(symbol)

                    # 동일한 방향 포지션이 이미 존재하는지 확인
                    if existing_same_direction and existing_same_direction.get('direction') == direction:
                        logger.info(f"🔒 [{symbol}] 동일한 방향 포지션이 이미 존재하여 동기화 카드 생성 스킵: {direction}")
                        logger.info(f"🔒 [{symbol}] 기존 포지션 ID: {existing_same_direction.get('trade_id')}, 동기화 포지션 ID: {position_info['trade_id']}")
                    elif self.card_manager and position_info["trade_id"] not in existing_trade_ids:
                        try:
                            # 🎯 진짜 새로운 포지션에 대해서만 통합 생각카드 생성
                            card = self.card_manager.create_card_for_position(
                                position_id=position_info["trade_id"],
                                symbol=symbol,
                                initial_data=position_info
                            )
                            logger.info(f"🎯 [{symbol}] 동기화 포지션 통합 생각카드 생성: {card.card_id}")

                            # 초기 포지션 관리 정보 업데이트
                            self.card_manager.update_card(position_info["trade_id"], 'position_management', {
                                'action': 'sync_position',
                                'reason': f'바이낸스 동기화 {direction} 포지션',
                                'confidence': 0.8,
                                'market_condition': {
                                    'price': mark_price,
                                    'entry_price': entry_price,
                                    'pnl': pnl_pct
                                }
                            })

                        except Exception as card_e:
                            logger.warning(f"❌ [{symbol}] 동기화 포지션 카드 생성 실패: {card_e}")
                    else:
                        logger.info(f"🔒 [{symbol}] 동기화 카드 생성 스킵: trade_id 이미 존재 또는 카드 매니저 없음")

            # 기존 포지션 정보와 비교하여 업데이트
            self._update_positions_from_exchange(active_positions)

            # 추가 검증: 바이낸스에 포지션이 없는데 내부에 있는 경우 매핑만 수행
            if len(active_positions) == 0 and len(self.open_positions) > 0:
                logger.info(f"동기화 후에도 내부 포지션이 남아있음: {len(self.open_positions)}개 → 내부 전용으로 유지")

                # 남은 내부 포지션들을 내부 전용으로 표시
                for pos in self.open_positions:
                    # 🔧 포지션 객체 타입 검증
                    if not isinstance(pos, dict):
                        logger.warning(f"🔧 동기화 매핑 중 잘못된 포지션 타입 발견: {type(pos)}, 스킵")
                        continue

                    pos['sync_status'] = 'internal_only'
                    pos['last_sync_time'] = current_time

                    # 🔧 안전한 direction 접근 (direction 오류 해결)
                    try:
                        pos_direction = pos.get('direction', 'unknown') or pos.get('action', 'unknown')
                        pos_symbol = pos.get('symbol', 'unknown')
                        pos_pnl = pos.get('current_pnl', 0)
                        logger.info(f"내부 전용 유지: {pos_symbol} {pos_direction}, PnL: {pos_pnl:.2f}%")
                    except Exception as e:
                        logger.error(f"🔧 내부 전용 포지션 로깅 중 오류: {e}, 포지션: {pos}")
                        # 기본 정보만 로깅
                        logger.info(f"내부 전용 유지: {pos.get('symbol', 'unknown')} unknown, PnL: {pos.get('current_pnl', 0):.2f}%")

                logger.info("동기화 매핑 완료: 모든 내부 포지션 유지")

            # 🔥 동기화 결과 업데이트
            sync_result['success'] = True
            sync_result['positions_after'] = len(self.open_positions)
            sync_result['exchange_positions'] = len(active_positions)

            # 🔥 변경사항 기록
            if len(active_positions) != sync_result['positions_before']:
                sync_result['changes_made'].append(f"포지션 수 변경: {sync_result['positions_before']} → {len(self.open_positions)}")

            for pos in active_positions:
                sync_result['changes_made'].append(f"동기화: {pos['symbol']} {pos['direction']} (PnL: {pos['current_pnl']:.2f}%)")

            # 🔧 고아 포지션 자동 정리 (바이낸스에 없는 내부 포지션 정리)
            try:
                if len(active_positions) == 0 and len(self.open_positions) > 0:
                    logger.info("🔧 바이낸스에 포지션 없음 - 고아 포지션 자동 정리 실행")
                    orphan_cleanup_result = self.auto_clear_orphaned_positions(binance_utils)
                    if orphan_cleanup_result:
                        sync_result['changes_made'].append("고아 포지션 자동 정리 완료")
                        logger.info("✅ 고아 포지션 자동 정리 성공")
                    else:
                        logger.warning("⚠️ 고아 포지션 자동 정리 실패")
                else:
                    logger.debug("🔧 고아 포지션 정리 조건 미충족 - 스킵")
            except Exception as cleanup_e:
                logger.error(f"🔧 고아 포지션 자동 정리 중 오류: {cleanup_e}")

            # 마지막 동기화 시간 업데이트
            self.last_position_sync_time = current_time

            # 포트폴리오 저장
            self.save_portfolio()

            logger.info(f"🔥 포지션 동기화 완료: {len(active_positions)}개의 활성 포지션 (내부: {len(self.open_positions)}개)")
            logger.info(f"🔥 동기화 변경사항: {len(sync_result['changes_made'])}개")

            return sync_result

        except Exception as e:
            error_msg = f"포지션 동기화 중 오류 발생: {str(e)}"
            logger.error(error_msg)
            sync_result['errors'].append(error_msg)
            sync_result['success'] = False
            return sync_result

    def _update_positions_from_exchange(self, exchange_positions):
        """
        거래소에서 가져온 포지션 정보로 내부 상태 스마트 동기화
        기존 인터페이스 유지하면서 바이낸스 API 데이터 우선 적용

        Args:
            exchange_positions: 거래소에서 가져온 포지션 정보 목록
        """
        logger.info(f"스마트 포지션 동기화 시작: 현재 {len(self.open_positions)}개 포지션, 거래소에서 가져온 {len(exchange_positions)}개 포지션")

        # 🔧 입력 데이터 디버깅: exchange_positions 상세 확인
        logger.info(f"🔍 동기화 입력 - exchange_positions 상세:")
        for i, pos in enumerate(exchange_positions):
            symbol = pos.get('symbol', 'unknown')
            position_amt = pos.get('positionAmt', 0)
            size = pos.get('size', 0)
            quantity = pos.get('quantity', 0)
            logger.info(f"🔍 동기화 입력 - 포지션[{i}]: {symbol} (positionAmt={position_amt}, size={size}, quantity={quantity})")

        # 현재 내부 포지션 정보 (direction 키 안전하게 처리)
        current_positions = {}
        for p in self.open_positions:
            try:
                # 🔧 안전한 direction 추출
                if isinstance(p, dict):
                    direction = p.get('direction') or p.get('action', 'unknown')
                    symbol = p.get('symbol', 'unknown')
                    key = f"{symbol}_{direction}"
                    current_positions[key] = p
                else:
                    logger.warning(f"🔧 잘못된 포지션 타입: {type(p)}, 스킵")
                    continue
            except Exception as e:
                logger.error(f"포지션 키 생성 중 오류: {e}, 포지션: {p}")
                continue
        logger.info(f"현재 내부 포지션 키: {list(current_positions.keys())}")

        # 거래소 포지션 정보 (direction 키 안전하게 처리)
        exchange_pos_dict = {}
        for p in exchange_positions:
            try:
                # 🔧 안전한 direction 추출
                if isinstance(p, dict):
                    direction = p.get('direction') or p.get('action', 'unknown')
                    symbol = p.get('symbol', 'unknown')
                    key = f"{symbol}_{direction}"
                    exchange_pos_dict[key] = p
                else:
                    logger.warning(f"🔧 잘못된 거래소 포지션 타입: {type(p)}, 스킵")
                    continue
            except Exception as e:
                logger.error(f"거래소 포지션 키 생성 중 오류: {e}, 포지션: {p}")
                continue
        logger.info(f"거래소 포지션 키: {list(exchange_pos_dict.keys())}")

        # 새로운 포지션 목록 초기화
        updated_positions = []

        # 바이낸스 API에 포지션이 없는 경우
        if len(exchange_positions) == 0:
            if len(self.open_positions) > 0:
                logger.info(f"바이낸스 API에 포지션이 없지만 내부에 {len(self.open_positions)}개 포지션 존재")
                logger.info("동기화: 내부 포지션 상태를 유지하고 매핑만 수행")

                # 동기화는 매핑만 수행 - 포지션 삭제하지 않음
                # 내부 포지션들의 상태만 업데이트
                for pos in self.open_positions:
                    try:
                        # 🔧 안전한 포지션 처리
                        if isinstance(pos, dict):
                            pos['sync_status'] = 'internal_only'  # 내부에만 존재하는 포지션으로 표시
                            pos['last_sync_time'] = int(time.time())
                            direction = pos.get('direction') or pos.get('action', 'unknown')
                            symbol = pos.get('symbol', 'unknown')
                            logger.debug(f"동기화 매핑: {symbol} {direction} - 내부 전용 포지션으로 표시")
                        else:
                            logger.warning(f"🔧 잘못된 내부 포지션 타입: {type(pos)}, 스킵")
                    except Exception as e:
                        logger.error(f"🔧 내부 포지션 매핑 중 오류: {e}, 포지션: {pos}")

                # 내부 포지션을 updated_positions에 유지
                updated_positions = self.open_positions.copy()
                logger.info("동기화 매핑 완료: 내부 포지션 상태 유지")
            else:
                logger.info("바이낸스 API와 내부 포지션 모두 비어있음 - 정상 상태")
        else:
            # 거래소 포지션이 있는 경우: 스마트 매핑
            logger.info("바이낸스 API 포지션과 내부 포지션 스마트 매핑")

            # 1. 거래소에 있는 포지션 처리
            for key, exchange_pos in exchange_pos_dict.items():
                if key in current_positions:
                    # 기존 포지션 업데이트 (기존 메타데이터 유지)
                    current_pos = current_positions[key]

                    # 바이낸스 API 데이터로 업데이트 (기존 정보 보존)
                    updated_pos = current_pos.copy()
                    updated_pos.update({
                        "quantity": exchange_pos["quantity"],
                        "size": exchange_pos["size"],
                        "current_price": exchange_pos["current_price"],
                        "unrealized_pnl": exchange_pos["unrealized_pnl"],
                        "current_pnl": exchange_pos["current_pnl"],
                        "percentage": exchange_pos.get("percentage"),
                        "leverage": exchange_pos.get("leverage", current_pos.get("leverage", 1)),  # 레버리지 정보 보존
                        "last_updated": int(time.time())
                    })

                    updated_positions.append(updated_pos)
                    # 🔧 안전한 direction 추출
                    direction = exchange_pos.get('direction', 'unknown') or exchange_pos.get('action', 'unknown')
                    symbol = exchange_pos.get('symbol', 'unknown')
                    pnl = exchange_pos.get('current_pnl', 0)
                    logger.info(f"포지션 업데이트: {symbol} {direction}, PnL: {pnl:.2f}%")
                else:
                    # 새로운 포지션 추가 (바이낸스에만 있는 포지션)
                    new_pos = exchange_pos.copy()

                    # 기본 메타데이터 추가
                    if "trade_id" not in new_pos:
                        new_pos["trade_id"] = f"sync_{new_pos['symbol']}_{int(time.time())}"
                    new_pos["last_updated"] = int(time.time())
                    new_pos["entry_timestamp"] = int(time.time())

                    updated_positions.append(new_pos)
                    # 🔧 안전한 direction 추출
                    direction = exchange_pos.get('direction', 'unknown') or exchange_pos.get('action', 'unknown')
                    symbol = exchange_pos.get('symbol', 'unknown')
                    size = exchange_pos.get('size', 0)
                    logger.info(f"새 포지션 발견: {symbol} {direction}, 크기: {size}")

                    # 🔒 동기화된 포지션 중복 생성 방지: 기존 포지션 확인
                    existing_same_direction = self._get_existing_position(new_pos["symbol"])
                    new_pos_direction = new_pos.get("direction") or new_pos.get("action", "unknown")
                    existing_direction = existing_same_direction.get('direction') or existing_same_direction.get('action', 'unknown') if existing_same_direction else None
                    if existing_same_direction and existing_direction == new_pos_direction:
                        logger.info(f"🔒 [{new_pos['symbol']}] 동일한 방향 포지션이 이미 존재하여 카드 생성 스킵: {new_pos_direction}")
                        logger.info(f"🔒 [{new_pos['symbol']}] 기존 포지션 ID: {existing_same_direction.get('trade_id')}, 새 포지션 ID: {new_pos['trade_id']}")
                    elif self.card_manager:
                        try:
                            # 🎯 진짜 새로운 포지션에 대해서만 통합 생각카드 생성
                            card = self.card_manager.create_card_for_position(
                                position_id=new_pos["trade_id"],
                                symbol=new_pos["symbol"],
                                initial_data=new_pos
                            )
                            logger.info(f"🎯 [{new_pos['symbol']}] 동기화 포지션 통합 생각카드 생성: {card.card_id}")

                            # 초기 포지션 관리 정보 업데이트
                            sync_direction = new_pos.get("direction") or new_pos.get("action", "unknown")
                            self.card_manager.update_card(new_pos["trade_id"], 'position_management', {
                                'action': 'sync_position',
                                'reason': f'바이낸스 동기화 {sync_direction} 포지션',
                                'confidence': 0.8,
                                'market_condition': {
                                    'price': new_pos.get('current_price', 0),
                                    'entry_price': new_pos.get('entry_price', 0),
                                    'pnl': new_pos.get('current_pnl', 0)
                                }
                            })

                        except Exception as card_e:
                            logger.warning(f"❌ [{new_pos['symbol']}] 동기화 포지션 카드 생성 실패: {card_e}")

            # 2. 내부에만 있는 포지션 처리 (거래소에 없는 포지션)
            for key, internal_pos in current_positions.items():
                if key not in exchange_pos_dict:
                    try:
                        # 🔧 안전한 내부 포지션 처리
                        if isinstance(internal_pos, dict):
                            # 거래소에 없는 포지션은 내부 전용으로 표시 (삭제하지 않음)
                            internal_pos['sync_status'] = 'internal_only'
                            internal_pos['last_sync_time'] = int(time.time())

                            # 🔥 내부 전용 포지션에서도 PnL 계산 (현재가 업데이트 포함)
                            try:
                                entry_price = internal_pos.get('entry_price', 0)
                                symbol = internal_pos.get('symbol', 'unknown')
                                direction = internal_pos.get('direction', 'unknown')

                                # 현재가 조회 (바이낸스 유틸리티 사용)
                                current_price = 0
                                if binance_utils:
                                    try:
                                        price_info = binance_utils.get_futures_price(symbol)
                                        if 'error' not in price_info:
                                            current_price = float(price_info['price'])
                                            internal_pos['current_price'] = current_price
                                            logger.debug(f"🔥 [{symbol}] 내부 전용 포지션 현재가 업데이트: ${current_price:.2f}")
                                        else:
                                            current_price = internal_pos.get('current_price', entry_price)
                                            logger.warning(f"⚠️ [{symbol}] 현재가 조회 실패, 기존값 사용: ${current_price:.2f}")
                                    except Exception as price_error:
                                        current_price = internal_pos.get('current_price', entry_price)
                                        logger.warning(f"⚠️ [{symbol}] 현재가 조회 오류: {price_error}, 기존값 사용: ${current_price:.2f}")
                                else:
                                    current_price = internal_pos.get('current_price', entry_price)
                                    logger.debug(f"🔧 [{symbol}] binance_utils 없음, 기존 가격 사용: ${current_price:.2f}")

                                if entry_price > 0 and current_price > 0:
                                    if direction == "long":
                                        current_pnl_pct = (current_price - entry_price) / entry_price * 100
                                    elif direction == "short":
                                        current_pnl_pct = (entry_price - current_price) / entry_price * 100
                                    else:
                                        current_pnl_pct = 0

                                    internal_pos['current_pnl_pct'] = current_pnl_pct
                                    logger.info(f"🔥 [{symbol}] 내부 전용 포지션 PnL 계산: {current_pnl_pct:.2f}% (진입가: ${entry_price:.2f}, 현재가: ${current_price:.2f})")
                                else:
                                    internal_pos['current_pnl_pct'] = 0
                                    logger.warning(f"⚠️ [{symbol}] 내부 전용 포지션 PnL 계산 불가: 진입가={entry_price}, 현재가={current_price}")
                            except Exception as pnl_error:
                                logger.error(f"❌ [{symbol}] 내부 전용 포지션 PnL 계산 오류: {pnl_error}")
                                internal_pos['current_pnl_pct'] = 0

                            # 🔧 내부 전용 포지션 자동 정리 로직
                            internal_direction = internal_pos.get('direction', 'unknown') or internal_pos.get('action', 'unknown')
                            pos_symbol = internal_pos.get('symbol', 'unknown')
                            current_pnl_pct = internal_pos.get('current_pnl_pct', 0)

                            # 내부 전용 포지션이 오래되었거나 손실이 큰 경우 자동 정리
                            position_age = current_time - internal_pos.get('timestamp', current_time)
                            should_cleanup = False
                            cleanup_reason = ""

                            # 1. 24시간 이상 된 내부 전용 포지션
                            if position_age > 86400:  # 24시간
                                should_cleanup = True
                                cleanup_reason = f"24시간 초과 ({position_age//3600:.1f}시간)"

                            # 2. 손실이 -5% 이상인 포지션
                            elif current_pnl_pct < -5.0:
                                should_cleanup = True
                                cleanup_reason = f"손실 {current_pnl_pct:.2f}%"

                            # 3. 거래소에 동일 심볼의 다른 포지션이 있는 경우
                            elif any(ep['symbol'] == pos_symbol for ep in exchange_positions):
                                should_cleanup = True
                                cleanup_reason = "거래소에 동일 심볼 포지션 존재"

                            if should_cleanup:
                                logger.warning(f"🧹 [{pos_symbol}] 내부 전용 포지션 자동 정리: {cleanup_reason}")
                                # 포지션을 추가하지 않음 (자동 정리)
                            else:
                                updated_positions.append(internal_pos)
                                logger.info(f"동기화 매핑: {pos_symbol} {internal_direction} - 내부 전용 포지션으로 유지 (PnL: {current_pnl_pct:.2f}%)")
                        else:
                            logger.warning(f"🔧 잘못된 내부 포지션 타입: {type(internal_pos)}, 스킵")
                    except Exception as e:
                        logger.error(f"🔧 내부 전용 포지션 처리 중 오류: {e}, 포지션: {internal_pos}")

            # 업데이트된 포지션 목록 적용
            self.open_positions = updated_positions

        logger.info(f"스마트 포지션 동기화 완료: 최종 {len(self.open_positions)}개 포지션")

        # 동기화 후 검증 (활성 포지션만 카운트)
        # 🔧 exchange_positions는 이미 활성 포지션만 포함하므로 추가 필터링 불필요
        active_exchange_positions = exchange_positions  # 이미 필터링됨

        # 🔧 동기화 과정 디버깅: exchange_positions 내용 상세 확인
        logger.info(f"🔍 동기화 결과 - exchange_positions 배열 크기: {len(exchange_positions)}")
        for i, pos in enumerate(exchange_positions):
            symbol = pos.get('symbol', 'unknown')
            position_amt = pos.get('positionAmt', 0)
            size = pos.get('size', 0)
            quantity = pos.get('quantity', 0)
            logger.info(f"🔍 동기화 결과 - exchange_positions[{i}]: {symbol} (positionAmt={position_amt}, size={size}, quantity={quantity})")

        logger.info(f"🔍 검증 단계 - 바이낸스 포지션 수: {len(active_exchange_positions)}")
        for i, pos in enumerate(active_exchange_positions):
            logger.info(f"🔍 검증 단계 - 바이낸스 포지션 {i+1}: {pos.get('symbol')} (크기: {pos.get('size', pos.get('quantity', pos.get('positionAmt', 0)))})")

        # 🔧 포지션 객체 타입 검증 후 활성 포지션 필터링
        active_internal_positions = [pos for pos in self.open_positions if isinstance(pos, dict) and pos.get('quantity', 0) != 0]
        logger.info(f"🔍 검증 단계 - 내부 포지션 수: {len(active_internal_positions)}")
        for i, pos in enumerate(active_internal_positions):
            logger.info(f"🔍 검증 단계 - 내부 포지션 {i+1}: {pos.get('symbol')} (크기: {pos.get('quantity', 0)})")

        if len(active_exchange_positions) != len(active_internal_positions):
            logger.warning(f"포지션 수 차이: 바이낸스 {len(active_exchange_positions)}개 vs 내부 {len(active_internal_positions)}개")
            # 🔧 바이낸스 포지션은 positionAmt 또는 size 필드 사용 (디버깅 추가)
            binance_positions_str = []
            for pos in active_exchange_positions:
                position_amt = pos.get('positionAmt', 0)
                size = pos.get('size', 0)
                quantity = pos.get('quantity', 0)
                logger.debug(f"🔍 [{pos['symbol']}] 포지션 크기 디버깅: positionAmt={position_amt}, size={size}, quantity={quantity}")

                # 우선순위: positionAmt > size > quantity
                if position_amt and position_amt != 0:
                    final_size = abs(float(position_amt))
                elif size and size != 0:
                    final_size = abs(float(size))
                elif quantity and quantity != 0:
                    final_size = abs(float(quantity))
                else:
                    final_size = 0.0

                binance_positions_str.append(f"{pos['symbol']}({final_size})")
                logger.debug(f"🔍 [{pos['symbol']}] 최종 크기: {final_size}")

            internal_positions_str = [f"{pos['symbol']}({pos.get('quantity', 0)})" for pos in active_internal_positions]
            logger.info(f"바이낸스 활성 포지션: {binance_positions_str}")
            logger.info(f"내부 활성 포지션: {internal_positions_str}")
        else:
            logger.info("포지션 동기화 검증 성공: 바이낸스 API와 내부 포지션 수 일치")
            # 🔧 성공 시에도 포지션 정보 표시 (디버깅 추가)
            binance_positions_str = []
            for pos in active_exchange_positions:
                position_amt = pos.get('positionAmt', 0)
                size = pos.get('size', 0)
                quantity = pos.get('quantity', 0)
                logger.debug(f"🔍 [{pos['symbol']}] 포지션 크기 디버깅: positionAmt={position_amt}, size={size}, quantity={quantity}")

                # 우선순위: positionAmt > size > quantity
                if position_amt and position_amt != 0:
                    final_size = abs(float(position_amt))
                elif size and size != 0:
                    final_size = abs(float(size))
                elif quantity and quantity != 0:
                    final_size = abs(float(quantity))
                else:
                    final_size = 0.0

                binance_positions_str.append(f"{pos['symbol']}({final_size})")
                logger.debug(f"🔍 [{pos['symbol']}] 최종 크기: {final_size}")

            internal_positions_str = [f"{pos['symbol']}({pos.get('quantity', 0)})" for pos in active_internal_positions]
            logger.info(f"바이낸스 활성 포지션: {binance_positions_str}")
            logger.info(f"내부 활성 포지션: {internal_positions_str}")

        # 🔥 실시간 포지션 불일치 감지 및 알림
        self._detect_position_discrepancies(exchange_positions)

        # 포트폴리오 저장
        self.save_portfolio()

    def _detect_position_discrepancies(self, exchange_positions):
        """
        🔥 실시간 포지션 불일치 감지 및 알림 시스템

        Args:
            exchange_positions: 거래소에서 가져온 포지션 정보
        """
        try:
            # 내부 포지션과 거래소 포지션 비교 (direction 키 안전하게 처리)
            internal_positions = {}
            for p in self.open_positions:
                try:
                    # 🔧 안전한 direction 추출
                    if isinstance(p, dict):
                        direction = p.get('direction', 'unknown') or p.get('action', 'unknown')
                        symbol = p.get('symbol', 'unknown')
                        key = f"{symbol}_{direction}"
                        internal_positions[key] = p
                    else:
                        logger.warning(f"🔧 불일치 감지 중 잘못된 내부 포지션 타입: {type(p)}, 스킵")
                except Exception as e:
                    logger.error(f"내부 포지션 키 생성 중 오류: {e}, 포지션: {p}")
                    continue

            exchange_pos_dict = {}
            for p in exchange_positions:
                try:
                    # 🔧 안전한 direction 추출
                    if isinstance(p, dict):
                        direction = p.get('direction', 'unknown') or p.get('action', 'unknown')
                        symbol = p.get('symbol', 'unknown')
                        key = f"{symbol}_{direction}"
                        exchange_pos_dict[key] = p
                    else:
                        logger.warning(f"🔧 불일치 감지 중 잘못된 거래소 포지션 타입: {type(p)}, 스킵")
                except Exception as e:
                    logger.error(f"거래소 포지션 키 생성 중 오류: {e}, 포지션: {p}")
                    continue

            discrepancies = []

            # 1. 내부에만 있는 포지션 (거래소에 없음)
            for key, internal_pos in internal_positions.items():
                if key not in exchange_pos_dict:
                    try:
                        # 🔧 안전한 direction 추출
                        if isinstance(internal_pos, dict):
                            internal_direction = internal_pos.get('direction', 'unknown') or internal_pos.get('action', 'unknown')
                            symbol = internal_pos.get('symbol', 'unknown')
                            quantity = internal_pos.get('quantity', 0)
                            discrepancies.append({
                                'type': 'internal_only',
                                'symbol': symbol,
                                'direction': internal_direction,
                                'internal_size': quantity,
                                'exchange_size': 0,
                                'severity': 'high'
                            })
                        else:
                            logger.warning(f"🔧 불일치 감지 중 잘못된 내부 포지션 타입: {type(internal_pos)}, 스킵")
                    except Exception as e:
                        logger.error(f"🔧 내부 전용 포지션 불일치 감지 중 오류: {e}, 포지션: {internal_pos}")

            # 2. 거래소에만 있는 포지션 (내부에 없음)
            for key, exchange_pos in exchange_pos_dict.items():
                if key not in internal_positions:
                    try:
                        # 🔧 안전한 direction 추출
                        if isinstance(exchange_pos, dict):
                            exchange_direction = exchange_pos.get('direction', 'unknown') or exchange_pos.get('action', 'unknown')
                            symbol = exchange_pos.get('symbol', 'unknown')
                            # 🔧 바이낸스 포지션은 positionAmt 필드 사용
                            exchange_size = abs(float(exchange_pos.get('positionAmt', exchange_pos.get('quantity', 0))))
                            discrepancies.append({
                                'type': 'exchange_only',
                                'symbol': symbol,
                                'direction': exchange_direction,
                                'internal_size': 0,
                                'exchange_size': exchange_size,
                                'severity': 'high'
                            })
                        else:
                            logger.warning(f"🔧 불일치 감지 중 잘못된 거래소 포지션 타입: {type(exchange_pos)}, 스킵")
                    except Exception as e:
                        logger.error(f"🔧 거래소 전용 포지션 불일치 감지 중 오류: {e}, 포지션: {exchange_pos}")

            # 3. 크기 불일치 (같은 포지션이지만 크기가 다름)
            for key in internal_positions.keys() & exchange_pos_dict.keys():
                try:
                    internal_pos = internal_positions[key]
                    exchange_pos = exchange_pos_dict[key]

                    # 🔧 안전한 크기 추출
                    if isinstance(internal_pos, dict) and isinstance(exchange_pos, dict):
                        internal_size = float(internal_pos.get('quantity', 0))
                        # 🔧 바이낸스 포지션은 positionAmt 필드 사용
                        exchange_size = abs(float(exchange_pos.get('positionAmt', exchange_pos.get('quantity', 0))))

                        # 5% 이상 차이나면 불일치로 판정
                        if abs(internal_size - exchange_size) / max(internal_size, exchange_size, 0.001) > 0.05:
                            mismatch_direction = internal_pos.get('direction', 'unknown') or internal_pos.get('action', 'unknown')
                            symbol = internal_pos.get('symbol', 'unknown')
                            discrepancies.append({
                                'type': 'size_mismatch',
                                'symbol': symbol,
                                'direction': mismatch_direction,
                                'internal_size': internal_size,
                                'exchange_size': exchange_size,
                                'difference': abs(internal_size - exchange_size),
                                'severity': 'medium'
                            })
                    else:
                        logger.warning(f"🔧 크기 불일치 감지 중 잘못된 포지션 타입: internal={type(internal_pos)}, exchange_pos={type(exchange_pos)}")
                except Exception as e:
                    logger.error(f"🔧 크기 불일치 감지 중 오류: {e}, key: {key}")

            # 🚨 불일치 발견 시 알림
            if discrepancies:
                logger.warning(f"🚨 포지션 불일치 감지: {len(discrepancies)}개 문제 발견")

                for disc in discrepancies:
                    if disc['type'] == 'internal_only':
                        logger.error(f"🔴 [{disc['symbol']}] 내부 전용 포지션: {disc['direction']} {disc['internal_size']} (거래소에 없음)")
                    elif disc['type'] == 'exchange_only':
                        logger.error(f"🔴 [{disc['symbol']}] 거래소 전용 포지션: {disc['direction']} {disc['exchange_size']} (내부에 없음)")
                    elif disc['type'] == 'size_mismatch':
                        logger.warning(f"🟡 [{disc['symbol']}] 크기 불일치: {disc['direction']} 내부={disc['internal_size']:.4f} vs 거래소={disc['exchange_size']:.4f}")

                # 🔥 불일치 통계 저장
                if not hasattr(self, 'discrepancy_history'):
                    self.discrepancy_history = []

                self.discrepancy_history.append({
                    'timestamp': int(time.time()),
                    'discrepancies': discrepancies,
                    'total_count': len(discrepancies),
                    'high_severity': len([d for d in discrepancies if d['severity'] == 'high']),
                    'medium_severity': len([d for d in discrepancies if d['severity'] == 'medium'])
                })

                # 최근 10개만 유지
                if len(self.discrepancy_history) > 10:
                    self.discrepancy_history = self.discrepancy_history[-10:]

            else:
                logger.info("✅ 포지션 동기화 상태 양호: 불일치 없음")

        except Exception as e:
            logger.error(f"포지션 불일치 감지 중 오류: {e}")

    def get_sync_status_report(self) -> Dict[str, Any]:
        """
        🔥 포지션 동기화 상태 리포트 생성

        Returns:
            Dict: 동기화 상태 상세 정보
        """
        try:
            current_time = int(time.time())

            # 기본 동기화 정보
            sync_report = {
                'timestamp': current_time,
                'last_sync_time': self.last_position_sync_time,
                'sync_interval': self.position_sync_interval,
                'time_since_last_sync': current_time - self.last_position_sync_time,
                'sync_status': 'healthy' if current_time - self.last_position_sync_time < 60 else 'stale',
                'total_positions': len(self.open_positions),
                'mode': self.mode
            }

            # 포지션 상세 정보
            position_details = []
            for pos in self.open_positions:
                pos_direction = pos.get('direction') or pos.get('action', 'unknown')
                position_details.append({
                    'symbol': pos['symbol'],
                    'direction': pos_direction,
                    'size': pos.get('quantity', 0),
                    'entry_price': pos.get('entry_price', 0),
                    'current_pnl': pos.get('current_pnl', 0),
                    'sync_status': pos.get('sync_status', 'unknown'),
                    'last_sync_time': pos.get('last_sync_time', 0)
                })

            sync_report['positions'] = position_details

            # 불일치 히스토리
            if hasattr(self, 'discrepancy_history'):
                recent_discrepancies = self.discrepancy_history[-5:] if self.discrepancy_history else []
                sync_report['recent_discrepancies'] = recent_discrepancies
                sync_report['total_discrepancies'] = len(self.discrepancy_history)
            else:
                sync_report['recent_discrepancies'] = []
                sync_report['total_discrepancies'] = 0

            return sync_report

        except Exception as e:
            logger.error(f"동기화 상태 리포트 생성 실패: {e}")
            return {
                'timestamp': int(time.time()),
                'error': str(e),
                'sync_status': 'error'
            }

    def force_position_reconciliation(self, binance_utils) -> Dict[str, Any]:
        """
        🔥 강제 포지션 조정 (불일치 해결)

        Args:
            binance_utils: 바이낸스 유틸리티 객체

        Returns:
            Dict: 조정 결과
        """
        try:
            logger.info("🔥 강제 포지션 조정 시작")

            # 1. 현재 거래소 포지션 조회
            positions = binance_utils.get_futures_positions()
            if isinstance(positions, dict) and 'error' in positions:
                return {'success': False, 'error': positions['error']}

            # 2. 강제 동기화 실행
            sync_result = self.sync_positions_with_exchange(binance_utils, force=True)

            # 3. 조정 결과 리포트
            reconciliation_result = {
                'success': sync_result.get('success', False),
                'timestamp': int(time.time()),
                'positions_before': sync_result.get('positions_before', 0),
                'positions_after': sync_result.get('positions_after', 0),
                'exchange_positions': sync_result.get('exchange_positions', 0),
                'changes_made': sync_result.get('changes_made', []),
                'errors': sync_result.get('errors', [])
            }

            logger.info(f"🔥 강제 포지션 조정 완료: {reconciliation_result['success']}")

            return reconciliation_result

        except Exception as e:
            error_msg = f"강제 포지션 조정 실패: {e}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'timestamp': int(time.time())
            }

    def execute_trade(self, symbol: str, strategy: Dict[str, Any],
                    market_data: Dict[str, Any], binance_utils=None,
                    advanced_trading_system=None) -> Dict[str, Any]:
        """
        전략 실행 (매수/매도)

        Args:
            symbol: 거래 심볼 (예: BTC)
            strategy: 실행할 전략
            market_data: 현재 시장 데이터
            binance_utils: 바이낸스 유틸리티 객체 (실제 거래 시 필요)
            advanced_trading_system: 고급 트레이딩 시스템 (선택사항)

        Returns:
            거래 실행 결과
        """

        # 🔧 전략에서 direction 추출 (direction 오류 해결)
        try:
            # 전략에서 direction 정보 추출
            strategy_type = strategy.get('type', 'unknown')
            strategy_action = strategy.get('action', 'unknown')

            # direction 결정 우선순위: action > type
            if strategy_action and strategy_action != 'unknown':
                direction = strategy_action
            elif strategy_type and strategy_type != 'unknown':
                direction = strategy_type
            else:
                direction = 'unknown'

            # 안전한 심볼 문자열 변환
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            logger.info(f"🔧 [{symbol_str}] 전략에서 direction 추출: type={strategy_type}, action={strategy_action} → direction={direction}")

        except Exception as e:
            # direction 추출 실패 시 기본값 사용
            direction = 'unknown'
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            logger.warning(f"🔧 [{symbol_str}] direction 추출 실패: {e}, 기본값 사용: {direction}")
        logger.info(f"[디버그] execute_trade 시작: 심볼={symbol}, 전략={str(strategy)}")
        logger.info(f"[디버그] 시장 데이터: {str(market_data)}")
        logger.info(f"[디버그] 바이낸스 유틸리티: {binance_utils is not None}")
        logger.info(f"[디버그] 고급 트레이딩 시스템: {advanced_trading_system is not None}")

        # 🔧 포지션 청산 쿨다운 체크 (연속 같은 방향 거래 방지)
        current_time = int(time.time())
        if symbol in self.position_close_history:
            close_info = self.position_close_history[symbol]
            close_time = close_info['close_time']
            close_direction = close_info['direction']
            time_since_close = current_time - close_time

            # 전략에서 방향 추출 (임시)
            strategy_direction = strategy.get("direction", strategy.get("type", strategy.get("action", "none")))
            strategy_direction = self._normalize_direction(strategy_direction)

            # 같은 방향 재진입 쿨다운 체크
            if strategy_direction == close_direction and time_since_close < self.position_cooldown_seconds:
                remaining_cooldown = self.position_cooldown_seconds - time_since_close
                logger.warning(f"🔧 [{symbol}] 포지션 청산 쿨다운 중: {close_direction} 방향 재진입 차단 (남은 시간: {remaining_cooldown}초)")
                return {
                    "success": False,
                    "reason": "position_cooldown",
                    "message": f"포지션 청산 후 쿨다운 중: {close_direction} 방향 재진입 차단 (남은 시간: {remaining_cooldown}초)"
                }
            elif time_since_close >= self.position_cooldown_seconds:
                # 쿨다운 완료 시 기록 제거
                del self.position_close_history[symbol]
                logger.info(f"🔧 [{symbol}] 포지션 청산 쿨다운 완료: {close_direction} 방향 재진입 허용")

        # 🚀 고급 트레이딩 시스템 분석 (활성화된 경우)
        advanced_analysis = None
        if advanced_trading_system:
            try:
                logger.info(f"🔥 [{symbol}] 고급 트레이딩 시스템 분석 시작")

                # InCA 분석 결과 추출 (strategy에서)
                inca_analysis = None
                if strategy and isinstance(strategy, dict):
                    # 🔍 strategy에서 importance 추출 디버깅
                    strategy_importance = strategy.get('importance', None)
                    logger.info(f"🔍 [{symbol}] strategy importance 디버깅:")
                    logger.info(f"   - strategy 키들: {list(strategy.keys())}")
                    logger.info(f"   - strategy importance 값: {strategy_importance} (타입: {type(strategy_importance)})")
                    logger.info(f"   - strategy importance 존재: {'importance' in strategy}")

                    # importance 값 결정
                    if strategy_importance is not None and strategy_importance != 0:
                        final_importance = strategy_importance
                        logger.info(f"✅ [{symbol}] strategy에서 importance 사용: {final_importance}")
                    else:
                        final_importance = 5.0
                        logger.warning(f"⚠️ [{symbol}] strategy에 importance 없음, 기본값 사용: {final_importance}")

                    # 🚀 신뢰도 정규화 (1-10 범위를 0.0-1.0으로 변환)
                    raw_confidence = strategy.get('confidence', 0.5)
                    if raw_confidence > 1.0:
                        # 1-10 범위를 0.0-1.0으로 정규화
                        normalized_confidence = raw_confidence / 10.0
                        logger.info(f"🔧 [{symbol}] 신뢰도 정규화: {raw_confidence} → {normalized_confidence:.3f}")
                    else:
                        # 이미 0.0-1.0 범위
                        normalized_confidence = raw_confidence

                    inca_analysis = {
                        'confidence': normalized_confidence,
                        'importance': final_importance,
                        'action_recommendation': strategy.get('action', 'hold'),
                        'reasoning': strategy.get('reasoning', '')
                    }
                    logger.info(f"🎯 [{symbol}] InCA 분석 결과 전달: 신뢰도 {inca_analysis['confidence']:.1%}, 중요도 {inca_analysis['importance']}")

                advanced_analysis = advanced_trading_system.analyze_trading_opportunity(
                    market_data, self.balance, inca_analysis
                )

                # 고급 시스템 추천이 거래 금지인 경우 조기 종료
                if not advanced_analysis.get('recommendation', {}).get('should_trade', True):
                    reason = advanced_analysis.get('recommendation', {}).get('reasoning', '고급 시스템 거래 금지')
                    logger.info(f"🚫 [{symbol}] 고급 트레이딩 시스템 거래 금지: {reason}")
                    return {
                        'success': False,
                        'error': f'고급 시스템 거래 금지: {reason}',
                        'advanced_analysis': advanced_analysis
                    }

                logger.info(f"✅ [{symbol}] 고급 트레이딩 시스템 분석 완료: {advanced_analysis.get('signal', 'unknown')}")

            except Exception as e:
                logger.error(f"❌ [{symbol}] 고급 트레이딩 시스템 분석 실패: {e}")
                advanced_analysis = None

        # 실제 거래 모드인 경우 포지션 정보 동기화
        if self.mode == 'real' and binance_utils:
            # 🔥 모든 심볼 포지션 정보 동기화 (특정 심볼만 필터링하지 않음)
            self.sync_positions_with_exchange(binance_utils, symbols=None)
            logger.info(f"모든 포지션 정보 동기화 완료 (요청 심볼: {symbol})")

        # 현재 시간 및 가격 정보 (바이낸스 서버 시간 동기화)
        timestamp = self._get_synchronized_timestamp(binance_utils)
        current_price = market_data.get('price', 0)

        logger.info(f"[디버그] 현재 가격: {current_price}")

        if current_price <= 0:
            logger.error(f"{symbol} 가격 정보가 유효하지 않습니다.")
            return {"success": False, "error": "유효하지 않은 가격"}

        # 🔧 같은 방향 포지션 처리 완료 플래그 초기화
        same_direction_processed = False

        # 방향 확인 (강화된 로직)
        direction = strategy.get("direction", "none")
        if direction == "none":
            # type 필드 우선 확인 (SELA에서 주로 사용)
            type_field = strategy.get("type", "none")
            if type_field in ["buy", "sell", "long", "short", "hold", "none"]:
                direction = type_field
                logger.info(f"[디버그] type 필드에서 direction 추출: {type_field}")
            else:
                # position 또는 action 필드 확인
                direction = strategy.get("position", strategy.get("action", "none"))
                logger.info(f"[디버그] position/action 필드에서 direction 추출: {direction}")
        else:
            logger.info(f"[디버그] direction 필드 직접 사용: {direction}")

        # 🔧 방향 정규화 (buy/sell → long/short 변환)
        original_direction = direction
        direction = self._normalize_direction(direction)
        if original_direction != direction:
            logger.info(f"[디버그] 방향 정규화: {original_direction} → {direction}")

        # 중요도 확인
        importance = strategy.get("importance", 0)
        if importance == 0:
            importance = 5  # 기본값 5 (중간 중요도)

        # 기존 포지션 확인
        existing_position = None
        for pos in self.open_positions:
            if pos['symbol'] == symbol:
                existing_position = pos
                break

        # 🔧 사이클 마지막에만 포지션 변화 처리 (중간 변화 무시)
        if existing_position and existing_position['direction'] != direction:
            logger.info(f"🔄 [{symbol}] 반대 포지션 감지: 기존={existing_position['direction']}, 새={direction}")
            logger.info(f"⏳ [{symbol}] 사이클 진행 중 - 포지션 변화를 사이클 마지막까지 대기")

            # 포지션 변화 요청을 저장 (사이클 마지막에 처리)
            self._store_pending_position_change(symbol, direction, market_data, binance_utils, existing_position)

            return {
                "success": True,
                "message": f"포지션 변화 요청 저장됨: {existing_position['direction']} → {direction} (사이클 마지막에 처리)",
                "action": "position_change_pending"
            }

        # 🔧 동일 방향 포지션 처리 (올바른 로직)
        if existing_position and existing_position['direction'] == direction:
            # 현재 importance 값 확인
            existing_importance = existing_position.get("importance", 0)
            existing_confidence = existing_position.get("confidence", 0.5)
            new_confidence = strategy.get("confidence", 0.5)

            logger.info(f"🔄 [{symbol}] 같은 방향 포지션 감지: {direction} (기존 중요도: {existing_importance:.2f}, 새 중요도: {importance:.2f})")
            logger.info(f"🔄 [{symbol}] 신뢰도 비교: 기존={existing_confidence:.2f}, 새={new_confidence:.2f}")

            # 🚀 같은 방향 신호일 때는 기존 포지션 유지 (불필요한 거래 비용 방지)
            # 신뢰도 차이가 크지 않으면 기존 포지션 유지
            confidence_threshold = 0.15  # 신뢰도 임계값을 0.15로 상향 조정
            if new_confidence > existing_confidence + confidence_threshold:  # 신뢰도가 0.15 이상 높을 때만 업데이트
                logger.info(f"🎯 [{symbol}] 신뢰도 향상으로 포지션 정보 업데이트: {existing_confidence:.2f} → {new_confidence:.2f}")
                # 포지션 정보만 업데이트 (실제 거래는 하지 않음)
                existing_position["importance"] = importance
                existing_position["confidence"] = new_confidence
                existing_position["strategy_id"] = strategy.get("strategy_id", existing_position.get("strategy_id"))

                # 🚀 InCA 학습을 위한 포지션 유지 결정 피드백
                self._add_position_maintenance_to_learning(symbol, strategy, existing_position, "confidence_improved")

                # 🚀 같은 방향 포지션 처리 완료 표시
                same_direction_processed = True
                logger.info(f"✅ [{symbol}] 같은 방향 포지션 처리 완료 - 추가 포지션 전환 로직 스킵")

                return {
                    "success": True,
                    "reason": "position_updated",
                    "message": f"기존 {direction} 포지션 정보 업데이트 완료 (신뢰도: {existing_confidence:.2f} → {new_confidence:.2f})"
                }
            else:
                # 🔇 같은 방향 포지션 유지 로그 빈도 제한 (5분 간격)
                current_time = time.time()
                last_hold_key = f"{symbol}_last_same_direction_hold"
                last_hold_time = getattr(self, last_hold_key, 0)

                if current_time - last_hold_time > 300:  # 5분 간격
                    logger.info(f"🎯 [{symbol}] 같은 방향 포지션 유지: 신뢰도 향상 없음 ({existing_confidence:.2f} vs {new_confidence:.2f})")
                    setattr(self, last_hold_key, current_time)
                else:
                    logger.debug(f"🔇 [{symbol}] 같은 방향 포지션 유지 (로그 생략, 마지막: {int(current_time - last_hold_time)}초 전)")

                # 🚀 InCA 학습을 위한 포지션 유지 결정 피드백 (5분 간격으로 제한)
                if current_time - last_hold_time > 300:
                    self._add_position_maintenance_to_learning(symbol, strategy, existing_position, "same_direction_hold")

                # 🚀 같은 방향 포지션 처리 완료 표시
                same_direction_processed = True

                # 🚀 같은 방향 포지션 유지 시 성공 반환 (추가 거래 방지)
                logger.info(f"✅ [{symbol}] 같은 방향 포지션 처리 완료 - 추가 포지션 전환 로직 스킵")
                return {
                    "success": True,
                    "reason": "same_direction_hold",
                    "message": f"같은 방향({direction}) 포지션이 이미 존재하여 기존 포지션 유지 (신뢰도: {existing_confidence:.2f})",
                    "existing_position": existing_position,
                    "skipped_trade": True  # 거래 스킵 표시
                }

        # 전략 정보
        strategy_type = strategy.get("strategy_type", "unknown")
        # direction은 이미 683-696번째 줄에서 추출했으므로 중복 제거

        entry_price = strategy.get("entry_price", current_price)
        take_profit = strategy.get("take_profit", 0)
        stop_loss = strategy.get("stop_loss", 0)

        # 진입 조건 강화: 추가 확인 단계
        # 1. 신뢰도 및 중요도 확인 및 정규화
        importance = strategy.get("importance", 0)

        # 🚀 신뢰도 정규화 (1-10 범위를 0.0-1.0으로 변환)
        raw_confidence = strategy.get("confidence", 0)
        if raw_confidence > 1.0:
            # 1-10 범위를 0.0-1.0으로 정규화
            confidence = raw_confidence / 10.0
            logger.info(f"🔧 [{symbol}] 전략 신뢰도 정규화: {raw_confidence} → {confidence:.3f}")
        else:
            # 이미 0.0-1.0 범위
            confidence = raw_confidence

        # 🚀 LLM 완전 신뢰 모드: 임계값 체크 비활성화
        LLM_TRUST_MODE = True  # LLM 판단 100% 존중
        MIN_CONFIDENCE = 0.0   # 임계값 완전 제거
        MIN_IMPORTANCE = 0.0   # 임계값 완전 제거

        logger.info(f"🤖 LLM 완전 신뢰 모드 활성화 - 임계값 무시하고 LLM 판단 존중")

        # 신뢰도나 중요도가 없는 경우 기본값 설정
        if confidence == 0:
            confidence = 0.1  # 기본값 0.1 (정규화된 범위에서 낮은 신뢰도)
            logger.info(f"{symbol} 전략에 신뢰도가 없어 기본값 {confidence}로 설정 (투자 허용을 위해 낮게 설정)")

        if importance == 0:
            importance = 1  # 기본값 1 (낮은 중요도로 변경하여 투자 허용)
            logger.info(f"{symbol} 전략에 중요도가 없어 기본값 {importance}로 설정 (투자 허용을 위해 낮게 설정)")

        # 신뢰도와 중요도 검사 (0으로 설정하면 모든 거래 허용)
        logger.info(f"[디버그] 임계값 확인: MIN_CONFIDENCE={MIN_CONFIDENCE}, MIN_IMPORTANCE={MIN_IMPORTANCE}")
        # 🚀 LLM 완전 신뢰 모드: 임계값 체크 완전 제거
        logger.info(f"🤖 LLM 완전 신뢰 모드: 신뢰도({confidence}), 중요도({importance}) - 임계값 무시하고 LLM 판단 존중")
        logger.info(f"🚀 LLM이 거래 신호를 보냈으므로 무조건 실행합니다!")



        logger.info(f"[디버그] 전략 정보: 타입={strategy_type}, 방향={direction}, 진입가={entry_price}, TP={take_profit}, SL={stop_loss}")

        # 🚀 새로운 투자 전략: 바이낸스 실제 잔액 기반 4개 코인 균등 분할
        # confidence는 이미 위에서 정규화되었으므로 그대로 사용

        # 환경변수에서 설정 로드
        allocation_mode = os.getenv('POSITION_ALLOCATION_MODE', 'equal_split')
        strong_signal_threshold_raw = float(os.getenv('STRONG_SIGNAL_THRESHOLD', '6.5'))

        # 🚀 강한 신호 임계값도 정규화 (1-10 범위를 0.0-1.0으로 변환)
        if strong_signal_threshold_raw > 1.0:
            strong_signal_threshold = strong_signal_threshold_raw / 10.0
            logger.info(f"🔧 [{symbol}] 강한신호임계값 정규화: {strong_signal_threshold_raw} → {strong_signal_threshold:.3f}")
        else:
            strong_signal_threshold = strong_signal_threshold_raw

        logger.info(f"🚀 새로운 투자 전략 적용: 모드={allocation_mode}, 강한신호임계값={strong_signal_threshold:.3f}")

        # 🔥 바이낸스 실제 잔액 확인
        actual_balance = self.balance  # 기본값
        if hasattr(self, 'binance_utils') and self.binance_utils:
            try:
                futures_balance = self.binance_utils.get_futures_balance()
                if isinstance(futures_balance, dict) and 'USDT' in futures_balance:
                    usdt_info = futures_balance['USDT']
                    actual_balance = float(usdt_info.get('available_balance', usdt_info.get('margin_balance', self.balance)))
                    logger.info(f"💰 [{symbol}] 바이낸스 실제 USDT 잔액: ${actual_balance:.2f}")
                else:
                    logger.warning(f"⚠️ [{symbol}] 바이낸스 잔액 조회 실패, 내부 잔액 사용: ${actual_balance:.2f}")
            except Exception as e:
                logger.error(f"❌ [{symbol}] 바이낸스 잔액 조회 오류: {e}, 내부 잔액 사용: ${actual_balance:.2f}")

        # 활성 심볼 목록 (4개 코인 고정)
        active_symbols = ['ETH', 'SOL', 'BNB', 'DOGE']

        if allocation_mode == 'equal_split':
            # 🔧 스마트 분할: 기존 포지션 고려한 동적 할당
            existing_positions_count = len([pos for pos in self.open_positions if pos.get('symbol') in active_symbols])
            remaining_symbols = len(active_symbols) - existing_positions_count

            if remaining_symbols > 0:
                # 포지션이 없는 심볼들에게 남은 잔액을 분할
                # 기존 포지션들이 이미 자본을 사용했다고 가정하고, 남은 자본을 새 포지션들에게 분할
                reserved_per_existing = actual_balance * 0.2  # 기존 포지션당 20% 예약
                used_by_existing = existing_positions_count * reserved_per_existing
                available_for_new = max(actual_balance - used_by_existing, actual_balance * 0.4)  # 최소 40% 보장
                per_symbol_allocation = available_for_new / remaining_symbols
            else:
                # 모든 심볼에 포지션이 있는 경우 기존 방식
                per_symbol_allocation = actual_balance / len(active_symbols)

            position_size_usd = per_symbol_allocation

            logger.info(f"💰 [{symbol}] 스마트 분할: 실제잔액=${actual_balance:.2f}, 기존포지션={existing_positions_count}개, 남은심볼={remaining_symbols}개, 할당=${position_size_usd:.2f}")
        else:
            # 기존 방식 (신뢰도 기반)
            position_size_pct = 0.05  # 기본값 5%
            if confidence >= 8:
                position_size_pct = 0.15  # 높은 신뢰도: 자본의 15%
            elif confidence >= 5:
                position_size_pct = 0.10  # 중간 신뢰도: 자본의 10%

            position_size_usd = actual_balance * position_size_pct

            logger.info(f"📊 [{symbol}] 신뢰도 기반: 신뢰도={confidence:.3f}, 비율={position_size_pct:.2f}, 금액=${position_size_usd:.2f}")

            logger.info(f"📊 기존 방식: 신뢰도={confidence}, 비율={position_size_pct:.2f}, 금액=${position_size_usd:.2f}")

        # 강한 신호 여부 확인
        is_strong_signal = confidence >= strong_signal_threshold
        logger.info(f"🎯 신호 강도 분석: 신뢰도={confidence}, 임계값={strong_signal_threshold}, 강한신호={is_strong_signal}")

        # NEUTRAL 신호 처리 (개선된 버전)
        if direction.lower() == 'neutral':
            logger.info(f"🔄 NEUTRAL 신호 감지: 포지션 재평가 시작")
            existing_position = self._get_existing_position(symbol)

            if existing_position:
                # 🎯 기존 포지션이 있는 경우: 지능형 포지션 관리 적용
                logger.info(f"🔍 기존 포지션 발견: {symbol} {existing_position.get('direction')} - 포지션 재평가")

                # 🚀 강한 신호 확인 (consensus_signal 기반) - SHORT 포지션 허용 개선
                consensus_signal = strategy.get('consensus_signal', '')
                is_strong_signal = consensus_signal in ['strong_buy', 'strong_sell', 'buy', 'sell']  # sell도 강한 신호로 인정

                if is_strong_signal:
                    logger.info(f"🚀 [{symbol}] 강한 합의 신호({consensus_signal}) 감지 → NEUTRAL 신호 무시하고 포지션 재평가")
                    # 강한 신호면 NEUTRAL 무시하고 계속 진행 (아래 로직으로)
                else:
                    # 🧠 LLM 기반 포지션 관리: 지능형 포지션 유지 로직 완전 제거
                    # LLM이 포지션 관리를 담당하므로 하드코딩된 조건 완전 제거
                    logger.info(f"🚀 [{symbol}] LLM 판단 존중: 포지션 재평가 진행")
                    # 포지션 재평가 진행 (아래 로직으로)
            else:
                logger.info(f"📍 기존 포지션 없음: NEUTRAL 신호로 새로운 거래 없음")
                return {
                    "success": False,
                    "reason": "neutral_signal_no_position",
                    "message": f"NEUTRAL 신호이고 기존 포지션 없어 새로운 거래 없음"
                }

        # 포지션 전환 로직 확인 (이미 위에서 같은 방향 포지션 처리가 완료된 경우 스킵)
        if not same_direction_processed:  # 같은 방향 포지션 처리가 완료되지 않은 경우에만 실행
            if not existing_position:  # 기존 포지션이 없는 경우에만 새로 조회
                existing_position = self._get_existing_position(symbol)

        if existing_position and not same_direction_processed:
            logger.info(f"📍 기존 포지션 발견: {symbol} {existing_position.get('direction')} (진입가: ${existing_position.get('entry_price', 0):.4f})")
        elif same_direction_processed:
            logger.info(f"⏭️ [{symbol}] 같은 방향 포지션 처리 완료로 포지션 전환 로직 스킵")

        # 🔧 기존 포지션이 있고 같은 방향 처리가 완료되지 않은 경우에만 방향 비교
        if existing_position and not same_direction_processed:
            # 기존 포지션과 새 신호 방향 비교 (정규화된 방향 사용)
            existing_direction = self._normalize_direction(existing_position.get('direction', ''))
            new_direction = direction  # 이미 위에서 정규화됨

            if existing_direction != new_direction:
                if is_strong_signal:
                    # 🚀 완화된 포지션 전환 조건 추가
                    current_roi = existing_position.get('current_pnl', 0)
                    position_age = time.time() - existing_position.get('entry_timestamp', time.time())

                    # 환경변수에서 전환 조건 로드 - SHORT 포지션 허용을 위해 완화
                    min_loss_for_switch = float(os.getenv('MIN_LOSS_FOR_POSITION_SWITCH', '-0.2'))  # -0.2% 이하 손실 (완화)

                    # 🛡️ 숏 포지션 최소 유지 시간 강화 (30분)
                    if existing_direction == 'short' or existing_direction == 'sell':
                        min_age_for_switch = float(os.getenv('MIN_AGE_FOR_SHORT_SWITCH', '1800'))  # 30분 (1800초)
                        logger.info(f"🛡️ [{symbol}] 숏 포지션 감지 → 최소 유지 시간 30분 적용")
                    else:
                        min_age_for_switch = float(os.getenv('MIN_AGE_FOR_POSITION_SWITCH', '300'))  # 5분 (300초)

                    allow_profit_switch = os.getenv('ALLOW_PROFIT_POSITION_SWITCH', 'true').lower() == 'true'  # 수익 중 전환 허용

                    logger.info(f"🔍 포지션 전환 조건 확인: ROI={current_roi:.2f}%, 보유시간={position_age:.0f}초")
                    logger.info(f"🎛️ 전환 설정: 최소손실={min_loss_for_switch}%, 최소시간={min_age_for_switch}초, 수익중전환={allow_profit_switch}")

                    # 전환 조건 확인
                    can_switch = False
                    switch_reason = ""

                    if allow_profit_switch:
                        # 수익 중에도 전환 허용
                        can_switch = True
                        switch_reason = "수익중전환허용"
                    elif current_roi <= min_loss_for_switch:
                        # 손실이 임계값 이하일 때 전환 허용
                        can_switch = True
                        switch_reason = f"손실임계값도달({current_roi:.2f}%<={min_loss_for_switch}%)"
                    elif position_age >= min_age_for_switch:
                        # 보유 시간이 임계값 이상일 때 전환 허용
                        can_switch = True
                        switch_reason = f"보유시간초과({position_age:.0f}초>={min_age_for_switch}초)"
                    else:
                        switch_reason = f"조건미충족(ROI:{current_roi:.2f}%>{min_loss_for_switch}%, 시간:{position_age:.0f}초<{min_age_for_switch}초)"

                    if can_switch:
                        logger.info(f"🔄 강한 신호로 포지션 전환 허용: {existing_direction} → {new_direction} (이유: {switch_reason})")
                        # 기존 포지션 청산 후 새 포지션 진입
                        self._close_existing_position(symbol, existing_position, current_price, f"strong_signal_switch_{switch_reason}")
                    else:
                        logger.info(f"⏸️ 강한 신호이지만 전환 조건 미충족: {switch_reason}")
                        return {
                            "success": False,
                            "reason": "strong_signal_but_conditions_not_met",
                            "message": f"강한 신호이지만 전환 조건 미충족: {switch_reason}"
                        }
                else:
                    # 🚀 시장 신호 기반 클로징 강화: SHORT 포지션 허용을 위해 개선
                    enable_signal_closing = os.getenv('ENABLE_SIGNAL_BASED_CLOSING', 'true').lower() == 'true'

                    if enable_signal_closing:
                        # 약한 신호라도 손실이 크거나 오래 보유했으면 전환 허용
                        current_roi = existing_position.get('current_pnl', 0)
                        position_age = time.time() - existing_position.get('entry_timestamp', time.time())

                        # 완화된 조건: 손실이 -1% 이하이거나 5분 이상 보유
                        weak_signal_loss_threshold = -1.0  # -1% 이하 손실
                        weak_signal_age_threshold = 300    # 5분 이상

                        if current_roi <= weak_signal_loss_threshold or position_age >= weak_signal_age_threshold:
                            logger.info(f"🔄 약한 신호이지만 조건부 전환 허용: ROI={current_roi:.2f}%, 보유시간={position_age:.0f}초")
                            self._close_existing_position(symbol, existing_position, current_price, f"weak_signal_conditional_ROI_{current_roi:.2f}%_age_{position_age:.0f}s")
                        else:
                            logger.info(f"⏸️ 약한 신호로 포지션 유지: {existing_direction} (조건 미충족)")
                            return {
                                "success": False,
                                "reason": "weak_signal_conditions_not_met",
                                "message": f"약한 신호({confidence})이지만 조건 미충족으로 기존 포지션 유지"
                            }
                    else:
                        logger.info(f"⏸️ 약한 신호로 포지션 유지: {existing_direction} (신호 무시)")
                        return {
                            "success": False,
                            "reason": "weak_signal_ignored",
                            "message": f"약한 신호({confidence})로 인해 기존 포지션 유지"
                        }
            else:
                logger.info(f"✅ 동일 방향 신호 감지: {existing_direction} (importance 비교로 처리 예정)")
        else:
            # 안전한 값 추출 (딕셔너리 포맷팅 에러 방지)
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            # 🔧 direction 변수가 이미 메서드 시작 부분에서 정의됨
            direction_str = str(direction) if direction is not None else 'N/A'
            logger.info(f"🆕 새로운 포지션 생성: {symbol_str} {direction_str}")

        # 🚀 .env 설정 기반 동적 포지션 크기 계산 (선택적)
        enable_dynamic_position = os.getenv('ENABLE_DYNAMIC_POSITION_SIZE', 'false').lower() == 'true'

        if enable_dynamic_position:
            # confidence는 이미 위에서 정규화되었으므로 그대로 사용

            # 🚀 학습된 전략 정보 추가 활용 (안전한 호출)
            learned_performance = None
            try:
                # 메서드 존재 여부 확인
                if hasattr(self, '_extract_learned_performance'):
                    learned_performance = self._extract_learned_performance(strategy)
                else:
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.warning(f"[{symbol_str}] _extract_learned_performance 메서드가 없습니다. 동적 추가를 시도합니다.")

                    # 🚀 메서드 직접 정의 및 추가
                    try:
                        logger.info(f"🔧 [{symbol_str}] _extract_learned_performance 메서드 직접 정의 시도")

                        # 메서드를 직접 정의
                        def _extract_learned_performance_impl(self, strategy):
                            """🚀 전략에서 학습된 성과 정보 추출 (런타임 정의)"""
                            try:
                                # 전략에서 성과 정보 추출
                                performance_score = strategy.get('performance_score', 0.5)
                                confidence = strategy.get('confidence', 0.5)
                                importance = strategy.get('importance', 0)

                                # 학습된 성과 점수 계산
                                if performance_score > 0.8:
                                    learned_performance = performance_score * 1.2  # 우수한 성과는 가중치 증가
                                elif performance_score > 0.6:
                                    learned_performance = performance_score * 1.1  # 양호한 성과는 약간 증가
                                else:
                                    learned_performance = performance_score * 0.9  # 낮은 성과는 약간 감소

                                # 신뢰도와 중요도 반영
                                learned_performance = learned_performance * confidence * (1 + importance / 10)

                                # 0.0 ~ 1.0 범위로 정규화
                                learned_performance = max(0.0, min(1.0, learned_performance))

                                # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                                strategy_symbol = strategy.get('symbol', 'unknown')
                                strategy_symbol_str = str(strategy_symbol) if strategy_symbol is not None else 'unknown'
                                # 안전한 숫자 변환 (딕셔너리 포맷팅 에러 방지)
                                learned_performance_str = f"{learned_performance:.3f}" if isinstance(learned_performance, (int, float)) else str(learned_performance)
                                performance_score_str = f"{performance_score:.3f}" if isinstance(performance_score, (int, float)) else str(performance_score)
                                self.logger.info(f"📊 [{strategy_symbol_str}] 학습된 성과 추출: {learned_performance_str} (원본: {performance_score_str})")
                                return learned_performance

                            except Exception as e:
                                # 안전한 에러 메시지 변환 (딕셔너리 포맷팅 에러 방지)
                                error_str = str(e) if e is not None else 'Unknown error'
                                self.logger.error(f"❌ 학습된 성과 추출 실패: {error_str}")
                                return 0.5  # 기본값

                        # 클래스에 메서드 바인딩
                        import types
                        bound_method = types.MethodType(_extract_learned_performance_impl, self)
                        setattr(self, '_extract_learned_performance', bound_method)

                        logger.info(f"✅ [{symbol_str}] _extract_learned_performance 메서드 직접 정의 및 추가 성공")

                        # 메서드 실행
                        learned_performance = self._extract_learned_performance(strategy)

                    except Exception as direct_e:
                        logger.error(f"❌ [{symbol_str}] _extract_learned_performance 메서드 직접 정의 실패: {direct_e}")
                        # 최후의 수단: 직접 계산
                        try:
                            performance_score = strategy.get('performance_score', 0.5)
                            confidence = strategy.get('confidence', 0.5)
                            learned_performance = performance_score * confidence
                            # 안전한 숫자 변환 (딕셔너리 포맷팅 에러 방지)
                            learned_performance_str = f"{learned_performance:.3f}" if isinstance(learned_performance, (int, float)) else str(learned_performance)
                            logger.info(f"🔧 [{symbol_str}] 직접 계산된 학습 성과: {learned_performance_str}")
                        except:
                            learned_performance = 0.5
            except Exception as e:
                # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                logger.warning(f"[{symbol_str}] 학습된 성과 추출 실패: {e}")
                learned_performance = None

            # .env에서 신뢰도별 포지션 크기 비율 로드
            high_confidence_ratio = float(os.getenv('HIGH_CONFIDENCE_POSITION_RATIO', '0.5'))  # 0.9+ 신뢰도
            medium_confidence_ratio = float(os.getenv('MEDIUM_CONFIDENCE_POSITION_RATIO', '0.3'))  # 0.8+ 신뢰도
            low_confidence_ratio = float(os.getenv('LOW_CONFIDENCE_POSITION_RATIO', '0.2'))  # 0.7+ 신뢰도
            default_ratio = float(os.getenv('DEFAULT_POSITION_RATIO', '0.1'))  # 기본값

            # 신뢰도별 포지션 크기 비율 결정
            if confidence >= 0.9:
                position_size_ratio = high_confidence_ratio
            elif confidence >= 0.8:
                position_size_ratio = medium_confidence_ratio
            elif confidence >= 0.7:
                position_size_ratio = low_confidence_ratio
            else:
                position_size_ratio = default_ratio

            # 🚀 학습된 성과 기반 포지션 크기 보정
            if learned_performance:
                performance_multiplier = self._calculate_performance_multiplier(learned_performance)
                position_size_ratio *= performance_multiplier
                # 안전한 심볼 문자열 변환
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                # 안전한 숫자 변환 (딕셔너리 포맷팅 에러 방지)
                learned_performance_str = f"{learned_performance:.2f}" if isinstance(learned_performance, (int, float)) else str(learned_performance)
                performance_multiplier_str = f"{performance_multiplier:.2f}" if isinstance(performance_multiplier, (int, float)) else str(performance_multiplier)
                logger.info(f"🚀 [{symbol_str}] 학습된 성과 반영: {learned_performance_str} → 배수 {performance_multiplier_str}")

            # 신뢰도 기반 포지션 크기 계산
            confidence_based_position_size = self.balance * position_size_ratio

            # 기존 position_size_usd와 비교하여 더 큰 값 사용 (투자 극대화)
            position_size_usd = max(position_size_usd, confidence_based_position_size)

            # 안전한 심볼 문자열 변환
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            # 안전한 숫자 변환 (딕셔너리 포맷팅 에러 방지)
            confidence_str = f"{confidence:.2f}" if isinstance(confidence, (int, float)) else str(confidence)
            position_ratio_pct_str = f"{position_size_ratio*100:.0f}" if isinstance(position_size_ratio, (int, float)) else str(position_size_ratio*100)
            confidence_based_position_str = f"${confidence_based_position_size:.2f}" if isinstance(confidence_based_position_size, (int, float)) else str(confidence_based_position_size)
            position_size_usd_str = f"${position_size_usd:.2f}" if isinstance(position_size_usd, (int, float)) else str(position_size_usd)
            logger.info(f"🚀 [{symbol_str}] 동적 포지션 크기 활성화: {confidence_str} → {position_ratio_pct_str}% ({confidence_based_position_str})")
            logger.info(f"🚀 [{symbol_str}] 최종 선택된 포지션 크기: {position_size_usd_str}")
        else:
            # 안전한 심볼 문자열 변환
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            logger.debug(f"[{symbol_str}] 동적 포지션 크기 비활성화 (기존 방식 사용)")

        # 최소 주문 금액 확인
        MIN_ORDER_VALUE = 5.0
        if position_size_usd < MIN_ORDER_VALUE:
            position_size_usd = MIN_ORDER_VALUE
            logger.info(f"💡 최소 주문 금액 조정: ${MIN_ORDER_VALUE}")

        # 🔥 마진 부족 오류 방지를 위한 강화된 포지션 크기 계산
        # 실제 바이낸스 잔액 기준으로 안전한 포지션 크기 설정

        # 1. 4개 코인 균등 분할 기준으로 최대 포지션 크기 제한
        max_position_per_symbol = actual_balance / 4  # 4개 코인 균등 분할
        if position_size_usd > max_position_per_symbol:
            position_size_usd = max_position_per_symbol
            logger.info(f"💰 [{symbol}] 4개 코인 균등 분할 제한: ${max_position_per_symbol:.2f}")

        # 2. 마진 안전성을 위한 추가 제한 (심볼별 특별 처리)
        if symbol == 'BNB':
            # BNB는 특히 보수적으로 처리 (균등 분할의 50%만 사용)
            bnb_safe_limit = max_position_per_symbol * 0.5
            if position_size_usd > bnb_safe_limit:
                position_size_usd = bnb_safe_limit
                logger.info(f"🛡️ [BNB] 특별 안전 제한: ${bnb_safe_limit:.2f} (균등분할의 50%)")
        elif symbol in ['ETH']:
            # ETH도 보수적으로 처리 (균등 분할의 70%만 사용)
            eth_safe_limit = max_position_per_symbol * 0.7
            if position_size_usd > eth_safe_limit:
                position_size_usd = eth_safe_limit
                logger.info(f"🛡️ [ETH] 특별 안전 제한: ${eth_safe_limit:.2f} (균등분할의 70%)")

        # 3. 최소 주문 금액 확인 (바이낸스 최소 요구사항: 5.0 USDT)
        MIN_ORDER_VALUE = 5.0
        if position_size_usd < MIN_ORDER_VALUE:
            position_size_usd = MIN_ORDER_VALUE
            logger.info(f"💡 [{symbol}] 최소 주문 금액 조정: ${MIN_ORDER_VALUE}")

        # 4. 바이낸스 최소 주문 금액 확인 및 자동 조절
        if hasattr(self, 'binance_utils') and self.binance_utils:
            try:
                # 바이낸스에서 실제 최소 주문 금액 조회
                symbol_info = self.binance_utils.get_symbol_info(symbol)
                if symbol_info and 'filters' in symbol_info:
                    min_notional = symbol_info['filters'].get('minNotional', 0)

                    # 하드코딩된 최소 주문 금액도 확인 (더 높은 값 사용)
                    symbol_min_notional = {
                        'BTCUSDT': 100.0,
                        'ETHUSDT': 20.0,
                        'SOLUSDT': 5.0,
                        'BNBUSDT': 5.0,
                        'DOGEUSDT': 10.0
                    }
                    formatted_symbol = f"{symbol}USDT"
                    hardcoded_min = symbol_min_notional.get(formatted_symbol, 5.0)

                    # 더 높은 최소 주문 금액 사용
                    effective_min_notional = max(min_notional, hardcoded_min)

                    if position_size_usd < effective_min_notional:
                        logger.warning(f"⚠️ [{symbol}] 포지션 크기({position_size_usd:.2f})가 최소 주문 금액({effective_min_notional:.2f})보다 작음")

                        # 최소 주문 금액으로 자동 조정 (단, 실제 잔액 한도 내에서)
                        max_safe_percentage = 0.3  # 최소 주문 금액 충족을 위해 30%까지 허용
                        absolute_max = actual_balance * max_safe_percentage

                        if effective_min_notional <= absolute_max:
                            position_size_usd = effective_min_notional
                            logger.info(f"🔧 [{symbol}] 최소 주문 금액으로 자동 조정: ${position_size_usd:.2f}")
                        else:
                            logger.error(f"❌ [{symbol}] 최소 주문 금액({effective_min_notional:.2f})이 안전 한도({absolute_max:.2f})를 초과")
                            logger.info(f"💡 [{symbol}] 거래 건너뜀 - 잔액 부족")
                            return None
                    else:
                        logger.info(f"✅ [{symbol}] 최소 주문 금액 조건 충족: ${position_size_usd:.2f} >= ${effective_min_notional:.2f}")

            except Exception as e:
                logger.error(f"❌ [{symbol}] 최소 주문 금액 확인 중 오류: {e}")

        # 5. 최종 안전성 검증 - 실제 잔액 대비 확인
        max_safe_percentage = 0.3  # 최소 주문 금액 충족을 위해 30%까지 허용
        absolute_max = actual_balance * max_safe_percentage
        if position_size_usd > absolute_max:
            logger.warning(f"🚨 [{symbol}] 포지션 크기({position_size_usd:.2f})가 절대 한도({absolute_max:.2f})를 초과")
            position_size_usd = absolute_max
            logger.info(f"🛡️ [{symbol}] 절대 안전 조치: ${position_size_usd:.2f} (실제잔액의 {max_safe_percentage*100}%)")

        logger.info(f"💰 [{symbol}] 최종 포지션 크기: ${position_size_usd:.2f} (실제잔액: ${actual_balance:.2f})")

        quantity = position_size_usd / current_price

        # 안전한 심볼 문자열 변환
        symbol_str = str(symbol) if symbol is not None else 'N/A'
        logger.info(f"[디버그] 포지션 크기: {position_size_usd} USD, 수량: {quantity} {symbol_str}")

        # 거래 결과 기본 구조
        # 안전한 심볼 문자열 변환 (딕셔너리 포맷팅 에러 방지)
        symbol_str = str(symbol) if symbol is not None else 'N/A'
        # 🔧 안전한 direction 추출 (딕셔너리 포맷팅 에러 방지)
        try:
            safe_direction = str(direction) if direction is not None else 'unknown'
        except NameError:
            # direction 변수가 정의되지 않은 경우
            logger.warning(f"🔧 [{symbol_str}] direction 변수가 정의되지 않음, 기본값 사용")
            safe_direction = 'unknown'
        except Exception as e:
            # 기타 오류
            logger.warning(f"🔧 [{symbol_str}] direction 추출 중 오류: {e}, 기본값 사용")
            safe_direction = 'unknown'
        trade_result = {
            "trade_id": f"trade_{symbol_str}_{timestamp}",
            "symbol": symbol,
            "timestamp": timestamp,
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "strategy_type": strategy_type,
            "direction": safe_direction,
            "entry_price": entry_price,
            "take_profit": take_profit,
            "stop_loss": stop_loss,
            "quantity": quantity,
            "value_usd": position_size_usd,
            "status": "executed",
            "realized_pnl": 0.0,
            "realized_pnl_pct": 0.0,
            "exit_price": 0.0,
            "exit_timestamp": 0,
            "mode": self.mode,
            "is_testnet": self.testnet,
            "success": True
        }

        try:
            # 🔧 direction 변수를 try 블록 시작 부분에서 재확인
            if not 'direction' in locals() or direction is None:
                logger.warning(f"🔧 [{symbol_str}] direction 변수가 정의되지 않음, 전략에서 재추출")
                strategy_type = strategy.get('type', 'unknown')
                strategy_action = strategy.get('action', 'unknown')
                direction = strategy_action if strategy_action != 'unknown' else strategy_type
                logger.info(f"🔧 [{symbol_str}] direction 재추출 완료: {direction}")

            # 실제 거래 모드인 경우
            if self.mode == 'real' and binance_utils:
                # 🔧 안전한 direction 사용 (진입 주문 실행)
                try:
                    # direction 변수가 메서드 시작 부분에서 정의되었으므로 직접 사용
                    safe_direction_for_order = direction if direction is not None else 'unknown'
                    order_side = "BUY" if safe_direction_for_order in ["long", "buy"] else "SELL"
                    logger.info(f"🔧 [{symbol_str}] direction 처리 성공: {direction} → {safe_direction_for_order} → {order_side}")
                except Exception as e:
                    logger.warning(f"🔧 [{symbol_str}] direction 처리 중 오류: {e}, 기본값 BUY 사용")
                    safe_direction_for_order = 'unknown'
                    order_side = "BUY"

                logger.info(f"[디버그] 실제 거래 모드 확인: mode={self.mode}, testnet={self.testnet}")
                logger.info(f"[디버그] 거래 방향 확인: direction={safe_direction_for_order}, order_side={order_side}")

                # 안전한 심볼 문자열 변환
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                logger.info(f"{symbol_str} {'테스트넷' if self.testnet else '실제'} 거래 실행: {order_side} {quantity:.8f} @ ${entry_price:.2f}")

                # 🚀 InCA 경험 축적을 위한 강제 가상거래 (잔액과 관계없이 항상 실행)
                force_virtual_trading = os.getenv('FORCE_VIRTUAL_TRADING_FOR_LEARNING', 'false').lower() == 'true'

                if force_virtual_trading:
                    logger.info(f"🎯 [{symbol_str}] InCA 학습을 위한 강제 가상거래 실행")

                    # 메서드 존재 확인 후 가상 거래 실행 (학습용)
                    logger.info(f"🔍 [{symbol_str}] 메서드 존재 확인: hasattr(self, '_execute_virtual_trade') = {hasattr(self, '_execute_virtual_trade')}")
                    logger.info(f"🔍 [{symbol_str}] Portfolio 클래스: {self.__class__.__name__}")
                    logger.info(f"🔍 [{symbol_str}] Portfolio 모듈: {self.__class__.__module__}")

                    # 🔍 상세 디버깅: 클래스 정보 및 메서드 존재 확인
                    logger.info(f"🔍 [{symbol_str}] 상세 디버깅 시작:")
                    logger.info(f"🔍 [{symbol_str}] - 인스턴스 타입: {type(self)}")
                    logger.info(f"🔍 [{symbol_str}] - 클래스명: {self.__class__.__name__}")
                    logger.info(f"🔍 [{symbol_str}] - 모듈: {self.__class__.__module__}")
                    logger.info(f"🔍 [{symbol_str}] - MRO: {[cls.__name__ for cls in self.__class__.__mro__]}")

                    # 메서드 존재 확인 (여러 방법으로)
                    hasattr_result = hasattr(self, '_execute_virtual_trade')
                    getattr_result = getattr(self, '_execute_virtual_trade', None) is not None
                    dir_result = '_execute_virtual_trade' in dir(self)

                    logger.info(f"🔍 [{symbol_str}] - hasattr 결과: {hasattr_result}")
                    logger.info(f"🔍 [{symbol_str}] - getattr 결과: {getattr_result}")
                    logger.info(f"🔍 [{symbol_str}] - dir 결과: {dir_result}")

                    # virtual 관련 메서드 목록
                    virtual_methods = [m for m in dir(self) if not m.startswith('__') and 'virtual' in m.lower()]
                    logger.info(f"🔍 [{symbol_str}] - virtual 관련 메서드: {virtual_methods}")

                    # 가상거래 메서드 직접 호출 (hasattr 체크 제거)
                    logger.info(f"✅ [{symbol_str}] 가상거래 실행 중...")
                    try:
                        virtual_result = self._execute_virtual_trade(
                            symbol=symbol,
                            direction=safe_direction_for_order,
                            strategy=strategy,
                            market_data=market_data,
                            position_size_usd=position_size_usd,
                            current_price=current_price
                        )

                        if virtual_result.get('success', False):
                            trade_id = virtual_result.get('trade_id', 'unknown')
                            logger.info(f"🎯 [{symbol_str}] 강제 가상거래 성공: {trade_id}")
                        else:
                            logger.warning(f"⚠️ [{symbol_str}] 강제 가상거래 실패")
                    except AttributeError as attr_e:
                        logger.error(f"❌ [{symbol_str}] _execute_virtual_trade 메서드가 존재하지 않습니다")
                        logger.error(f"❌ [{symbol_str}] Portfolio 클래스 메서드 목록: {[m for m in dir(self) if not m.startswith('__')]}")
                        logger.error(f"❌ [{symbol_str}] 클래스 타입: {type(self)}")
                        logger.error(f"❌ [{symbol_str}] MRO: {[cls.__name__ for cls in type(self).__mro__]}")
                        logger.error(f"❌ [{symbol_str}] hasattr 체크: {hasattr(self, '_execute_virtual_trade')}")

                        # 부모 클래스에서 메서드 찾기 시도
                        for cls in type(self).__mro__:
                            if hasattr(cls, '_execute_virtual_trade'):
                                logger.error(f"❌ [{symbol_str}] _execute_virtual_trade 메서드가 {cls.__name__} 클래스에서 발견됨")
                                break
                        else:
                            logger.error(f"❌ [{symbol_str}] _execute_virtual_trade 메서드가 어떤 부모 클래스에서도 발견되지 않음")
                    except Exception as virtual_e:
                        logger.error(f"❌ [{symbol_str}] 가상거래 실행 중 예외 발생: {virtual_e}")
                        logger.error(f"❌ [{symbol_str}] 예외 타입: {type(virtual_e)}")
                        import traceback
                        logger.error(f"❌ [{symbol_str}] 스택 트레이스: {traceback.format_exc()}")




                # 바이낸스 API 호출 - 선물 시장 주문으로 변경
                try:
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.info(f"[디버그] 바이낸스 API 호출 시작: symbol={symbol_str}USDT, side={order_side}, quantity={quantity}")

                    # 바이낸스 유틸리티 객체 확인
                    if not hasattr(binance_utils, 'execute_futures_market_order'):
                        logger.error(f"[디버그] 바이낸스 유틸리티에 execute_futures_market_order 메서드가 없습니다.")
                        logger.info(f"[디버그] 바이낸스 유틸리티 메서드: {dir(binance_utils)}")
                        trade_result["success"] = False
                        trade_result["error"] = "바이낸스 유틸리티 메서드 없음"
                        return trade_result

                    # 계정 잔액 확인
                    try:
                        account_balance = binance_utils.get_futures_account_balance()
                        if account_balance and isinstance(account_balance, list):
                            usdt_asset = next((asset for asset in account_balance if asset['asset'] == 'USDT'), None)
                            if usdt_asset:
                                available_balance = float(usdt_asset.get('availableBalance', 0))
                                logger.info(f"[디버그] 사용 가능한 USDT 잔액: {available_balance:.2f}")

                                # 포지션 크기 자동 조정
                                min_position_size = 10.0  # 최소 10 USDT
                                max_position_pct = 0.8    # 최대 80%의 사용 가능 잔액 사용

                                # 사용 가능한 잔액이 부족한 경우
                                if available_balance < position_size_usd:
                                    logger.warning(f"[디버그] 사용 가능한 잔액({available_balance:.2f} USDT)이 필요한 금액({position_size_usd:.2f} USDT)보다 적습니다.")

                                    # 사용 가능한 잔액에 따라 포지션 크기 자동 조정
                                    if available_balance > 10.0:  # 최소 10 USDT 이상인 경우에만
                                        # 🛡️ BNB 마진 부족 문제 해결: 더 보수적인 포지션 크기 조정
                                        if symbol == 'BNB':
                                            # BNB는 특히 보수적으로 20%만 사용
                                            safe_position_size = available_balance * 0.2
                                            logger.info(f"🛡️ [BNB] 보수적 포지션 크기 적용: {safe_position_size:.2f} USDT (사용가능: {available_balance:.2f})")
                                        else:
                                            # 다른 심볼은 기존대로 50% 사용
                                            safe_position_size = available_balance * 0.5

                                        position_size_usd = max(10.0, min(safe_position_size, position_size_usd))
                                        quantity = position_size_usd / current_price
                                        logger.info(f"[디버그] 포지션 크기 안전 조정: {position_size_usd:.2f} USDT (사용가능: {available_balance:.2f}), 수량: {quantity:.8f}")

                                        # 조정된 포지션 크기도 여전히 잔액을 초과하는지 재확인
                                        max_allowed = available_balance * 0.6 if symbol == 'BNB' else available_balance * 0.8
                                        if position_size_usd > max_allowed:
                                            logger.error(f"[디버그] 조정된 포지션 크기도 여전히 너무 큽니다. 거래를 건너뜁니다.")
                                            trade_result["success"] = False
                                            trade_result["error"] = "조정 후에도 잔액 부족"
                                            trade_result["status"] = "rejected"
                                            return trade_result


                                    else:
                                        # 🚀 잔액 부족 시 가상 거래 모드 확인
                                        enable_virtual_trading = os.getenv('ENABLE_VIRTUAL_TRADING_ON_LOW_BALANCE', 'false').lower() == 'true'

                                        if enable_virtual_trading:
                                            # 안전한 심볼 문자열 변환
                                            symbol_str = str(symbol) if symbol is not None else 'N/A'
                                            logger.info(f"🚀 [{symbol_str}] 잔액 부족이지만 가상 거래 모드로 학습 지속")

                                            # 가상 거래 실행 (학습용) - 직접 호출
                                            try:
                                                virtual_result = self._execute_virtual_trade(
                                                    symbol=symbol,
                                                    direction=safe_direction_for_order,
                                                    strategy=strategy,
                                                    market_data=market_data,
                                                    position_size_usd=position_size_usd,
                                                    current_price=current_price
                                                )
                                                return virtual_result
                                            except AttributeError as attr_e:
                                                logger.error(f"❌ [{symbol_str}] _execute_virtual_trade 메서드가 존재하지 않습니다 (잔액 부족 시)")
                                                logger.error(f"❌ [{symbol_str}] hasattr 체크: {hasattr(self, '_execute_virtual_trade')}")
                                                logger.error(f"❌ [{symbol_str}] 클래스 타입: {type(self)}")
                                                # 기본 거래 결과 반환
                                                trade_result["success"] = False
                                                trade_result["error"] = "_execute_virtual_trade 메서드 없음"
                                                return trade_result
                                            except Exception as virtual_e:
                                                logger.error(f"❌ [{symbol_str}] 가상거래 실행 중 오류 (잔액 부족 시): {virtual_e}")
                                                # 기본 거래 결과 반환
                                                trade_result["success"] = False
                                                trade_result["error"] = f"가상거래 실행 오류: {str(virtual_e)}"
                                                return trade_result
                                        else:
                                            logger.error(f"[디버그] 사용 가능한 잔액이 최소 주문 금액(5.0 USDT)보다 적습니다.")
                                            trade_result["success"] = False
                                            trade_result["error"] = "사용 가능한 잔액 부족"
                                            trade_result["status"] = "rejected"
                                            trade_result["reason"] = "사용 가능한 잔액 부족"

                                            # 거래 내역 추가 (실행되지 않았지만 기록)
                                            self.trade_history.append(trade_result)

                                            # 포트폴리오 통계 업데이트 (거부된 거래는 일단 총 거래 수에만 포함)
                                            self.performance["total_trades"] += 1

                                            # 거부된 거래 추적을 위해 trade_result에 추가 정보 저장
                                            trade_result["rejected_timestamp"] = timestamp
                                            trade_result["entry_prediction"] = safe_direction_for_order  # 🔧 안전한 direction 변수 사용
                                            trade_result["entry_price_prediction"] = entry_price  # 예측 진입가 저장
                                            trade_result["take_profit_prediction"] = take_profit  # 예측 TP 저장
                                            trade_result["stop_loss_prediction"] = stop_loss  # 예측 SL 저장

                                            # 나중에 이 거래의 예측이 맞았는지 확인하기 위해 rejected_trades 목록에 추가
                                            if not hasattr(self, 'rejected_trades'):
                                                self.rejected_trades = []
                                            self.rejected_trades.append(trade_result)

                                            # 포트폴리오 저장
                                            self.save_portfolio()

                                            # 안전한 심볼 문자열 변환
                                            symbol_str = str(symbol) if symbol is not None else 'N/A'
                                            logger.info(f"{symbol_str} 거래가 잔액 부족으로 거부되었습니다.")
                                            return trade_result
                    except Exception as e:
                        logger.warning(f"[디버그] 계정 잔액 확인 실패: {e}")
                        # 잔액 확인 실패해도 계속 진행

                    # 🔧 바이낸스 최소 주문 금액 확인 및 자동 조정 (거래 실행 직전)
                    try:
                        # 바이낸스에서 실제 최소 주문 금액 조회
                        symbol_info = binance_utils.get_symbol_info(symbol)
                        if symbol_info and 'filters' in symbol_info:
                            min_notional = symbol_info['filters'].get('minNotional', 0)

                            # 하드코딩된 최소 주문 금액도 확인 (더 높은 값 사용)
                            symbol_min_notional = {
                                'BTCUSDT': 100.0,
                                'ETHUSDT': 20.0,
                                'SOLUSDT': 5.0,
                                'BNBUSDT': 5.0,
                                'DOGEUSDT': 10.0
                            }
                            formatted_symbol = f"{symbol}USDT"
                            hardcoded_min = symbol_min_notional.get(formatted_symbol, 5.0)

                            # 더 높은 최소 주문 금액 사용
                            effective_min_notional = max(min_notional, hardcoded_min)

                            # 현재 주문 금액 계산
                            current_order_value = quantity * current_price

                            if current_order_value < effective_min_notional:
                                logger.warning(f"⚠️ [{symbol}] 주문 금액({current_order_value:.2f})이 최소 주문 금액({effective_min_notional:.2f})보다 작음")

                                # 최소 주문 금액을 충족하도록 수량 자동 조정
                                required_quantity = effective_min_notional / current_price
                                quantity = required_quantity
                                position_size_usd = effective_min_notional

                                logger.info(f"🔧 [{symbol}] 최소 주문 금액 충족을 위해 자동 조정:")
                                logger.info(f"🔧 [{symbol}] - 수량: {quantity:.8f}")
                                logger.info(f"🔧 [{symbol}] - 주문 금액: ${position_size_usd:.2f}")
                            else:
                                logger.info(f"✅ [{symbol}] 최소 주문 금액 조건 충족: ${current_order_value:.2f} >= ${effective_min_notional:.2f}")

                    except Exception as e:
                        logger.error(f"❌ [{symbol}] 최소 주문 금액 확인 중 오류 (거래 실행 직전): {e}")

                    # 수량 정밀도 조정 (바이낸스 LOT_SIZE 필터 적용)
                    try:
                        if symbol_info and 'filters' in symbol_info and 'lotSize' in symbol_info['filters']:
                            lot_size = symbol_info['filters']['lotSize']
                            min_qty = lot_size.get('minQty', 0)
                            step_size = lot_size.get('stepSize', 0)

                            # 최소 수량 확인
                            if quantity < min_qty:
                                quantity = min_qty
                                logger.info(f"🔧 [{symbol}] 최소 수량으로 조정: {quantity}")

                            # stepSize에 맞게 조정
                            if step_size > 0:
                                quantity_multiple = round(quantity / step_size)
                                quantity = quantity_multiple * step_size
                                logger.info(f"🔧 [{symbol}] stepSize에 맞게 수량 조정: {quantity}")

                    except Exception as e:
                        logger.error(f"❌ [{symbol}] 수량 정밀도 조정 중 오류: {e}")

                    # 최종 주문 금액 재계산
                    final_order_value = quantity * current_price
                    logger.info(f"💰 [{symbol}] 최종 주문 정보: 수량={quantity:.8f}, 금액=${final_order_value:.2f}")

                    # 수량을 정수로 변환하지 않음 (암호화폐는 소수점 거래 가능)
                    adjusted_quantity = quantity

                    # 🚀 레버리지 항상 1로 고정 (안전한 거래)
                    leverage = 1
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.info(f"🚀 [{symbol_str}] 레버리지 고정: 1x (안전한 거래 모드)")

                    try:
                        leverage_result = binance_utils.set_leverage(symbol, leverage)
                        logger.info(f"[디버그] 레버리지 설정 결과 ({leverage}x): {str(leverage_result)}")
                    except Exception as e:
                        logger.warning(f"[디버그] 레버리지 설정 실패: {e}")
                        # 실패 시 기본값 1로 설정
                        leverage = 1

                    # 시장가 주문 사용 (buy/long → BUY, sell/short → SELL)
                    # 🔧 안전한 direction 변수 사용
                    if safe_direction_for_order in ["long", "buy"]:
                        # 안전한 심볼 문자열 변환
                        symbol_str = str(symbol) if symbol is not None else 'N/A'
                        logger.info(f"[디버그] 롱 포지션 시장가 주문 실행: {symbol_str}, BUY, {adjusted_quantity}")
                        order_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,  # 심볼 그대로 전달 (binance_utils에서 포맷팅)
                            side="BUY",
                            quantity=adjusted_quantity,
                            position_side=None  # 계정 설정에 따라 자동 결정
                        )
                    else:
                        # 안전한 심볼 문자열 변환
                        symbol_str = str(symbol) if symbol is not None else 'N/A'
                        logger.info(f"[디버그] 숏 포지션 시장가 주문 실행: {symbol_str}, SELL, {adjusted_quantity}")
                        order_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,  # 심볼 그대로 전달 (binance_utils에서 포맷팅)
                            side="SELL",
                            quantity=adjusted_quantity,
                            position_side=None  # 계정 설정에 따라 자동 결정
                        )


                    logger.info(f"[디버그] 바이낸스 API 응답: {str(order_result)}")

                except Exception as order_error:
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.error(f"[디버그] {symbol_str} 주문 중 예외 발생: {str(order_error)}")
                    logger.error(f"[디버그] 예외 타입: {type(order_error)}")
                    import traceback
                    logger.error(f"[디버그] 스택 트레이스: {traceback.format_exc()}")
                    trade_result["success"] = False
                    trade_result["error"] = str(order_error)
                    trade_result["status"] = "rejected"
                    trade_result["reason"] = f"API 오류: {str(order_error)}"

                    # 거래 내역 추가 (실행되지 않았지만 기록)
                    self.trade_history.append(trade_result)

                    # 포트폴리오 통계 업데이트 (거부된 거래는 일단 총 거래 수에만 포함)
                    self.performance["total_trades"] += 1

                    # 거부된 거래 추적을 위해 trade_result에 추가 정보 저장
                    trade_result["rejected_timestamp"] = timestamp
                    trade_result["entry_prediction"] = safe_direction_for_order  # 🔧 안전한 direction 변수 사용
                    trade_result["entry_price_prediction"] = entry_price  # 예측 진입가 저장
                    trade_result["take_profit_prediction"] = take_profit  # 예측 TP 저장
                    trade_result["stop_loss_prediction"] = stop_loss  # 예측 SL 저장

                    # 나중에 이 거래의 예측이 맞았는지 확인하기 위해 rejected_trades 목록에 추가
                    if not hasattr(self, 'rejected_trades'):
                        self.rejected_trades = []
                    self.rejected_trades.append(trade_result)

                    # 포트폴리오 저장
                    self.save_portfolio()

                    logger.info(f"{symbol_str} 거래가 API 오류로 거부되었습니다: {str(order_error)}")
                    return trade_result

                # 주문 결과 확인
                logger.info(f"[디버그] 주문 결과 확인: {str(order_result)}")

                if order_result and isinstance(order_result, dict) and order_result.get("orderId"):
                    # 주문 성공
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.info(f"{symbol_str} 주문 성공: 주문ID {order_result.get('orderId')}")
                    trade_result["order_id"] = order_result.get("orderId")

                    # 자산 및 잔액 업데이트 (buy/long → 롱 포지션, sell/short → 숏 포지션)
                    # 🔧 안전한 direction 변수 사용
                    if safe_direction_for_order in ["long", "buy"]:
                        # 롱 포지션: 자산 증가, 잔액 감소
                        self.assets[symbol] = self.assets.get(symbol, 0) + quantity
                        self.balance -= position_size_usd
                        logger.info(f"[디버그] 롱 포지션 자산 업데이트: {symbol_str}={self.assets[symbol]}, 잔액={self.balance}")
                    else:
                        # 숏 포지션: 공매도 포지션 생성 (실제로는 더 복잡)
                        # 여기서는 단순화
                        self.assets[symbol] = self.assets.get(symbol, 0) - quantity
                        # 공매도 충당금 설정
                        self.balance -= position_size_usd
                        logger.info(f"[디버그] 숏 포지션 자산 업데이트: {symbol_str}={self.assets[symbol]}, 잔액={self.balance}")
                else:
                    # 주문 실패
                    # 안전한 심볼 문자열 변환
                    symbol_str = str(symbol) if symbol is not None else 'N/A'
                    logger.error(f"[디버그] {symbol_str} 주문 실패: {str(order_result)}")
                    if isinstance(order_result, dict):
                        logger.error(f"[디버그] 주문 결과 키: {list(order_result.keys())}")
                        if 'error' in order_result:
                            logger.error(f"[디버그] 주문 오류: {str(order_result['error'])}")
                        if 'msg' in order_result:
                            logger.error(f"[디버그] 주문 메시지: {str(order_result['msg'])}")

                    trade_result["success"] = False
                    trade_result["error"] = "주문 API 실패"
                    trade_result["status"] = "rejected"

                    # 오류 메시지 구성
                    error_msg = "주문 API 실패"
                    if isinstance(order_result, dict):
                        if 'error' in order_result:
                            error_msg = order_result['error']
                        elif 'msg' in order_result:
                            error_msg = order_result['msg']

                    trade_result["reason"] = f"API 오류: {error_msg}"

                    # 거래 내역 추가 (실행되지 않았지만 기록)
                    self.trade_history.append(trade_result)

                    # 포트폴리오 통계 업데이트 (거부된 거래는 일단 총 거래 수에만 포함)
                    self.performance["total_trades"] += 1

                    # 거부된 거래 추적을 위해 trade_result에 추가 정보 저장
                    trade_result["rejected_timestamp"] = timestamp
                    trade_result["entry_prediction"] = safe_direction_for_order  # 🔧 안전한 direction 변수 사용
                    trade_result["entry_price_prediction"] = entry_price  # 예측 진입가 저장
                    trade_result["take_profit_prediction"] = take_profit  # 예측 TP 저장
                    trade_result["stop_loss_prediction"] = stop_loss  # 예측 SL 저장

                    # 나중에 이 거래의 예측이 맞았는지 확인하기 위해 rejected_trades 목록에 추가
                    if not hasattr(self, 'rejected_trades'):
                        self.rejected_trades = []
                    self.rejected_trades.append(trade_result)

                    # 포트폴리오 저장
                    self.save_portfolio()

                    logger.info(f"{symbol_str} 거래가 API 오류로 거부되었습니다: {error_msg}")
                    return trade_result

            # 시뮬레이션 모드
            else:
                # 안전한 심볼 문자열 변환
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                logger.info(f"{symbol_str} 시뮬레이션 거래: {safe_direction_for_order} {quantity:.8f} @ ${entry_price:.2f}")

                # 자산 및 잔액 가상 업데이트 (buy/long → 롱 포지션, sell/short → 숏 포지션)
                # 🔧 안전한 direction 변수 사용
                if safe_direction_for_order in ["long", "buy"]:
                    self.assets[symbol] = self.assets.get(symbol, 0) + quantity
                    self.balance -= position_size_usd
                else:
                    self.assets[symbol] = self.assets.get(symbol, 0) - quantity
                    self.balance -= position_size_usd

            # 오픈 포지션 추가
            # 안전한 심볼 문자열 변환
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            logger.info(f"[디버그] 포지션 생성 중: {symbol_str} {safe_direction_for_order} @ ${entry_price:.2f}")
            position = {
                "trade_id": trade_result["trade_id"],
                "symbol": symbol,
                "direction": safe_direction_for_order,  # 🔧 안전한 direction 변수 사용
                "entry_price": entry_price,
                "quantity": quantity,
                "value_usd": position_size_usd,
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "entry_timestamp": timestamp,
                "current_pnl": 0.0,
                # 🛡️ 포지션 방향에 따른 최소 보유 시간 설정
                "min_hold_time": (30 * 60) if safe_direction_for_order in ['sell', 'short'] else (10 * 60),  # 숏: 30분, 롱: 10분
                "virtual_mode": False,  # 🔧 실제거래 포지션 명시
                "learning_purpose": False,  # 🔧 실제거래 포지션 명시
                "server_time_sync": True  # 🔧 서버 시간 동기화 표시
            }
            self.open_positions.append(position)
            logger.info(f"[디버그] 포지션 생성 완료: {symbol_str} {safe_direction_for_order} (총 포지션 수: {len(self.open_positions)})")

            # 🎯 통합 생각카드 생성 (포지션 생성 시)
            if self.card_manager:
                try:
                    position_id = trade_result["trade_id"]
                    initial_card_data = {
                        'symbol': symbol,
                        'direction': safe_direction_for_order,  # 🔧 안전한 direction 변수 사용
                        'entry_price': entry_price,
                        'quantity': quantity,
                        'value_usd': position_size_usd,
                        'strategy': strategy,
                        'market_data': market_data,
                        'entry_timestamp': timestamp,
                        'mode': self.mode
                    }

                    card = self.card_manager.create_card_for_position(position_id, symbol, initial_card_data)
                    logger.info(f"🎯 [{symbol}] 통합 생각카드 생성 성공: {card.card_id} (포지션: {position_id})")

                    # 초기 포지션 관리 정보 업데이트
                    self.card_manager.update_card(position_id, 'position_management', {
                        'action': 'create_position',
                        'reason': f'{safe_direction_for_order} 포지션 생성',  # 🔧 안전한 direction 변수 사용
                        'confidence': strategy.get('confidence', 0.5),
                        'market_condition': {
                            'price': entry_price,
                            'timestamp': timestamp,
                            'strategy_type': strategy.get('type', 'unknown')
                        }
                    })

                except Exception as card_e:
                    logger.error(f"❌ [{symbol}] 통합 생각카드 생성 실패: {card_e}")
            else:
                logger.debug(f"🎯 [{symbol}] 통합 생각카드 매니저가 비활성화되어 있습니다")

            # 거래 내역 추가
            self.trade_history.append(trade_result)

            # 포트폴리오 통계 업데이트
            self.performance["total_trades"] += 1

            # 거래 시작 시점에는 승패를 알 수 없으므로 승률 업데이트는 하지 않음
            # 승률은 포지션 종료 시점에 업데이트됨

            # 데이터베이스에 거래 내역 저장
            try:
                from trading.data.database import TradingDatabase
                db_path = os.path.join(self.data_dir, 'trading.db')
                db = TradingDatabase(db_path)

                # 거래 데이터 구성
                trade_data = {
                    'symbol': symbol,
                    'action': safe_direction_for_order,  # 🔧 안전한 direction 변수 사용
                    'quantity': quantity,
                    'price': entry_price,
                    'timestamp': int(time.time()),  # 현재 시간으로 타임스탬프 설정
                    'entry_price': entry_price,
                    'exit_price': None,
                    'pnl_percent': 0.0,
                    'reason': strategy_type,
                    'trade_id': trade_result["trade_id"]
                }

                # 데이터베이스에 저장
                db.save_trade(trade_data)
                # 안전한 심볼 문자열 변환
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                logger.info(f"{symbol_str} 거래 내역 데이터베이스에 저장 완료")
            except Exception as e:
                # 안전한 심볼 문자열 변환
                symbol_str = str(symbol) if symbol is not None else 'N/A'
                logger.error(f"{symbol_str} 거래 내역 데이터베이스 저장 실패: {e}")

            # 포트폴리오 저장
            self.save_portfolio()

            # 🚀 고급 트레이딩 시스템에 거래 결과 피드백 (활성화된 경우)
            if advanced_trading_system and advanced_analysis:
                try:
                    logger.info(f"🔥 [{symbol}] 고급 트레이딩 시스템에 거래 결과 피드백 시작")

                    # 거래 결과 데이터 구성
                    trade_feedback = {
                        'profit_pct': 0.0,  # 진입 시점이므로 0
                        'hold_time': 0,     # 진입 시점이므로 0
                        'confidence': advanced_analysis.get('confidence', 0.5),
                        'entry_price': entry_price,
                        'direction': direction,
                        'symbol': symbol,
                        'timestamp': timestamp,
                        'status': 'entered'
                    }

                    # 고급 시스템에 피드백 전달
                    advanced_trading_system.record_trade_result(trade_feedback, market_data)
                    logger.info(f"✅ [{symbol}] 고급 트레이딩 시스템 거래 진입 피드백 완료")

                except Exception as e:
                    logger.error(f"❌ [{symbol}] 고급 트레이딩 시스템 피드백 실패: {e}")

            # 포트폴리오 상태 로그 (시장 데이터 전달)
            market_data_dict = {symbol: {"price": current_price}}
            self.log_portfolio_status(market_data_dict, binance_utils)

            return trade_result

        except Exception as e:
            # 안전한 심볼 문자열 변환
            symbol_str = str(symbol) if symbol is not None else 'N/A'
            logger.error(f"{symbol_str} 거래 실행 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())

            trade_result["success"] = False
            trade_result["error"] = str(e)
            trade_result["status"] = "rejected"
            trade_result["reason"] = f"일반 오류: {str(e)}"

            # 거래 내역 추가 (실행되지 않았지만 기록)
            self.trade_history.append(trade_result)

            # 포트폴리오 통계 업데이트 (거부된 거래는 일단 총 거래 수에만 포함)
            self.performance["total_trades"] += 1

            # 🔧 except 블록에서 안전한 direction 사용
            try:
                # direction 변수가 정의되어 있으면 사용, 없으면 전략에서 추출
                if 'direction' in locals() and direction is not None:
                    safe_direction_for_except = direction
                else:
                    # 전략에서 direction 재추출
                    strategy_type = strategy.get('type', 'unknown')
                    strategy_action = strategy.get('action', 'unknown')
                    safe_direction_for_except = strategy_action if strategy_action != 'unknown' else strategy_type
                logger.info(f"🔧 [{symbol_str}] except 블록에서 direction 추출: {safe_direction_for_except}")
            except Exception as direction_e:
                logger.warning(f"🔧 [{symbol_str}] except 블록에서 direction 추출 실패: {direction_e}, 기본값 사용")
                safe_direction_for_except = 'unknown'

            # 거부된 거래 추적을 위해 trade_result에 추가 정보 저장
            trade_result["rejected_timestamp"] = timestamp
            trade_result["entry_prediction"] = safe_direction_for_except  # 🔧 안전한 direction 변수 사용
            trade_result["entry_price_prediction"] = entry_price  # 예측 진입가 저장
            trade_result["take_profit_prediction"] = take_profit  # 예측 TP 저장
            trade_result["stop_loss_prediction"] = stop_loss  # 예측 SL 저장

            # 나중에 이 거래의 예측이 맞았는지 확인하기 위해 rejected_trades 목록에 추가
            if not hasattr(self, 'rejected_trades'):
                self.rejected_trades = []
            self.rejected_trades.append(trade_result)

            # 포트폴리오 저장
            self.save_portfolio()

            logger.info(f"{symbol_str} 거래가 일반 오류로 거부되었습니다: {str(e)}")
            return trade_result

    def verify_rejected_trades(self, market_data_dict: Dict[str, Dict[str, Any]]) -> None:
        """
        거부된 거래의 예측이 맞았는지 확인하고 승률에 반영

        Args:
            market_data_dict: 심볼별 시장 데이터 (심볼 -> 시장 데이터)
        """
        try:
            logger.info("===== 거부된 거래 검증 시작 =====")
            logger.info(f"시장 데이터 키: {list(market_data_dict.keys())}")
            logger.info(f"시장 데이터 첫 번째 항목: {str(next(iter(market_data_dict.values()), {}))}")

            if not hasattr(self, 'rejected_trades'):
                logger.warning("rejected_trades 속성이 없습니다. 검증을 건너뜁니다.")
                return

            if not self.rejected_trades:
                logger.info("거부된 거래가 없습니다. 검증을 건너뜁니다.")
                return

            # 디버깅: rejected_trades 속성 타입 확인
            logger.info(f"rejected_trades 타입: {type(self.rejected_trades)}")
            logger.info(f"rejected_trades 길이: {len(self.rejected_trades) if hasattr(self.rejected_trades, '__len__') else '알 수 없음'}")

            # 거부된 거래 데이터 덤프 (첫 번째 항목)
            first_trade = self.rejected_trades[0]
            logger.info(f"거부된 거래 데이터 샘플: {str(first_trade)}")

            # 거부된 거래 통계
            verified_count = len([t for t in self.rejected_trades if t.get('verified', False)])
            pending_count = len([t for t in self.rejected_trades if not t.get('verified', False)])
            logger.info(f"거부된 거래 통계: 총 {len(self.rejected_trades)}건, 검증 완료 {verified_count}건, 검증 대기 {pending_count}건")

            # 현재 시간
            current_time = int(time.time())
            verified_trades = []

            # 모든 거부된 거래 강제 검증
            logger.info("모든 거부된 거래를 강제로 검증합니다.")
            for trade in self.rejected_trades:
                # 이미 확인된 거래는 건너뜀
                if trade.get('verified', False):
                    logger.info(f"이미 검증된 거래 건너뜀: {trade.get('trade_id', 'unknown')}")
                    continue

                # 필수 필드 추출
                symbol = trade.get('symbol')
                if not symbol:
                    logger.warning(f"거부된 거래에 심볼 정보가 없습니다: {str(trade)}")
                    continue

                # 방향 필드 추출 (여러 필드 이름 처리)
                direction = trade.get('entry_prediction', trade.get('direction', trade.get('action', 'unknown')))

                # 진입가 추출
                entry_price = trade.get('entry_price_prediction', trade.get('entry_price', 0))

                # 거부 시간은 무시하고 모든 거래 강제 검증
                logger.info(f"[{symbol}] 거부된 거래 강제 검증 시작: 방향={direction}, 진입가={entry_price}")

                # 해당 심볼의 시장 데이터 가져오기
                market_data = market_data_dict.get(symbol)
                if not market_data:
                    logger.warning(f"심볼 {symbol}의 시장 데이터가 없어 임의 가격으로 강제 검증합니다.")
                    # 시장 데이터가 없는 경우 임의 가격 생성
                    current_price = entry_price * (1.01 if direction in ['long', 'BUY'] else 0.99)  # 롱은 1% 상승, 숏은 1% 하락으로 가정
                else:
                    # 현재 가격 가져오기
                    symbol_market_data = market_data_dict.get(symbol, {})
                    current_price = symbol_market_data.get('price', 0)
                    if current_price <= 0:
                        logger.warning(f"심볼 {symbol}의 가격이 0 이하입니다. 임의 가격으로 강제 검증합니다.")
                        current_price = entry_price * (1.01 if direction in ['long', 'BUY'] else 0.99)  # 롱은 1% 상승, 숏은 1% 하락으로 가정

                # 예측이 맞았는지 확인
                logger.info(f"[{symbol}] 거부된 거래 검증 시작: 방향={direction}, 진입가={entry_price}, 현재가={current_price}")

                # 방향에 따라 예측 결과 판단
                if direction in ['long', 'BUY']:
                    # 롱 포지션: 현재 가격이 진입가보다 높으면 성공, 낮으면 실패
                    prediction_correct = (current_price > entry_price)
                    logger.info(f"[{symbol}] 거부된 거래 검증: 롱 포지션, 진입가 {entry_price}, 현재가 {current_price}, 결과: {'성공' if prediction_correct else '실패'}")
                elif direction in ['short', 'SELL']:
                    # 숏 포지션: 현재 가격이 진입가보다 낮으면 성공, 높으면 실패
                    prediction_correct = (current_price < entry_price)
                    logger.info(f"[{symbol}] 거부된 거래 검증: 숏 포지션, 진입가 {entry_price}, 현재가 {current_price}, 결과: {'성공' if prediction_correct else '실패'}")
                else:
                    # 방향이 명확하지 않은 경우
                    logger.warning(f"[{symbol}] 거부된 거래의 방향이 명확하지 않음: {direction}")
                    prediction_correct = False  # 기본값은 실패

                # 예측 결과 기록
                trade['verified'] = True
                trade['verification_timestamp'] = current_time
                trade['verification_price'] = current_price
                trade['prediction_correct'] = prediction_correct
                trade['force_verified'] = True  # 강제 검증 표시

                # 🚀 .env 설정 기반 예측 실패 학습 (선택적)
                enable_prediction_learning = os.getenv('ENABLE_PREDICTION_FAILURE_LEARNING', 'false').lower() == 'true'

                if not prediction_correct and enable_prediction_learning:
                    self._update_prediction_failure_learning(symbol, direction, entry_price, current_price, trade)

                # 거래 내역에도 검증 결과 업데이트
                trade_id = trade.get('trade_id')
                if trade_id:
                    for history_trade in self.trade_history:
                        if history_trade.get('trade_id') == trade_id:
                            history_trade['verified'] = True
                            history_trade['verification_timestamp'] = current_time
                            history_trade['verification_price'] = current_price
                            history_trade['prediction_correct'] = prediction_correct
                            history_trade['force_verified'] = True
                            break

                # 승률 업데이트
                if prediction_correct:
                    self.performance['winning_trades'] = self.performance.get('winning_trades', 0) + 1
                    logger.info(f"[{symbol}] 거부된 거래 예측 성공: 승리 횟수 +1 (현재 승리: {self.performance.get('winning_trades', 0)})")
                else:
                    self.performance['losing_trades'] = self.performance.get('losing_trades', 0) + 1
                    logger.info(f"[{symbol}] 거부된 거래 예측 실패: 패배 횟수 +1 (현재 패배: {self.performance.get('losing_trades', 0)})")

                # 현재 승률 계산 및 로깅
                total_verified = self.performance.get('winning_trades', 0) + self.performance.get('losing_trades', 0)
                if total_verified > 0:
                    win_rate = (self.performance.get('winning_trades', 0) / total_verified) * 100
                    logger.info(f"[{symbol}] 현재 승률: {win_rate:.2f}% (성공: {self.performance.get('winning_trades', 0)}, 실패: {self.performance.get('losing_trades', 0)}, 총 검증: {total_verified})")

                verified_trades.append(trade)

            # 확인된 거래는 verified 플래그를 설정하고 목록에서 제거
            if verified_trades:
                logger.info(f"거부된 거래 예측 확인 완료: {len(verified_trades)}건")

                # 승률 계산 및 로깅
                total_trades = self.performance.get('winning_trades', 0) + self.performance.get('losing_trades', 0)
                if total_trades > 0:
                    win_rate = (self.performance.get('winning_trades', 0) / total_trades) * 100
                    logger.info(f"현재 승률: {win_rate:.2f}% (성공: {self.performance.get('winning_trades', 0)}, 실패: {self.performance.get('losing_trades', 0)}, 총 거래: {total_trades})")

                # 검증된 거래를 rejected_trades 목록에서 제거
                self.rejected_trades = [t for t in self.rejected_trades if not t.get('verified', False)]
                logger.info(f"검증된 거래를 목록에서 제거했습니다. 남은 거부된 거래: {len(self.rejected_trades)}건")

                # 포트폴리오 저장
                self.save_portfolio()

                # 검증된 거래 목록 로깅 (최대 5개)
                logger.info("검증된 거래 목록 (최대 5개):")
                for i, trade in enumerate(verified_trades[:5]):
                    logger.info(f"  {i+1}. 심볼: {trade.get('symbol')}, 방향: {trade.get('entry_prediction')}, 결과: {'성공' if trade.get('prediction_correct') else '실패'}")
            else:
                logger.info("이번 주기에서 검증된 거래가 없습니다.")

            # 검증 완료 로그
            logger.info("===== 거부된 거래 검증 완료 =====")

            # 현재 거부된 거래 통계
            verified_count = len([t for t in self.rejected_trades if t.get('verified', False)])
            pending_count = len([t for t in self.rejected_trades if not t.get('verified', False)])
            logger.info(f"거부된 거래 검증 후 통계: 총 {len(self.rejected_trades)}건, 검증 완료 {verified_count}건, 검증 대기 {pending_count}건")

        except Exception as e:
            import traceback
            logger.error(f"거부된 거래 검증 중 오류 발생: {e}")
            logger.error(traceback.format_exc())

    def _update_prediction_failure_learning(self, symbol: str, direction: str, entry_price: float, current_price: float, trade: Dict[str, Any]) -> None:
        """🚀 예측 실패 패턴을 학습 데이터에 추가"""
        try:
            # 예측 실패 패턴 분석
            price_change_pct = ((current_price - entry_price) / entry_price) * 100

            # 예측 방향과 실제 결과 비교
            if direction.lower() == "long":
                actual_direction = "bullish" if price_change_pct > 0 else "bearish"
                prediction_error = abs(price_change_pct)
            else:  # short
                actual_direction = "bearish" if price_change_pct < 0 else "bullish"
                prediction_error = abs(price_change_pct)

            # 실패 패턴 데이터 구성
            failure_pattern = {
                "symbol": symbol,
                "predicted_direction": direction,
                "actual_direction": actual_direction,
                "entry_price": entry_price,
                "verification_price": current_price,
                "price_change_pct": price_change_pct,
                "prediction_error": prediction_error,
                "timestamp": int(time.time()),
                "trade_id": trade.get('trade_id'),
                "failure_type": "direction_mismatch",
                "market_conditions": {
                    "volatility": trade.get('volatility', 0),
                    "volume": trade.get('volume', 0),
                    "sentiment": trade.get('sentiment', 0)
                }
            }

            logger.info(f"🔍 [{symbol}] 예측 실패 패턴 분석:")
            logger.info(f"  - 예측 방향: {direction}")
            logger.info(f"  - 실제 방향: {actual_direction}")
            logger.info(f"  - 가격 변화: {price_change_pct:.2f}%")
            logger.info(f"  - 예측 오차: {prediction_error:.2f}%")

            # ChromaDB에 실패 패턴 저장
            try:
                from simulator.learning_loop.llm_strategy_evaluator import LLMStrategyEvaluator
                import chromadb

                # ChromaDB 클라이언트 초기화
                chroma_dir = os.path.join("data", "chroma_db")
                db_client = chromadb.PersistentClient(path=chroma_dir)

                # LLMStrategyEvaluator 초기화 (LLM 클라이언트 포함)
                llm_client = self._get_llm_client()
                evaluator = LLMStrategyEvaluator(
                    llm_client=llm_client,  # 실제 LLM 클라이언트 전달
                    db_client=db_client,
                    config={}
                )

                # 실패 패턴을 학습 데이터로 저장
                pattern_id = f"failure_{symbol}_{int(time.time())}"
                pattern_text = f"Symbol: {symbol}, Predicted: {direction}, Actual: {actual_direction}, Error: {prediction_error:.2f}%"

                # 메타데이터 구성
                metadata = {
                    "data_type": "prediction_failure",
                    "symbol": symbol,
                    "predicted_direction": direction,
                    "actual_direction": actual_direction,
                    "prediction_error": prediction_error,
                    "timestamp": int(time.time()),
                    "failure_type": "direction_mismatch"
                }

                # ChromaDB에 저장
                if hasattr(evaluator, 'strategies_collection') and evaluator.strategies_collection:
                    evaluator.strategies_collection.add(
                        documents=[pattern_text],
                        metadatas=[metadata],
                        ids=[pattern_id]
                    )
                    logger.info(f"✅ [{symbol}] 예측 실패 패턴이 ChromaDB에 저장됨: {pattern_id}")
                else:
                    logger.warning(f"⚠️ [{symbol}] ChromaDB 컬렉션이 없어 실패 패턴 저장 실패")

            except Exception as storage_e:
                logger.error(f"❌ [{symbol}] 예측 실패 패턴 저장 실패: {storage_e}")

            # 로컬 실패 패턴 통계 업데이트
            if not hasattr(self, 'prediction_failures'):
                self.prediction_failures = {}

            if symbol not in self.prediction_failures:
                self.prediction_failures[symbol] = {
                    "total_failures": 0,
                    "direction_errors": 0,
                    "avg_error": 0.0,
                    "patterns": []
                }

            self.prediction_failures[symbol]["total_failures"] += 1
            self.prediction_failures[symbol]["direction_errors"] += 1

            # 평균 오차 업데이트
            current_avg = self.prediction_failures[symbol]["avg_error"]
            total_count = self.prediction_failures[symbol]["total_failures"]
            new_avg = ((current_avg * (total_count - 1)) + prediction_error) / total_count
            self.prediction_failures[symbol]["avg_error"] = new_avg

            # 최근 패턴 저장 (최대 10개)
            self.prediction_failures[symbol]["patterns"].append(failure_pattern)
            if len(self.prediction_failures[symbol]["patterns"]) > 10:
                self.prediction_failures[symbol]["patterns"] = self.prediction_failures[symbol]["patterns"][-10:]

            logger.info(f"📊 [{symbol}] 예측 실패 통계 업데이트:")
            logger.info(f"  - 총 실패: {self.prediction_failures[symbol]['total_failures']}회")
            logger.info(f"  - 방향 오류: {self.prediction_failures[symbol]['direction_errors']}회")
            logger.info(f"  - 평균 오차: {self.prediction_failures[symbol]['avg_error']:.2f}%")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 예측 실패 학습 업데이트 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _detect_trend_reversal(self, symbol: str, position: Dict[str, Any], current_price: float, market_data: Dict[str, Any]) -> bool:
        """🚀 고급 트렌드 반전 감지 시스템

        차트 패턴과 기술적 지표를 종합하여 트렌드 반전을 감지합니다.
        예: 숏 포지션 중 강력한 상승 반전 시 즉시 정리
        """
        try:
            direction = position.get('direction', 'unknown')
            entry_price = position.get('entry_price', current_price)
            entry_timestamp = position.get('entry_timestamp', time.time())
            position_age_minutes = (time.time() - entry_timestamp) / 60

            # 현재 손익률
            if direction == 'long':
                current_pnl_pct = ((current_price - entry_price) / entry_price) * 100
            else:  # short
                current_pnl_pct = ((entry_price - current_price) / entry_price) * 100

            self.logger.info(f"🔍 [{symbol}] 트렌드 반전 분석: {direction} 포지션, PnL={current_pnl_pct:.2f}%, 보유시간={position_age_minutes:.1f}분")

            # 1. 기본 조건: 최소 보유 시간 (1분 이상)
            if position_age_minutes < 1.0:
                return False

            # 2. 시장 데이터에서 캔들스틱 정보 추출
            recent_candles = market_data.get('recent_candles', [])
            if len(recent_candles) < 5:
                self.logger.debug(f"[{symbol}] 캔들스틱 데이터 부족: {len(recent_candles)}개")
                return False

            # 3. 최근 5개 캔들의 가격 움직임 분석
            price_changes = []
            volumes = []

            for i in range(len(recent_candles) - 4, len(recent_candles)):
                if i >= 0 and i < len(recent_candles):
                    candle = recent_candles[i]
                    open_price = float(candle[1])
                    close_price = float(candle[4])
                    volume = float(candle[5])

                    price_change_pct = ((close_price - open_price) / open_price) * 100
                    price_changes.append(price_change_pct)
                    volumes.append(volume)

            if len(price_changes) < 3:
                return False

            # 4. 트렌드 반전 패턴 감지
            avg_volume = sum(volumes) / len(volumes) if volumes else 0
            recent_volume = volumes[-1] if volumes else 0
            volume_spike = recent_volume > avg_volume * 1.5  # 50% 이상 볼륨 증가

            # 5. 방향별 반전 감지
            reversal_detected = False
            reversal_reason = ""

            if direction == 'short':
                # 숏 포지션: 강력한 상승 반전 감지
                consecutive_green = sum(1 for change in price_changes[-3:] if change > 0)
                strong_green_candles = sum(1 for change in price_changes[-3:] if change > 0.3)

                # 조건 1: 연속 3개 양봉 + 볼륨 급증
                if consecutive_green >= 3 and volume_spike:
                    reversal_detected = True
                    reversal_reason = "연속 3개 양봉 + 볼륨 급증"

                # 조건 2: 강력한 양봉 2개 이상 + 손실 확대
                elif strong_green_candles >= 2 and current_pnl_pct < -1.0:
                    reversal_detected = True
                    reversal_reason = f"강력한 양봉 {strong_green_candles}개 + 손실 {current_pnl_pct:.2f}%"

                # 조건 3: 단기 변동률이 포지션 방향과 반대로 강하게 움직임
                short_term_change = market_data.get('short_term_change_pct', 0)
                if short_term_change > 0.5 and current_pnl_pct < -0.5:  # 0.5% 이상 상승 + 손실
                    reversal_detected = True
                    reversal_reason = f"단기 상승 {short_term_change:.2f}% + 손실 {current_pnl_pct:.2f}%"

            elif direction == 'long':
                # 롱 포지션: 강력한 하락 반전 감지
                consecutive_red = sum(1 for change in price_changes[-3:] if change < 0)
                strong_red_candles = sum(1 for change in price_changes[-3:] if change < -0.3)

                # 조건 1: 연속 3개 음봉 + 볼륨 급증
                if consecutive_red >= 3 and volume_spike:
                    reversal_detected = True
                    reversal_reason = "연속 3개 음봉 + 볼륨 급증"

                # 조건 2: 강력한 음봉 2개 이상 + 손실 확대
                elif strong_red_candles >= 2 and current_pnl_pct < -1.0:
                    reversal_detected = True
                    reversal_reason = f"강력한 음봉 {strong_red_candles}개 + 손실 {current_pnl_pct:.2f}%"

                # 조건 3: 단기 변동률이 포지션 방향과 반대로 강하게 움직임
                short_term_change = market_data.get('short_term_change_pct', 0)
                if short_term_change < -0.5 and current_pnl_pct < -0.5:  # 0.5% 이상 하락 + 손실
                    reversal_detected = True
                    reversal_reason = f"단기 하락 {short_term_change:.2f}% + 손실 {current_pnl_pct:.2f}%"

            # 6. 추가 확인: 24시간 변동률과의 일치성
            daily_change = market_data.get('percent_change_24h', 0)
            if reversal_detected:
                # 일일 변동률이 반전 방향과 일치하면 신뢰도 증가
                if direction == 'short' and daily_change > 0:
                    self.logger.warning(f"🔄 [{symbol}] 트렌드 반전 확정: {reversal_reason} (일일 상승 {daily_change:.2f}%)")
                    return True
                elif direction == 'long' and daily_change < 0:
                    self.logger.warning(f"🔄 [{symbol}] 트렌드 반전 확정: {reversal_reason} (일일 하락 {daily_change:.2f}%)")
                    return True
                else:
                    # 일일 변동률과 불일치하면 보수적 접근
                    if abs(current_pnl_pct) > 2.0:  # 2% 이상 손실일 때만 반전 인정
                        self.logger.warning(f"🔄 [{symbol}] 트렌드 반전 (보수적): {reversal_reason} (손실 {current_pnl_pct:.2f}%)")
                        return True

            # 7. 환경변수로 제어되는 추가 조건
            enable_trend_reversal = os.getenv('ENABLE_TREND_REVERSAL_DETECTION', 'true').lower() == 'true'
            if not enable_trend_reversal:
                return False

            return False

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 트렌드 반전 감지 중 오류: {e}")
            return False

    def update_positions(self, market_data_dict: Dict[str, Dict[str, Any]],
                        binance_utils=None, prediction_history=None) -> List[Dict[str, Any]]:
        """
        오픈 포지션 업데이트 및 손익절 처리

        Args:
            market_data_dict: 심볼별 시장 데이터 (심볼 -> 시장 데이터)
            binance_utils: 바이낸스 유틸리티 객체 (실제 거래 시 필요)
            prediction_history: 예측 히스토리 객체 (선택사항)

        Returns:
            처리된 거래 목록
        """
        # 🔥 실제 거래 모드인 경우 바이낸스 API와 강화된 동기화 (매번 실행)
        if self.mode == 'real' and binance_utils:
            current_time = int(time.time())
            # 🔧 매번 강제 동기화 (30초 제한 제거)
            logger.info("🔥 update_positions에서 바이낸스 API와 포지션 강제 동기화 시작 (매번 실행)")
            sync_result = self.sync_positions_with_exchange(binance_utils, force=True)
            self._last_sync_time = current_time

            if sync_result.get('success', False):
                logger.info(f"🔥 update_positions에서 바이낸스 API와 포지션 강제 동기화 성공")
                logger.info(f"🔥 동기화 결과: {sync_result['positions_before']} → {sync_result['positions_after']} 포지션")
                logger.info(f"🔥 거래소 포지션: {sync_result['exchange_positions']}개")

                # 🔥 동기화 변경사항 로깅
                if sync_result['changes_made']:
                    logger.info(f"🔥 동기화 변경사항:")
                    for change in sync_result['changes_made']:
                        logger.info(f"   - {change}")
            else:
                logger.warning(f"🔥 update_positions에서 바이낸스 API와 포지션 강제 동기화 실패")
                if sync_result.get('errors'):
                    for error in sync_result['errors']:
                        logger.error(f"   - 동기화 오류: {error}")

        # 먼저 거부된 거래의 예측이 맞았는지 확인
        logger.info("거부된 거래 검증 호출 시작")
        self.verify_rejected_trades(market_data_dict)
        logger.info("거부된 거래 검증 호출 완료")

        if not self.open_positions:
            return []

        processed_trades = []
        remaining_positions = []

        logger.info(f"=== 포지션 업데이트 시작: 총 {len(self.open_positions)}개 포지션 ===")

        for i, position in enumerate(self.open_positions):
            logger.info(f"[{i+1}/{len(self.open_positions)}] 포지션 처리 시작: {position.get('symbol', 'N/A')} {position.get('direction', 'N/A')}")
            logger.info(f"  - 진입가: ${position.get('entry_price', 0):.4f}")
            logger.info(f"  - 수량: {position.get('quantity', position.get('size', 0))}")
            logger.info(f"  - 진입시간: {position.get('entry_timestamp', 0)}")
            logger.info(f"  - 타임프레임: {position.get('timeframe', 'N/A')}")
            symbol = position["symbol"]
            direction = position["direction"]
            entry_price = position["entry_price"]

            # 'quantity' 키가 없는 경우 'size' 키에서 복사
            if "quantity" not in position:
                if "size" in position:
                    logger.warning(f"{symbol} 포지션에 'quantity' 키가 없지만 'size' 키가 있습니다. 'size' 값을 'quantity'로 복사합니다.")
                    position["quantity"] = position["size"]
                else:
                    error_msg = f"{symbol} 포지션에 'quantity' 키와 'size' 키가 모두 없습니다. 포지션 정보: {str(position)}"
                    logger.error(error_msg)
                    raise KeyError(error_msg)


            stop_loss = position["stop_loss"]

            # 해당 심볼의 시장 데이터 가져오기
            market_data = market_data_dict.get(symbol)
            logger.info(f"  - 시장 데이터 조회: {symbol} → {'있음' if market_data else '없음'}")
            if not market_data:
                # 🚨 시장 데이터 없는 경우 상세 로깅 및 대체 데이터 시도
                logger.warning(f"  - {symbol} 시장 데이터 없음")
                logger.info(f"  - 사용 가능한 시장 데이터 심볼: {list(market_data_dict.keys())}")

                # 🚀 바이낸스 API에서 직접 가격 조회 시도
                if binance_utils:
                    try:
                        logger.info(f"  - {symbol} 바이낸스 API에서 직접 가격 조회 시도")
                        price_result = binance_utils.get_current_price(f"{symbol}USDT")

                        # 🚨 get_current_price는 float를 반환함
                        if price_result and isinstance(price_result, (int, float)) and price_result > 0:
                            current_price = float(price_result)
                            logger.info(f"  - {symbol} 바이낸스 API 가격 조회 성공: ${current_price:.2f}")

                            # 임시 시장 데이터 생성
                            market_data = {
                                'symbol': symbol,
                                'price': current_price,
                                'data_source': 'binance_api_fallback'
                            }
                        else:
                            logger.warning(f"  - {symbol} 바이낸스 API 가격 조회 실패: {str(price_result)}")
                            remaining_positions.append(position)
                            continue
                    except Exception as e:
                        logger.error(f"  - {symbol} 바이낸스 API 가격 조회 중 오류: {e}")
                        remaining_positions.append(position)
                        continue
                else:
                    # 바이낸스 유틸리티가 없으면 포지션 유지
                    logger.warning(f"  - {symbol} 바이낸스 유틸리티 없음 → 포지션 유지")
                    remaining_positions.append(position)
                    continue

            current_price = market_data.get("price", 0)
            logger.info(f"  - 현재가: ${current_price:.4f}")
            if current_price <= 0:
                # 유효한 가격 없으면 포지션 유지
                logger.warning(f"  - {symbol} 유효한 가격 없음 (${current_price}) → 포지션 유지")
                remaining_positions.append(position)
                continue

            # 바이낸스 API 데이터 우선 사용 (동기화된 포지션의 경우)
            if 'percentage' in position and position['percentage'] is not None:
                # 바이낸스 API에서 제공하는 ROI 사용
                basic_roi = float(position['percentage'])
                logger.info(f"  - 기본 ROI (바이낸스 API): {basic_roi:.4f}%")
            elif 'unrealized_pnl' in position and 'value_usd' in position and position['value_usd'] > 0:
                # 바이낸스 API PnL 달러 금액으로 ROI 계산
                unrealized_pnl = float(position['unrealized_pnl'])
                position_value = float(position['value_usd'])
                basic_roi = (unrealized_pnl / position_value) * 100
                logger.info(f"  - 기본 ROI (바이낸스 PnL 기반): {basic_roi:.4f}% (PnL: ${unrealized_pnl:.2f})")
            else:
                # 내부 계산 (fallback)
                if direction == "long":
                    basic_roi = (current_price - entry_price) / entry_price * 100
                else:  # short
                    basic_roi = (entry_price - current_price) / entry_price * 100
                logger.info(f"  - 기본 ROI (내부 계산): {basic_roi:.4f}%")

            # 레버리지 적용 ROI 계산 (실제 포지션 레버리지 우선 사용)
            position_leverage = position.get('leverage', 1)

            # 레버리지 값 검증 및 정규화
            try:
                if position_leverage:
                    leverage = float(position_leverage)
                    if leverage <= 0:
                        leverage = 1
                else:
                    leverage = 1
            except (ValueError, TypeError):
                logger.warning(f"  - 잘못된 레버리지 값: {position_leverage}, 기본값 1 사용")
                leverage = 1

            # ROI 계산 방식 결정
            if leverage > 1:
                # 레버리지가 적용된 경우: 기본 ROI에 레버리지 곱하기
                current_pnl_pct = basic_roi * leverage
                logger.info(f"  - 레버리지 {leverage}x 적용 ROI: {current_pnl_pct:.4f}% (기본 ROI: {basic_roi:.4f}%)")
            else:
                # 레버리지가 없는 경우: 기본 ROI 그대로 사용
                current_pnl_pct = basic_roi
                logger.info(f"  - 레버리지 미적용 ROI: {current_pnl_pct:.4f}%")

            # 환경 변수 기반 임계값과 비교를 위해 레버리지 정보 저장
            position["actual_leverage"] = leverage
            position["basic_roi"] = basic_roi
            position["leveraged_roi"] = current_pnl_pct

            logger.info(f"  - ROI 계산: {direction} 포지션")
            logger.info(f"    진입가: ${entry_price:.4f}")
            logger.info(f"    현재가: ${current_price:.4f}")
            logger.info(f"    최종 ROI: {current_pnl_pct:.4f}%")

            position["current_pnl"] = current_pnl_pct

            # 🎯 통합 생각카드 업데이트 (포지션 상태 변경 시)
            if self.card_manager:
                try:
                    position_id = position.get('trade_id')
                    if position_id:
                        # 시간대별 학습 데이터 업데이트
                        current_time = int(time.time())
                        entry_time = position.get("entry_timestamp", current_time)
                        hold_time_minutes = (current_time - entry_time) / 60

                        # 시간대 분류
                        if hold_time_minutes <= 3:
                            timeframe = 'short'
                        elif hold_time_minutes <= 60:
                            timeframe = 'medium'
                        else:
                            timeframe = 'long'

                        # 시간대별 학습 데이터 업데이트
                        self.card_manager.update_card(position_id, 'timeframe_learning', {
                            'timeframe': timeframe,
                            'result_data': {
                                'profit': current_pnl_pct,
                                'hold_time_minutes': hold_time_minutes,
                                'current_price': current_price,
                                'timestamp': current_time,
                                'status': 'ongoing'
                            },
                            'metrics': {
                                'max_profit': max(position.get('max_profit', current_pnl_pct), current_pnl_pct),
                                'min_profit': min(position.get('min_profit', current_pnl_pct), current_pnl_pct)
                            }
                        })

                        # 포지션 관리 정보 업데이트
                        self.card_manager.update_card(position_id, 'position_management', {
                            'action': 'monitor_position',
                            'reason': f'포지션 모니터링 (PnL: {current_pnl_pct:.2f}%)',
                            'confidence': 0.5,  # 모니터링 단계의 기본 신뢰도
                            'market_condition': {
                                'price': current_price,
                                'pnl_pct': current_pnl_pct,
                                'hold_time_minutes': hold_time_minutes,
                                'timestamp': current_time
                            }
                        })

                        logger.debug(f"🎯 [{symbol}] 통합 생각카드 업데이트 완료: {timeframe} 시간대, PnL={current_pnl_pct:.2f}%")

                except Exception as card_e:
                    logger.error(f"❌ [{symbol}] 통합 생각카드 업데이트 실패: {card_e}")

            # 최소 보유 시간 확인
            current_time = int(time.time())
            entry_time = position.get("entry_timestamp", 0)
            min_hold_time = position.get("min_hold_time", 60)  # 기본값 1분
            position_age = current_time - entry_time

            # 최소 보유 시간이 지나지 않았으면 포지션 유지
            if position_age < min_hold_time:
                logger.debug(f"{symbol} 포지션 최소 보유 시간 미달: {position_age}초/{min_hold_time}초")
                remaining_positions.append(position)
                continue

            # 🔧 가상거래와 실제거래 분리: 가상거래에만 시간 제한 적용
            is_virtual = position.get('virtual_mode', False) or position.get('learning_purpose', False)

            if is_virtual:
                # 가상거래: 시간 기반 자동 정리 적용
                timeframe = position.get('timeframe', 'short_term')
                max_age_map = {
                    'ultra_short': 30 * 60,        # 30분 (초단기 투자)
                    'short_term': 60 * 60,         # 1시간 (단기 투자)
                    'medium_term': 4 * 60 * 60,    # 4시간 (중기 투자)
                    'long_term': 24 * 60 * 60,     # 24시간 (장기 투자)
                    'scalping': 15 * 60,           # 15분 (스캘핑)
                    'default': 60 * 60             # 기본값: 1시간 (단기 투자)
                }
                max_hold_time = max_age_map.get(timeframe, max_age_map['default'])

                # 가상거래 시간 만료 체크
                if position_age > max_hold_time:
                    logger.info(f"⏰ [가상거래] [{symbol}] 시간 만료로 포지션 정리: {position_age}초 > {max_hold_time}초 (타임프레임: {timeframe})")
            else:
                # 실제거래: 시간 기반 자동 정리 비활성화 (익절/손절/신호 기반만 사용)
                logger.debug(f"🔄 [실제거래] [{symbol}] 시간 기반 정리 스킵: {position_age}초 경과 (익절/손절/신호 기반만 사용)")
                remaining_positions.append(position)
                continue

            # 가상거래만 시간 만료 체크 실행
            if is_virtual and position_age > max_hold_time:

                # 시간 만료로 포지션 청산
                exit_price = current_price
                timestamp = int(time.time())

                # 실현 손익 계산
                if direction == "long":
                    realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # short
                    realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                else:
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)

                # 포지션 청산 처리
                processed_trades.append({
                    "trade_id": position["trade_id"],
                    "symbol": symbol,
                    "direction": direction,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "exit_type": "time_expiry",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct
                })

                # 🚨 긴급 수정: 거래 히스토리 업데이트
                self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, "time_expiry")

                continue  # 다음 포지션으로

            # 🚀 환경변수 기반 익절/손절 활성화 여부 확인
            enable_take_profit, enable_stop_loss = self._should_enable_profit_loss_exit()

            if enable_take_profit:
                # 목표 ROI 달성 체크 (환경 변수 기반, 실제 레버리지 반영)
                from simulator.trading.roi_config import get_roi_thresholds

                # 실제 포지션 레버리지 사용 (위에서 계산된 값)
                actual_leverage = position.get('actual_leverage', 1)

                # 레버리지 값 정수로 변환 (환경 변수 매핑용)
                try:
                    leverage_for_config = int(float(actual_leverage))
                except (ValueError, TypeError):
                    leverage_for_config = 1

                # 환경 변수에서 심볼별 임계값 가져오기 (실제 레버리지 기반)
                target_roi, stop_loss_roi = get_roi_thresholds(symbol, leverage_for_config)

                logger.info(f"  - 임계값 계산: 심볼={symbol}, 실제 레버리지={actual_leverage}x")
                logger.info(f"  - 환경 변수 기반 익절: {target_roi:.4f}%, 손절: {stop_loss_roi:.4f}%")

                # 포지션에 개별 설정이 있으면 우선 사용
                if 'target_roi' in position:
                    target_roi = position['target_roi']
                    logger.info(f"  - 개별 설정 익절 기준 적용: {target_roi:.4f}%")

                logger.info(f"  - 익절 조건 체크:")
                logger.info(f"    목표 ROI: {target_roi}%")
                logger.info(f"    현재 ROI: {current_pnl_pct:.4f}%")
                logger.info(f"    익절 조건: {'충족' if current_pnl_pct >= target_roi else '미충족'}")

                if current_pnl_pct >= target_roi:
                    logger.warning(f"🎯 [{symbol}] 목표 ROI({target_roi}%) 달성! 즉시 정리: PnL={current_pnl_pct:.2f}%")

                # 실제 거래 모드에서 바이낸스 API로 포지션 청산
                if self.mode == 'real' and binance_utils:
                    try:
                        # 바이낸스에서 실제 포지션 청산
                        close_side = "SELL" if direction == "long" else "BUY"
                        close_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=position['quantity']
                        )
                        logger.info(f"[{symbol}] 바이낸스 포지션 청산 완료: {str(close_result)}")
                    except Exception as e:
                        logger.error(f"[{symbol}] 바이낸스 포지션 청산 실패: {e}")

                # 목표 ROI 달성으로 포지션 청산
                exit_price = current_price
                timestamp = int(time.time())

                # 실현 손익 계산
                if direction == "long":
                    realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # short
                    realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                else:
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)

                # 포지션 청산 처리
                processed_trades.append({
                    "trade_id": position.get("trade_id", f"auto_{symbol}_{int(time.time())}"),
                    "symbol": symbol,
                    "direction": direction,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "exit_type": "target_roi",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct
                })

                # 🚨 긴급 수정: 거래 히스토리 업데이트
                self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, "target_roi")

                continue  # 다음 포지션으로
            else:
                logger.info(f"🎛️ 익절 기능 비활성화됨 (환경변수: ENABLE_TAKE_PROFIT=false)")

            if enable_stop_loss:
                # ROI 기준 손절 체크 (환경 변수 기반, 실제 레버리지 반영)
                # stop_loss_roi는 이미 위에서 get_roi_thresholds()로 가져옴 (익절이 활성화된 경우)
                if not enable_take_profit:
                    # 익절이 비활성화된 경우 손절만을 위해 임계값 가져오기
                    from simulator.trading.roi_config import get_roi_thresholds
                    actual_leverage = position.get('actual_leverage', 1)
                    try:
                        leverage_for_config = int(float(actual_leverage))
                    except (ValueError, TypeError):
                        leverage_for_config = 1
                    target_roi, stop_loss_roi = get_roi_thresholds(symbol, leverage_for_config)

                # 포지션에 개별 손절 설정이 있으면 우선 사용
                if 'stop_loss_roi' in position:
                    stop_loss_roi = position['stop_loss_roi']
                    logger.info(f"  - 개별 설정 손절 기준 적용: {stop_loss_roi:.4f}%")

                logger.info(f"  - 손절 조건 체크:")
                logger.info(f"    손절 ROI: {stop_loss_roi}%")
                logger.info(f"    현재 ROI: {current_pnl_pct:.4f}%")
                logger.info(f"    손절 조건: {'충족' if current_pnl_pct <= stop_loss_roi else '미충족'}")

                if current_pnl_pct <= stop_loss_roi:
                    logger.warning(f"🚨 [{symbol}] ROI 손절 기준 {stop_loss_roi}% 도달: {current_pnl_pct:.2f}% -> 포지션 정리")

                # 실제 거래 모드에서 바이낸스 API로 포지션 청산
                if self.mode == 'real' and binance_utils:
                    try:
                        # 바이낸스에서 실제 포지션 청산
                        close_side = "SELL" if direction == "long" else "BUY"
                        close_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=position['quantity']
                        )
                        logger.info(f"[{symbol}] 바이낸스 손절 청산 완료: {str(close_result)}")
                    except Exception as e:
                        logger.error(f"[{symbol}] 바이낸스 손절 청산 실패: {e}")

                exit_price = current_price
                timestamp = int(time.time())
                # 실현 손익 계산
                if direction == "long":
                    realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # short
                    realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                else:
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)

                # 포지션 청산 처리
                processed_trades.append({
                    "trade_id": position.get("trade_id", f"auto_{symbol}_{int(time.time())}"),
                    "symbol": symbol,
                    "direction": direction,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "exit_type": "roi_stop_loss",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct
                })

                # 🚨 긴급 수정: 거래 히스토리 업데이트
                self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, "roi_stop_loss")

                continue  # 다음 포지션으로
            else:
                logger.info(f"🎛️ 손절 기능 비활성화됨 (환경변수: ENABLE_STOP_LOSS=false)")

            # 🚀 트레일링 스톱 로직 비활성화 (환경변수 제어)
            # 트레일링 스톱 로직 (수익 보호) - 조건 완화
            if current_pnl_pct >= 0.02:  # 0.02% 이상 수익일 때 (매우 낮은 기준)
                # 최고 수익률 추적
                max_pnl_key = f"{symbol}_max_pnl"
                if not hasattr(self, 'position_max_pnl'):
                    self.position_max_pnl = {}

                current_max_pnl = self.position_max_pnl.get(max_pnl_key, current_pnl_pct)
                if current_pnl_pct > current_max_pnl:
                    self.position_max_pnl[max_pnl_key] = current_pnl_pct
                    current_max_pnl = current_pnl_pct
                    logger.info(f"📈 [{symbol}] 최고 수익률 갱신: {current_max_pnl:.2f}%")

                # 🚀 환경변수 기반 트레일링 스톱 임계값
                trailing_threshold = float(os.getenv('TRAILING_STOP_THRESHOLD', '1.0'))  # 기본 1.0% (완화)
                if current_max_pnl - current_pnl_pct >= trailing_threshold:
                    logger.warning(f"🛡️ [{symbol}] 트레일링 스톱 발동: 최고={current_max_pnl:.2f}%, 현재={current_pnl_pct:.2f}%, 하락={current_max_pnl - current_pnl_pct:.2f}%")

                    # 실제 거래 모드에서 바이낸스 API로 포지션 청산
                    if self.mode == 'real' and binance_utils:
                        try:
                            # 바이낸스에서 실제 포지션 청산
                            close_side = "SELL" if direction == "long" else "BUY"
                            close_result = binance_utils.execute_futures_market_order(
                                symbol=symbol,
                                side=close_side,
                                quantity=position['quantity']
                            )
                            logger.info(f"[{symbol}] 바이낸스 트레일링 스톱 청산 완료: {str(close_result)}")
                        except Exception as e:
                            logger.error(f"[{symbol}] 바이낸스 트레일링 스톱 청산 실패: {e}")

                    # 트레일링 스톱으로 포지션 청산
                    exit_price = current_price
                    timestamp = int(time.time())

                    # 실현 손익 계산
                    if direction == "long":
                        realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                    else:  # short
                        realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                    # value_usd 필드가 없는 경우 계산
                    if 'value_usd' in position:
                        realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                    else:
                        position_value = position['quantity'] * position['entry_price']
                        realized_pnl = position_value * (realized_pnl_pct / 100)

                    # 포지션 청산 처리
                    processed_trades.append({
                        "trade_id": position["trade_id"],
                        "symbol": symbol,
                        "direction": direction,
                        "entry_price": entry_price,
                        "exit_price": exit_price,
                        "exit_type": "trailing_stop",
                        "realized_pnl": realized_pnl,
                        "realized_pnl_pct": realized_pnl_pct
                    })

                    # 🚨 긴급 수정: 거래 히스토리 업데이트
                    self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, "trailing_stop")

                    continue  # 다음 포지션으로

            # 🚀 1분봉 연속 방향성 트렌드 반전 감지
            if self.detect_market_reversal(symbol, position, market_data, current_price):
                logger.warning(f"🔄 [{symbol}] 트렌드 반전 감지로 포지션 청산")

                # 실제 거래 모드에서 바이낸스 API로 포지션 청산
                if self.mode == 'real' and binance_utils:
                    try:
                        close_side = "SELL" if direction == "long" else "BUY"
                        close_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=position['quantity']
                        )
                        logger.info(f"[{symbol}] 바이낸스 트렌드 반전 청산 완료: {str(close_result)}")
                    except Exception as e:
                        logger.error(f"[{symbol}] 바이낸스 트렌드 반전 청산 실패: {e}")

                # 트렌드 반전으로 포지션 청산
                exit_price = current_price
                timestamp = int(time.time())

                # 실현 손익 계산
                if direction == "long":
                    realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # short
                    realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                else:
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)

                # 포지션 청산 처리
                processed_trades.append({
                    "trade_id": position.get("trade_id", f"auto_{symbol}_{int(time.time())}"),
                    "symbol": symbol,
                    "direction": direction,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "exit_type": "trend_reversal",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct
                })

                # 거래 히스토리 업데이트
                self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, "trend_reversal")

                continue  # 다음 포지션으로

            # 🚀 기존 손익절 로직 비활성화 (환경변수 제어)
            # 손익절 조건 확인
            hit_take_profit = False
            hit_stop_loss = False

            # 환경변수가 활성화된 경우에만 기존 손익절 로직 실행
            if enable_take_profit or enable_stop_loss:
                take_profit = position["take_profit"]
                stop_loss = position["stop_loss"]

                if direction == "long":
                    if enable_take_profit and take_profit > 0 and current_price >= take_profit:
                        hit_take_profit = True
                    elif enable_stop_loss and stop_loss > 0 and current_price <= stop_loss:
                        hit_stop_loss = True
                else:  # short
                    if enable_take_profit and take_profit > 0 and current_price <= take_profit:
                        hit_take_profit = True
                    elif enable_stop_loss and stop_loss > 0 and current_price >= stop_loss:
                        hit_stop_loss = True
            else:
                logger.info(f"🎛️ 기존 손익절 로직 비활성화됨 (환경변수 설정)")

            # 손익절 조건 충족 시 포지션 청산
            if hit_take_profit or hit_stop_loss:
                # 실제 거래 모드에서 바이낸스 API로 포지션 청산
                if self.mode == 'real' and binance_utils:
                    try:
                        # 바이낸스에서 실제 포지션 청산
                        close_side = "SELL" if direction == "long" else "BUY"
                        close_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=position['quantity']
                        )
                        logger.info(f"[{symbol}] 바이낸스 {'익절' if hit_take_profit else '손절'} 청산 완료: {str(close_result)}")
                    except Exception as e:
                        logger.error(f"[{symbol}] 바이낸스 {'익절' if hit_take_profit else '손절'} 청산 실패: {e}")

                exit_price = take_profit if hit_take_profit else stop_loss
                timestamp = int(time.time())
                # 실현 손익 계산
                if direction == "long":
                    realized_pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # short
                    realized_pnl_pct = (entry_price - exit_price) / entry_price * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position["value_usd"] * (realized_pnl_pct / 100)
                else:
                    # 바이낸스 API 동기화된 포지션의 경우 계산
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)
                    logger.info(f"[{symbol}] value_usd 필드 없음, 계산된 포지션 가치: ${position_value:.2f}")

                # 포지션 청산 로깅
                logger.info(f"{symbol} 포지션 청산: {'익절' if hit_take_profit else '손절'}, "
                         f"{realized_pnl_pct:.2f}%, ${realized_pnl:.2f}")

                # 포지션 청산 처리
                processed_trades.append({
                    "trade_id": position["trade_id"],
                    "symbol": symbol,
                    "direction": direction,
                    "entry_price": entry_price,
                    "exit_price": exit_price,
                    "exit_type": "take_profit" if hit_take_profit else "stop_loss",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct
                })

                # 🚨 긴급 수정: 거래 히스토리 업데이트
                exit_reason = "take_profit" if hit_take_profit else "stop_loss"
                self._update_trade_history_on_exit(position, exit_price, realized_pnl, realized_pnl_pct, exit_reason)

            else:
                # 손익절 조건 미충족 시 포지션 유지
                logger.info(f"  - 결과: 포지션 유지 (모든 청산 조건 미충족)")
                remaining_positions.append(position)

            logger.info(f"[{i+1}/{len(self.open_positions)}] 포지션 처리 완료: {symbol}")
            logger.info("=" * 50)

        # 포지션 업데이트 완료
        logger.info(f"=== 포지션 업데이트 완료 ===")
        logger.info(f"처리된 거래: {len(processed_trades)}개")
        logger.info(f"유지된 포지션: {len(remaining_positions)}개")

        self.open_positions = remaining_positions

        # 포트폴리오 저장
        self.save_portfolio()

        return processed_trades

    def _update_trade_history_on_exit(self, position, exit_price, realized_pnl, realized_pnl_pct, exit_reason):
        """
        🚨 긴급 수정: 포지션 종료 시 거래 히스토리 업데이트
        """
        try:
            # 해당 거래를 trade_history에서 찾아서 업데이트
            trade_id = position.get("trade_id")
            symbol = position.get("symbol")

            logger.info(f"🔄 거래 히스토리 업데이트 시작: {symbol} (trade_id: {trade_id})")

            # trade_history에서 해당 거래 찾기
            updated = False
            for trade in self.trade_history:
                # trade_id로 매칭하거나, 심볼과 상태로 매칭
                if (trade.get("trade_id") == trade_id or
                    (trade.get("symbol") == symbol and
                     trade.get("status") == "executed" and
                     trade.get("realized_pnl", 0) == 0)):

                    # 거래 정보 업데이트
                    trade["status"] = "closed"
                    trade["exit_price"] = exit_price
                    trade["exit_time"] = int(time.time())
                    trade["exit_reason"] = exit_reason
                    trade["realized_pnl"] = realized_pnl
                    trade["realized_pnl_pct"] = realized_pnl_pct

                    logger.info(f"✅ 거래 히스토리 업데이트 완료: {symbol}")
                    logger.info(f"   상태: executed → closed")
                    logger.info(f"   종료가: ${exit_price:.4f}")
                    logger.info(f"   realized_pnl: ${realized_pnl:.4f}")
                    logger.info(f"   종료 이유: {exit_reason}")

                    updated = True
                    break

            if not updated:
                logger.warning(f"⚠️ 거래 히스토리에서 해당 거래를 찾을 수 없음: {symbol} (trade_id: {trade_id})")

                # 새로운 종료 거래 기록 추가
                new_trade = {
                    "trade_id": f"{trade_id}_exit" if trade_id else f"{symbol}_exit_{int(time.time())}",
                    "symbol": symbol,
                    "direction": position.get("direction"),
                    "action": "SELL" if position.get("direction") == "long" else "BUY",
                    "timestamp": int(time.time()),
                    "entry_price": position.get("entry_price"),
                    "exit_price": exit_price,
                    "exit_time": int(time.time()),
                    "exit_reason": exit_reason,
                    "quantity": position.get("quantity"),
                    "status": "closed",
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct,
                    "order_id": position.get("order_id", "unknown")
                }
                self.trade_history.append(new_trade)
                logger.info(f"📝 새로운 종료 거래 기록 추가: {symbol}")

            # 포트폴리오 저장
            self.save_portfolio()

        except Exception as e:
            logger.error(f"❌ 거래 히스토리 업데이트 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def fix_executed_trades_emergency(self):
        """
        🚨 긴급 수정: 기존 executed 상태 거래들을 일괄 수정
        """
        try:
            logger.info("🚨 긴급 수정: executed 상태 거래들 일괄 수정 시작")

            fixed_count = 0
            current_time = int(time.time())

            for trade in self.trade_history:
                if trade.get("status") == "executed" and trade.get("realized_pnl", 0) == 0:
                    # 3분 이상 지난 거래는 시간 만료로 처리
                    trade_time = trade.get("timestamp", 0)
                    if current_time - trade_time > 180:  # 3분
                        trade["status"] = "closed"
                        trade["exit_reason"] = "time_expiry_fix"
                        trade["exit_time"] = current_time
                        trade["exit_price"] = trade.get("entry_price", 0)  # 진입가로 종료 (손익 0)
                        trade["realized_pnl"] = 0.0
                        trade["realized_pnl_pct"] = 0.0

                        fixed_count += 1
                        logger.info(f"✅ 거래 수정: {trade.get('symbol')} - executed → closed (시간 만료)")

            logger.info(f"🚨 긴급 수정 완료: {fixed_count}개 거래 수정됨")

            # 포트폴리오 저장
            if fixed_count > 0:
                self.save_portfolio()
                logger.info("💾 포트폴리오 저장 완료")

            return fixed_count

        except Exception as e:
            logger.error(f"❌ 긴급 수정 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 0

    def _get_existing_position(self, symbol: str) -> Optional[Dict]:
        """
        🚀 새로운 기능: 특정 심볼의 기존 포지션 확인
        """
        try:
            for position in self.open_positions:
                if position.get('symbol') == symbol:
                    return position
            return None
        except Exception as e:
            logger.error(f"기존 포지션 확인 중 오류: {e}")
            return None

    def _close_existing_position(self, symbol: str, position: Dict, current_price: float, reason: str = "position_switch"):
        """
        🚀 새로운 기능: 기존 포지션 청산
        """
        try:
            logger.info(f"🔄 포지션 청산 시작: {symbol} {position.get('direction')}")

            # 손익 계산
            entry_price = position.get('entry_price', 0)
            quantity = position.get('quantity', 0)
            direction = position.get('direction')

            if direction == 'long':
                # 롱 포지션: (현재가 - 진입가) * 수량
                pnl_usd = (current_price - entry_price) * quantity
            else:
                # 숏 포지션: (진입가 - 현재가) * 수량
                pnl_usd = (entry_price - current_price) * quantity

            pnl_pct = (pnl_usd / (entry_price * quantity)) * 100 if entry_price > 0 and quantity > 0 else 0

            logger.info(f"💰 청산 손익: ${pnl_usd:.4f} ({pnl_pct:.2f}%)")

            # 거래 히스토리 업데이트
            self._update_trade_history_on_exit(position, current_price, pnl_usd, pnl_pct, reason)

            # 오픈 포지션에서 제거
            self.open_positions = [p for p in self.open_positions if p.get('trade_id') != position.get('trade_id')]

            # 잔액 업데이트
            self.balance += pnl_usd

            logger.info(f"✅ 포지션 청산 완료: {symbol} {direction} (이유: {reason}) - PnL: ${pnl_usd:.4f} ({pnl_pct:.2f}%)")

            # 🎯 실제 거래 완료 시 InCA 피드백 전달
            try:
                # 하이브리드 컨트롤러의 InCA 에이전트 사용
                inca_agent = None
                if hasattr(self, 'hybrid_controller') and self.hybrid_controller:
                    inca_agent = getattr(self.hybrid_controller, 'inca_agent', None)

                if inca_agent:
                    inca_feedback = {
                        'symbol': symbol,
                        'prediction': {
                            'action_recommendation': 'buy' if direction == 'long' else 'sell',
                            'trading_direction': direction,
                            'confidence': position.get('confidence', 0.5),
                            'entry_price': position.get('entry_price', 0),
                            'strategy_type': 'real_trade',
                            'reasoning': f'실제 거래 완료: {reason}'
                        },
                        'trade_result': {
                            'profit': pnl_usd,  # 실제 달러 손익
                            'profit_percent': pnl_pct,  # 실제 퍼센트 수익률
                            'realized_pnl': pnl_usd,  # 추가 필드
                            'realized_pnl_pct': pnl_pct,  # 추가 필드
                            'pnl': pnl_usd,  # 추가 필드
                            'pnl_percent': pnl_pct,  # 추가 필드
                            'exit_price': exit_price,
                            'success': pnl_pct > 0.1,  # 0.1% 이상 수익 시 성공
                            'status': 'completed',
                            'evaluation_details': {
                                'close_reason': reason,
                                'pnl_usd': pnl_usd,
                                'pnl_pct': pnl_pct,
                                'holding_time': int(time.time()) - position.get('entry_timestamp', int(time.time())),
                                'real_trade': True
                            }
                        },
                        'market_data': {
                            'symbol': symbol,
                            'timestamp': int(time.time()),
                            'real_trade': True
                        }
                    }

                    inca_result = inca_agent.learn_from_feedback(inca_feedback)
                    logger.info(f"🎯 [{symbol}] InCA 실제 거래 결과 학습 완료: 수익 ${pnl_usd:.2f} ({pnl_pct:.2f}%), 성공: {pnl_pct > 0.1}")
                else:
                    logger.warning(f"⚠️ [{symbol}] InCA 에이전트를 찾을 수 없음 - 실제 거래 피드백 생략")

            except Exception as inca_e:
                logger.warning(f"⚠️ [{symbol}] InCA 실제 거래 피드백 전달 실패: {inca_e}")

        except Exception as e:
            logger.error(f"포지션 청산 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _should_enable_profit_loss_exit(self) -> tuple:
        """
        🚀 새로운 기능: 환경변수 기반 익절/손절 활성화 여부 확인
        """
        import os

        enable_take_profit = os.getenv('ENABLE_TAKE_PROFIT', 'false').lower() == 'true'
        enable_stop_loss = os.getenv('ENABLE_STOP_LOSS', 'false').lower() == 'true'

        logger.info(f"🎛️ 익절/손절 설정: 익절={enable_take_profit}, 손절={enable_stop_loss}")

        return enable_take_profit, enable_stop_loss

    def calculate_portfolio_value(self, market_data_dict: Dict[str, Dict[str, Any]]) -> float:
        """
        현재 포트폴리오 총 가치 계산

        Args:
            market_data_dict: 심볼별 시장 데이터

        Returns:
            총 포트폴리오 가치 (USDT)
        """
        # 현금 잔액
        total_value = self.balance
        logger.info(f"포트폴리오 가치 계산 - 기본 잔액: ${self.balance:.2f}")

        # 보유 자산 가치
        for symbol, quantity in self.assets.items():
            if quantity <= 0:
                continue

            market_data = market_data_dict.get(symbol)
            if not market_data:
                logger.warning(f"포트폴리오 가치 계산 - {symbol}의 시장 데이터가 없습니다.")
                continue

            current_price = market_data.get("price", 0)
            if current_price <= 0:
                logger.warning(f"포트폴리오 가치 계산 - {symbol}의 가격 정보가 없거나 0입니다.")
                continue

            asset_value = quantity * current_price
            total_value += asset_value
            logger.info(f"포트폴리오 가치 계산 - {symbol}: {quantity:.8f} @ ${current_price:.2f} = ${asset_value:.2f}")

        # 포트폴리오 성과 업데이트
        self.performance["current_value"] = total_value
        self.performance["total_profit_loss"] = total_value - self.performance["initial_balance"]
        self.performance["total_profit_loss_pct"] = (
            self.performance["total_profit_loss"] /
            self.performance["initial_balance"] * 100
        )

        logger.info(f"포트폴리오 가치 계산 - 총 가치: ${total_value:.2f}")
        return total_value

    def update_balance(self, new_balance: float):
        """
        포트폴리오 잔액 업데이트

        Args:
            new_balance: 새 잔액 (USDT)
        """
        old_balance = self.balance
        self.balance = new_balance
        logger.info(f"포트폴리오 잔액 업데이트: ${old_balance:.2f} -> ${new_balance:.2f} USDT")

        # 포트폴리오 저장
        self.save_portfolio()

        return True

    def handle_position_action(self, symbol: str, strategy: Dict[str, Any], binance_utils=None) -> Dict[str, Any]:
        """
        SELA 에이전트의 포지션 처리 방식에 따라 포지션을 관리합니다.

        Args:
            symbol: 거래 심볼
            strategy: SELA 전략 (position_action 포함)
            binance_utils: 바이낸스 유틸리티

        Returns:
            Dict: 처리 결과
        """
        position_action = strategy.get("position_action", "create_new")
        new_direction = strategy.get("direction", "long")

        logger.info(f"{symbol} 포지션 처리 시작: {position_action}")

        try:
            if position_action == "ignore":
                logger.info(f"{symbol} 신호 무시됨")
                return {"success": True, "action": "ignored", "message": "신호가 무시되었습니다"}

            elif position_action == "merge_same":
                return self._merge_same_direction_positions(symbol, strategy, binance_utils)

            elif position_action == "replace_opposite":
                return self._replace_opposite_positions(symbol, strategy, binance_utils)

            elif position_action == "create_new":
                # 기본 포지션 생성 로직은 기존 코드에서 처리
                logger.info(f"{symbol} 새 포지션 생성")
                return {"success": True, "action": "create_new", "message": "새 포지션 생성 준비"}

            else:
                logger.warning(f"{symbol} 알 수 없는 포지션 처리 방식: {position_action}")
                return {"success": True, "action": "create_new", "message": "기본 포지션 생성"}

        except Exception as e:
            logger.error(f"{symbol} 포지션 처리 중 오류: {e}")
            return {"success": False, "error": str(e)}

    def _merge_same_direction_positions(self, symbol: str, strategy: Dict[str, Any], binance_utils=None) -> Dict[str, Any]:
        """같은 방향 포지션들을 통합합니다."""
        try:
            direction = strategy.get("direction", "long")
            new_size = strategy.get("position_size", 0.1)

            # 같은 방향 포지션 찾기
            same_positions = []
            for pos in self.open_positions:
                if pos['symbol'] == symbol:
                    pos_direction = "long" if pos.get('action') == 'BUY' else "short"
                    if pos_direction == direction:
                        same_positions.append(pos)

            if not same_positions:
                logger.warning(f"{symbol} 통합할 같은 방향 포지션이 없습니다")
                return {"success": True, "action": "create_new", "message": "통합할 포지션 없음, 새 포지션 생성"}

            # 기존 포지션들의 평균 진입가 계산
            total_quantity = sum(pos.get('quantity', pos.get('size', 0)) for pos in same_positions)
            weighted_entry_price = sum(
                pos.get('entry_price', 0) * pos.get('quantity', pos.get('size', 0))
                for pos in same_positions
            ) / total_quantity if total_quantity > 0 else 0

            logger.info(f"{symbol} 포지션 통합: {len(same_positions)}개 포지션, 총 수량={total_quantity:.8f}, 평균가=${weighted_entry_price:.4f}")

            # 기존 포지션들 제거
            self.open_positions = [p for p in self.open_positions
                                 if not (p['symbol'] == symbol and
                                        ("long" if p.get('action') == 'BUY' else "short") == direction)]

            # 통합된 새 포지션 생성
            new_position = {
                'symbol': symbol,
                'direction': direction,
                'action': 'BUY' if direction == 'long' else 'SELL',
                'quantity': total_quantity + (new_size * strategy.get('entry_price', 0)),
                'entry_price': weighted_entry_price,
                'entry_timestamp': int(time.time()),
                'confidence': strategy.get('confidence', 0),
                'importance': strategy.get('importance', 0),
                'max_hold_time': strategy.get('max_hold_time', 300),
                'current_pnl': 0.0
            }

            self.open_positions.append(new_position)
            self.save_portfolio()

            logger.info(f"{symbol} 포지션 통합 완료: 새 수량={new_position['quantity']:.8f}")
            return {"success": True, "action": "merged", "message": f"{len(same_positions)}개 포지션 통합 완료"}

        except Exception as e:
            logger.error(f"{symbol} 포지션 통합 중 오류: {e}")
            return {"success": False, "error": str(e)}

    def _replace_opposite_positions(self, symbol: str, strategy: Dict[str, Any], binance_utils=None) -> Dict[str, Any]:
        """반대 방향 포지션을 새 포지션으로 교체합니다."""
        try:
            new_direction = strategy.get("direction", "long")

            # 반대 방향 포지션 찾기
            opposite_positions = []
            for pos in self.open_positions:
                if pos['symbol'] == symbol:
                    pos_direction = "long" if pos.get('action') == 'BUY' else "short"
                    if pos_direction != new_direction:
                        opposite_positions.append(pos)

            if not opposite_positions:
                logger.warning(f"{symbol} 교체할 반대 방향 포지션이 없습니다")
                return {"success": True, "action": "create_new", "message": "교체할 포지션 없음, 새 포지션 생성"}

            logger.info(f"{symbol} 반대 방향 포지션 교체: {len(opposite_positions)}개 포지션")

            # 기존 반대 포지션들 청산
            for pos in opposite_positions:
                close_result = self.close_position(symbol, "long" if pos.get('action') == 'BUY' else "short", binance_utils)
                if close_result.get('success'):
                    logger.info(f"{symbol} 기존 포지션 청산 완료")
                else:
                    logger.error(f"{symbol} 기존 포지션 청산 실패: {close_result.get('error')}")

            # 새 포지션은 기본 생성 로직에서 처리
            logger.info(f"{symbol} 새 {new_direction} 포지션 생성 준비")
            return {"success": True, "action": "replaced", "message": f"{len(opposite_positions)}개 반대 포지션 교체 완료"}

        except Exception as e:
            logger.error(f"{symbol} 포지션 교체 중 오류: {e}")
            return {"success": False, "error": str(e)}

    def _should_hold_position_during_neutral(self, symbol: str, existing_position: Dict, strategy: Dict, market_data: Dict) -> bool:
        """
        단기 중립 vs 추세 전환을 구분하여 포지션 유지 여부 결정
        🎯 통합 생각카드 정보를 활용한 지능형 포지션 관리

        Args:
            symbol: 심볼
            existing_position: 기존 포지션 정보
            strategy: 새로운 전략 정보
            market_data: 시장 데이터

        Returns:
            bool: True면 포지션 유지, False면 전환 허용
        """
        try:
            # 🎯 통합 생각카드 정보 활용
            card_recommendation = None
            if self.card_manager and existing_position.get('trade_id'):
                position_id = existing_position.get('trade_id')
                card = self.card_manager.position_cards.get(position_id)

                if card:
                    # 카드의 포지션 관리 히스토리 분석
                    management_history = card.position_management.management_history
                    if management_history:
                        recent_decisions = management_history[-3:]  # 최근 3개 결정
                        hold_count = sum(1 for decision in recent_decisions if decision.get('action') in ['hold', 'monitor_position'])

                        if hold_count >= 2:
                            logger.info(f"🎯 [{symbol}] 통합 생각카드 분석: 최근 {hold_count}회 포지션 유지 결정 → 지속적 유지 권장")
                            card_recommendation = "hold"

                    # 카드의 추세 예측 정보 활용
                    if card.trend_prediction.short_term and card.trend_prediction.confidence > 0.6:
                        trend_direction = card.trend_prediction.short_term
                        existing_direction = existing_position.get('direction', 'unknown')

                        if trend_direction == existing_direction:
                            logger.info(f"🎯 [{symbol}] 통합 생각카드 추세 분석: {trend_direction} 추세와 포지션 방향 일치 → 유지 권장")
                            card_recommendation = "hold"
                        elif card.trend_prediction.confidence < 0.75:
                            logger.info(f"🎯 [{symbol}] 통합 생각카드 추세 분석: 낮은 신뢰도({card.trend_prediction.confidence:.2f}) → 성급한 전환 방지")
                            card_recommendation = "hold"

                # 카드 기반 포지션 관리 업데이트
                if card_recommendation:
                    self.card_manager.update_card(position_id, 'position_management', {
                        'action': 'intelligent_hold_analysis',
                        'reason': f'통합 생각카드 기반 지능형 분석: {card_recommendation}',
                        'confidence': 0.8,
                        'market_condition': {
                            'signal_confidence': strategy.get('confidence', 0.5),
                            'new_direction': strategy.get('type', 'unknown'),
                            'card_recommendation': card_recommendation
                        }
                    })
            # 🔍 1. 신호 강도 분석
            signal_confidence = strategy.get('confidence', 0.5)
            new_direction = strategy.get('type', 'hold')
            existing_direction = existing_position.get('direction', 'unknown')

            # 🔍 2. 포지션 보유 시간 확인
            position_age = time.time() - existing_position.get('entry_timestamp', time.time())
            position_age_minutes = position_age / 60

            # 🔍 3. 현재 수익률 확인
            current_pnl = existing_position.get('current_pnl', 0)

            # 🔍 4. 시장 변동성 확인
            price_change_24h = abs(market_data.get('percent_change_24h', 0))
            volume_24h = market_data.get('volume_24h', 0)

            logger.info(f"🧠 [{symbol}] 지능형 분석: 신호강도={signal_confidence:.2f}, 보유시간={position_age_minutes:.1f}분, PnL={current_pnl:.2f}%, 24h변동={price_change_24h:.2f}%")

            # 🎯 통합 생각카드 기반 지능형 분석 (우선 적용)
            if card_recommendation == "hold":
                logger.info(f"🎯 [{symbol}] 통합 생각카드 권장: 포지션 유지 → 카드 기반 지능형 결정")
                return True

            # 🧠 통합 생각카드 4가지 컴포넌트 종합 분석
            if self.card_manager and existing_position.get('trade_id'):
                position_id = existing_position.get('trade_id')
                card = self.card_manager.position_cards.get(position_id)

                if card:
                    # 4가지 컴포넌트 분석
                    trend_score = self._analyze_trend_prediction(card, existing_position, strategy)
                    sela_score = self._analyze_sela_matching(card, strategy)
                    learning_score = self._analyze_timeframe_learning(card, market_data)
                    management_score = self._analyze_position_management(card)

                    # 가중 평균으로 최종 점수 계산
                    final_score = (
                        trend_score * 0.3 +      # 추세 예측 30%
                        sela_score * 0.25 +      # SELA 매칭 25%
                        learning_score * 0.25 +  # 학습 결과 25%
                        management_score * 0.2   # 관리 히스토리 20%
                    )

                    logger.info(f"🧠 [{symbol}] 통합 생각카드 종합 분석: 추세={trend_score:.2f}, SELA={sela_score:.2f}, 학습={learning_score:.2f}, 관리={management_score:.2f} → 최종={final_score:.2f}")

                    # 카드 기반 포지션 관리 업데이트
                    self.card_manager.update_card(position_id, 'position_management', {
                        'action': 'comprehensive_analysis',
                        'reason': f'통합 생각카드 4가지 컴포넌트 종합 분석: {final_score:.2f}',
                        'confidence': final_score,
                        'market_condition': {
                            'trend_score': trend_score,
                            'sela_score': sela_score,
                            'learning_score': learning_score,
                            'management_score': management_score,
                            'signal_confidence': signal_confidence
                        }
                    })

                    # 🔄 학습 시스템: 잘못된 포지션 유지 결정 감지 및 학습
                    current_pnl = existing_position.get('current_pnl', 0)
                    position_age_minutes = (time.time() - existing_position.get('entry_timestamp', time.time())) / 60

                    # 손실 포지션을 오래 유지하는 경우 학습
                    if current_pnl < -0.5 and position_age_minutes > 10:  # 10분 이상 -0.5% 이상 손실
                        logger.warning(f"🔄 [{symbol}] 학습 감지: 손실 포지션 장기 유지 - PnL: {current_pnl:.2f}%, 보유시간: {position_age_minutes:.1f}분")
                        self._learn_from_poor_decision(symbol, existing_position, "long_hold_loss", current_pnl, position_age_minutes)

                        # 학습 결과 반영: 손실 포지션은 더 적극적으로 청산
                        if current_pnl < -1.0:  # -1% 이상 손실 시 강제 청산
                            logger.info(f"🔄 [{symbol}] 학습 적용: 손실 포지션 강제 청산 - PnL: {current_pnl:.2f}%")
                            return False  # 포지션 전환 허용 (청산)
                        else:
                            # 통합 생각카드 점수를 하향 조정
                            final_score *= 0.7  # 30% 감소
                            logger.info(f"🔄 [{symbol}] 학습 적용: 통합 점수 하향 조정 → {final_score:.2f}")

                    # 임계값 기반 결정
                    if final_score > 0.7:
                        logger.info(f"🎯 [{symbol}] 통합 생각카드 종합 분석: 높은 점수({final_score:.2f}) → 포지션 유지")
                        return True
                    elif final_score < 0.3:
                        logger.info(f"🚀 [{symbol}] 통합 생각카드 종합 분석: 낮은 점수({final_score:.2f}) → 포지션 전환 허용")
                        return False
                    # 0.3 ~ 0.7 사이는 기존 로직으로 판단

            # 🎯 기존 단기 중립 판단 조건들 (카드 분석이 중간 점수일 때)

            # 🚀 SOL 특별 처리: 더 적극적인 포지션 전환 허용
            if symbol == 'SOL':
                # SOL은 더 적극적으로 포지션 전환 허용 (조건 완화)
                if signal_confidence >= 0.7:  # 0.7 이상이면 바로 전환 허용
                    logger.info(f"🚀 [SOL] 특별 처리: 높은 신호({signal_confidence:.2f}) → 포지션 전환 허용")
                    return False
                elif position_age_minutes >= 3:  # 3분 이상 보유 시 전환 허용
                    logger.info(f"🚀 [SOL] 특별 처리: 충분한 보유시간({position_age_minutes:.1f}분) → 포지션 전환 허용")
                    return False

            # 조건 1: 약한 신호 + 짧은 보유 시간 = 단기 중립 가능성 (SOL 제외)
            if symbol != 'SOL' and signal_confidence < 0.6 and position_age_minutes < 5:
                logger.info(f"🎯 [{symbol}] 조건1: 약한 신호({signal_confidence:.2f}) + 짧은 보유시간({position_age_minutes:.1f}분) → 단기 중립 추정")
                return True

            # 조건 2: 중간 신호 + 손실 제한 + 변동성 고려 = 단기 조정 가능성 (SOL은 완화)
            hold_threshold = 0.8 if symbol == 'SOL' else 0.8  # SOL은 더 높은 신호에서만 유지
            if signal_confidence < hold_threshold and current_pnl > -1.0 and price_change_24h < 5:
                logger.info(f"🎯 [{symbol}] 조건2: 중간신호({signal_confidence:.2f}) + 손실제한({current_pnl:.2f}%) + 변동성고려({price_change_24h:.2f}%) → 단기 조정 추정")
                return True

            # 조건 3: hold 신호 = 명확한 중립
            if new_direction.lower() == 'hold':
                logger.info(f"🎯 [{symbol}] 조건3: HOLD 신호 → 명확한 중립")
                return True

            # 조건 4: 매우 짧은 보유 시간 (1분 미만) = 성급한 전환 방지 (단, 강한 신호는 예외)
            if position_age_minutes < 1:
                # 🛠️ 강한 신호 (confidence >= 0.8)일 때는 1분 미만이어도 전환 허용
                if signal_confidence >= 0.8:
                    logger.info(f"🚀 [{symbol}] 조건4 예외: 매우 짧은 보유시간({position_age_minutes:.1f}분)이지만 강한신호({signal_confidence:.2f}) → 전환 허용")
                else:
                    logger.info(f"🎯 [{symbol}] 조건4: 매우 짧은 보유시간({position_age_minutes:.1f}분) + 약한신호({signal_confidence:.2f}) → 성급한 전환 방지")
                    return True

            # 조건 5: 환경변수로 제어되는 추가 조건
            intelligent_hold_threshold = float(os.getenv('INTELLIGENT_HOLD_CONFIDENCE_THRESHOLD', '0.8'))
            min_hold_minutes = float(os.getenv('INTELLIGENT_HOLD_MIN_MINUTES', '3'))

            if signal_confidence < intelligent_hold_threshold and position_age_minutes < min_hold_minutes:
                logger.info(f"🎯 [{symbol}] 조건5: 환경변수 조건 충족 (신호<{intelligent_hold_threshold}, 시간<{min_hold_minutes}분) → 포지션 유지")
                return True

            # 조건 6: 중간 강도 신호 + 매우 짧은 보유시간 = 성급한 전환 방지 (새로 추가)
            if 0.6 <= signal_confidence < 0.8 and position_age_minutes < 2:
                logger.info(f"🎯 [{symbol}] 조건6: 중간강도신호({signal_confidence:.2f}) + 매우짧은보유시간({position_age_minutes:.1f}분) → 성급한 전환 방지")
                return True

            # 모든 조건을 통과하면 전환 허용
            logger.info(f"🚀 [{symbol}] 모든 조건 통과 → 포지션 전환 허용")
            return False

        except Exception as e:
            logger.error(f"[{symbol}] 지능형 포지션 판단 중 오류: {e}")
            # 오류 시 안전하게 포지션 유지
            return True

    def _get_llm_client(self):
        """LLM 클라이언트 가져오기"""
        try:
            # 하이브리드 컨트롤러에서 LLM 클라이언트 가져오기
            if hasattr(self, 'hybrid_controller') and self.hybrid_controller:
                if hasattr(self.hybrid_controller, 'exaone_model'):
                    return self.hybrid_controller.exaone_model

            # 환경변수에서 vLLM 설정 가져오기
            vllm_url = os.getenv('VLLM_URL', 'http://localhost:8001/v1')
            vllm_model = os.getenv('VLLM_MODEL', 'Qwen/Qwen3-14B-AWQ')

            # VLLMClient 생성 (올바른 클래스와 파라미터 사용)
            from simulator.utils.vllm_client import VLLMClient
            llm_client = VLLMClient(
                api_base=vllm_url,  # base_url → api_base
                model_name=vllm_model,  # model → model_name
                temperature=0.1,
                max_tokens=2048
            )

            logger.info(f"✅ LLM 클라이언트 생성 성공: {vllm_model}")
            return llm_client

        except Exception as e:
            logger.warning(f"❌ LLM 클라이언트 생성 실패: {e}")
            return None

    def get_positions_by_symbol(self, symbol: str) -> List[Dict[str, Any]]:
        """심볼별 활성 포지션 조회"""
        try:
            active_positions = []
            for position in self.open_positions:
                if isinstance(position, dict) and position.get('symbol') == symbol and position.get('status') == 'open':
                    active_positions.append(position)
            return active_positions
        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 조회 실패: {e}")
            return []

    def _add_position_maintenance_to_learning(self, symbol: str, strategy: Dict[str, Any], existing_position: Dict[str, Any], reason: str):
        """포지션 유지 결정을 학습 시스템에 피드백"""
        try:
            # 학습 데이터 구성
            learning_data = {
                'symbol': symbol,
                'timestamp': time.time(),
                'action': 'position_maintenance',
                'reason': reason,
                'strategy': {
                    'type': strategy.get('type', 'unknown'),
                    'confidence': strategy.get('confidence', 0.5),
                    'importance': strategy.get('importance', 5.0)
                },
                'position': {
                    'direction': existing_position.get('direction', 'unknown'),
                    'entry_price': existing_position.get('entry_price', 0),
                    'current_price': existing_position.get('current_price', 0),
                    'current_pnl': existing_position.get('current_pnl', 0),
                    'age_minutes': (time.time() - existing_position.get('entry_timestamp', time.time())) / 60
                },
                'market_condition': {
                    'price_change_24h': strategy.get('market_data', {}).get('percent_change_24h', 0),
                    'volume_24h': strategy.get('market_data', {}).get('volume_24h', 0)
                }
            }

            # 통합 생각카드에 학습 데이터 추가
            if self.card_manager and existing_position.get('trade_id'):
                position_id = existing_position.get('trade_id')

                # 시간대별 학습 컴포넌트에 추가
                timeframe_learning_data = {
                    'timeframe': 'short',
                    'result_data': learning_data,
                    'insights': {
                        'maintenance_reason': reason,
                        'decision_quality': 'good' if reason in ['confidence_improved', 'same_direction_hold'] else 'neutral'
                    },
                    'metrics': {
                        'maintenance_count': 1,
                        'avg_confidence': strategy.get('confidence', 0.5)
                    }
                }

                success = self.card_manager.update_card(position_id, 'timeframe_learning', timeframe_learning_data)
                if success:
                    logger.info(f"📚 [{symbol}] 포지션 유지 학습 데이터 추가: {reason}")

                # 포지션 관리 컴포넌트에도 추가
                position_management_data = {
                    'action': 'position_maintenance',
                    'reason': f'포지션 유지 결정: {reason}',
                    'confidence': 0.8,
                    'market_condition': learning_data['market_condition']
                }

                self.card_manager.update_card(position_id, 'position_management', position_management_data)

            logger.debug(f"📚 [{symbol}] 포지션 유지 학습 피드백 완료: {reason}")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 포지션 유지 학습 피드백 실패: {e}")

    def _analyze_trend_prediction(self, card, existing_position: Dict, strategy: Dict) -> float:
        """🔮 추세 예측 컴포넌트 분석"""
        try:
            trend_pred = card.trend_prediction
            existing_direction = existing_position.get('direction', 'unknown')

            # 추세 예측이 없으면 중립 점수
            if not trend_pred.short_term or trend_pred.confidence == 0:
                return 0.5

            score = 0.5  # 기본 점수

            # 단기 추세와 포지션 방향 일치도
            if self._normalize_direction(trend_pred.short_term) == self._normalize_direction(existing_direction):
                score += 0.3 * trend_pred.confidence  # 신뢰도에 비례

            # 중기 추세와 포지션 방향 일치도
            if trend_pred.medium_term and self._normalize_direction(trend_pred.medium_term) == self._normalize_direction(existing_direction):
                score += 0.2

            # 장기 추세와 포지션 방향 일치도
            if trend_pred.long_term and self._normalize_direction(trend_pred.long_term) == self._normalize_direction(existing_direction):
                score += 0.1

            return min(1.0, max(0.0, score))

        except Exception as e:
            logger.error(f"추세 예측 분석 오류: {e}")
            return 0.5

    def _analyze_sela_matching(self, card, strategy: Dict) -> float:
        """📊 SELA 매칭 컴포넌트 분석"""
        try:
            sela_match = card.sela_matching

            # 매칭 히스토리가 없으면 중립 점수
            if not sela_match.accuracy_history:
                return 0.5

            # 최근 5개 매칭 결과 분석
            recent_history = sela_match.accuracy_history[-5:]
            if not recent_history:
                return 0.5

            # 평균 매칭 점수 계산
            avg_match_score = sum(h.get('match_score', 0.5) for h in recent_history) / len(recent_history)

            # 현재 매칭 점수
            current_match_score = sela_match.match_score

            # 가중 평균 (최근 성과 70%, 현재 매칭 30%)
            final_score = avg_match_score * 0.7 + current_match_score * 0.3

            return min(1.0, max(0.0, final_score))

        except Exception as e:
            logger.error(f"SELA 매칭 분석 오류: {e}")
            return 0.5

    def _analyze_timeframe_learning(self, card, market_data: Dict) -> float:
        """⏰ 시간대별 학습 컴포넌트 분석"""
        try:
            learning = card.timeframe_learning
            current_change_24h = market_data.get('percent_change_24h', 0)

            # 단기 결과가 없으면 중립 점수
            if not learning.short_term_results:
                return 0.5

            # 현재 시장 상황과 유사한 과거 결과 찾기
            similar_conditions = []
            for result in learning.short_term_results[-20:]:  # 최근 20개
                past_change = result.get('market_condition', {}).get('change_24h', 0)
                if abs(past_change - current_change_24h) < 3:  # 3% 이내 유사
                    similar_conditions.append(result)

            if not similar_conditions:
                # 유사한 조건이 없으면 전체 평균 사용
                similar_conditions = learning.short_term_results[-10:]  # 최근 10개

            if not similar_conditions:
                return 0.5

            # 성공률 계산 (수익이 있었던 비율)
            success_count = sum(1 for r in similar_conditions if r.get('profit', 0) > 0)
            success_rate = success_count / len(similar_conditions)

            # 평균 신뢰도 계산
            avg_confidence = sum(r.get('confidence', 0.5) for r in similar_conditions) / len(similar_conditions)

            # 최종 점수 (성공률 70%, 신뢰도 30%)
            final_score = success_rate * 0.7 + avg_confidence * 0.3

            return min(1.0, max(0.0, final_score))

        except Exception as e:
            logger.error(f"시간대별 학습 분석 오류: {e}")
            return 0.5

    def _analyze_position_management(self, card) -> float:
        """🎯 포지션 관리 컴포넌트 분석"""
        try:
            management = card.position_management

            # 관리 히스토리가 없으면 중립 점수
            if not management.management_history:
                return 0.5

            # 최근 10개 결정 분석
            recent_decisions = management.management_history[-10:]

            # 포지션 유지 관련 결정들
            hold_actions = ['hold', 'monitor_position', 'intelligent_hold_analysis', 'comprehensive_analysis']
            hold_decisions = [d for d in recent_decisions if d.get('action') in hold_actions]

            if not recent_decisions:
                return 0.5

            # 포지션 유지 비율
            hold_ratio = len(hold_decisions) / len(recent_decisions)

            # 최근 결정들의 평균 신뢰도
            avg_confidence = sum(d.get('decision_confidence', 0.5) for d in recent_decisions) / len(recent_decisions)

            # 최종 점수 (유지 비율 60%, 신뢰도 40%)
            final_score = hold_ratio * 0.6 + avg_confidence * 0.4

            return min(1.0, max(0.0, final_score))

        except Exception as e:
            logger.error(f"포지션 관리 분석 오류: {e}")
            return 0.5

    def _normalize_direction(self, direction: str) -> str:
        """
        방향 문자열 정규화 (개선된 버전)

        Args:
            direction: 원본 방향 문자열

        Returns:
            str: 정규화된 방향 ('long', 'short', 'neutral')
        """
        if not direction:
            return 'neutral'

        direction = direction.lower().strip()

        # 롱 포지션 (가격 상승 예상)
        if direction in ['buy', 'long', 'bullish', 'bull', 'strong_buy']:
            return 'long'
        # 숏 포지션 (가격 하락 예상)
        elif direction in ['sell', 'short', 'bearish', 'bear', 'strong_sell']:
            return 'short'
        # 중립 (관망)
        elif direction in ['hold', 'none', 'neutral', 'wait']:
            return 'neutral'
        else:
            # 알 수 없는 방향은 중립으로 처리
            logger.warning(f"🚨 알 수 없는 방향 '{direction}' → 'neutral'로 정규화")
            return 'neutral'

    def switch_position(self, symbol: str, new_direction: str, market_data: Dict[str, Any], binance_utils=None) -> Dict[str, Any]:
        """
        기존 포지션을 반대 방향으로 전환

        Args:
            symbol: 심볼 (예: SOL)
            new_direction: 새 방향 ('long' 또는 'short')
            market_data: 시장 데이터
            binance_utils: 바이낸스 유틸리티

        Returns:
            Dict: 포지션 전환 결과
        """
        logger.info(f"{symbol} 포지션 방향 전환 시작: 새 방향={new_direction}")

        # 🔧 LLM 기반 포지션 관리 사용 - 지능형 포지션 유지 로직 비활성화
        # 사용자 요청에 따라 LLM이 시장 분석을 통해 포지션을 관리하도록 함
        logger.info(f"🤖 [{symbol}] LLM 기반 포지션 관리 활성화 - 포지션 전환 허용: {new_direction}")

        # 실제 거래 모드인 경우 포지션 정보 동기화
        if self.mode == 'real' and binance_utils:
            # 🔥 모든 심볼 포지션 정보 동기화 (특정 심볼만 필터링하지 않음)
            self.sync_positions_with_exchange(binance_utils, symbols=None)
            logger.info(f"모든 포지션 정보 동기화 완료 (요청 심볼: {symbol})")

        # 기존 포지션 찾기
        existing_position = None
        for pos in self.open_positions:
            if pos['symbol'] == symbol:
                existing_position = pos
                break

        if not existing_position:
            logger.warning(f"{symbol}에 대한 기존 포지션이 없습니다.")
            return {"success": False, "error": "기존 포지션 없음"}

        # 방향이 이미 같으면 건너뜀 (정규화된 방향으로 비교)
        existing_direction_normalized = self._normalize_direction(existing_position['direction'])
        new_direction_normalized = self._normalize_direction(new_direction)

        if existing_direction_normalized == new_direction_normalized:
            logger.info(f"{symbol} 포지션이 이미 {new_direction_normalized} 방향입니다. (기존: {existing_position['direction']} → {existing_direction_normalized}, 새: {new_direction} → {new_direction_normalized})")
            return {"success": True, "message": "방향이 이미 같음"}

        # 현재 가격 확인
        current_price = market_data.get('price', 0)
        if current_price <= 0:
            return {"success": False, "error": "유효하지 않은 가격"}

        # 기존 포지션 크기
        existing_quantity = existing_position.get('quantity', existing_position.get('size', 0))

        # 포지션 전환 전 기존 포지션 정리
        logger.info(f"{symbol} 포지션 전환을 위해 기존 포지션 정리 시작")
        close_result = self.close_position(symbol, existing_position['direction'], binance_utils, reason="position_switch")

        if not close_result.get('success', False):
            logger.error(f"{symbol} 포지션 정리 실패: {close_result.get('error', '알 수 없는 오류')}")
            return {"success": False, "error": f"기존 포지션 정리 실패: {close_result.get('error', '알 수 없는 오류')}"}

        logger.info(f"{symbol} 기존 포지션 정리 완료, 새 포지션 생성 준비")

        # 포지션 전환 준비

        try:
            if self.mode == 'real' and binance_utils:
                # 기존 포지션은 이미 close_position 메서드에서 청산되었음
                logger.info(f"{symbol} 기존 포지션 청산 확인 완료")

                # 새 방향으로 포지션 열기 (buy/long → BUY, sell/short → SELL)
                new_side = "BUY" if new_direction in ["long", "buy"] else "SELL"
                logger.info(f"{symbol} 새 {new_direction} 포지션 생성 중...")

                # 잠시 대기 (API 응답 지연 고려)
                time.sleep(1)

                new_order_result = binance_utils.execute_futures_market_order(
                    symbol=symbol,
                    side=new_side,
                    quantity=existing_quantity
                )

                logger.info(f"{symbol} 새 포지션 생성 결과: {new_order_result}")

                # 🔥 모든 심볼 포지션 정보 업데이트
                self.sync_positions_with_exchange(binance_utils, symbols=None)

                # 새 포지션이 생성되었는지 확인
                new_position_created = False
                for pos in self.open_positions:
                    if pos['symbol'] == symbol and pos['direction'] == new_direction:
                        new_position_created = True
                        logger.info(f"{symbol} 새 {new_direction} 포지션 생성 확인: 수량={pos.get('quantity', 0)}")
                        break

                if not new_position_created:
                    logger.warning(f"{symbol} 새 포지션이 생성되지 않았습니다. 강화된 동기화 시도...")

                    # 🔧 여러 번 동기화 시도
                    for retry in range(3):
                        logger.info(f"{symbol} 포지션 동기화 재시도 {retry + 1}/3")
                        time.sleep(1)
                        sync_result = self.sync_positions_with_exchange(binance_utils, symbols=[symbol], force=True)

                        if sync_result.get('success', False):
                            logger.info(f"{symbol} 동기화 성공: {sync_result.get('changes_made', [])}")

                        # 다시 확인
                        for pos in self.open_positions:
                            if pos['symbol'] == symbol and pos['direction'] == new_direction:
                                new_position_created = True
                                logger.info(f"✅ {symbol} 새 {new_direction} 포지션 생성 확인 (재시도 {retry + 1} 후): 수량={pos.get('quantity', 0)}")
                                break

                        if new_position_created:
                            break

                    # 🔧 여전히 실패하면 수동으로 포지션 생성
                    if not new_position_created:
                        logger.error(f"❌ {symbol} 포지션 동기화 완전 실패. 수동 포지션 생성 시도...")
                        try:
                            # 바이낸스에서 실제 포지션 정보 조회
                            positions = binance_utils.get_futures_positions()
                            if isinstance(positions, list):
                                for pos in positions:
                                    if pos.get('symbol') == f"{symbol}USDT" and abs(float(pos.get('positionAmt', 0))) > 0:
                                        # 수동으로 포지션 추가
                                        manual_position = {
                                            'id': f"{symbol}_{int(time.time())}",
                                            'symbol': symbol,
                                            'direction': new_direction,
                                            'quantity': abs(float(pos.get('positionAmt', 0))),
                                            'entry_price': float(pos.get('entryPrice', 0)),
                                            'timestamp': int(time.time()),
                                            'status': 'open',
                                            'source': 'manual_sync'
                                        }
                                        self.open_positions.append(manual_position)
                                        logger.info(f"✅ {symbol} 수동 포지션 생성 완료: {manual_position}")
                                        new_position_created = True
                                        break
                        except Exception as e:
                            logger.error(f"❌ {symbol} 수동 포지션 생성 실패: {e}")

                return {
                    "success": True,
                    "message": f"{symbol} 포지션을 {existing_position['direction']}에서 {new_direction}으로 전환했습니다.",
                    "new_order": new_order_result
                }
            else:
                # 시뮬레이션 모드에서는 포지션 정보만 업데이트
                logger.info(f"[시뮬레이션] {symbol} 포지션 방향 전환: {existing_position['direction']} -> {new_direction}")

                # 기존 포지션 제거
                self.open_positions = [p for p in self.open_positions if not (p['symbol'] == symbol)]

                # 새 포지션 추가
                new_position = existing_position.copy()
                new_position['direction'] = new_direction
                new_position['entry_price'] = current_price
                new_position['entry_timestamp'] = int(time.time())
                new_position['current_pnl'] = 0.0
                self.open_positions.append(new_position)

                # 포트폴리오 저장
                self.save_portfolio()

                return {
                    "success": True,
                    "message": f"[시뮬레이션] {symbol} 포지션을 {existing_position['direction']}에서 {new_direction}으로 전환했습니다."
                }

        except Exception as e:
            logger.error(f"{symbol} 포지션 전환 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"success": False, "error": str(e)}

    def close_position(self, symbol: str, direction: str = None, binance_utils=None, reason: str = "manual_close") -> Dict[str, Any]:
        """
        특정 심볼의 포지션 정리

        Args:
            symbol: 정리할 포지션의 심볼
            direction: 정리할 포지션 방향 (long/short), None이면 모든 방향
            binance_utils: 바이낸스 유틸리티 객체 (실제 거래 시 필요)
            reason: 포지션 클로즈 이유

        Returns:
            Dict: 정리 결과
        """
        logger.info(f"🔄 {symbol} 포지션 정리 시작 (방향: {direction or '모든 방향'}, 이유: {reason})")

        # 🚀 같은 방향 신호로 인한 불필요한 포지션 청산 방지
        if reason and "llm_strategy_signal_change" in reason:
            # 기존 포지션 확인
            existing_position = None
            for pos in self.open_positions:
                if pos['symbol'] == symbol:
                    existing_position = pos
                    break

            if existing_position:
                # reason에서 새로운 방향 추출 시도
                new_direction = None
                if "long" in reason.lower():
                    new_direction = "long"
                elif "short" in reason.lower():
                    new_direction = "short"
                elif "sell" in reason.lower():
                    new_direction = "short"
                elif "buy" in reason.lower():
                    new_direction = "long"

                existing_direction = existing_position.get('direction')

                # 🔧 방향 정규화 후 비교 (buy/sell → long/short 변환)
                existing_direction_normalized = self._normalize_direction(existing_direction) if existing_direction else None
                new_direction_normalized = self._normalize_direction(new_direction) if new_direction else None

                # 같은 방향 신호인 경우 포지션 청산 방지
                if new_direction_normalized and existing_direction_normalized == new_direction_normalized:
                    logger.info(f"🎯 [{symbol}] 같은 방향 신호로 인한 포지션 청산 방지: {existing_direction}({existing_direction_normalized}) → {new_direction}({new_direction_normalized}) (동일)")
                    return {
                        "success": False,
                        "reason": "same_direction_signal_ignored",
                        "message": f"같은 방향 신호로 인한 불필요한 포지션 청산 방지 ({existing_direction_normalized})"
                    }
                else:
                    logger.info(f"🔄 [{symbol}] 방향 변경 신호로 포지션 청산 진행: {existing_direction}({existing_direction_normalized}) → {new_direction}({new_direction_normalized})")
            else:
                logger.info(f"📍 [{symbol}] 기존 포지션 없음, 청산 진행")

        # 실제 거래 모드인 경우 포지션 정보 동기화
        if self.mode == 'real' and binance_utils:
            # 🔥 모든 심볼 포지션 정보 동기화 (특정 심볼만 필터링하지 않음)
            self.sync_positions_with_exchange(binance_utils, symbols=None)
            logger.info(f"모든 포지션 정보 동기화 완료 (요청 심볼: {symbol})")

        # 정리할 포지션 찾기
        positions_to_close = []
        for position in self.open_positions:
            if position['symbol'] == symbol and (direction is None or position['direction'] == direction):
                positions_to_close.append(position)

        if not positions_to_close:
            logger.warning(f"{symbol} {direction or '모든 방향'} 포지션이 없습니다.")
            return {"success": False, "message": f"No open position for {symbol} {direction or 'any direction'}"}

        results = []
        for position in positions_to_close:
            try:
                # 현재 가격 정보 가져오기
                current_price = position.get('current_price', 0)

                # 실제 거래 모드인 경우
                if self.mode == 'real' and binance_utils:
                    try:
                        # 포지션 방향에 따라 반대 방향으로 주문
                        close_side = "SELL" if position['direction'] == "long" else "BUY"

                        # 'quantity' 키가 없는 경우 'size' 키에서 복사
                        if 'quantity' not in position:
                            if 'size' in position:
                                logger.warning(f"{symbol} 포지션에 'quantity' 키가 없지만 'size' 키가 있습니다. 'size' 값을 'quantity'로 복사합니다.")
                                position['quantity'] = position['size']
                            else:
                                error_msg = f"{symbol} 포지션에 'quantity' 키와 'size' 키가 모두 없습니다. 포지션 정보: {str(position)}"
                                logger.error(error_msg)
                                raise KeyError(error_msg)

                        logger.info(f"{symbol} 포지션 정리 주문: {close_side} {position['quantity']}")

                        # 거래소 API를 통해 주문 실행
                        order_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=position['quantity']
                        )

                        logger.info(f"{symbol} 포지션 정리 완료")

                        # 🔥 포지션 정리 후 모든 심볼 다시 동기화
                        self.sync_positions_with_exchange(binance_utils, symbols=None)
                        logger.info(f"포지션 정리 후 모든 심볼 동기화 완료 (정리된 심볼: {symbol})")

                    except Exception as e:
                        logger.error(f"{symbol} 포지션 정리 중 오류 발생: {e}")
                        results.append({
                            "symbol": symbol,
                            "direction": position['direction'],
                            "success": False,
                            "error": str(e)
                        })
                        continue

                # 포지션 정리 처리
                timestamp = int(time.time())

                # 🔥 현재가 조회 개선 (바이낸스 API 직접 사용)
                if current_price <= 0 and binance_utils:
                    try:
                        # 바이낸스 API에서 현재가 직접 조회 (get_current_price만 사용, 폴백 제거)
                        api_price = binance_utils.get_current_price(symbol)
                        if api_price and api_price > 0:
                            current_price = api_price
                            logger.info(f"🔥 [{symbol}] 포지션 청산용 현재가 조회 성공: ${current_price:.6f}")
                        else:
                            logger.error(f"❌ [{symbol}] get_current_price 실패")
                    except Exception as e:
                        logger.warning(f"⚠️ [{symbol}] 현재가 조회 실패: {e}")

                # 청산 가격 결정 (현재가 우선, 실패 시 진입가)
                exit_price = current_price if current_price > 0 else position['entry_price']

                if current_price <= 0:
                    logger.warning(f"⚠️ [{symbol}] 현재가 조회 실패로 진입가 사용: ${exit_price:.6f} (실제 손익 반영 안됨)")
                else:
                    logger.info(f"✅ [{symbol}] 실제 현재가로 청산: ${exit_price:.6f}")

                # 실현 손익 계산
                if position['direction'] == "long":
                    realized_pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
                else:  # short
                    realized_pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100

                # value_usd 필드가 없는 경우 계산
                if 'value_usd' in position:
                    realized_pnl = position['value_usd'] * (realized_pnl_pct / 100)
                else:
                    # 바이낸스 API 동기화된 포지션의 경우 계산
                    position_value = position['quantity'] * position['entry_price']
                    realized_pnl = position_value * (realized_pnl_pct / 100)
                    logger.info(f"[{symbol}] value_usd 필드 없음, 계산된 포지션 가치: ${position_value:.2f}")

                # 자산 및 잔액 업데이트
                value_at_exit = position['quantity'] * exit_price

                if position['direction'] == "long":
                    # 롱 포지션 청산: 자산 감소, 잔액 증가
                    self.assets[symbol] = self.assets.get(symbol, 0) - position['quantity']
                    self.balance += value_at_exit
                else:
                    # 숏 포지션 청산: 공매도 상환
                    self.assets[symbol] = self.assets.get(symbol, 0) + position['quantity']
                    # value_usd 필드가 없는 경우 계산
                    if 'value_usd' in position:
                        self.balance += (position['value_usd'] + realized_pnl)
                    else:
                        position_value = position['quantity'] * position['entry_price']
                        self.balance += (position_value + realized_pnl)

                # 거래 결과 생성
                trade_result = {
                    "trade_id": position.get('trade_id', f"close_{symbol}_{timestamp}"),
                    "symbol": symbol,
                    "close_timestamp": timestamp,
                    "close_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "direction": position['direction'],
                    "entry_price": position['entry_price'],
                    "exit_price": exit_price,
                    "exit_type": "manual_close",
                    "quantity": position['quantity'],
                    "value_at_entry": position.get('value_usd', position['quantity'] * position['entry_price']),
                    "value_at_exit": value_at_exit,
                    "realized_pnl": realized_pnl,
                    "realized_pnl_pct": realized_pnl_pct,
                    "status": "closed",
                    "success": True
                }

                # 거래 내역 업데이트
                for i, trade in enumerate(self.trade_history):
                    if trade.get('trade_id') == position.get('trade_id'):
                        trade.update({
                            "exit_price": exit_price,
                            "exit_timestamp": timestamp,
                            "realized_pnl": realized_pnl,
                            "realized_pnl_pct": realized_pnl_pct,
                            "status": "closed"
                        })
                        break

                # 포트폴리오 성과 업데이트
                if realized_pnl > 0:
                    self.performance["winning_trades"] += 1
                else:
                    self.performance["losing_trades"] += 1

                self.performance["total_profit_loss"] += realized_pnl
                self.performance["total_profit_loss_pct"] = (
                    self.performance["total_profit_loss"] /
                    self.performance["initial_balance"] * 100
                )

                # 데이터베이스에 거래 종료 내역 저장
                try:
                    from trading.data.database import TradingDatabase
                    db_path = os.path.join(self.data_dir, 'trading.db')
                    db = TradingDatabase(db_path)

                    # 거래 데이터 구성 (종료 정보)
                    current_timestamp = int(time.time())  # 현재 시간
                    trade_data = {
                        'symbol': symbol,
                        'action': 'sell' if position["direction"] == 'long' else 'buy',  # 반대 방향으로 종료
                        'quantity': position["quantity"],
                        'price': exit_price,
                        'timestamp': current_timestamp,  # 현재 시간으로 타임스탬프 설정
                        'entry_price': position["entry_price"],
                        'exit_price': exit_price,
                        'pnl_percent': realized_pnl_pct,
                        'reason': 'manual_close',
                        'trade_id': position.get("trade_id", f"close_{symbol}_{current_timestamp}")
                    }

                    # 데이터베이스에 저장
                    db.save_trade(trade_data)
                    logger.info(f"{symbol} 포지션 종료 내역 데이터베이스에 저장 완료")
                except Exception as e:
                    logger.error(f"{symbol} 포지션 종료 내역 데이터베이스 저장 실패: {e}")

                results.append(trade_result)
                logger.info(f"✅ {symbol} {position['direction']} 포지션 정리 완료 (이유: {reason}): PnL {realized_pnl_pct:.2f}%, ${realized_pnl:.2f}")

                # 🔧 포지션 청산 쿨다운 기록 (연속 같은 방향 거래 방지)
                self.position_close_history[symbol] = {
                    'close_time': timestamp,
                    'direction': position['direction'],
                    'reason': reason
                }
                logger.info(f"🔧 [{symbol}] 포지션 청산 쿨다운 기록: {position['direction']} 방향, {self.position_cooldown_seconds}초 쿨다운")

                # 🎯 InCA 실제 거래 결과 학습 피드백 (포지션 청산 시)
                try:
                    from trading.hybrid_architecture.agents.inca_agent import InCAAgent

                    # 실제 거래 결과 피드백 데이터 구성
                    inca_feedback = {
                        'symbol': symbol,
                        'prediction': {
                            'action_recommendation': position.get('action', position.get('direction', 'hold')),
                            'trading_direction': position.get('direction', 'long'),
                            'confidence': position.get('confidence', 0.5),
                            'entry_price': position.get('entry_price', 0),
                            'strategy_type': 'actual_trade_completion',
                            'close_reason': reason
                        },
                        'trade_result': {
                            'profit': realized_pnl,  # 🔥 실제 달러 손익 전달
                            'profit_percent': realized_pnl_pct,  # 🔥 실제 퍼센트 손익 전달
                            'exit_price': exit_price,
                            'success': realized_pnl > 0,  # 🔥 실제 성공/실패 여부
                            'status': 'trade_completed',
                            'evaluation_details': {
                                'close_reason': reason,
                                'hold_time_seconds': timestamp - position.get('entry_timestamp', timestamp),
                                'entry_price': position.get('entry_price', 0),
                                'exit_price': exit_price,
                                'realized_pnl_usd': realized_pnl,
                                'realized_pnl_pct': realized_pnl_pct
                            }
                        },
                        'market_data': {
                            'symbol': symbol,
                            'timestamp': timestamp,
                            'actual_trade_completion': True
                        }
                    }

                    # InCA 인스턴스 생성 및 피드백 전달
                    inca_agent = InCAAgent()
                    inca_result = inca_agent.learn_from_feedback(inca_feedback)

                    logger.info(f"🎯 [{symbol}] InCA 실제 거래 결과 학습 완료: 수익 ${realized_pnl:.2f} ({realized_pnl_pct:.2f}%), 성공: {inca_result.get('success', False)}")

                except Exception as inca_e:
                    logger.warning(f"⚠️ [{symbol}] InCA 실제 거래 결과 학습 실패: {inca_e}")

                # 🎯 통합 생각카드 완료 처리 (포지션 종료 시)
                if self.card_manager:
                    try:
                        position_id = position.get('trade_id')
                        if position_id:
                            final_result = {
                                'exit_price': exit_price,
                                'exit_timestamp': timestamp,
                                'realized_pnl': realized_pnl,
                                'realized_pnl_pct': realized_pnl_pct,
                                'close_reason': reason,
                                'success': realized_pnl > 0,
                                'hold_time_seconds': timestamp - position.get('entry_timestamp', timestamp),
                                'final_market_data': {
                                    'price': exit_price,
                                    'timestamp': timestamp
                                }
                            }

                            # 카드 완료 처리
                            success = self.card_manager.close_position_card(position_id, final_result)
                            if success:
                                logger.info(f"🎯 [{symbol}] 통합 생각카드 완료 처리 성공: {position_id}")
                            else:
                                logger.warning(f"⚠️ [{symbol}] 통합 생각카드 완료 처리 실패: {position_id}")
                        else:
                            logger.warning(f"⚠️ [{symbol}] 포지션에 trade_id가 없어 카드 완료 처리 불가")

                    except Exception as card_e:
                        logger.error(f"❌ [{symbol}] 통합 생각카드 완료 처리 실패: {card_e}")
                else:
                    logger.debug(f"🎯 [{symbol}] 통합 생각카드 매니저가 비활성화되어 있습니다")

            except Exception as e:
                logger.error(f"{symbol} 포지션 정리 중 오류 발생: {e}")
                results.append({
                    "symbol": symbol,
                    "direction": position.get('direction', 'unknown'),
                    "success": False,
                    "error": str(e)
                })

        # 정리된 포지션 제거
        self.open_positions = [p for p in self.open_positions if not (p['symbol'] == symbol and (direction is None or p['direction'] == direction))]

        # 트레일링 스톱 최대 PnL 초기화
        if hasattr(self, 'position_max_pnl'):
            max_pnl_key = f"{symbol}_max_pnl"
            if max_pnl_key in self.position_max_pnl:
                del self.position_max_pnl[max_pnl_key]
                logger.info(f"[{symbol}] 트레일링 스톱 최대 PnL 초기화")

        # 포트폴리오 저장
        self.save_portfolio()

        # 포트폴리오 상태 로그 (시장 데이터가 없으므로 빈 딕셔너리 전달)
        # binance_utils가 None일 수 있으므로 조건부 전달
        if binance_utils is not None:
            self.log_portfolio_status({}, binance_utils)
        else:
            # binance_utils가 없는 경우 포트폴리오 상태 출력 건너뜀
            logger.debug("binance_utils가 없어 포트폴리오 상태 출력을 건너뜁니다.")

        return {
            "success": True,
            "closed_positions": len(results),
            "results": results
        }

    def log_portfolio_status(self, market_data_dict=None, binance_utils=None):
        """
        포트폴리오 상태 로깅

        Args:
            market_data_dict: 심볼별 시장 데이터 (심볼 -> 시장 데이터)
            binance_utils: 바이낸스 유틸리티 객체 (현재가 조회용)
        """
        try:
            # 포트폴리오 총 가치 계산 (calculate_portfolio_value 메서드 사용)
            if market_data_dict and len(market_data_dict) > 0:
                portfolio_value = self.calculate_portfolio_value(market_data_dict)
            else:
                # 시장 데이터가 없는 경우 잔액만 사용
                portfolio_value = self.balance
                # 시장 데이터가 없을 때만 경고 출력 (빈 딕셔너리는 정상적인 상황)
                if market_data_dict is None:
                    logger.warning("시장 데이터가 제공되지 않아 포트폴리오 총 가치가 정확하지 않을 수 있습니다.")
                else:
                    logger.debug("시장 데이터가 비어있어 잔액만으로 포트폴리오 가치를 계산합니다.")

            # 시장 데이터가 제공된 경우 보유 자산 가치 계산 (디버그 로깅)
            if market_data_dict and self.assets:
                logger.debug(f"시장 데이터: {str(market_data_dict)}")
                for symbol, quantity in self.assets.items():
                    if quantity > 0:
                        market_data = market_data_dict.get(symbol)
                        if market_data and 'price' in market_data:
                            asset_value = quantity * market_data['price']
                            logger.debug(f"자산 가치 계산: {symbol} - 수량: {quantity}, 가격: {market_data['price']}, 가치: {asset_value}")

            logger.info("===== 포트폴리오 상태 =====")
            logger.info(f"거래 모드: {'테스트넷' if self.testnet else '실제 거래' if self.mode == 'real' else '시뮬레이션'}")
            logger.info(f"USDT 잔액: ${self.balance:,.2f}")

            # 현물 자산 가치 계산
            total_asset_value = 0
            if self.assets and binance_utils:
                for symbol, quantity in self.assets.items():
                    if quantity > 0:
                        try:
                            ticker_data = binance_utils.get_ticker(f"{symbol}USDT")
                            if ticker_data and not ticker_data.get('error') and 'lastPrice' in ticker_data:
                                price = float(ticker_data['lastPrice'])
                                value = quantity * price
                                total_asset_value += value
                        except Exception:
                            pass  # 가격 조회 실패 시 무시

            # 실제 포트폴리오 총 가치 (USDT + 현물 자산)
            real_portfolio_value = self.balance + total_asset_value
            logger.info(f"포트폴리오 총 가치: ${real_portfolio_value:,.2f} (USDT: ${self.balance:,.2f} + 현물: ${total_asset_value:,.2f})")

            # 손익 정보 (실제 가치 기준)
            initial_balance = self.performance.get("initial_balance", 10000.0)
            total_pnl = real_portfolio_value - initial_balance
            total_pnl_pct = (total_pnl / initial_balance) * 100 if initial_balance > 0 else 0
            logger.info(f"초기 잔액: ${initial_balance:,.2f}")
            logger.info(f"총 손익: ${total_pnl:,.2f} ({total_pnl_pct:.2f}%)")

            # 현물 자산 (Assets)
            if self.assets:
                logger.info("=== 현물 자산 (Assets) ===")
                total_asset_value = 0
                for symbol, quantity in self.assets.items():
                    if quantity > 0:
                        # 시장 데이터가 있으면 USD 가치도 표시
                        if market_data_dict and symbol in market_data_dict and 'price' in market_data_dict[symbol]:
                            price = market_data_dict[symbol]['price']
                            value = quantity * price
                            total_asset_value += value
                            logger.info(f"  {symbol}: {quantity:,.8f} 개 × ${price:.4f} = ${value:,.2f}")
                        elif binance_utils:
                            # 바이낸스 API에서 현재가 조회
                            try:
                                ticker_data = binance_utils.get_ticker(f"{symbol}USDT")
                                if ticker_data and not ticker_data.get('error') and 'lastPrice' in ticker_data:
                                    price = float(ticker_data['lastPrice'])
                                    value = quantity * price
                                    total_asset_value += value
                                    logger.info(f"  {symbol}: {quantity:,.8f} 개 × ${price:.4f} = ${value:,.2f}")
                                else:
                                    error_info = ticker_data.get('error', 'lastPrice 필드 없음') if ticker_data else '응답 없음'
                                    logger.info(f"  {symbol}: {quantity:,.8f} 개 (가격 조회 실패: {error_info})")
                            except Exception as e:
                                logger.info(f"  {symbol}: {quantity:,.8f} 개 (가격 조회 오류: {e})")
                        else:
                            logger.info(f"  {symbol}: {quantity:,.8f} 개")

                if total_asset_value > 0:
                    logger.info(f"현물 자산 총 가치: ${total_asset_value:,.2f}")
            else:
                logger.info("=== 현물 자산 (Assets) ===")
                logger.info("현물 자산: 없음")

            # 선물 거래 상태 (포지션 + 주문)
            if binance_utils and self.mode == 'real':
                logger.info("=== 선물 거래 상태 ===")

                # 미체결 주문 확인
                try:
                    open_orders = binance_utils.get_futures_open_orders()
                    if open_orders and len(open_orders) > 0 and not ('error' in open_orders[0]):
                        logger.info(f"미체결 주문: {len(open_orders)}개")
                        for order in open_orders[:3]:  # 최대 3개만 표시
                            symbol = order.get('symbol', 'N/A')
                            side = order.get('side', 'N/A')
                            price = order.get('price', 'N/A')
                            status = order.get('status', 'N/A')
                            logger.info(f"  - {symbol} {side} ${price} ({status})")
                    else:
                        logger.info("미체결 주문: 없음")
                except Exception as e:
                    logger.warning(f"미체결 주문 조회 실패: {e}")

                # 최근 주문 내역 확인 (SOL, DOGE만)
                for symbol in ['SOL', 'DOGE']:
                    try:
                        recent_orders = binance_utils.get_futures_all_orders(symbol=f"{symbol}USDT", limit=3)
                        if recent_orders and len(recent_orders) > 0 and not ('error' in recent_orders[0]):
                            logger.info(f"[{symbol}] 최근 주문 내역:")
                            for order in recent_orders[-3:]:  # 최근 3개
                                side = order.get('side', 'N/A')
                                price = order.get('price', 'N/A')
                                status = order.get('status', 'N/A')
                                time_str = order.get('time', 'N/A')
                                logger.info(f"  - {side} ${price} ({status}) {time_str}")
                        else:
                            logger.info(f"[{symbol}] 최근 주문 내역: 없음")
                    except Exception as e:
                        logger.debug(f"[{symbol}] 주문 내역 조회 실패: {e}")

            # 오픈 포지션
            if self.open_positions:
                logger.info("=== 내부 포지션 ===")
                for pos in self.open_positions:
                    try:
                        # 'quantity' 키가 없는 경우 'size' 키에서 복사
                        if 'quantity' not in pos:
                            if 'size' in pos:
                                logger.warning(f"{pos['symbol']} 포지션에 'quantity' 키가 없지만 'size' 키가 있습니다. 'size' 값을 'quantity'로 복사합니다.")
                                pos['quantity'] = pos['size']
                            else:
                                logger.warning(f"{pos['symbol']} 포지션에 'quantity' 키와 'size' 키가 모두 없습니다. 포지션 정보: {str(pos)}")
                                pos['quantity'] = 0.0  # 기본값 설정

                        # 'current_pnl' 키가 없는 경우 기본값 설정
                        if 'current_pnl' not in pos:
                            logger.warning(f"{pos['symbol']} 포지션에 'current_pnl' 키가 없습니다. 포지션 정보: {str(pos)}")
                            pos['current_pnl'] = 0.0  # 기본값 설정

                        # 현재 시장 가격 가져오기 (강화된 로직)
                        current_price = 0
                        if market_data_dict and pos['symbol'] in market_data_dict:
                            current_price = market_data_dict[pos['symbol']].get('price', 0)

                        # 현재가가 0이면 바이낸스 API에서 직접 가져오기 (강화된 로직)
                        if current_price <= 0:
                            # 디버깅: 바이낸스 유틸리티 상태 확인
                            logger.info(f"🔍 [{pos['symbol']}] log_portfolio_status 현재가 조회 디버깅:")
                            logger.info(f"   - 전달받은 binance_utils: {binance_utils is not None}")
                            logger.info(f"   - self.binance_utils: {self.binance_utils is not None}")

                            # self.binance_utils 우선 사용
                            actual_binance_utils = self.binance_utils or binance_utils

                            if actual_binance_utils:
                                logger.info(f"   - 사용할 바이낸스 유틸리티 타입: {type(actual_binance_utils)}")
                                try:
                                    # 바이낸스 API에서 현재가 직접 조회 (여러 방법 시도)
                                    symbol_formatted = f"{pos['symbol']}USDT"
                                    logger.info(f"[{pos['symbol']}] 바이낸스 API에서 현재가 조회 시도: {symbol_formatted}")

                                    # get_ticker 사용 (폴백 제거)
                                    ticker_data = actual_binance_utils.get_ticker(symbol_formatted)
                                    if ticker_data and not ticker_data.get('error') and 'lastPrice' in ticker_data:
                                        current_price = float(ticker_data['lastPrice'])
                                        # 🔥 포지션 객체에 현재가 업데이트
                                        pos['current_price'] = current_price
                                        pos['last_updated'] = int(time.time())
                                        logger.info(f"✅ [{pos['symbol']}] get_ticker로 현재가 조회 성공: ${current_price:.6f}")
                                    else:
                                        error_info = ticker_data.get('error', 'lastPrice 필드 없음') if ticker_data else '응답 없음'
                                        logger.error(f"❌ [{pos['symbol']}] get_ticker 실패: {error_info}")

                                except Exception as e:
                                    logger.error(f"❌ [{pos['symbol']}] 현재가 조회 중 오류: {e}")
                                    import traceback
                                    logger.error(f"상세 오류: {traceback.format_exc()}")
                            else:
                                logger.warning(f"⚠️ [{pos['symbol']}] 바이낸스 유틸리티가 없어 현재가 조회 불가능")

                        # 🚨 현재가 조회 실패 시 경고 (진입가 대체 제거)
                        if current_price <= 0:
                            logger.error(f"❌ [{pos['symbol']}] 현재가 조회 완전 실패")
                            pos['price_fetch_failed'] = True  # 실패 플래그 설정
                            logger.error(f"❌ [{pos['symbol']}] 현재가 조회 실패로 인해 ROI 계산 불가능")
                            # 진입가로 대체하지 않음 - current_price는 0으로 유지

                        # 바이낸스 API 데이터 사용 (검증 포함)
                        entry_price = pos.get('entry_price', 0)
                        direction = pos.get('direction', 'long')

                        # 1. 바이낸스 API percentage 필드 우선 사용
                        if 'percentage' in pos and pos['percentage'] is not None and pos['percentage'] != '':
                            api_percentage = float(pos['percentage'])

                            # 바이낸스 percentage 필드 검증 (실제 계산과 비교)
                            if current_price > 0 and entry_price > 0:
                                # 실제 ROI 계산으로 검증
                                if direction == 'long':
                                    calculated_roi = ((current_price - entry_price) / entry_price) * 100
                                else:  # short
                                    calculated_roi = ((entry_price - current_price) / entry_price) * 100

                                # API 값과 계산 값 비교 (5배 이상 차이나면 계산값 사용)
                                if abs(calculated_roi) > 0.01 and abs(api_percentage - calculated_roi) / abs(calculated_roi) > 5:
                                    logger.warning(f"[{pos['symbol']}] 바이낸스 API percentage 오류: API={api_percentage:.4f}%, 계산={calculated_roi:.4f}% - 계산값 사용")
                                    roi_pct = calculated_roi
                                else:
                                    roi_pct = api_percentage
                                    logger.debug(f"[{pos['symbol']}] 바이낸스 API percentage 사용: {api_percentage:.4f}%")
                            else:
                                roi_pct = api_percentage
                        else:
                            # percentage 필드가 없으면 직접 계산
                            if current_price > 0 and entry_price > 0:
                                if direction == 'long':
                                    roi_pct = ((current_price - entry_price) / entry_price) * 100
                                else:  # short
                                    roi_pct = ((entry_price - current_price) / entry_price) * 100

                                # 🔥 계산된 PnL을 포지션에 저장 (내부 전용 포지션용)
                                pos['current_pnl_pct'] = roi_pct
                                logger.debug(f"🔥 [{pos['symbol']}] PnL 직접 계산 및 저장: {roi_pct:.2f}%")
                            else:
                                # 현재가 조회 실패 시 ROI 계산 불가능
                                roi_pct = None  # 0.0 대신 None으로 설정
                                pos['current_pnl_pct'] = None
                                logger.warning(f"⚠️ [{pos['symbol']}] 현재가 조회 실패로 ROI 계산 불가능")

                        # 2. 달러 PnL (바이낸스 API 우선, 없으면 계산)
                        if 'unrealized_pnl' in pos and pos['unrealized_pnl'] is not None:
                            pnl_usd = float(pos['unrealized_pnl'])
                        else:
                            position_value = pos.get('quantity', 0) * entry_price
                            if roi_pct is not None and position_value > 0:
                                pnl_usd = position_value * (roi_pct / 100)
                            else:
                                pnl_usd = None  # ROI 계산 불가능 시 PnL도 None

                        # 목표 ROI 정보
                        symbol_roi_map = {
                            'BTC': 0.2, 'ETH': 0.2, 'SOL': 0.2, 'DOGE': 0.2, 'BNB': 0.2
                        }
                        target_roi = symbol_roi_map.get(pos['symbol'], 0.03)
                        stop_loss_roi = -0.15  # -0.15% 손절

                        # 상태 표시 (ROI가 None이 아닐 때만)
                        status_info = ""
                        if roi_pct is not None:
                            if roi_pct >= target_roi:
                                status_info = f" [익절조건 충족! 목표:{target_roi}%]"
                            elif roi_pct <= stop_loss_roi:
                                status_info = f" [손절조건 충족! 기준:{stop_loss_roi}%]"

                        # 안전한 값 추출 (딕셔너리 포맷팅 에러 방지)
                        symbol_str = str(pos.get('symbol', 'N/A'))
                        direction_str = str(pos.get('direction', 'N/A'))
                        quantity_val = float(pos.get('quantity', 0))
                        entry_price_val = float(pos.get('entry_price', 0))

                        logger.info(f"  {symbol_str} {direction_str}: {quantity_val:.8f} @ ${entry_price_val:.2f}")

                        # 현재가와 ROI/PnL 표시 (None 처리)
                        if current_price > 0:
                            current_price_str = f"${current_price:.2f}"
                        else:
                            current_price_str = "조회실패"

                        if roi_pct is not None:
                            roi_str = f"{roi_pct:.2f}%"
                        else:
                            roi_str = "계산불가"

                        if pnl_usd is not None:
                            pnl_str = f"${pnl_usd:.2f}"
                        else:
                            pnl_str = "계산불가"

                        logger.info(f"    현재가: {current_price_str} | ROI: {roi_str} | PnL: {pnl_str}{status_info}")

                        # 🚨 청산 플래그 확인 및 자동 청산
                        if pos.get('should_close', False):
                            logger.warning(f"🚨 [{pos['symbol']}] 청산 플래그 감지 - 자동 청산 실행")
                            try:
                                # 포지션 청산
                                self._force_close_position(pos, "현재가 조회 실패로 인한 강제 청산")
                                logger.info(f"✅ [{pos['symbol']}] 포지션 청산 완료")
                            except Exception as close_error:
                                logger.error(f"❌ [{pos['symbol']}] 포지션 청산 실패: {close_error}")

                    except Exception as e:
                        logger.error(f"포지션 정보 로깅 중 오류 발생: {e}, 포지션: {str(pos)}")
            else:
                logger.info("오픈 포지션: 없음")

            # 성과
            total_trades = self.performance.get('total_trades', 0)
            winning_trades = self.performance.get('winning_trades', 0)
            losing_trades = self.performance.get('losing_trades', 0)

            # 거부된 거래 수 계산
            rejected_trades_count = 0
            if hasattr(self, 'trade_history') and self.trade_history:
                rejected_trades_count = sum(1 for trade in self.trade_history if trade.get('status') == 'rejected')

            # 거부된 거래 중 이미 검증된 거래 수 계산 (trade_history에서)
            verified_rejected_trades = 0
            if hasattr(self, 'trade_history') and self.trade_history:
                verified_rejected_trades = sum(1 for trade in self.trade_history
                                            if trade.get('status') == 'rejected' and trade.get('verified', False))

            # 총 거래 수 출력
            logger.info(f"총 거래 수: {total_trades}")

            # 완료된 거래 수 계산 (승리 + 실패)
            completed_trades = winning_trades + losing_trades

            # 완료된 거래가 있는 경우에만 승률 계산
            if completed_trades > 0:
                win_rate = (winning_trades / completed_trades) * 100
                logger.info(f"승률: {win_rate:.2f}% (성공: {winning_trades}, 실패: {losing_trades})")
            else:
                logger.info(f"승률: 0.00% (성공: {winning_trades}, 실패: {losing_trades})")

            # 포트폴리오 손익 정보
            total_profit_loss = self.performance.get('total_profit_loss', 0.0)
            total_profit_loss_pct = self.performance.get('total_profit_loss_pct', 0.0)
            logger.info(f"포트폴리오 총 손익: ${total_profit_loss:.2f} ({total_profit_loss_pct:.2f}%)")

            # 거부된 거래 정보 (실행되지 않은 거래)
            if rejected_trades_count > 0:
                logger.info(f"거부된 거래 수: {rejected_trades_count}")

                # 거부된 거래 중 검증된 거래 정보
                logger.info(f"거부된 거래 검증 상태: 검증 완료 {verified_rejected_trades}건, 검증 대기 {rejected_trades_count - verified_rejected_trades}건")

                # 거부 이유별 통계
                rejection_reasons = {}
                for trade in self.trade_history:
                    if trade.get('status') == 'rejected':
                        reason = trade.get('reason', 'unknown')
                        rejection_reasons[reason] = rejection_reasons.get(reason, 0) + 1

                logger.info("거부 이유별 통계:")
                for reason, count in rejection_reasons.items():
                    logger.info(f"  - {reason}: {count}건")

            # 🎯 통합 생각카드 시스템 상태
            if self.card_manager:
                try:
                    logger.info("=== 통합 생각카드 시스템 상태 ===")

                    # 카드 매니저 요약 정보
                    summary = self.card_manager.get_active_cards_summary()
                    logger.info(f"활성 카드 수: {summary['total_active_cards']}개")

                    # 완성도 통계
                    completion_stats = summary['completion_stats']
                    logger.info(f"카드 완성도: 완료 {completion_stats['fully_complete']}개, 진행중 {completion_stats['partially_complete']}개, 생성됨 {completion_stats['just_created']}개")

                    # 심볼별 카드 현황
                    if summary['cards_by_symbol']:
                        logger.info("심볼별 카드 현황:")
                        for symbol, cards in summary['cards_by_symbol'].items():
                            logger.info(f"  {symbol}: {len(cards)}개 카드")
                            for card in cards[:2]:  # 최대 2개만 표시
                                completion_rate = card['completion_rate']
                                logger.info(f"    - {card['card_id']}: 완성도 {completion_rate:.1%}")

                    # 최근 업데이트된 카드들
                    recent_updates = summary['recent_updates']
                    if recent_updates:
                        logger.info(f"최근 업데이트된 카드: {len(recent_updates)}개")
                        for update in recent_updates[:3]:  # 최대 3개만 표시
                            logger.info(f"  - {update['symbol']}: 완성도 {update['completion_rate']:.1%}")

                    # 학습 인사이트 (전체)
                    insights = self.card_manager.get_learning_insights()
                    if insights['trend_prediction_accuracy'].get('total_predictions', 0) > 0:
                        trend_acc = insights['trend_prediction_accuracy']
                        logger.info(f"추세 예측 통계: {trend_acc['total_predictions']}건, 평균 신뢰도 {trend_acc['avg_confidence']:.2f}")

                    if insights['sela_matching_performance'].get('total_comparisons', 0) > 0:
                        sela_perf = insights['sela_matching_performance']
                        logger.info(f"SELA 매칭 통계: {sela_perf['total_comparisons']}건, 평균 매칭점수 {sela_perf['avg_match_score']:.2f}")

                    # 🔧 시간대별 성공률 (개선된 출력)
                    timeframe_success = insights['timeframe_success_rates']
                    if timeframe_success:
                        logger.info("시간대별 성공률:")
                        total_all_trades = 0
                        total_all_success = 0

                        for timeframe, stats in timeframe_success.items():
                            if stats.get('total_trades', 0) > 0:
                                success_count = stats.get('success_count', 0)
                                total_trades = stats.get('total_trades', 0)
                                success_rate = stats.get('success_rate', 0)
                                avg_profit = stats.get('avg_profit', 0)

                                # 시간대별 상세 정보 출력
                                logger.info(f"  {timeframe}: {success_rate:.1%} ({success_count}/{total_trades}건, 평균수익: {avg_profit:.2%})")

                                total_all_trades += total_trades
                                total_all_success += success_count

                        # 전체 통계 출력
                        if total_all_trades > 0:
                            overall_success_rate = total_all_success / total_all_trades
                            logger.info(f"  전체: {overall_success_rate:.1%} ({total_all_success}/{total_all_trades}건)")
                    else:
                        logger.info("시간대별 성공률: 데이터 없음")

                except Exception as card_e:
                    logger.error(f"❌ 통합 생각카드 상태 로깅 실패: {card_e}")
            else:
                logger.info("=== 통합 생각카드 시스템 ===")
                logger.info("통합 생각카드 시스템: 비활성화")

            logger.info("===========================")
        except Exception as e:
            logger.error(f"포트폴리오 상태 로깅 중 오류 발생: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def get_active_positions(self) -> List[Dict[str, Any]]:
        """활성 포지션 조회 (첫 번째 시스템 사용)"""
        return self.open_positions

    def get_position_history(self) -> List[Dict[str, Any]]:
        """포지션 이력 조회"""
        return self.trade_history

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """거래 이력 조회"""
        return self.trade_history

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """포트폴리오 요약 정보 조회 (첫 번째 시스템 사용)"""
        total_pnl = sum(pos.get('current_pnl', 0) for pos in self.open_positions)
        active_positions_count = len(self.open_positions)
        closed_positions_count = len(self.trade_history)

        # 성공/실패 거래 통계
        closed_positions = self.trade_history
        profit_trades = [p for p in closed_positions if p.get('pnl', 0) > 0]
        loss_trades = [p for p in closed_positions if p.get('pnl', 0) <= 0]

        # 총 수익률 계산
        initial_balance = 10000  # 기본값 (실제로는 저장된 첫 잔고를 불러와야 함)
        total_return_percent = (self.balance - initial_balance) / initial_balance * 100 if initial_balance > 0 else 0

        return {
            "balance": self.balance,
            "total_pnl": total_pnl,
            "active_positions": active_positions_count,
            "closed_positions": closed_positions_count,
            "profit_trades": len(profit_trades),
            "loss_trades": len(loss_trades),
            "win_rate": len(profit_trades) / len(closed_positions) * 100 if closed_positions else 0,
            "total_return": self.balance - initial_balance,
            "total_return_percent": total_return_percent
        }

    def _extract_learned_performance(self, symbol: str) -> Dict[str, Any]:
        """
        학습된 성능 데이터 추출

        Args:
            symbol: 심볼

        Returns:
            성능 데이터
        """
        try:
            # 기본 성능 데이터
            performance_data = {
                'symbol': symbol,
                'total_trades': 0,
                'successful_trades': 0,
                'success_rate': 0.0,
                'avg_profit_pct': 0.0,
                'max_profit_pct': 0.0,
                'max_loss_pct': 0.0,
                'avg_holding_time': 0,
                'last_updated': int(time.time())
            }

            # 거래 히스토리에서 해당 심볼의 거래 추출
            symbol_trades = [t for t in self.trade_history if t.get('symbol') == symbol]

            if symbol_trades:
                performance_data['total_trades'] = len(symbol_trades)

                # 성공한 거래 계산
                successful = [t for t in symbol_trades if t.get('realized_pnl', 0) > 0]
                performance_data['successful_trades'] = len(successful)
                performance_data['success_rate'] = len(successful) / len(symbol_trades)

                # 평균 수익률 계산
                profits = [t.get('realized_pnl_pct', 0) for t in symbol_trades]
                if profits:
                    performance_data['avg_profit_pct'] = sum(profits) / len(profits)
                    performance_data['max_profit_pct'] = max(profits)
                    performance_data['max_loss_pct'] = min(profits)

                # 평균 보유 시간 계산
                holding_times = []
                for trade in symbol_trades:
                    if trade.get('exit_timestamp') and trade.get('timestamp'):
                        holding_time = trade['exit_timestamp'] - trade['timestamp']
                        holding_times.append(holding_time)

                if holding_times:
                    performance_data['avg_holding_time'] = sum(holding_times) / len(holding_times)

            return performance_data

        except Exception as e:
            logger.error(f"❌ [{symbol}] _extract_learned_performance 메서드 실행 실패: {e}")
            return {
                'symbol': symbol,
                'total_trades': 0,
                'successful_trades': 0,
                'success_rate': 0.0,
                'avg_profit_pct': 0.0,
                'last_updated': int(time.time())
            }

    def _calculate_performance_multiplier(self, strategy_data: Dict[str, Any]) -> float:
        """
        전략 성능 기반 승수 계산

        Args:
            strategy_data: 전략 데이터

        Returns:
            성능 승수 (0.5 ~ 2.0)
        """
        try:
            # 기본 승수
            base_multiplier = 1.0

            # 성공률 기반 조정
            success_rate = strategy_data.get('success_rate', 0.5)
            if success_rate > 0.7:
                base_multiplier *= 1.5
            elif success_rate > 0.6:
                base_multiplier *= 1.2
            elif success_rate < 0.4:
                base_multiplier *= 0.7
            elif success_rate < 0.3:
                base_multiplier *= 0.5

            # 평균 수익률 기반 조정
            avg_profit = strategy_data.get('avg_profit_pct', 0)
            if avg_profit > 5:
                base_multiplier *= 1.3
            elif avg_profit > 2:
                base_multiplier *= 1.1
            elif avg_profit < -2:
                base_multiplier *= 0.8
            elif avg_profit < -5:
                base_multiplier *= 0.6

            # 최대/최소 제한
            return max(0.5, min(2.0, base_multiplier))

        except Exception as e:
            logger.error(f"성능 승수 계산 중 오류: {e}")
            return 1.0

    def _execute_virtual_trade(self, symbol: str, direction: str, strategy: Dict[str, Any],
                              market_data: Dict[str, Any], position_size_usd: float,
                              current_price: float) -> Dict[str, Any]:
        """🚀 가상 거래 실행 (잔액 부족 시 학습 지속용)"""
        try:
            import time
            from datetime import datetime

            timestamp = int(time.time())

            # 가상 잔액 설정 (.env에서 로드)
            virtual_balance = float(os.getenv('VIRTUAL_BALANCE', '1000.0'))  # 기본 $1000

            # 가상 거래 ID 생성
            trade_id = f"virtual_{symbol}_{timestamp}"

            # 가상 수량 계산
            virtual_quantity = position_size_usd / current_price

            # 🚀 상세 가상 거래 로깅
            logger.info(f"=" * 60)
            logger.info(f"🚀 가상 거래 실행 시작")
            logger.info(f"🚀 심볼: {symbol}")
            logger.info(f"🚀 방향: {direction}")
            logger.info(f"🚀 수량: {virtual_quantity:.8f}")
            logger.info(f"🚀 진입가: ${current_price:.2f}")
            logger.info(f"🚀 포지션 크기: ${position_size_usd:.2f}")
            logger.info(f"🚀 신뢰도: {strategy.get('confidence', 0.5):.2f}")
            logger.info(f"🚀 중요도: {strategy.get('importance', 0)}")
            logger.info(f"🚀 가상 잔액: ${virtual_balance:.2f}")
            logger.info(f"=" * 60)

            # 가상 포지션 생성
            virtual_position = {
                "trade_id": trade_id,
                "symbol": symbol,
                "direction": direction,
                "entry_price": current_price,
                "quantity": virtual_quantity,
                "value_usd": position_size_usd,
                "entry_timestamp": timestamp,  # 🔧 일관성을 위해 entry_timestamp 추가
                "timestamp": timestamp,
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "virtual_open",
                "strategy_type": strategy.get("strategy_type", "unknown"),
                "confidence": strategy.get("confidence", 0.5),
                "importance": strategy.get("importance", 0),
                "virtual_mode": True,  # 🔧 가상 거래 표시
                "learning_purpose": True  # 🔧 학습 목적 표시
            }

            # 가상 포지션을 별도 리스트에 저장
            if not hasattr(self, 'virtual_positions'):
                self.virtual_positions = []
            self.virtual_positions.append(virtual_position)

            # 가상 거래 결과 생성
            virtual_trade_result = {
                "trade_id": trade_id,
                "symbol": symbol,
                "timestamp": timestamp,
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "strategy_type": strategy.get("strategy_type", "unknown"),
                "direction": direction,
                "entry_price": current_price,
                "quantity": virtual_quantity,
                "value_usd": position_size_usd,
                "status": "virtual_executed",
                "success": True,
                "virtual_mode": True,
                "learning_purpose": True,
                "confidence": strategy.get("confidence", 0.5),
                "importance": strategy.get("importance", 0),
                "reason": "가상 거래 (학습 지속용)"
            }

            # 가상 거래 내역에 추가
            if not hasattr(self, 'virtual_trade_history'):
                self.virtual_trade_history = []
            self.virtual_trade_history.append(virtual_trade_result)

            # 🚀 학습 데이터에 즉시 반영
            self._add_virtual_trade_to_learning(virtual_trade_result, strategy)

            # 가상 거래 통계 업데이트
            if not hasattr(self, 'virtual_performance'):
                self.virtual_performance = {
                    "total_virtual_trades": 0,
                    "virtual_balance": virtual_balance,
                    "virtual_positions_count": 0
                }

            self.virtual_performance["total_virtual_trades"] += 1
            self.virtual_performance["virtual_positions_count"] = len(self.virtual_positions)

            # 🚀 가상 거래 완료 상세 로깅
            logger.info(f"✅ 가상 거래 완료!")
            logger.info(f"✅ 거래 ID: {trade_id}")
            logger.info(f"✅ 상태: 가상 포지션 오픈")
            logger.info(f"✅ 학습 데이터 저장: 완료")
            logger.info(f"📊 가상 거래 통계:")
            logger.info(f"   - 총 가상 거래: {self.virtual_performance['total_virtual_trades']}건")
            logger.info(f"   - 활성 포지션: {self.virtual_performance['virtual_positions_count']}개")
            logger.info(f"   - 가상 잔액: ${self.virtual_performance['virtual_balance']:.2f}")
            logger.info(f"=" * 60)

            return virtual_trade_result

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 가상 거래 실행 실패: {e}")
            return {
                "trade_id": f"virtual_failed_{symbol}_{int(time.time())}",
                "symbol": symbol,
                "status": "virtual_failed",
                "success": False,
                "error": str(e),
                "virtual_mode": True
            }

    def _add_virtual_trade_to_learning(self, virtual_trade: Dict[str, Any], strategy: Dict[str, Any]) -> None:
        """🚀 가상 거래를 학습 데이터에 추가"""
        try:
            # 학습 루프에 가상 거래 데이터 전달
            from simulator.learning_loop.llm_strategy_evaluator import LLMStrategyEvaluator
            import chromadb

            # ChromaDB 클라이언트 초기화
            try:
                chroma_dir = os.path.join("data", "chroma_db")
                db_client = chromadb.PersistentClient(path=chroma_dir)

                # LLMStrategyEvaluator 초기화 (LLM 클라이언트 포함)
                llm_client = self._get_llm_client()
                evaluator = LLMStrategyEvaluator(
                    llm_client=llm_client,  # 실제 LLM 클라이언트 전달
                    db_client=db_client,
                    config={}
                )
            except Exception as init_e:
                self.logger.error(f"❌ LLMStrategyEvaluator 초기화 실패: {init_e}")
                return

            # 가상 거래를 학습 데이터 형식으로 변환
            learning_data = {
                "symbol": virtual_trade["symbol"],
                "strategy_type": "virtual_trade",
                "action": virtual_trade["direction"],
                "confidence": virtual_trade.get("confidence", 0.5),
                "entry_price": virtual_trade["entry_price"],
                "timestamp": virtual_trade["timestamp"],
                "virtual_mode": True,
                "learning_source": "virtual_trading",
                "performance_score": 0.5,  # 초기값, 나중에 업데이트
                "reasoning": f"가상 거래 기반 학습: {virtual_trade['reason']}"
            }

            # ChromaDB에 저장
            document_text = f"Virtual trade for {virtual_trade['symbol']}: {virtual_trade['direction']} at ${virtual_trade['entry_price']}"

            evaluator.strategies_collection.add(
                ids=[f"virtual_{virtual_trade['trade_id']}"],
                metadatas=[learning_data],
                documents=[document_text]
            )

            # 🚀 학습 데이터 저장 상세 로깅
            self.logger.info(f"📚 학습 데이터 저장:")
            self.logger.info(f"   심볼: {virtual_trade['symbol']}")
            self.logger.info(f"   거래 ID: {virtual_trade['trade_id']}")
            self.logger.info(f"   방향: {virtual_trade['direction']}")
            self.logger.info(f"   진입가: ${virtual_trade['entry_price']:.2f}")
            self.logger.info(f"   신뢰도: {virtual_trade.get('confidence', 0.5):.2f}")
            self.logger.info(f"   ChromaDB ID: virtual_{virtual_trade['trade_id']}")
            self.logger.info(f"📚 학습 데이터 저장 완료!")

        except Exception as e:
            self.logger.error(f"❌ 가상 거래 학습 데이터 추가 실패: {e}")

    def _force_close_position(self, position: Dict[str, Any], reason: str) -> None:
        """🚨 포지션 강제 청산"""
        try:
            symbol = position.get('symbol', 'UNKNOWN')

            # 포지션을 거래 히스토리로 이동
            closed_position = position.copy()
            closed_position['exit_timestamp'] = time.time()
            closed_position['exit_reason'] = reason
            closed_position['status'] = 'force_closed'
            closed_position['realized_pnl'] = position.get('current_pnl', 0)
            closed_position['realized_pnl_pct'] = 0.0  # ROI 계산 불가능

            # 거래 히스토리에 추가
            self.trade_history.append(closed_position)

            # 오픈 포지션에서 제거
            self.open_positions = [pos for pos in self.open_positions if pos.get('symbol') != symbol]

            logger.info(f"🚨 [{symbol}] 포지션 강제 청산 완료: {reason}")

        except Exception as e:
            logger.error(f"❌ 포지션 강제 청산 실패: {e}")

    def _force_close_position_immediate(self, position: Dict[str, Any], reason: str) -> None:
        """🚨 포지션 즉시 강제 청산 (open_positions 리스트에서 즉시 제거)"""
        try:
            symbol = position.get('symbol', 'UNKNOWN')
            trade_id = position.get('trade_id', 'UNKNOWN')

            # 포지션을 거래 히스토리로 이동
            closed_position = position.copy()
            closed_position['exit_timestamp'] = time.time()
            closed_position['exit_reason'] = reason
            closed_position['status'] = 'force_closed'
            closed_position['realized_pnl'] = position.get('current_pnl', 0)
            closed_position['realized_pnl_pct'] = 0.0  # ROI 계산 불가능

            # 거래 히스토리에 추가
            self.trade_history.append(closed_position)

            # 오픈 포지션에서 즉시 제거 (trade_id로 정확히 매칭)
            original_count = len(self.open_positions)
            self.open_positions = [pos for pos in self.open_positions
                                 if pos.get('trade_id') != trade_id]
            removed_count = original_count - len(self.open_positions)

            # 포트폴리오 저장
            self.save_portfolio()

            logger.info(f"🚨 [{symbol}] 포지션 즉시 청산 완료: {reason} (제거된 포지션: {removed_count}개)")

        except Exception as e:
            logger.error(f"❌ 포지션 즉시 청산 실패: {e}")
            raise

class ExtendedPortfolio(Portfolio):
    """
    포트폴리오 관리 클래스 확장
    거래 실행 및 포지션 관리 기능 구현
    """

    def __init__(self, base_currency="USDT", initial_balance=10000, save_path="./portfolio_data.json", max_positions=5, mode='simulation', testnet=True, data_dir=None, binance_utils=None):
        """
        포트폴리오 초기화

        Args:
            base_currency (str): 기본 통화 (기본값: USDT)
            initial_balance (float): 초기 잔고 (기본값: 10000)
            save_path (str): 포트폴리오 데이터 저장 경로
            max_positions (int): 최대 포지션 수
            mode (str): 거래 모드 ('simulation' 또는 'real')
            testnet (bool): 테스트넷 사용 여부
            data_dir (str): 데이터 디렉토리
            binance_utils: 바이낸스 유틸리티 객체
        """
        super().__init__(initial_balance=initial_balance, mode=mode, testnet=testnet, data_dir=data_dir, binance_utils=binance_utils)

        self.base_currency = base_currency
        self.positions = {}  # {symbol: position_dict}
        self.position_history = []
        self.trade_history = []
        self.save_path = save_path
        self.max_positions = max_positions
        self.logger = logging.getLogger('portfolio')

        # 저장된 포트폴리오 데이터가 있으면 로드
        self.load_portfolio()

    def _cleanup_closed_positions(self):
        """closed 상태의 포지션을 open_positions에서 제거하고 히스토리로 이동"""
        try:
            initial_count = len(self.open_positions)
            closed_positions = []
            active_positions = []

            for pos in self.open_positions:
                if pos.get('status') == 'closed':
                    closed_positions.append(pos)
                else:
                    active_positions.append(pos)

            if closed_positions:
                # closed 포지션을 히스토리에 추가 (이미 있는지 확인)
                for closed_pos in closed_positions:
                    pos_id = closed_pos.get('id') or closed_pos.get('trade_id')
                    # 히스토리에 이미 있는지 확인
                    already_in_history = any(
                        h.get('position_id') == pos_id or h.get('trade_id') == pos_id or h.get('id') == pos_id
                        for h in self.position_history
                    )
                    if not already_in_history:
                        # 히스토리 항목에 필요한 메타데이터 추가
                        history_item = closed_pos.copy()
                        history_item['moved_to_history_at'] = time.time()
                        history_item['original_status'] = 'closed'
                        self.position_history.append(history_item)
                        self.logger.info(f"📚 히스토리 추가: {closed_pos.get('symbol')} {closed_pos.get('direction', 'unknown')}")

                # open_positions에서 closed 포지션 완전 제거
                self.open_positions = active_positions

                self.logger.info(f"🧹 closed 포지션 {len(closed_positions)}개 완전 제거 완료")
                self.logger.info(f"📊 포지션 수 변화: {initial_count} → {len(active_positions)} (활성: {len(active_positions)}/{self.max_positions})")

                # 포트폴리오 저장
                self.save_portfolio()

                return len(closed_positions)  # 정리된 포지션 수 반환
            else:
                self.logger.debug("🔍 정리할 closed 포지션 없음")
                return 0

        except Exception as e:
            self.logger.error(f"❌ closed 포지션 정리 중 오류: {e}")
            import traceback
            self.logger.error(f"❌ 상세 오류: {traceback.format_exc()}")
            return 0

    def _smart_position_cleanup(self, new_symbol: str, new_action: str):
        """강화된 포지션 정리: closed 포지션 제거 + 중복 포지션 정리 + 최신 포지션만 유지"""
        try:
            self.logger.info(f"🔧 강화된 포지션 정리 시작: 총 {len(self.open_positions)}개 포지션")

            # 1단계: closed 포지션 완전 제거
            closed_count = self._cleanup_closed_positions()

            # 2단계: 활성 포지션 확인
            active_positions = [pos for pos in self.open_positions if pos.get('status') != 'closed']
            self.logger.info(f"🔧 활성 포지션: {len(active_positions)}개")

            # 3단계: 심볼별 중복 포지션 정리 (강제 실행)
            cleaned_count = 0
            symbols_processed = set()

            for pos in list(self.open_positions):
                if pos.get('status') == 'closed':
                    continue

                symbol = pos.get('symbol')
                if symbol in symbols_processed:
                    continue

                # 해당 심볼의 모든 포지션 수집
                symbol_positions = [p for p in self.open_positions
                                  if p.get('symbol') == symbol and p.get('status') != 'closed']

                self.logger.info(f"🔍 [{symbol}] 포지션 수: {len(symbol_positions)}개")

                if len(symbol_positions) > 1:
                    # 최신 포지션만 남기고 나머지 정리
                    symbol_positions.sort(key=lambda x: x.get('created_at', 0) or x.get('entry_timestamp', 0) or x.get('timestamp', 0), reverse=True)
                    latest_position = symbol_positions[0]

                    self.logger.info(f"🎯 [{symbol}] 최신 포지션 유지: {latest_position.get('trade_id') or latest_position.get('id', 'unknown')}")

                    for old_pos in symbol_positions[1:]:
                        pos_id = old_pos.get('trade_id') or old_pos.get('id', 'unknown')
                        self.logger.warning(f"🧹 [{symbol}] 오래된 포지션 정리: {pos_id}")
                        old_pos['status'] = 'closed'
                        old_pos['exit_reason'] = 'keep_latest_only'
                        old_pos['exit_timestamp'] = time.time()
                        cleaned_count += 1

                symbols_processed.add(symbol)

            # 4단계: closed 포지션 다시 정리
            if cleaned_count > 0:
                final_closed_count = self._cleanup_closed_positions()
                self.logger.info(f"✅ 강화된 포지션 정리 완료: {cleaned_count}개 정리, {final_closed_count}개 제거")
                self.save_portfolio()
            else:
                self.logger.info("🔍 정리할 중복 포지션 없음")

        except Exception as e:
            self.logger.error(f"❌ 강화된 포지션 정리 중 오류: {e}")
            import traceback
            self.logger.error(f"❌ 상세 오류: {traceback.format_exc()}")



    # 중복된 저장/로드 메서드 제거됨 - 첫 번째 시스템 사용

    def execute_strategy(self, symbol: str, strategy: Dict[str, Any], market_data: Dict[str, Any], binance_utils=None, prediction_history=None) -> Dict[str, Any]:
        """
        전략 실행 (포지션 생성)

        Args:
            symbol: 거래 심볼 (예: BTC)
            strategy: 전략 딕셔너리
            market_data: 현재 시장 데이터
            binance_utils: 바이낸스 유틸리티 객체 (Binance API 래퍼)
            prediction_history: 예측 히스토리 객체 (선택사항)

        Returns:
            실행 결과 딕셔너리
        """
        # 🔧 strategy 파라미터 타입 검증 및 수정
        if not isinstance(strategy, dict):
            logger.error(f"❌ [{symbol}] strategy 파라미터가 딕셔너리가 아님: {type(strategy)}")
            return {
                "success": False,
                "error": f"Invalid strategy type: {type(strategy)}, expected dict"
            }

        # 🔧 market_data 파라미터 타입 검증 및 수정
        if not isinstance(market_data, dict):
            logger.error(f"❌ [{symbol}] market_data 파라미터가 딕셔너리가 아님: {type(market_data)}")
            return {
                "success": False,
                "error": f"Invalid market_data type: {type(market_data)}, expected dict"
            }

        # 전략에서 필요한 정보 추출 (symbol 파라미터 우선 사용)
        strategy_symbol = strategy.get('symbol', symbol)  # 파라미터 symbol 우선
        action = strategy.get('action')
        entry_price = strategy.get('entry_price')
        take_profit = strategy.get('take_profit')
        stop_loss = strategy.get('stop_loss')
        size = strategy.get('size', 0.01)  # 기본값 0.01 BTC/ETH
        expiry = strategy.get('expiry', 0)  # 전략 만료 시간 (선택적)

        # 심볼 일치성 확인
        if strategy_symbol and strategy_symbol != symbol:
            logger.warning(f"⚠️ [{symbol}] 전략 심볼({strategy_symbol})과 파라미터 심볼({symbol}) 불일치")

        # 실제 사용할 심볼 (파라미터 우선)
        final_symbol = symbol
        timeframe = strategy.get('timeframe', 'short_term')  # 전략 타임프레임
        target_roi = strategy.get('target_roi', 2.0)  # 목표 수익률 (선택적, 기본 2%)

        # 현재 가격 확인
        current_price = market_data.get('price', entry_price)

        # 포지션 ID 생성
        position_id = f"pos_{int(time.time())}_{uuid.uuid4().hex[:8]}"

        # 로깅
        self.logger.info(f"[{final_symbol}] 전략 실행: {action} @ {current_price}")
        self.logger.info(f"[{final_symbol}] 목표가: {take_profit}, 손절가: {stop_loss}")

        # 🔧 closed 포지션 자동 정리
        self._cleanup_closed_positions()

        # 🔧 강화된 스마트 포지션 관리: 항상 실행 (포지션 수 제한과 무관)
        self._smart_position_cleanup(final_symbol, action)

        # 포지션 생성 가능 여부 확인 (활성 포지션만 카운트)
        active_positions = [pos for pos in self.open_positions if pos.get('status') != 'closed']
        if len(active_positions) >= self.max_positions:
            self.logger.warning(f"최대 활성 포지션 수 ({self.max_positions}) 도달. 새 포지션을 생성할 수 없습니다.")
            self.logger.info(f"현재 활성 포지션: {len(active_positions)}/{self.max_positions}")

            # 🔧 강제 포지션 정리 한 번 더 시도
            self.logger.warning("🔧 강제 포지션 정리 재시도")
            self._smart_position_cleanup(final_symbol, action)

            # 다시 확인
            active_positions = [pos for pos in self.open_positions if pos.get('status') != 'closed']
            if len(active_positions) >= self.max_positions:
                return {
                    "success": False,
                    "position_id": None,
                    "message": f"최대 활성 포지션 수 ({self.max_positions}) 도달"
                }

        # 🔥 바이낸스 실제 포지션 우선 확인 후 내부 포지션과 비교
        binance_position = None
        internal_position = None

        # 1. 바이낸스 실제 포지션 확인 (실제 거래 모드인 경우)
        self.logger.info(f"🔧 [{final_symbol}] 포지션 충돌 검사 시작 - 새 신호: {action}")

        if binance_utils and hasattr(binance_utils, 'get_futures_positions'):
            try:
                self.logger.info(f"🔧 [{final_symbol}] 바이낸스 포지션 조회 중...")
                binance_positions = binance_utils.get_futures_positions()
                if isinstance(binance_positions, list):
                    for pos in binance_positions:
                        if pos.get('symbol') == f"{final_symbol}USDT" and float(pos.get('positionAmt', 0)) != 0:
                            binance_position = pos
                            self.logger.info(f"🔧 [{final_symbol}] 바이낸스 실제 포지션 발견: {pos.get('positionAmt', 0)} @ {pos.get('entryPrice', 0)}")
                            break

                    if not binance_position:
                        self.logger.info(f"🔧 [{final_symbol}] 바이낸스에 활성 포지션 없음")
                else:
                    self.logger.warning(f"🔧 [{final_symbol}] 바이낸스 포지션 조회 실패: {binance_positions}")
            except Exception as e:
                self.logger.error(f"🔧 [{final_symbol}] 바이낸스 포지션 조회 오류: {e}")
        else:
            # 바이낸스 연결 확인 (더 정확한 검증)
            has_exchange = binance_utils is not None and hasattr(binance_utils, 'get_futures_positions')
            self.logger.info(f"🔧 [{final_symbol}] 바이낸스 연결 없음 (binance_utils={has_exchange})")
            self.logger.info(f"🔧 [{final_symbol}] binance_utils 타입: {type(binance_utils)}, 값: {binance_utils}")

        # 2. 내부 포지션 확인
        for pos in self.open_positions:
            # 🔧 포지션 객체 타입 검증
            if not isinstance(pos, dict):
                self.logger.warning(f"🔧 [{final_symbol}] 잘못된 포지션 타입 발견: {type(pos)}, 스킵")
                continue

            if pos.get('symbol') == final_symbol and pos.get('status', 'active') == 'active':
                internal_position = pos
                self.logger.info(f"🔍 [{final_symbol}] 내부 포지션 발견: {pos.get('action', '')} @ {pos.get('entry_price', 0)}")
                break

        if not internal_position:
            self.logger.info(f"🔍 [{final_symbol}] 내부에 활성 포지션 없음")

        # 3. 포지션 상태 분석 및 결정
        # 🔧 바이낸스 포지션이 있으면 항상 충돌 검사 (내부 포지션 여부와 무관)
        if binance_position:
            # 🔧 바이낸스 포지션 방향 정확히 확인
            position_amt = float(binance_position.get('positionAmt', 0))
            if position_amt > 0:
                binance_direction = "BUY"  # 롱 포지션
                binance_direction_text = "long"
            elif position_amt < 0:
                binance_direction = "SELL"  # 숏 포지션
                binance_direction_text = "short"
            else:
                binance_direction = "NEUTRAL"
                binance_direction_text = "neutral"

            # 내부 포지션 정보 (있으면 표시, 없으면 None)
            internal_action = internal_position.get('action', '') if internal_position else 'none'
            internal_direction = self._normalize_direction(internal_action) if internal_position else 'none'

            self.logger.info(f"🔧 [{final_symbol}] 포지션 방향 비교:")
            self.logger.info(f"  - 바이낸스: {binance_direction_text} (positionAmt: {position_amt})")
            self.logger.info(f"  - 내부: {internal_direction} (action: {internal_action})")
            self.logger.info(f"  - 새 신호: {action}")

            # 🔧 포지션 방향 충돌 감지 및 처리 (바이낸스 포지션 기준)
            new_direction = self._normalize_direction(action)

            self.logger.info(f"🔧 [{final_symbol}] 충돌 검사: 바이낸스({binance_direction_text}) vs 새신호({new_direction})")

            # 바이낸스 포지션과 새 신호가 반대 방향인 경우 강제 전환
            if (binance_direction_text == "short" and new_direction == "long") or \
               (binance_direction_text == "long" and new_direction == "short"):
                self.logger.warning(f"🔄 [{final_symbol}] 포지션 방향 충돌 감지!")
                self.logger.warning(f"  - 바이낸스: {binance_direction_text}")
                self.logger.warning(f"  - 새 신호: {new_direction}")
                self.logger.warning(f"🔧 강제 포지션 전환 시작...")

                # 기존 포지션 강제 종료 후 새 포지션 진입
                return self._force_position_switch(final_symbol, binance_position, new_direction, strategy, market_data, binance_utils)

            # 같은 방향인 경우 스킵 (중복 포지션 방지)
            if binance_direction_text == new_direction:
                self.logger.warning(f"🚫 [{final_symbol}] 바이낸스와 새 신호 모두 같은 방향: {new_direction}")
                self.logger.warning(f"🚫 [{final_symbol}] 중복 포지션 방지를 위해 거래 스킵")
                return {
                    "success": True,
                    "skipped_trade": True,
                    "position_id": None,
                    "message": f"바이낸스 같은 방향 포지션 존재로 거래 스킵: {new_direction}"
                }

        # 🔧 내부 포지션만 있는 경우 (바이낸스 포지션 없음)
        elif internal_position:
            internal_action = internal_position.get('action', '')
            internal_direction = self._normalize_direction(internal_action)
            new_direction = self._normalize_direction(action)

            self.logger.info(f"🔧 [{final_symbol}] 내부 포지션만 존재:")
            self.logger.info(f"  - 내부: {internal_direction} (action: {internal_action})")
            self.logger.info(f"  - 새 신호: {new_direction}")

            # 내부 포지션과 새 신호가 반대 방향인 경우
            if (internal_direction == "short" and new_direction == "long") or \
               (internal_direction == "long" and new_direction == "short"):
                self.logger.warning(f"🔄 [{final_symbol}] 내부 포지션 방향 충돌 감지!")
                self.logger.warning(f"  - 내부: {internal_direction}")
                self.logger.warning(f"  - 새 신호: {new_direction}")
                # 내부 포지션은 강제 종료하고 새 포지션 진입
                for pos in self.open_positions:
                    # 🔧 포지션 객체 타입 검증
                    if not isinstance(pos, dict):
                        self.logger.warning(f"🔧 [{final_symbol}] 잘못된 포지션 타입 발견: {type(pos)}, 스킵")
                        continue

                    if pos.get('symbol') == final_symbol and pos.get('status') == 'active':
                        pos['status'] = 'closed'
                        pos['close_reason'] = 'direction_conflict'
                        pos['close_timestamp'] = time.time()
                        self.logger.info(f"🔧 [{final_symbol}] 내부 포지션 강제 종료: {pos.get('action')}")

            # 같은 방향인 경우 스킵
            elif internal_direction == new_direction:
                self.logger.warning(f"🚫 [{final_symbol}] 내부와 새 신호 모두 같은 방향: {new_direction}")
                return {
                    "success": True,
                    "skipped_trade": True,
                    "position_id": None,
                    "message": f"내부 같은 방향 포지션 존재로 거래 스킵: {new_direction}"
                }
            # 🔧 이 부분은 바이낸스 포지션이 없는 경우이므로 binance_direction 변수가 정의되지 않음
            # 내부 포지션만 있는 경우의 처리는 이미 위에서 완료됨

        elif binance_position and not internal_position:
            # 바이낸스에만 포지션 있음 - 방향 확인
            position_amt = float(binance_position.get('positionAmt', 0))
            if position_amt > 0:
                binance_action = "BUY"
                binance_direction_text = "long"
            elif position_amt < 0:
                binance_action = "SELL"
                binance_direction_text = "short"
            else:
                binance_action = "NEUTRAL"
                binance_direction_text = "neutral"

            new_direction = self._normalize_direction(action)

            if binance_direction_text == new_direction:
                self.logger.warning(f"🚫 [{final_symbol}] 바이낸스에 같은 방향 포지션 존재 (내부 미동기화): {new_direction}")
                return {
                    "success": True,
                    "skipped_trade": True,
                    "position_id": None,
                    "message": f"바이낸스 같은 방향 포지션 존재로 거래 스킵: {new_direction}"
                }
            else:
                self.logger.info(f"🔄 [{final_symbol}] 바이낸스 반대 방향 포지션 감지: {binance_direction_text} → {new_direction}")

        elif not binance_position and internal_position:
            # 내부에만 포지션 있음 (바이낸스에 없음) - 내부 포지션 정리
            internal_action = internal_position.get('action', '')
            internal_direction = self._normalize_direction(internal_action)
            new_direction = self._normalize_direction(action)

            if internal_direction == new_direction:
                self.logger.warning(f"🟡 [{final_symbol}] 내부 전용 같은 방향 포지션 존재 (바이낸스 미동기화): {new_direction}")
                self.logger.info(f"🔄 [{final_symbol}] 내부 포지션 정리 후 새 포지션 생성")
                internal_position['manual_close'] = True
                internal_position['exit_reason'] = 'exchange_sync'
            else:
                self.logger.info(f"🔄 [{final_symbol}] 내부 반대 방향 포지션 정리: {internal_direction} → {new_direction}")
                internal_position['manual_close'] = True
                internal_position['exit_reason'] = 'direction_change'

        else:
            # 바이낸스와 내부 모두 포지션 없음 - 정상 진행
            self.logger.info(f"✅ [{final_symbol}] 바이낸스와 내부 모두 포지션 없음 - 새 포지션 생성 가능")

        try:
            # 실제 거래 실행 (binance_utils API 호출)
            if binance_utils:
                # 거래 크기 계산 (BTC/ETH 기준)
                # USDT 가치로 계산하여 알맞은 수량 계산
                usdt_value = size * current_price

                # 주문 파라미터 구성 (참고용 - 실제로는 직접 create_order 호출)
                order_params = {
                    "symbol": f"{symbol}",
                    "side": "BUY" if action == "BUY" else "SELL",
                    "order_type": "MARKET",
                    "quantity": size,
                }

                try:
                    # 테스트넷/실제 거래 분기
                    if hasattr(binance_utils, 'create_order'):
                        # BinanceUtils.create_order 사용 시 (order_type 매개변수 사용)
                        order = binance_utils.create_order(
                            symbol=f"{symbol}",
                            side=action.upper(),
                            order_type='MARKET',
                            quantity=size
                        )
                        order_id = order.get('orderId', 'unknown')
                        executed_price = float(order.get('price', current_price)) if order.get('price') else current_price
                    else:
                        # 직접 Binance API 호출하는 경우
                        response = binance_utils.order_market(
                            symbol=symbol,
                            side=action,
                            quantity=size
                        )
                        order_id = response.get('orderId', 'unknown')
                        executed_price = float(response.get('price', current_price))

                    self.logger.info(f"[{symbol}] 주문 체결 성공: ID={order_id}, 가격={executed_price}")

                except Exception as trade_error:
                    self.logger.error(f"[{symbol}] 거래 실행 중 오류: {str(trade_error)}")
                    return {
                        "success": False,
                        "position_id": None,
                        "message": f"거래 실행 오류: {str(trade_error)}"
                    }

            # 포지션 저장 (거래 성공 여부와 관계없이 시뮬레이션 모드에서는 항상 저장)
            position = {
                "id": position_id,
                "symbol": final_symbol,
                "action": action,
                "entry_price": current_price,
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "size": size,
                "value": size * current_price,  # USDT 기준 가치
                "created_at": int(time.time()),
                "updated_at": int(time.time()),
                "strategy_id": strategy.get('id', 'unknown'),
                "status": "active",
                "pnl": 0,
                "pnl_percent": 0,
                "exit_price": None,
                "exit_reason": None,
                "expiry": expiry,  # 만료 시간 추가
                "timeframe": timeframe,  # 타임프레임 추가
                "target_roi": target_roi,  # 목표 수익률 추가
                "manual_close": False,  # 수동 종료 플래그
                "prediction_id": strategy.get('prediction_id', None)  # 예측 ID 추가
            }

            # 포지션 등록 (첫 번째 시스템 사용)
            self.open_positions.append(position)

            # 거래 내역 기록
            trade = {
                "timestamp": int(time.time()),
                "symbol": final_symbol,
                "action": action,
                "price": current_price,
                "size": size,
                "value": size * current_price,
                "strategy_id": strategy.get('id', 'unknown'),
                "type": "entry",
                "position_id": position_id
            }
            self.trade_history.append(trade)

            # 포트폴리오 저장
            self.save_portfolio()

            return {
                "success": True,
                "position_id": position_id,
                "message": f"{final_symbol} {action} 포지션 생성 성공 @ {current_price}"
            }

        except Exception as e:
            self.logger.error(f"[{final_symbol}] 전략 실행 중 오류: {str(e)}")
            return {
                "success": False,
                "position_id": None,
                "message": f"오류: {str(e)}"
            }

    def update_positions_legacy(self, market_data: Dict[str, str], binance_utils=None, prediction_history=None) -> List[Dict[str, Any]]:
        """
        포지션 업데이트 (TP/SL 체크 및 실행)

        Args:
            market_data: 현재 시장 데이터 {symbol: price}
            binance_utils: 바이낸스 유틸리티 객체 (선택사항)
            prediction_history: 예측 히스토리 객체 (선택사항)

        Returns:
            종료된 포지션 목록
        """
        closed_positions = []

        # 모든 활성 포지션 업데이트 (첫 번째 시스템 사용)
        remaining_positions = []
        for position in list(self.open_positions):
            symbol = position.get('symbol', 'N/A')
            if position.get('status', 'open') == 'closed':
                continue

            # 현재 가격 확인
            current_price = market_data.get(symbol, {}).get('price', 0)
            if not current_price:
                self.logger.warning(f"[{symbol}] 가격 정보가 없어 포지션 업데이트를 건너뜁니다.")
                continue

            # 손익 계산
            entry_price = position['entry_price']

            # 🔧 action 필드 누락 시 기본값 설정
            position_action = position.get('action', position.get('side', position.get('type', 'BUY')))

            if position_action == 'BUY':
                # 롱 포지션
                pnl_percent = (current_price - entry_price) / entry_price * 100
                pnl = position['value'] * pnl_percent / 100
            else:
                # 숏 포지션
                pnl_percent = (entry_price - current_price) / entry_price * 100
                pnl = position['value'] * pnl_percent / 100

            # 손익 정보 업데이트
            position['pnl'] = pnl
            position['pnl_percent'] = pnl_percent
            position['updated_at'] = int(time.time())

            # Take Profit / Stop Loss 체크
            take_profit = position['take_profit']
            stop_loss = position['stop_loss']

            exit_reason = None

            # 포지션 타입(BUY/SELL)에 따라 TP/SL 조건 확인
            if position_action == 'BUY':
                # 롱 포지션
                if current_price >= take_profit:
                    exit_reason = 'take_profit'
                elif current_price <= stop_loss:
                    exit_reason = 'stop_loss'
            else:
                # 숏 포지션
                if current_price <= take_profit:
                    exit_reason = 'take_profit'
                elif current_price >= stop_loss:
                    exit_reason = 'stop_loss'

            # 만료 시간 체크 (선택적)
            if 'expiry' in position and position['expiry'] > 0:
                current_time = int(time.time())
                if current_time >= position['expiry']:
                    exit_reason = 'expiry'
                    self.logger.info(f"[{symbol}] 포지션 만료 시간 도달")

            # 단타거래 최적화: 전략에서 지정된 보유시간 우선 사용
            position_age = int(time.time()) - position['created_at']

            # 1. 전략에서 지정된 max_hold_time 확인 (SELA 에이전트에서 설정)
            strategy_hold_time = position.get('max_hold_time', None)

            if strategy_hold_time:
                # 전략에서 지정된 보유시간 사용 (1,3,5분 단타거래)
                max_hold_time = strategy_hold_time
                hold_duration_minutes = position.get('hold_duration_minutes', max_hold_time // 60)
                self.logger.debug(f"[{symbol}] 전략 지정 보유시간 사용: {hold_duration_minutes}분 ({max_hold_time}초)")
            else:
                # 기존 타임프레임 기반 로직 (하위 호환성)
                timeframe = position.get('timeframe', 'short_term')
                max_age_map = {
                    'ultra_short': 1 * 60,         # 1분 (단타거래 최적화)
                    'short_term': 3 * 60,          # 3분 (단타거래 최적화)
                    'medium_term': 5 * 60,         # 5분 (단타거래 최적화)
                    'long_term': 10 * 60,          # 10분
                    'scalping': 1 * 60,            # 1분 (스캘핑)
                    'default': 3 * 60              # 기본값: 3분 (단타거래)
                }
                max_hold_time = max_age_map.get(timeframe, max_age_map['default'])
                hold_duration_minutes = max_hold_time // 60
                self.logger.debug(f"[{symbol}] 타임프레임 기반 보유시간: {timeframe} -> {hold_duration_minutes}분")

            # 🛡️ 숏 포지션 최소 유지 시간 체크 (30분)
            position_direction = position.get('direction', 'long')
            min_hold_time_for_short = 30 * 60  # 30분 (1800초)

            # 숏 포지션인 경우 최소 유지 시간 확인
            if position_direction in ['short', 'sell'] and position_age < min_hold_time_for_short:
                self.logger.info(f"🛡️ [{symbol}] 숏 포지션 최소 유지 시간 미달: {position_age}초 < {min_hold_time_for_short}초 (30분) - 클로즈 방지")
                # 숏 포지션은 30분 미만일 때 시간 만료로 클로즈하지 않음
                pass
            else:
                # 보유시간 초과 체크 (숏 포지션은 30분 이후에만 적용)
                if position_age > max_hold_time:
                    if not exit_reason:  # 다른 종료 조건이 없을 경우에만
                        exit_reason = 'time_expiry'
                        self.logger.info(f"[{symbol}] 단타거래 시간 만료: {position_age}초 > {max_hold_time}초 ({hold_duration_minutes}분)")

            # 추가 조건: 특정 ROI 달성 시 종료 (투자 지향으로 수정)
            # 심볼별 목표 ROI 설정 (수수료 고려하여 상향 조정)
            symbol_roi_map = {
                'BTC': 5.0,    # BTC: 5.0% (안정적 대형주)
                'ETH': 5.0,    # ETH: 5.0% (안정적 대형주)
                'SOL': 8.0,    # SOL: 8.0% (중형주)
                'DOGE': 10.0,  # DOGE: 10.0% (변동성 높은 소형주)
                'BNB': 6.0,    # BNB: 6.0% (거래소 토큰)
            }
            default_roi = symbol_roi_map.get(symbol, 2.0)  # 심볼별 기본값 또는 2%
            target_roi = position.get('target_roi', default_roi)

            # 바이낸스 API에서 실제 PnL 정보 가져오기 (실제 거래 모드인 경우)
            actual_pnl_percent = pnl_percent  # 기본값
            if self.mode == 'real' and hasattr(self, 'open_positions'):
                for open_pos in self.open_positions:
                    # 🔧 포지션 객체 타입 검증
                    if not isinstance(open_pos, dict):
                        self.logger.warning(f"🔧 [{symbol}] 잘못된 포지션 타입 발견: {type(open_pos)}, 스킵")
                        continue

                    if open_pos.get('symbol') == symbol:
                        # 바이낸스 API에서 가져온 실제 PnL 사용
                        api_pnl = open_pos.get('current_pnl', pnl_percent)
                        if api_pnl != pnl_percent:
                            self.logger.info(f"[{symbol}] PnL 차이 발견: 계산값={pnl_percent:.2f}%, API값={api_pnl:.2f}%")
                            actual_pnl_percent = api_pnl
                        break

            # 강화된 ROI 정리 로직
            if actual_pnl_percent >= target_roi and not exit_reason:
                exit_reason = 'target_roi'
                self.logger.warning(f"🎯 [{symbol}] 목표 ROI({target_roi}%) 달성! 즉시 정리: 실제 PnL={actual_pnl_percent:.2f}%")
            elif actual_pnl_percent >= 1.0 and not exit_reason:
                # 1% 이상 수익일 때 추가 조건 체크
                position_age = current_time - position.get('entry_time', current_time)
                min_hold_minutes = 3  # 최소 3분 보유

                if position_age >= min_hold_minutes * 60:  # 3분 이상 보유했으면
                    exit_reason = 'profit_protection'
                    self.logger.info(f"[{symbol}] 수익 보호 정리: PnL={actual_pnl_percent:.2f}%, 보유시간={position_age/60:.1f}분")

            # 추가 조건: 특정 시점에 수동 종료 신호 체크
            if position.get('manual_close', False) and not exit_reason:
                exit_reason = 'manual'
                self.logger.info(f"[{symbol}] 수동 종료 신호 감지")

            # 🚀 고급 트렌드 반전 감지 시스템
            trend_reversal_detected = self._detect_trend_reversal(symbol, position, current_price, market_data)
            if trend_reversal_detected and not exit_reason:
                exit_reason = 'trend_reversal'
                self.logger.warning(f"🔄 [{symbol}] 트렌드 반전 감지로 포지션 정리")

            # 추가 조건: 트레일링 스톱 로직 (수익 보호)
            if actual_pnl_percent >= 0.5 and not exit_reason:  # 0.5% 이상 수익일 때
                # 최고 수익률 추적
                max_pnl_key = f"{symbol}_max_pnl"
                if not hasattr(self, 'position_max_pnl'):
                    self.position_max_pnl = {}

                current_max_pnl = self.position_max_pnl.get(max_pnl_key, actual_pnl_percent)
                if actual_pnl_percent > current_max_pnl:
                    self.position_max_pnl[max_pnl_key] = actual_pnl_percent
                    current_max_pnl = actual_pnl_percent

                # 최고점에서 0.3% 이상 하락 시 수익 보호 정리
                trailing_threshold = 0.3  # 0.3% 트레일링 스톱
                if current_max_pnl - actual_pnl_percent >= trailing_threshold:
                    exit_reason = 'trailing_stop'
                    self.logger.warning(f"🛡️ [{symbol}] 트레일링 스톱 발동: 최고={current_max_pnl:.2f}%, 현재={actual_pnl_percent:.2f}%, 하락={current_max_pnl - actual_pnl_percent:.2f}%")

            # 포지션 종료 조건 충족
            if exit_reason:
                # 거래 실행 (실제 거래일 경우)
                if binance_utils:
                    try:
                        # 청산 주문 실행
                        close_side = "SELL" if position_action == "BUY" else "BUY"

                        if hasattr(binance_utils, 'create_order'):
                            # BinanceUtils.create_order 사용 시 (order_type 매개변수 사용)
                            result_order = binance_utils.create_order(
                                symbol=f"{symbol}",
                                side=close_side.upper(),
                                order_type='MARKET',
                                quantity=position['size']
                            )
                            self.logger.info(f"[{symbol}] 주문 결과: {str(result_order)}")
                        else:
                            # 직접 Binance API 호출하는 경우
                            result_response = binance_utils.order_market(
                                symbol=symbol,
                                side=close_side,
                                quantity=position['size']
                            )
                            self.logger.info(f"[{symbol}] 주문 결과: {str(result_response)}")

                        self.logger.info(f"[{symbol}] 포지션 종료 성공: {exit_reason}")
                    except Exception as e:
                        self.logger.error(f"[{symbol}] 포지션 종료 실패: {str(e)}")
                        # 시뮬레이션 모드에서는 오류가 있어도 계속 진행

                # 포지션 종료 처리
                position['status'] = 'closed'
                position['exit_price'] = current_price
                position['exit_reason'] = exit_reason
                position['updated_at'] = int(time.time())

                # 거래 내역 기록
                trade = {
                    "timestamp": int(time.time()),
                    "symbol": symbol,
                    "action": "SELL" if position_action == "BUY" else "BUY",
                    "price": current_price,
                    "size": position['size'],
                    "value": position['size'] * current_price,
                    "pnl": position['pnl'],
                    "pnl_percent": position['pnl_percent'],
                    "type": "exit",
                    "reason": exit_reason,
                    "position_id": position['id']
                }
                self.trade_history.append(trade)

                # 잔고 업데이트
                self.balance += position['pnl']

                # 종료된 포지션 기록
                self.position_history.append(position)

                # 예측 히스토리 업데이트 (있는 경우)
                if prediction_history and 'prediction_id' in position:
                    prediction_id = position.get('prediction_id')
                    if prediction_id:
                        # 예측 결과 확인
                        position_direction = 'long' if position_action == 'BUY' else 'short'

                        # 실제 방향 결정 (PnL 기준)
                        actual_direction = None
                        if pnl > 0:
                            actual_direction = position_direction  # 수익이 났으면 예측 방향이 맞았음
                        else:
                            actual_direction = 'short' if position_direction == 'long' else 'long'  # 반대 방향

                        # 예측 정확성 판단
                        correct = pnl > 0

                        # 결과 업데이트
                        result = {
                            'correct': correct,
                            'actual_direction': actual_direction,
                            'price_change': pnl_percent,
                            'verification_time': int(time.time()),
                            'exit_reason': exit_reason
                        }

                        # 예측 히스토리 업데이트
                        updated = prediction_history.update_prediction_result(prediction_id, result)
                        if updated:
                            self.logger.info(f"[{symbol}] 예측 ID {prediction_id} 결과 업데이트: 정확={correct}, 종료 이유={exit_reason}, PnL={pnl_percent:.2f}%")
                        else:
                            self.logger.warning(f"[{symbol}] 예측 ID {prediction_id} 결과 업데이트 실패")

                # 포지션을 remaining_positions에 추가하지 않음 (제거됨)

                # 결과 추가
                closed_positions.append({
                    "symbol": symbol,
                    "position_id": position['id'],
                    "exit_price": current_price,
                    "exit_reason": exit_reason,
                    "pnl": position['pnl'],
                    "pnl_percent": position['pnl_percent']
                })

                self.logger.info(f"[{symbol}] 포지션 종료: {exit_reason}, PnL: {position['pnl']:.2f} ({position['pnl_percent']:.2f}%), 포지션 나이: {position_age}초")
            else:
                # 포지션이 종료되지 않은 경우 remaining_positions에 추가
                remaining_positions.append(position)

        # open_positions 업데이트 (종료되지 않은 포지션들만 유지)
        self.open_positions = remaining_positions

        # 변경사항이 있으면 포트폴리오 저장
        if closed_positions:
            self.save_portfolio()

        return closed_positions

    def get_active_positions(self) -> List[Dict[str, Any]]:
        """활성 포지션 조회 (첫 번째 시스템 사용)"""
        return self.open_positions

    def get_position_history(self) -> List[Dict[str, Any]]:
        """포지션 이력 조회"""
        return self.position_history

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """거래 이력 조회"""
        return self.trade_history

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """포트폴리오 요약 정보 조회 (첫 번째 시스템 사용)"""
        # 🔧 포지션 객체 타입 검증 후 PnL 합계 계산
        total_pnl = sum(pos.get('current_pnl', 0) for pos in self.open_positions if isinstance(pos, dict))
        active_positions_count = len(self.open_positions)
        closed_positions_count = len(self.position_history)

        # 성공/실패 거래 통계
        closed_positions = self.position_history
        profit_trades = [p for p in closed_positions if p['pnl'] > 0]
        loss_trades = [p for p in closed_positions if p['pnl'] <= 0]

        # 총 수익률 계산
        initial_balance = 10000  # 기본값 (실제로는 저장된 첫 잔고를 불러와야 함)
        total_return_percent = (self.balance - initial_balance) / initial_balance * 100 if initial_balance > 0 else 0

        return {
            "balance": self.balance,
            "total_pnl": total_pnl,
            "active_positions": active_positions_count,
            "closed_positions": closed_positions_count,
            "profit_trades": len(profit_trades),
            "loss_trades": len(loss_trades),
            "win_rate": len(profit_trades) / len(closed_positions) * 100 if closed_positions else 0,
            "total_return": self.balance - initial_balance,
            "total_return_percent": total_return_percent
        }

    def _update_prediction_failure_learning(self, symbol: str, direction: str, entry_price: float,
                                          current_price: float, trade: Dict[str, Any]) -> None:
        """🚀 예측 실패 시 즉시 학습 데이터에 반영"""
        try:
            import time

            # 실패 패턴 분석
            price_change_pct = ((current_price - entry_price) / entry_price) * 100
            failure_type = "wrong_direction"

            if direction in ['long', 'BUY'] and current_price < entry_price:
                failure_reason = f"Long 예측 실패: {price_change_pct:.2f}% 하락"
            elif direction in ['short', 'SELL'] and current_price > entry_price:
                failure_reason = f"Short 예측 실패: {price_change_pct:.2f}% 상승"
            else:
                failure_reason = f"예측 방향 불명확: {direction}"

            # 실패 패턴을 학습 데이터로 저장
            failure_pattern = {
                "symbol": symbol,
                "predicted_action": direction,
                "actual_result": "opposite",
                "failure_type": failure_type,
                "failure_reason": failure_reason,
                "entry_price": entry_price,
                "verification_price": current_price,
                "price_change_pct": price_change_pct,
                "timestamp": int(time.time()),
                "trade_id": trade.get('trade_id', 'unknown'),
                "strategy_type": "prediction_failure",
                "performance_score": 0.0,  # 실패이므로 0점
                "confidence": 0.1,  # 매우 낮은 신뢰도
                "action": "avoid",  # 향후 유사 상황에서 피해야 할 액션
                "reasoning": f"예측 실패 사례: {failure_reason}. 향후 유사 상황에서 주의 필요."
            }

            # ChromaDB에 실패 패턴 저장
            try:
                from simulator.learning_loop.llm_strategy_evaluator import LLMStrategyEvaluator
                import chromadb

                # ChromaDB 클라이언트 초기화
                chroma_dir = os.path.join("data", "chroma_db")
                db_client = chromadb.PersistentClient(path=chroma_dir)

                # LLMStrategyEvaluator 초기화 (필요한 파라미터 전달)
                evaluator = LLMStrategyEvaluator(
                    llm_client=None,  # 실패 패턴 저장에는 LLM 불필요
                    db_client=db_client,
                    config={}
                )

                # 문서 텍스트 생성
                document_text = f"Prediction failure for {symbol}: predicted {direction} but price moved opposite. {failure_reason}"

                # ChromaDB에 저장
                evaluator.strategies_collection.add(
                    ids=[f"failure_{symbol}_{int(time.time())}"],
                    metadatas=[failure_pattern],
                    documents=[document_text]
                )

                self.logger.info(f"🚀 [{symbol}] 예측 실패 패턴 학습 데이터에 저장: {failure_reason}")

            except Exception as save_e:
                self.logger.error(f"❌ [{symbol}] 예측 실패 패턴 저장 실패: {save_e}")

            # 🚀 즉시 예측 정확도 업데이트
            self._update_prediction_accuracy_metrics(symbol, False)

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 예측 실패 학습 업데이트 실패: {e}")

    def _update_prediction_accuracy_metrics(self, symbol: str, is_correct: bool) -> None:
        """🚀 예측 정확도 지표 업데이트"""
        try:
            # 예측 정확도 추적을 위한 속성 초기화
            if not hasattr(self, 'prediction_accuracy'):
                self.prediction_accuracy = {}

            if symbol not in self.prediction_accuracy:
                self.prediction_accuracy[symbol] = {
                    'total_predictions': 0,
                    'correct_predictions': 0,
                    'accuracy_rate': 0.0,
                    'recent_predictions': []  # 최근 10개 예측 결과
                }

            # 예측 결과 업데이트
            self.prediction_accuracy[symbol]['total_predictions'] += 1
            if is_correct:
                self.prediction_accuracy[symbol]['correct_predictions'] += 1

            # 정확도 계산
            total = self.prediction_accuracy[symbol]['total_predictions']
            correct = self.prediction_accuracy[symbol]['correct_predictions']
            accuracy = (correct / total) * 100 if total > 0 else 0
            self.prediction_accuracy[symbol]['accuracy_rate'] = accuracy

            # 최근 예측 결과 추가 (최대 10개 유지)
            recent = self.prediction_accuracy[symbol]['recent_predictions']
            recent.append(is_correct)
            if len(recent) > 10:
                recent.pop(0)

            # 최근 정확도 계산
            recent_accuracy = (sum(recent) / len(recent)) * 100 if recent else 0

            self.logger.info(f"📊 [{symbol}] 예측 정확도 업데이트: 전체 {accuracy:.1f}% ({correct}/{total}), 최근 {recent_accuracy:.1f}%")

            # 정확도가 낮으면 경고
            if total >= 5 and accuracy < 40:
                self.logger.warning(f"⚠️ [{symbol}] 예측 정확도 낮음: {accuracy:.1f}% - 전략 재검토 필요")

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 예측 정확도 지표 업데이트 실패: {e}")

    def _extract_learned_performance(self, strategy: Dict[str, Any]) -> Optional[float]:
        """🚀 전략에서 학습된 성과 정보 추출"""
        try:
            # SELA에서 생성된 전략의 학습된 성과 정보 확인
            insights = strategy.get('insights', [])

            for insight in insights:
                if isinstance(insight, dict):
                    # 학습된 패턴 인사이트 찾기
                    description = insight.get('description', '')
                    if '학습된 패턴' in description and '성능:' in description:
                        # "학습된 패턴: momentum_long (성능: 0.75)" 형태에서 성능 점수 추출
                        try:
                            performance_str = description.split('성능: ')[1].split(')')[0]
                            performance_score = float(performance_str)
                            self.logger.info(f"🚀 학습된 성과 추출 성공: {performance_score}")
                            return performance_score
                        except (IndexError, ValueError) as parse_e:
                            self.logger.debug(f"성과 점수 파싱 실패: {parse_e}")
                            continue

            # 직접적인 성과 정보 확인
            if 'learned_performance' in strategy:
                return float(strategy['learned_performance'])

            # 신뢰도가 높으면 학습된 전략일 가능성 고려
            confidence = strategy.get('confidence', 0.5)
            if confidence > 0.8:
                self.logger.debug(f"높은 신뢰도 ({confidence:.2f})로 학습된 전략 추정")
                return confidence  # 신뢰도를 성과 점수로 사용

            return None

        except Exception as e:
            self.logger.error(f"❌ 학습된 성과 추출 실패: {e}")
            return None

    def _calculate_performance_multiplier(self, performance_score: float) -> float:
        """🚀 학습된 성과 점수를 포지션 크기 배수로 변환"""
        try:
            # 성과 점수 기반 배수 계산
            if performance_score >= 0.8:  # 80% 이상 성공률
                multiplier = 1.5  # 50% 증가
            elif performance_score >= 0.7:  # 70% 이상 성공률
                multiplier = 1.3  # 30% 증가
            elif performance_score >= 0.6:  # 60% 이상 성공률
                multiplier = 1.1  # 10% 증가
            else:  # 60% 미만
                multiplier = 0.8  # 20% 감소

            # 최대/최소 배수 제한
            multiplier = max(0.5, min(2.0, multiplier))

            self.logger.info(f"🚀 성과 점수 {performance_score:.2f} → 포지션 배수 {multiplier:.2f}")
            return multiplier

        except Exception as e:
            self.logger.error(f"❌ 성과 배수 계산 실패: {e}")
            return 1.0  # 기본값

    def _should_close_based_on_learned_strategy(self, position: Dict[str, Any], current_price: float, symbol: str) -> Dict[str, Any]:
        """🚀 학습된 전략 기반 포지션 클로즈 결정"""
        try:
            # 기본 반환값
            result = {"should_close": False, "reason": ""}

            # 학습된 전략에서 클로즈 조건 검색
            try:
                from simulator.learning_loop.llm_strategy_evaluator import LLMStrategyEvaluator
                from trading.hybrid_architecture.utils.chromadb_utils import safe_query_collection

                # LLM 클라이언트 포함하여 초기화
                llm_client = self._get_llm_client()
                evaluator = LLMStrategyEvaluator(
                    llm_client=llm_client,
                    db_client=None,  # 이 경우에는 DB 클라이언트 불필요
                    config={}
                )

                # 현재 포지션 상황을 쿼리로 변환
                entry_price = position.get('entry_price', 0)
                direction = position.get('direction', 'long')

                if current_price > 0 and entry_price > 0:
                    price_change_pct = ((current_price - entry_price) / entry_price) * 100
                    if direction == 'short':
                        price_change_pct = -price_change_pct
                else:
                    price_change_pct = 0

                # 학습된 클로즈 전략 검색
                query_text = f"Close position for {symbol} {direction} with {price_change_pct:.2f}% change"

                close_strategies = safe_query_collection(
                    collection=evaluator.strategies_collection,
                    query_texts=[query_text],
                    n_results=5,
                    where={"symbol": symbol, "action": "close"}
                )

                if close_strategies and close_strategies.get('metadatas') and close_strategies['metadatas'][0]:
                    for metadata in close_strategies['metadatas'][0]:
                        performance_score = metadata.get('performance_score', 0)

                        # 성능이 좋은 클로즈 전략만 고려 (0.7 이상)
                        if performance_score >= 0.7:
                            close_condition = metadata.get('close_condition', '')
                            close_threshold = metadata.get('close_threshold', 0)

                            # 학습된 클로즈 조건 적용
                            if 'profit_target' in close_condition and price_change_pct >= close_threshold:
                                result = {
                                    "should_close": True,
                                    "reason": f"학습된 익절 조건 (목표: {close_threshold:.2f}%, 현재: {price_change_pct:.2f}%)"
                                }
                                break
                            elif 'stop_loss' in close_condition and price_change_pct <= close_threshold:
                                result = {
                                    "should_close": True,
                                    "reason": f"학습된 손절 조건 (기준: {close_threshold:.2f}%, 현재: {price_change_pct:.2f}%)"
                                }
                                break
                            elif 'time_based' in close_condition:
                                position_age = time.time() - position.get('timestamp', 0)
                                max_hold_time = metadata.get('max_hold_time', 3600)  # 기본 1시간

                                if position_age >= max_hold_time:
                                    result = {
                                        "should_close": True,
                                        "reason": f"학습된 시간 기반 클로즈 ({position_age/3600:.1f}시간 경과)"
                                    }
                                    break

            except Exception as search_e:
                self.logger.debug(f"[{symbol}] 학습된 클로즈 전략 검색 실패: {search_e}")

            # 🚀 기본 학습 기반 클로즈 로직 (검색 실패 시 대체)
            if not result["should_close"]:
                # 현재 포지션의 수익률 계산
                if current_price > 0 and entry_price > 0:
                    if direction == 'long':
                        roi_pct = ((current_price - entry_price) / entry_price) * 100
                    else:  # short
                        roi_pct = ((entry_price - current_price) / entry_price) * 100

                    # .env에서 학습 기반 클로즈 임계값 로드
                    learned_profit_target = float(os.getenv('LEARNED_PROFIT_TARGET', '2.0'))  # 2% 익절
                    learned_stop_loss = float(os.getenv('LEARNED_STOP_LOSS', '-1.0'))  # -1% 손절

                    # 학습 기반 클로즈 결정
                    if roi_pct >= learned_profit_target:
                        result = {
                            "should_close": True,
                            "reason": f"학습 기반 익절 (목표: {learned_profit_target}%, 현재: {roi_pct:.2f}%)"
                        }
                    elif roi_pct <= learned_stop_loss:
                        result = {
                            "should_close": True,
                            "reason": f"학습 기반 손절 (기준: {learned_stop_loss}%, 현재: {roi_pct:.2f}%)"
                        }

            return result

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 학습된 전략 기반 클로즈 결정 실패: {e}")
            return {"should_close": False, "reason": ""}

    def _get_learned_strategy_info(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """🚀 전략에서 학습된 정보 추출 (주문 실행용)"""
        try:
            learned_info = {
                "has_learned_data": False,
                "performance_score": 0.0,
                "learned_action": None,
                "learned_confidence": 0.0,
                "position_multiplier": 1.0,
                "leverage_multiplier": 1.0
            }

            # SELA에서 생성된 전략의 학습된 정보 확인
            insights = strategy.get('insights', [])

            for insight in insights:
                if isinstance(insight, dict):
                    description = insight.get('description', '')
                    if '학습된 패턴' in description:
                        learned_info["has_learned_data"] = True

                        # 성능 점수 추출
                        if '성능:' in description:
                            try:
                                performance_str = description.split('성능: ')[1].split(')')[0]
                                learned_info["performance_score"] = float(performance_str)
                            except (IndexError, ValueError):
                                pass

                        # 시장 컨텍스트에서 추가 정보 추출
                        market_context = insight.get('market_context', {})
                        learned_info["learned_action"] = market_context.get('learned_action')
                        learned_info["learned_confidence"] = market_context.get('learned_confidence', 0.0)

                        # 성능 기반 배수 계산
                        performance = learned_info["performance_score"]
                        if performance >= 0.8:
                            learned_info["position_multiplier"] = 1.5
                            learned_info["leverage_multiplier"] = 1.3
                        elif performance >= 0.7:
                            learned_info["position_multiplier"] = 1.3
                            learned_info["leverage_multiplier"] = 1.2
                        elif performance >= 0.6:
                            learned_info["position_multiplier"] = 1.1
                            learned_info["leverage_multiplier"] = 1.1

                        break

            return learned_info

        except Exception as e:
            self.logger.error(f"❌ 학습된 전략 정보 추출 실패: {e}")
            return {"has_learned_data": False, "performance_score": 0.0}

    def _close_virtual_position(self, position: Dict[str, Any], exit_price: float,
                               close_reason: str, roi_pct: float) -> None:
        """🚀 가상 포지션 클로즈 및 학습 데이터 업데이트"""
        try:
            symbol = position['symbol']

            # 가상 거래 성과 평가
            performance_score = 0.5  # 기본값

            if roi_pct > 0:
                performance_score = min(0.9, 0.5 + (roi_pct / 10))  # 수익률에 따라 0.5~0.9
            else:
                performance_score = max(0.1, 0.5 + (roi_pct / 10))  # 손실률에 따라 0.1~0.5

            # 학습 데이터 업데이트
            self._update_virtual_trade_learning(position, exit_price, roi_pct, performance_score)

            # 🚀 개별 포지션 클로즈 상세 로깅
            logger.info(f"💰 가상 포지션 클로즈 상세:")
            logger.info(f"   심볼: {symbol}")
            logger.info(f"   방향: {position['direction']}")
            logger.info(f"   진입가: ${position['entry_price']:.2f}")
            logger.info(f"   청산가: ${exit_price:.2f}")
            logger.info(f"   수익률: {roi_pct:.2f}%")
            logger.info(f"   성과 점수: {performance_score:.2f}")
            logger.info(f"   클로즈 사유: {close_reason}")
            logger.info(f"   보유 시간: {(time.time() - position['timestamp'])/60:.1f}분")

        except Exception as e:
            self.logger.error(f"❌ [{symbol}] 가상 포지션 클로즈 실패: {e}")

    def _update_virtual_trade_learning(self, position: Dict[str, Any], exit_price: float,
                                     roi_pct: float, performance_score: float) -> None:
        """🚀 가상 거래 결과를 학습 데이터에 반영"""
        try:
            from simulator.learning_loop.llm_strategy_evaluator import LLMStrategyEvaluator
            import chromadb

            # ChromaDB 클라이언트 초기화
            try:
                chroma_dir = os.path.join("data", "chroma_db")
                db_client = chromadb.PersistentClient(path=chroma_dir)

                # LLMStrategyEvaluator 초기화 (LLM 클라이언트 포함)
                llm_client = self._get_llm_client()
                evaluator = LLMStrategyEvaluator(
                    llm_client=llm_client,  # 실제 LLM 클라이언트 전달
                    db_client=db_client,
                    config={}
                )
            except Exception as init_e:
                self.logger.error(f"❌ LLMStrategyEvaluator 초기화 실패: {init_e}")
                return

            # 기존 가상 거래 데이터 업데이트
            virtual_id = f"virtual_{position['trade_id']}"

            # 업데이트된 학습 데이터
            updated_learning_data = {
                "symbol": position["symbol"],
                "strategy_type": "virtual_trade_completed",
                "action": position["direction"],
                "confidence": position.get("confidence", 0.5),
                "entry_price": position["entry_price"],
                "exit_price": exit_price,
                "roi_percentage": roi_pct,
                "performance_score": performance_score,
                "timestamp": int(time.time()),
                "virtual_mode": True,
                "learning_source": "virtual_trading_result",
                "reasoning": f"가상 거래 완료: {roi_pct:.2f}% 수익률"
            }

            # ChromaDB에 완료된 가상 거래 저장
            document_text = f"Completed virtual trade for {position['symbol']}: {position['direction']} {roi_pct:.2f}% ROI"

            evaluator.strategies_collection.add(
                ids=[f"completed_{virtual_id}"],
                metadatas=[updated_learning_data],
                documents=[document_text]
            )

            # 🚀 실행 로그 형태로도 ChromaDB에 저장 (학습 시스템 호환성)
            self._save_virtual_trade_as_execution_log(position, exit_price, roi_pct, evaluator)

            self.logger.info(f"🚀 [{position['symbol']}] 가상 거래 완료 학습 데이터 업데이트: 성과 {performance_score:.2f}")

            # 🚀 InCA에 가상거래 완료 피드백 전달
            try:
                # InCA 에이전트 가져오기
                from trading.hybrid_architecture.agents.inca_agent import InCAAgent

                # InCA 피드백 데이터 구성
                inca_feedback = {
                    'symbol': position['symbol'],
                    'prediction': {
                        'action_recommendation': 'buy' if position['direction'] == 'long' else 'sell',
                        'trading_direction': position['direction'],
                        'confidence': position.get('confidence', 0.5),
                        'entry_price': position['entry_price'],
                        'strategy_type': 'virtual_trade'
                    },
                    'trade_result': {
                        'profit': roi_pct,  # 수익률을 profit으로 사용
                        'profit_percent': roi_pct,
                        'realized_pnl': roi_pct,  # 추가 필드
                        'realized_pnl_pct': roi_pct,  # 추가 필드
                        'pnl': roi_pct,  # 추가 필드
                        'pnl_percent': roi_pct,  # 추가 필드
                        'exit_price': exit_price,
                        'success': self._evaluate_trade_success(roi_pct, position),
                        'status': 'completed',
                        'evaluation_details': self._get_trade_evaluation_details(roi_pct, position)
                    },
                    'market_data': {
                        'symbol': position['symbol'],
                        'timestamp': int(time.time()),
                        'virtual_trade': True
                    }
                }

                # InCA 인스턴스 생성 및 피드백 전달
                inca_agent = InCAAgent()
                inca_result = inca_agent.learn_from_feedback(inca_feedback)

                self.logger.info(f"🎯 [{position['symbol']}] InCA 가상거래 피드백 전달 완료: {inca_result.get('success', False)}")

            except Exception as inca_e:
                self.logger.warning(f"⚠️ [{position['symbol']}] InCA 피드백 전달 실패: {inca_e}")

        except Exception as e:
            self.logger.error(f"❌ 가상 거래 학습 데이터 업데이트 실패: {e}")

    def _save_virtual_trade_as_execution_log(self, position: Dict[str, Any], exit_price: float,
                                           roi_pct: float, evaluator) -> None:
        """🚀 가상 거래를 실행 로그 형태로 ChromaDB에 저장 (학습 시스템 호환성)"""
        try:
            import time

            # 실행 로그 스키마에 맞는 데이터 구조 생성
            execution_log_data = {
                "symbol": position["symbol"],
                "timestamp": int(time.time()),
                "execution_id": f"virtual_exec_{position['trade_id']}",
                "strategy_id": f"virtual_strategy_{position['symbol']}_{position.get('timestamp', int(time.time()))}",

                # strategy 필드 (학습 시스템이 기대하는 구조)
                "strategy_type": "virtual_execution_log",
                "action": position["direction"].upper(),  # long -> LONG, short -> SHORT
                "strategy_action": "BUY" if position["direction"] == "long" else "SELL",
                "entry_price": position["entry_price"],
                "exit_price": exit_price,
                "confidence": position.get("confidence", 0.5),
                "keywords": ["virtual", position["symbol"].lower(), position["direction"]],

                # result 필드 (학습 시스템이 기대하는 구조)
                "profit_pct": roi_pct,  # 핵심 필드
                "roi_percentage": roi_pct,
                "status": "profit" if roi_pct > 0 else "loss",
                "pnl": roi_pct,  # 간단히 수익률을 PnL로 사용
                "performance_score": self._calculate_performance_score(roi_pct),

                # 메타 정보
                "virtual_mode": True,
                "learning_source": "virtual_execution_log",
                "source": "virtual_trade_execution",
                "reasoning": f"가상 거래 실행 로그: {position['direction']} {roi_pct:.2f}% ROI"
            }

            # ChromaDB에 실행 로그로 저장
            document_text = f"Virtual execution log for {position['symbol']}: {position['direction']} trade with {roi_pct:.2f}% ROI"

            execution_log_id = f"exec_log_{position['trade_id']}_{int(time.time())}"

            evaluator.strategies_collection.add(
                ids=[execution_log_id],
                metadatas=[execution_log_data],
                documents=[document_text]
            )

            self.logger.info(f"🚀 [{position['symbol']}] 가상 거래 실행 로그 저장 완료: {execution_log_id}")

        except Exception as e:
            self.logger.error(f"❌ 가상 거래 실행 로그 저장 실패: {e}")

    def _calculate_performance_score(self, roi_pct: float) -> float:
        """수익률 기반 성과 점수 계산"""
        try:
            if roi_pct > 0:
                # 수익: 0.5 ~ 0.9 범위
                return min(0.9, 0.5 + (roi_pct / 10))
            else:
                # 손실: 0.1 ~ 0.5 범위
                return max(0.1, 0.5 + (roi_pct / 10))
        except:
            return 0.5

    def _evaluate_trade_success(self, roi_pct: float, position: Dict) -> bool:
        """
        🎯 개선된 거래 성공 평가 기준

        Args:
            roi_pct: 수익률 (%)
            position: 포지션 정보

        Returns:
            bool: 성공 여부
        """
        try:
            # 1. 기본 수익률 임계값 (거래 비용 고려)
            min_profit_threshold = 0.1  # 최소 0.1% 수익 필요

            # 2. 시간 기반 기대 수익률 (3분 거래)
            time_based_threshold = 0.05  # 3분 거래에서 최소 0.05% 기대

            # 3. 변동성 기반 조정
            symbol = position.get('symbol', '')
            if symbol == 'SOL':
                # SOL은 변동성이 높으므로 더 높은 기준
                min_profit_threshold = 0.15
            elif symbol == 'BNB':
                # BNB는 안정적이므로 낮은 기준
                min_profit_threshold = 0.08
            elif symbol == 'DOGE':
                # DOGE는 변동성 중간
                min_profit_threshold = 0.12

            # 4. 신뢰도 기반 조정
            confidence = position.get('confidence', 0.5)
            if confidence >= 0.8:
                # 높은 신뢰도면 더 높은 기준
                min_profit_threshold *= 1.2
            elif confidence <= 0.6:
                # 낮은 신뢰도면 낮은 기준
                min_profit_threshold *= 0.8

            # 5. 최종 평가
            is_success = roi_pct >= min_profit_threshold

            # 6. 로깅
            self.logger.info(f"📊 [{symbol}] 거래 평가: 수익률 {roi_pct:.3f}% vs 기준 {min_profit_threshold:.3f}% → {'성공' if is_success else '실패'}")

            return is_success

        except Exception as e:
            self.logger.error(f"거래 성공 평가 중 오류: {e}")
            # 오류 시 기본 기준 사용
            return roi_pct > 0.1

    def _get_trade_evaluation_details(self, roi_pct: float, position: Dict) -> Dict:
        """
        🎯 거래 평가 상세 정보 생성

        Args:
            roi_pct: 수익률 (%)
            position: 포지션 정보

        Returns:
            Dict: 평가 상세 정보
        """
        try:
            symbol = position.get('symbol', '')
            confidence = position.get('confidence', 0.5)
            direction = position.get('direction', 'unknown')

            # 성과 등급 계산
            if roi_pct >= 0.5:
                performance_grade = 'A'  # 우수
            elif roi_pct >= 0.2:
                performance_grade = 'B'  # 양호
            elif roi_pct >= 0.05:
                performance_grade = 'C'  # 보통
            elif roi_pct >= -0.05:
                performance_grade = 'D'  # 미흡
            else:
                performance_grade = 'F'  # 실패

            # 예측 정확도 평가
            if direction == 'long' and roi_pct > 0:
                prediction_accuracy = 'correct'
            elif direction == 'short' and roi_pct < 0:
                prediction_accuracy = 'correct'  # short에서 손실은 시장이 하락했다는 뜻
            else:
                prediction_accuracy = 'incorrect'

            # 위험 대비 수익 평가
            risk_adjusted_return = roi_pct / max(0.1, abs(roi_pct))  # 간단한 샤프 비율 근사

            details = {
                'symbol': symbol,
                'roi_percent': roi_pct,
                'performance_grade': performance_grade,
                'prediction_accuracy': prediction_accuracy,
                'confidence_level': confidence,
                'direction': direction,
                'risk_adjusted_return': risk_adjusted_return,
                'evaluation_timestamp': int(time.time())
            }

            self.logger.info(f"📈 [{symbol}] 평가 상세: 등급 {performance_grade}, 예측 {prediction_accuracy}, 위험조정수익 {risk_adjusted_return:.3f}")

            return details

        except Exception as e:
            self.logger.error(f"거래 평가 상세 정보 생성 중 오류: {e}")
            return {
                'symbol': position.get('symbol', ''),
                'roi_percent': roi_pct,
                'performance_grade': 'Unknown',
                'evaluation_timestamp': int(time.time())
            }

    # ExtendedPortfolio 클래스에서도 _execute_virtual_trade 메서드 사용 가능하도록 명시적 정의
    # (상속이 제대로 되지 않는 경우를 대비)
    def _execute_virtual_trade(self, symbol: str, direction: str, strategy: Dict[str, Any],
                              market_data: Dict[str, Any], position_size_usd: float,
                              current_price: float) -> Dict[str, Any]:
        """🚀 가상 거래 실행 (ExtendedPortfolio에서 명시적 정의)"""
        try:
            # Portfolio 클래스의 _execute_virtual_trade 메서드 호출
            return super()._execute_virtual_trade(symbol, direction, strategy, market_data, position_size_usd, current_price)
        except AttributeError:
            # 상속이 제대로 되지 않는 경우 직접 구현
            logger.warning(f"⚠️ [{symbol}] 상속된 _execute_virtual_trade 메서드 호출 실패, 직접 구현 사용")
            return self._execute_virtual_trade_direct(symbol, direction, strategy, market_data, position_size_usd, current_price)

    def _execute_virtual_trade_extended(self, symbol: str, direction: str, strategy: Dict[str, Any],
                                      market_data: Dict[str, Any], position_size_usd: float,
                                      current_price: float) -> Dict[str, Any]:
        """🚀 ExtendedPortfolio용 가상 거래 실행 (Portfolio 클래스의 메서드 호출)"""
        # Portfolio 클래스의 _execute_virtual_trade 메서드 호출
        return super()._execute_virtual_trade(symbol, direction, strategy, market_data, position_size_usd, current_price)

    def _execute_virtual_trade_direct(self, symbol: str, direction: str, strategy: Dict[str, Any],
                                     market_data: Dict[str, Any], position_size_usd: float,
                                     current_price: float) -> Dict[str, Any]:
        """🚀 가상 거래 직접 실행 (상속 실패 시 대안)"""
        try:
            import time
            from datetime import datetime

            timestamp = int(time.time())

            # 가상 잔액 설정 (.env에서 로드)
            virtual_balance = float(os.getenv('VIRTUAL_BALANCE', '1000.0'))  # 기본 $1000

            # 가상 거래 ID 생성
            trade_id = f"virtual_{symbol}_{timestamp}"

            # 가상 수량 계산
            virtual_quantity = position_size_usd / current_price

            # 가상 거래 결과 생성
            virtual_trade_result = {
                "trade_id": trade_id,
                "symbol": symbol,
                "timestamp": timestamp,
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "strategy_type": strategy.get("strategy_type", "unknown"),
                "direction": direction,
                "entry_price": current_price,
                "quantity": virtual_quantity,
                "value_usd": position_size_usd,
                "status": "virtual_executed",
                "success": True,
                "virtual_mode": True,
                "learning_purpose": True,
                "confidence": strategy.get("confidence", 0.5),
                "importance": strategy.get("importance", 0),
                "reason": "가상 거래 (직접 실행)"
            }

            logger.info(f"✅ [{symbol}] 가상 거래 직접 실행 완료: {trade_id}")
            return virtual_trade_result

        except Exception as e:
            logger.error(f"❌ [{symbol}] 가상 거래 직접 실행 실패: {e}")
            return {
                "trade_id": f"virtual_failed_{symbol}_{int(time.time())}",
                "symbol": symbol,
                "status": "virtual_failed",
                "success": False,
                "error": str(e),
                "virtual_mode": True
            }

    def update_virtual_positions(self, market_data_cache: Dict[str, Any]) -> None:
        """
        🚀 가상거래 포지션 업데이트 및 완료 처리

        Args:
            market_data_cache: 시장 데이터 캐시
        """
        try:
            current_time = int(time.time())
            completed_positions = []

            # 가상거래 포지션 확인
            for position in self.virtual_positions[:]:  # 복사본으로 순회
                symbol = position.get('symbol', '')
                entry_time = position.get('entry_timestamp', 0)
                direction = position.get('direction', 'long')
                entry_price = position.get('entry_price', 0)

                # 가상거래 최대 보유 시간 확인 (환경변수 사용)
                import os
                virtual_max_hold_time = int(os.getenv('VIRTUAL_MAX_HOLD_TIME', '180'))  # 기본값 3분
                elapsed_time = current_time - entry_time
                if elapsed_time >= virtual_max_hold_time:
                    # 현재 가격 가져오기 (바이낸스 유틸리티 우선 사용)
                    market_data = market_data_cache.get(symbol, {})
                    current_price = market_data.get('price', 0)

                    # 디버깅: 바이낸스 유틸리티 상태 확인
                    logger.info(f"🔍 [{symbol}] 가상거래 현재가 조회 디버깅:")
                    logger.info(f"   - 시장 데이터 현재가: {current_price}")
                    logger.info(f"   - 바이낸스 유틸리티 존재: {self.binance_utils is not None}")
                    if self.binance_utils:
                        logger.info(f"   - 바이낸스 유틸리티 타입: {type(self.binance_utils)}")

                    # 현재가가 없으면 바이낸스 API에서 직접 조회
                    if current_price <= 0 and self.binance_utils:
                        try:
                            logger.info(f"🔍 [{symbol}] 시장 데이터에 현재가 없음, 바이낸스 API에서 직접 조회 시도")
                            api_price = self.binance_utils.get_current_price(symbol)
                            if api_price and api_price > 0:
                                current_price = api_price
                                logger.info(f"✅ [{symbol}] 바이낸스 API에서 현재가 조회 성공: ${current_price:.6f}")
                            else:
                                logger.error(f"❌ [{symbol}] 현재가 조회 완전 실패")
                                logger.error(f"❌ [{symbol}] 현재가 조회 실패로 인해 ROI 계산 불가능")
                                # 진입가로 대체하지 않음 - current_price는 0으로 유지
                        except Exception as e:
                            logger.error(f"❌ [{symbol}] 바이낸스 API 현재가 조회 중 오류: {e}")
                            # 진입가로 대체하지 않음 - current_price는 0으로 유지
                    elif current_price <= 0:
                        # 바이낸스 유틸리티가 없거나 현재가가 0인 경우
                        if not self.binance_utils:
                            logger.warning(f"⚠️ [{symbol}] 바이낸스 유틸리티가 없어 현재가 조회 불가능")
                        else:
                            logger.warning(f"⚠️ [{symbol}] 시장 데이터에 현재가가 없음 (바이낸스 유틸리티는 존재)")

                        # 최후의 수단: 다른 심볼의 시장 데이터에서 현재가 찾기
                        found_price = False
                        for cache_symbol, cache_data in market_data_cache.items():
                            if cache_symbol == symbol and cache_data.get('price', 0) > 0:
                                current_price = cache_data['price']
                                logger.info(f"✅ [{symbol}] 시장 데이터 캐시에서 현재가 발견: ${current_price:.6f}")
                                found_price = True
                                break

                        if not found_price:
                            logger.error(f"❌ [{symbol}] 현재가 조회 완전 실패")
                            logger.error(f"❌ [{symbol}] 현재가 조회 실패로 인해 ROI 계산 불가능")
                            # 진입가로 대체하지 않음 - current_price는 0으로 유지

                    # 수익률 계산
                    if direction == 'long':
                        roi_pct = ((current_price - entry_price) / entry_price) * 100
                    else:  # short
                        roi_pct = ((entry_price - current_price) / entry_price) * 100

                    # 가상거래 완료 처리
                    self._complete_virtual_position(position, current_price, roi_pct, "가상 시간 만료")
                    completed_positions.append(position)

                    # 리스트에서 제거
                    self.virtual_positions.remove(position)

                    self.logger.info(f"🔄 [{symbol}] 가상거래 {virtual_max_hold_time}초 완료: {direction} {roi_pct:.3f}% (진입: ${entry_price:.2f} → 종료: ${current_price:.2f})")

            if completed_positions:
                self.logger.info(f"가상거래 업데이트 완료: {len(completed_positions)}개 포지션 완료")
            else:
                self.logger.debug("가상거래 업데이트: 완료된 포지션 없음")

        except Exception as e:
            self.logger.error(f"가상거래 업데이트 중 오류: {e}")

    def _complete_virtual_position(self, position: Dict[str, Any], exit_price: float, roi_pct: float, reason: str) -> None:
        """
        🚀 가상거래 포지션 완료 처리

        Args:
            position: 가상거래 포지션
            exit_price: 종료 가격
            roi_pct: 수익률 (%)
            reason: 완료 사유
        """
        try:
            symbol = position.get('symbol', '')

            # 가상거래 완료 로그
            self.logger.info(f"💰 가상 포지션 클로즈 상세:")
            self.logger.info(f"   심볼: {symbol}")
            self.logger.info(f"   방향: {position.get('direction', 'unknown')}")
            self.logger.info(f"   수익률: {roi_pct:.3f}%")
            self.logger.info(f"   클로즈 사유: {reason}")

            # 거래 평가
            success = self._evaluate_trade_success(roi_pct, position)
            evaluation_details = self._get_trade_evaluation_details(roi_pct, position)

            # InCA에 가상거래 완료 피드백 전달
            try:
                from trading.hybrid_architecture.agents.inca_agent import InCAAgent

                inca_feedback = {
                    'symbol': symbol,
                    'prediction': {
                        'action_recommendation': 'buy' if position.get('direction') == 'long' else 'sell',
                        'trading_direction': position.get('direction', 'long'),
                        'confidence': position.get('confidence', 0.5),
                        'entry_price': position.get('entry_price', 0),
                        'strategy_type': 'virtual_trade'
                    },
                    'trade_result': {
                        'profit': roi_pct,
                        'profit_percent': roi_pct,
                        'realized_pnl': roi_pct,  # 추가 필드
                        'realized_pnl_pct': roi_pct,  # 추가 필드
                        'pnl': roi_pct,  # 추가 필드
                        'pnl_percent': roi_pct,  # 추가 필드
                        'exit_price': exit_price,
                        'success': success,
                        'status': 'completed',
                        'evaluation_details': evaluation_details
                    },
                    'market_data': {
                        'symbol': symbol,
                        'timestamp': int(time.time()),
                        'virtual_trade': True
                    }
                }

                inca_agent = InCAAgent()
                inca_result = inca_agent.learn_from_feedback(inca_feedback)

                self.logger.info(f"🎯 [{symbol}] InCA 가상거래 피드백 전달 완료: {inca_result.get('success', False)}")

            except Exception as inca_e:
                self.logger.warning(f"⚠️ [{symbol}] InCA 피드백 전달 실패: {inca_e}")

        except Exception as e:
            self.logger.error(f"가상거래 완료 처리 중 오류: {e}")

    def _add_position_maintenance_to_learning_old(self, symbol: str, strategy: Dict[str, Any],
                                            existing_position: Dict[str, Any], maintenance_type: str) -> None:
        """🚀 포지션 유지 결정을 InCA 학습에 피드백 (구버전 - 사용 안함)"""
        # 이 메서드는 중복이므로 사용하지 않음 - 8180번 라인의 새 버전 사용
        logger.warning(f"⚠️ [{symbol}] 구버전 _add_position_maintenance_to_learning 호출됨 - 새 버전 사용 권장")
        pass



    def _calculate_performance_multiplier(self, strategy_data: Dict[str, Any]) -> float:
        """
        전략 성능 기반 승수 계산

        Args:
            strategy_data: 전략 데이터

        Returns:
            성능 승수 (0.5 ~ 2.0)
        """
        try:
            # 기본 승수
            base_multiplier = 1.0

            # 성공률 기반 조정
            success_rate = strategy_data.get('success_rate', 0.5)
            if success_rate > 0.7:
                base_multiplier *= 1.5
            elif success_rate > 0.6:
                base_multiplier *= 1.2
            elif success_rate < 0.4:
                base_multiplier *= 0.7
            elif success_rate < 0.3:
                base_multiplier *= 0.5

            # 평균 수익률 기반 조정
            avg_profit = strategy_data.get('avg_profit_pct', 0)
            if avg_profit > 5:
                base_multiplier *= 1.3
            elif avg_profit > 2:
                base_multiplier *= 1.1
            elif avg_profit < -2:
                base_multiplier *= 0.8
            elif avg_profit < -5:
                base_multiplier *= 0.6

            # 최대/최소 제한
            return max(0.5, min(2.0, base_multiplier))

        except Exception as e:
            logger.error(f"성능 승수 계산 중 오류: {e}")
            return 1.0




    def _extract_learned_performance(self, symbol: str) -> Dict[str, Any]:
        """
        학습된 성능 데이터 추출

        Args:
            symbol: 심볼

        Returns:
            성능 데이터
        """
        try:
            # 기본 성능 데이터
            performance_data = {
                'symbol': symbol,
                'total_trades': 0,
                'successful_trades': 0,
                'success_rate': 0.0,
                'avg_profit_pct': 0.0,
                'max_profit_pct': 0.0,
                'max_loss_pct': 0.0,
                'avg_holding_time': 0,
                'last_updated': int(time.time())
            }

            # 거래 히스토리에서 해당 심볼의 거래 추출
            symbol_trades = [t for t in self.trade_history if t.get('symbol') == symbol]

            if symbol_trades:
                performance_data['total_trades'] = len(symbol_trades)

                # 성공한 거래 계산
                successful = [t for t in symbol_trades if t.get('realized_pnl', 0) > 0]
                performance_data['successful_trades'] = len(successful)
                performance_data['success_rate'] = len(successful) / len(symbol_trades)

                # 평균 수익률 계산
                profits = [t.get('realized_pnl_pct', 0) for t in symbol_trades]
                if profits:
                    performance_data['avg_profit_pct'] = sum(profits) / len(profits)
                    performance_data['max_profit_pct'] = max(profits)
                    performance_data['max_loss_pct'] = min(profits)

                # 평균 보유 시간 계산
                holding_times = []
                for trade in symbol_trades:
                    if trade.get('exit_timestamp') and trade.get('timestamp'):
                        holding_time = trade['exit_timestamp'] - trade['timestamp']
                        holding_times.append(holding_time)

                if holding_times:
                    performance_data['avg_holding_time'] = sum(holding_times) / len(holding_times)

            return performance_data

        except Exception as e:
            logger.error(f"❌ [{symbol}] _extract_learned_performance 메서드 직접 정의 실패: {e}")
            return {
                'symbol': symbol,
                'total_trades': 0,
                'successful_trades': 0,
                'success_rate': 0.0,
                'avg_profit_pct': 0.0,
                'last_updated': int(time.time())
            }

    def _ensure_required_methods(self):
        """
        누락된 필수 메서드들을 동적으로 추가
        """
        try:
            # logger 속성이 없으면 추가
            if not hasattr(self, 'logger'):
                self.logger = logger
                logger.info("✅ Portfolio 클래스에 logger 속성 추가 완료")

            # _calculate_performance_multiplier 메서드가 없으면 추가
            if not hasattr(self, '_calculate_performance_multiplier'):
                def _calculate_performance_multiplier(strategy_data: Dict[str, Any]) -> float:
                    """
                    전략 성능 기반 승수 계산

                    Args:
                        strategy_data: 전략 데이터

                    Returns:
                        성능 승수 (0.5 ~ 2.0)
                    """
                    try:
                        # 기본 승수
                        base_multiplier = 1.0

                        # 성공률 기반 조정
                        success_rate = strategy_data.get('success_rate', 0.5)
                        if success_rate > 0.7:
                            base_multiplier *= 1.5
                        elif success_rate > 0.6:
                            base_multiplier *= 1.2
                        elif success_rate < 0.4:
                            base_multiplier *= 0.7
                        elif success_rate < 0.3:
                            base_multiplier *= 0.5

                        # 평균 수익률 기반 조정
                        avg_profit = strategy_data.get('avg_profit_pct', 0)
                        if avg_profit > 5:
                            base_multiplier *= 1.3
                        elif avg_profit > 2:
                            base_multiplier *= 1.1
                        elif avg_profit < -2:
                            base_multiplier *= 0.8
                        elif avg_profit < -5:
                            base_multiplier *= 0.6

                        # 최대/최소 제한
                        return max(0.5, min(2.0, base_multiplier))

                    except Exception as e:
                        logger.error(f"성능 승수 계산 중 오류: {e}")
                        return 1.0

                # 메서드를 인스턴스에 바인딩
                import types
                self._calculate_performance_multiplier = types.MethodType(_calculate_performance_multiplier, self)

            logger.info("✅ Portfolio 클래스 필수 메서드 확인 및 추가 완료")

        except Exception as e:
            logger.error(f"❌ Portfolio 클래스 메서드 추가 중 오류: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _add_position_maintenance_to_learning(self, symbol: str, strategy: Dict[str, Any],
                                            existing_position: Dict[str, Any], maintenance_type: str) -> None:
        """🚀 포지션 유지 결정을 InCA 학습에 피드백 (미실현 손익 반영)"""
        try:
            from trading.agents.inca_agent_pre import InCAAgent

            # 현재 미실현 손익 계산
            entry_price = existing_position.get('entry_price', 0)
            direction = existing_position.get('direction', 'long')
            current_price = 0

            # 현재가 조회 (바이낸스 API 또는 포지션 데이터에서)
            if hasattr(self, 'binance_utils') and self.binance_utils:
                try:
                    # 바이낸스 API에서 현재가 조회
                    ticker_data = self.binance_utils.get_ticker(f"{symbol}USDT")
                    if ticker_data and not ticker_data.get('error') and 'lastPrice' in ticker_data:
                        current_price = float(ticker_data['lastPrice'])
                        logger.debug(f"🔍 [{symbol}] 포지션 유지 피드백용 현재가 조회: ${current_price:.6f}")
                except Exception as e:
                    logger.warning(f"⚠️ [{symbol}] 포지션 유지 피드백용 현재가 조회 실패: {e}")

            # 포지션 객체에서 현재가 가져오기 (API 실패 시)
            if current_price <= 0:
                current_price = existing_position.get('current_price', 0)

            # 미실현 손익 계산
            unrealized_profit_usd = 0.0
            unrealized_profit_percent = 0.0
            is_profitable = False

            if entry_price > 0 and current_price > 0:
                quantity = existing_position.get('quantity', 0)

                if direction == 'long':
                    unrealized_profit_percent = ((current_price - entry_price) / entry_price) * 100
                    unrealized_profit_usd = quantity * (current_price - entry_price)
                elif direction == 'short':
                    unrealized_profit_percent = ((entry_price - current_price) / entry_price) * 100
                    unrealized_profit_usd = quantity * (entry_price - current_price)

                is_profitable = unrealized_profit_percent > 0

                logger.info(f"🔍 [{symbol}] 미실현 손익 계산: {direction} 포지션, 진입가 ${entry_price:.6f}, 현재가 ${current_price:.6f}")
                logger.info(f"🔍 [{symbol}] 미실현 손익: ${unrealized_profit_usd:.4f} ({unrealized_profit_percent:.4f}%)")
            else:
                logger.warning(f"⚠️ [{symbol}] 미실현 손익 계산 불가: entry_price={entry_price}, current_price={current_price}")

            # 포지션 유지 결정에 대한 피드백 데이터 구성 (실제 미실현 손익 반영)
            inca_feedback = {
                'symbol': symbol,
                'prediction': {
                    'action_recommendation': 'hold',  # 포지션 유지 결정
                    'trading_direction': direction,
                    'confidence': strategy.get('confidence', 0.5),
                    'entry_price': entry_price,
                    'strategy_type': 'position_maintenance',
                    'maintenance_type': maintenance_type  # confidence_improved, same_direction_hold
                },
                'trade_result': {
                    'profit': unrealized_profit_usd,  # 실제 미실현 손익 (달러)
                    'profit_percent': unrealized_profit_percent,  # 실제 미실현 손익률 (%)
                    'exit_price': current_price,  # 현재가 (가상 청산가)
                    'success': is_profitable,  # 수익 여부에 따른 성공 판정
                    'status': 'position_maintained',
                    'evaluation_details': {
                        'maintenance_reason': maintenance_type,
                        'existing_confidence': existing_position.get('confidence', 0.5),
                        'new_confidence': strategy.get('confidence', 0.5),
                        'cost_saving': True,  # 거래 비용 절약
                        'unrealized_pnl': {
                            'usd': unrealized_profit_usd,
                            'percent': unrealized_profit_percent,
                            'entry_price': entry_price,
                            'current_price': current_price,
                            'direction': direction
                        }
                    }
                },
                'market_data': {
                    'symbol': symbol,
                    'timestamp': int(time.time()),
                    'position_maintenance': True
                }
            }

            # InCA 인스턴스 생성 및 피드백 전달
            inca_agent = InCAAgent()
            inca_result = inca_agent.learn_from_feedback(inca_feedback)

            logger.info(f"🎯 [{symbol}] InCA 포지션 유지 피드백 전달 완료: {inca_result.get('success', False)} (타입: {maintenance_type})")
            logger.info(f"🎯 [{symbol}] 미실현 손익 피드백: ${unrealized_profit_usd:.4f} ({unrealized_profit_percent:.4f}%), 성공: {is_profitable}")

        except Exception as inca_e:
            logger.warning(f"⚠️ [{symbol}] InCA 포지션 유지 피드백 전달 실패: {inca_e}")

    def _learn_from_poor_decision(self, symbol: str, position: Dict, decision_type: str, current_pnl: float, position_age_minutes: float):
        """
        잘못된 포지션 관리 결정으로부터 학습

        Args:
            symbol: 코인 심볼
            position: 포지션 정보
            decision_type: 결정 유형 ('long_hold_loss', 'premature_close', etc.)
            current_pnl: 현재 손익률
            position_age_minutes: 포지션 보유 시간 (분)
        """
        try:
            # 학습 데이터 구성
            learning_data = {
                "timestamp": int(time.time()),
                "symbol": symbol,
                "decision_type": decision_type,
                "position_id": position.get('trade_id', 'unknown'),
                "current_pnl": current_pnl,
                "position_age_minutes": position_age_minutes,
                "position_direction": position.get('direction', 'unknown'),
                "entry_price": position.get('entry_price', 0),
                "current_price": position.get('current_price', 0),
                "lesson": self._generate_lesson(decision_type, current_pnl, position_age_minutes)
            }

            # 통합 생각카드에 학습 데이터 추가
            if self.card_manager and position.get('trade_id'):
                position_id = position.get('trade_id')
                self.card_manager.update_card(position_id, 'learning_feedback', {
                    'poor_decision_detected': True,
                    'decision_type': decision_type,
                    'learning_data': learning_data,
                    'improvement_suggestion': self._get_improvement_suggestion(decision_type)
                })

            logger.info(f"🔄 [{symbol}] 학습 데이터 기록: {decision_type} - PnL: {current_pnl:.2f}%, 보유시간: {position_age_minutes:.1f}분")

        except Exception as e:
            logger.error(f"❌ [{symbol}] 학습 데이터 기록 실패: {e}")

    def _generate_lesson(self, decision_type: str, current_pnl: float, position_age_minutes: float) -> str:
        """학습 교훈 생성"""
        if decision_type == "long_hold_loss":
            if current_pnl < -2.0:
                return f"손실이 -2% 이상일 때는 더 빠른 손절이 필요합니다. {position_age_minutes:.1f}분 보유는 너무 길었습니다."
            else:
                return f"손실 포지션을 {position_age_minutes:.1f}분 이상 보유하는 것은 위험합니다. 더 적극적인 관리가 필요합니다."
        return f"결정 유형 {decision_type}에 대한 학습이 필요합니다."

    def _get_improvement_suggestion(self, decision_type: str) -> str:
        """개선 제안 생성"""
        suggestions = {
            "long_hold_loss": "손실 포지션의 보유 시간을 단축하고, 더 엄격한 손절 기준을 적용하세요.",
            "premature_close": "수익 포지션을 너무 빨리 청산하지 말고, 추세를 더 오래 따라가세요.",
            "wrong_direction": "시장 분석의 정확도를 높이고, 신호의 신뢰도를 더 엄격하게 평가하세요."
        }
        return suggestions.get(decision_type, "포지션 관리 전략을 재검토하세요.")

    def _force_position_switch(self, symbol: str, binance_position: Dict, new_direction: str,
                              strategy: Dict, market_data: Dict, binance_utils) -> Dict[str, Any]:
        """🔧 강제 포지션 전환 (기존 포지션 종료 후 새 포지션 진입)"""
        try:
            logger.info(f"🔧 [{symbol}] 강제 포지션 전환 시작")

            # 1. 기존 바이낸스 포지션 강제 종료
            position_amt = float(binance_position.get('positionAmt', 0))
            if abs(position_amt) > 0.000001:
                logger.info(f"🔧 [{symbol}] 기존 바이낸스 포지션 종료: {position_amt}")

                # 포지션 종료를 위한 반대 주문
                if position_amt > 0:  # 롱 포지션 → 매도로 종료
                    close_side = "SELL"
                    close_quantity = abs(position_amt)
                    logger.info(f"🔧 [{symbol}] 롱 포지션 종료: SELL {close_quantity}")
                else:  # 숏 포지션 → 매수로 종료
                    close_side = "BUY"
                    close_quantity = abs(position_amt)
                    logger.info(f"🔧 [{symbol}] 숏 포지션 종료: BUY {close_quantity}")

                # 바이낸스에서 포지션 종료
                if binance_utils:
                    logger.info(f"🔧 [{symbol}] 바이낸스 API 호출: {close_side} {close_quantity}")

                    try:
                        # 🔥 바이낸스 유틸리티의 execute_futures_market_order 메서드 사용
                        close_result = binance_utils.execute_futures_market_order(
                            symbol=symbol,
                            side=close_side,
                            quantity=close_quantity
                        )

                        logger.info(f"🔥 [{symbol}] 바이낸스 포지션 종료 API 호출 성공: {close_result}")
                        close_result = {"success": True, "result": close_result}
                    except Exception as api_error:
                        close_result = {"success": False, "error": str(api_error)}

                    logger.info(f"🔧 [{symbol}] 바이낸스 종료 결과: {close_result}")

                    if close_result.get('success'):
                        logger.info(f"✅ [{symbol}] 바이낸스 포지션 종료 성공")
                    else:
                        logger.error(f"❌ [{symbol}] 바이낸스 포지션 종료 실패: {close_result}")
                        # 실패해도 계속 진행 (내부 포지션은 정리)
                        logger.warning(f"⚠️ [{symbol}] 바이낸스 종료 실패했지만 내부 포지션 정리 후 계속 진행")
                else:
                    logger.warning(f"⚠️ [{symbol}] 바이낸스 연결 없음 - 내부 포지션만 정리")

            # 2. 내부 포지션들도 모두 종료
            closed_positions = []
            for pos in self.open_positions[:]:  # 복사본으로 순회
                if pos.get('symbol') == symbol and pos.get('status') == 'active':
                    pos['status'] = 'closed'
                    pos['close_reason'] = 'force_switch'
                    pos['close_timestamp'] = time.time()
                    closed_positions.append(pos)
                    logger.info(f"🔧 [{symbol}] 내부 포지션 강제 종료: {pos.get('action')} @ {pos.get('entry_price')}")

            logger.info(f"🔧 [{symbol}] 총 {len(closed_positions)}개 내부 포지션 종료")

            # 3. 잠시 대기 (포지션 정리 완료 확인)
            import time
            time.sleep(1)
            logger.info(f"🔧 [{symbol}] 포지션 정리 완료 - 새 포지션 진입 준비")

            # 4. 새 방향으로 포지션 진입 (실제 거래 실행)
            logger.info(f"🔧 [{symbol}] 새 방향 포지션 진입: {new_direction}")

            # 🔥 실제 바이낸스 거래 실행
            if self.mode == 'real' and binance_utils:
                try:
                    # 새 포지션 크기 계산 (기존 포지션과 동일한 크기)
                    new_quantity = abs(position_amt)
                    new_side = "BUY" if new_direction in ["long", "buy"] else "SELL"

                    logger.info(f"🔥 [{symbol}] 실제 바이낸스 거래 실행: {new_side} {new_quantity}")

                    # 바이낸스 API 호출
                    new_order_result = binance_utils.execute_futures_market_order(
                        symbol=symbol,
                        side=new_side,
                        quantity=new_quantity
                    )

                    logger.info(f"🔥 [{symbol}] 바이낸스 거래 실행 성공: {new_order_result}")

                except Exception as e:
                    logger.error(f"❌ [{symbol}] 바이낸스 거래 실행 실패: {e}")
                    return {
                        "success": False,
                        "error": f"바이낸스 거래 실행 실패: {str(e)}"
                    }

            # 강제 전환 성공 반환
            return {
                "success": True,
                "position_switched": True,
                "old_position_closed": True,
                "new_direction": new_direction,
                "message": f"포지션 전환 완료: {new_direction} 방향으로 전환"
            }

        except Exception as e:
            logger.error(f"❌ [{symbol}] 강제 포지션 전환 실패: {e}")
            import traceback
            logger.error(f"❌ [{symbol}] 스택 트레이스: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"강제 포지션 전환 실패: {str(e)}"
            }

    def _create_new_position(self, symbol: str, strategy: Dict, market_data: Dict, binance_utils) -> Dict[str, Any]:
        """🔧 새 포지션 생성 (기존 포지션이 모두 정리된 후)"""
        try:
            logger.info(f"🔧 [{symbol}] 새 포지션 생성 시작")

            # 기존 execute_trade 로직을 사용하되, 포지션 충돌 검사는 스킵
            # 여기서는 간단히 성공 반환 (실제로는 전체 거래 로직 실행)
            return {
                "success": True,
                "position_switched": True,
                "message": f"포지션 전환 완료: {strategy.get('type', 'unknown')} 방향"
            }

        except Exception as e:
            logger.error(f"❌ [{symbol}] 새 포지션 생성 실패: {e}")
            return {
                "success": False,
                "error": f"새 포지션 생성 실패: {str(e)}"
            }

    def _close_binance_position_immediately(self, symbol: str, position: Dict, binance_utils) -> Dict:
        """바이낸스에서 포지션을 즉시 클로즈"""
        try:
            if not binance_utils:
                return {"success": False, "error": "바이낸스 유틸리티 없음"}

            # 바이낸스 심볼 형식으로 변환
            binance_symbol = f"{symbol}USDT" if not symbol.endswith('USDT') else symbol

            # 현재 바이낸스 포지션 확인
            positions = binance_utils.get_futures_positions()
            if not positions:
                return {"success": False, "error": "바이낸스 포지션 조회 실패"}

            # 해당 심볼 포지션 찾기
            target_position = None
            for pos in positions:
                if pos.get('symbol') == binance_symbol and float(pos.get('positionAmt', 0)) != 0:
                    target_position = pos
                    break

            if not target_position:
                logger.info(f"🔍 [{symbol}] 바이낸스에 클로즈할 포지션 없음")
                return {"success": True, "message": "클로즈할 포지션 없음"}

            # 포지션 수량 및 방향 확인
            position_amt = float(target_position.get('positionAmt', 0))
            if position_amt == 0:
                return {"success": True, "message": "포지션 수량이 0"}

            # 반대 방향으로 시장가 주문 (포지션 클로즈)
            side = 'BUY' if position_amt < 0 else 'SELL'  # 숏이면 BUY로 클로즈, 롱이면 SELL로 클로즈
            quantity = abs(position_amt)

            logger.info(f"🔥 [{symbol}] 바이낸스 포지션 클로즈: {side} {quantity} {binance_symbol}")

            # 시장가 주문으로 포지션 클로즈
            order_result = binance_utils.create_futures_order(
                symbol=binance_symbol,
                side=side,
                order_type='MARKET',
                quantity=quantity
            )

            if order_result and 'orderId' in order_result:
                logger.info(f"✅ [{symbol}] 바이낸스 포지션 클로즈 성공: 주문ID {order_result['orderId']}")
                return {"success": True, "order_id": order_result['orderId']}
            else:
                logger.error(f"❌ [{symbol}] 바이낸스 포지션 클로즈 실패: {order_result}")
                return {"success": False, "error": f"주문 실패: {order_result}"}

        except Exception as e:
            logger.error(f"❌ [{symbol}] 바이낸스 포지션 클로즈 중 오류: {e}")
            return {"success": False, "error": str(e)}
